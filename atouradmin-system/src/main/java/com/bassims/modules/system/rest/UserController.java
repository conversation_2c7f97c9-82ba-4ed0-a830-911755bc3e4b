/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.system.rest;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.config.RsaProperties;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.ProjectStakeholders;
import com.bassims.modules.atour.repository.ProjectStakeholdersRepository;
import com.bassims.modules.atourWithout.domain.AtosUserRequest;
import com.bassims.modules.system.domain.User;
import com.bassims.modules.system.domain.vo.UserPassVo;
import com.bassims.modules.system.service.*;
import com.bassims.modules.system.service.dto.*;
import com.bassims.utils.PageUtil;
import com.bassims.utils.RsaUtils;
import com.bassims.utils.SecurityUtils;
import com.bassims.utils.enums.CodeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Jie
 * @date 2018-11-23
 */
@Api(tags = "系统：用户管理")
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
public class UserController {

    private final PasswordEncoder passwordEncoder;
    private final UserService userService;
    private final DataService dataService;
    private final DeptService deptService;
    private final RoleService roleService;
    private final VerifyService verificationCodeService;
    private final ProjectStakeholdersRepository projectStakeholdersRepository;

    @ResponseBody
    @AnonymousAccess
    @Log("更新用户角色")
    @ApiOperation("更新用户角色")
    @PostMapping("/upUserRole")
    public String upUserRole() {
        userService.upUserRole();
        return "更新用户角色成功";
    }

    /**
     * @real_return {@link ResponseEntity<List< TemplateConfigDto >>}
     */
    @GetMapping("/findByName")
    @Log("查询用户")
    @ApiOperation("查询用户")
    public ResponseEntity<Object> findByName(TemplateConfigQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(userService.findByName(criteria.getUserName()), HttpStatus.OK);
    }

    @GetMapping("/findByPhone")
    @Log("查询用户")
    @ApiOperation("查询用户")
    @AnonymousAccess
    public ResponseEntity<Object> findByPhone(String phone) {
        return new ResponseEntity<>(userService.findByPhone(phone), HttpStatus.OK);
    }

    @GetMapping("/findByEnabled")
    @Log("查询用户")
    @ApiOperation("查询用户")
    @MyDataPermission(title = "供应商管理,营建对接启动,项目推进,用户管理,数据处理,批量变更干系人")
    public ResponseEntity<Object> findByEnabled(TemplateConfigQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(userService.findByEnabled(criteria.getNickName(), criteria.getEnabled()), HttpStatus.OK);
    }

    @ApiOperation("导出用户数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('user:list')")
    public void download(HttpServletResponse response, UserQueryCriteria criteria) throws IOException {
        userService.download(userService.queryAll(criteria), response);
    }

    @ApiOperation("用户详情")
    @GetMapping("/{id}")
    @PreAuthorize("@el.check('user:list')")
    public ResponseEntity<Object> query(@PathVariable Long id) {
        return new ResponseEntity<>(userService.detail(id), HttpStatus.OK);
    }

    @ApiOperation("查询用户")
    @GetMapping
    @PreAuthorize("@el.check('user:list')")
//    @MyDataPermission(title = "用户管理")
    public ResponseEntity<Object> query(UserQueryCriteria criteria, Pageable pageable) {
        if (!ObjectUtils.isEmpty(criteria.getDeptId())) {
            criteria.getDeptIds().add(criteria.getDeptId());
            criteria.getDeptIds().addAll(deptService.getDeptChildren(deptService.findByPid(criteria.getDeptId())));
        }
//        if (!ObjectUtils.isEmpty(criteria.getRolesName())) {
//            List<Long> byRoleName = roleService.findRoleIdsByRoleName(criteria.getRolesName());
//            if (ObjectUtils.isEmpty(byRoleName)) {
//                return new ResponseEntity<>(PageUtil.toPage(null,0),HttpStatus.OK);
//            }
//            criteria.getRolesIds().addAll(byRoleName);
//        }

        // 数据权限
        List<Long> dataScopes = dataService.getDeptIds(userService.findByName(SecurityUtils.getCurrentUsername()));
        // criteria.getDeptIds() 不为空并且数据权限不为空则取交集
        if (!CollectionUtils.isEmpty(criteria.getDeptIds()) && !CollectionUtils.isEmpty(dataScopes)) {
            // 取交集
            criteria.getDeptIds().retainAll(dataScopes);
            if (!CollectionUtil.isEmpty(criteria.getDeptIds())) {
                return new ResponseEntity<>(userService.queryAll(criteria, pageable), HttpStatus.OK);
            }
        } else if (!CollectionUtils.isEmpty(criteria.getRolesIds()) && !CollectionUtils.isEmpty(dataScopes)) {
            // 取交集
            criteria.getRolesIds().retainAll(dataScopes);
            if (!CollectionUtil.isEmpty(criteria.getRolesIds())) {
                return new ResponseEntity<>(userService.queryAll(criteria, pageable), HttpStatus.OK);
            }
        } else {
            // 否则取并集
            criteria.getDeptIds().addAll(dataScopes);
            return new ResponseEntity<>(userService.queryAll(criteria, pageable), HttpStatus.OK);
        }
        return new ResponseEntity<>(PageUtil.toPage(null, 0), HttpStatus.OK);
    }

    @Log("新增用户")
    @ApiOperation("新增用户")
    @PostMapping
    @PreAuthorize("@el.check('user:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody User resources) {
        checkLevel(resources);
        // 默认密码 123456
        resources.setPassword(passwordEncoder.encode("yaduo201203.acms"));
        if (resources.getDept() == null || resources.getDept().getId() == null) {
            resources.setDept(null);
        }
        User user = userService.create(resources);
        return new ResponseEntity<>(user, HttpStatus.CREATED);
    }

    @Log("修改用户")
    @ApiOperation("修改用户")
    @PutMapping
    @PreAuthorize("@el.check('user:edit')")
    public ResponseEntity<Object> update(@Validated(User.Update.class) @RequestBody User resources) {
//        checkLevel(resources);
        userService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("修改用户：个人中心")
    @ApiOperation("修改用户：个人中心")
    @PutMapping(value = "center")
    public ResponseEntity<Object> center(@Validated(User.Update.class) @RequestBody User resources) {
        if (!resources.getId().equals(SecurityUtils.getCurrentUserId())) {
            throw new BadRequestException("不能修改他人资料");
        }
        userService.updateCenter(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除用户")
    @ApiOperation("删除用户")
    @DeleteMapping
    @PreAuthorize("@el.check('user:del')")
    public ResponseEntity<Object> delete(@RequestBody Set<Long> ids) {
        for (Long id : ids) {
            Integer currentLevel = Collections.min(roleService.findByUsersId(SecurityUtils.getCurrentUserId()).stream().map(RoleSmallDto::getLevel).collect(Collectors.toList()));
            List<RoleSmallDto> byUsersId = roleService.findByUsersId(id);
            if (ObjectUtil.isNotEmpty(byUsersId)) {
                Integer optLevel = Collections.min(roleService.findByUsersId(id).stream().map(RoleSmallDto::getLevel).collect(Collectors.toList()));
                if (currentLevel > optLevel) {
                    throw new BadRequestException("角色权限不足，不能删除：" + userService.findById(id).getUsername());
                }
            }
            LambdaQueryWrapper<ProjectStakeholders> wrapper = Wrappers.lambdaQuery(ProjectStakeholders.class)
                    .eq(ProjectStakeholders::getUserId, id)
                    .eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
            Long aLong = projectStakeholdersRepository.selectCount(wrapper);
            if (aLong > 0) {
                throw new BadRequestException("当前用户已经存在项目干系人中，暂不允许删除！");
            }
        }
        userService.delete(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("修改密码")
    @PostMapping(value = "/updatePass")
    public ResponseEntity<Object> updatePass(@RequestBody UserPassVo passVo) throws Exception {
        String oldPass = RsaUtils.decryptByPrivateKey(RsaProperties.privateKey, passVo.getOldPass());
        String newPass = RsaUtils.decryptByPrivateKey(RsaProperties.privateKey, passVo.getNewPass());
        UserDto user = userService.findByName(SecurityUtils.getCurrentUsername());
        if (!passwordEncoder.matches(oldPass, user.getPassword())) {
            throw new BadRequestException("修改失败，旧密码错误");
        }
        if (passwordEncoder.matches(newPass, user.getPassword())) {
            throw new BadRequestException("新密码不能与旧密码相同");
        }
        userService.updatePass(user.getUsername(), passwordEncoder.encode(newPass));
        return new ResponseEntity<>(HttpStatus.OK);
    }

    public static void main(String[] args) {
        PasswordEncoder passwordEncoder1 = new BCryptPasswordEncoder();
        System.out.println(passwordEncoder1.encode("kids-1qaz2wsx"));
    }

    @ApiOperation("重置密码")
    @PostMapping(value = "/resetPass")
    public ResponseEntity<Object> resetPass(@RequestBody UserQueryCriteria criteria) throws Exception {

        Long id = criteria.getId();
        if (null == id || 0 == id) {
            throw new IllegalArgumentException("用户id错误");
        }
        UserDto user = userService.findById(id);
        String newPass = user.getPhone().substring(5);
        userService.updatePass(user.getUsername(), passwordEncoder.encode(newPass));
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("修改头像")
    @PostMapping(value = "/updateAvatar")
    public ResponseEntity<Object> updateAvatar(@RequestParam MultipartFile avatar) {
        return new ResponseEntity<>(userService.updateAvatar(avatar), HttpStatus.OK);
    }

    @Log("修改邮箱")
    @ApiOperation("修改邮箱")
    @PostMapping(value = "/updateEmail/{code}")
    public ResponseEntity<Object> updateEmail(@PathVariable String code, @RequestBody User user) throws Exception {
        String password = RsaUtils.decryptByPrivateKey(RsaProperties.privateKey, user.getPassword());
        UserDto userDto = userService.findByName(SecurityUtils.getCurrentUsername());
        if (!passwordEncoder.matches(password, userDto.getPassword())) {
            throw new BadRequestException("密码错误");
        }
        verificationCodeService.validated(CodeEnum.EMAIL_RESET_EMAIL_CODE.getKey() + user.getEmail(), code);
        userService.updateEmail(userDto.getUsername(), user.getEmail());
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * 如果当前用户的角色级别低于创建用户的角色级别，则抛出权限不足的错误
     *
     * @param resources /
     */
    private void checkLevel(User resources) {
        Integer currentLevel = Collections.min(roleService.findByUsersId(SecurityUtils.getCurrentUserId()).stream().map(RoleSmallDto::getLevel).collect(Collectors.toList()));
        Integer optLevel = roleService.findByRoles(resources.getRoles());
        if (currentLevel > optLevel) {
            throw new BadRequestException("角色权限不足");
        }
    }


    @Log("修改用户")
    @ApiOperation("修改用户")
    @PutMapping("/changeEnabled")
    @AnonymousAccess
    public ResponseEntity<Object> changeEnabled(@Validated(User.Update.class) @RequestBody User resources) {
//        checkLevel(resources);
        userService.changeEnabled(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }


    @Log("分配人列表")
    @GetMapping("/getAllotList")
    @ApiOperation(value = "分配人列表")
    @MyDataPermission(title = "供应商管理,营建对接启动,项目推进,用户管理,数据处理,批量变更干系人")
    public ResponseEntity<Object> getAllotList(UserQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(userService.getAllotList(criteria, pageable), HttpStatus.OK);
    }

    @Log("获取经销商列表")
    @GetMapping("/getDealers")
    @ApiOperation(value = "获取经销商列表")
//    @PreAuthorize("@el.check('user:list')")
    @MyDataPermission(title = "供应商管理,营建对接启动,项目推进,用户管理,数据处理,批量变更干系人")
    public ResponseEntity<Object> getDealers(UserQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(userService.getDealers(criteria, pageable), HttpStatus.OK);
    }

    @Log("用户列表")
    @GetMapping("/getUserList")
    @ApiOperation(value = "用户列表")
    @MyDataPermission(title = "供应商管理,营建对接启动,项目推进,用户管理,数据处理,批量变更干系人")
    public ResponseEntity<Object> getUserList(UserQueryCriteria2 criteria, Pageable pageable) {
        return new ResponseEntity<>(userService.getUserList(criteria, pageable), HttpStatus.OK);
    }


    @Log("全部用户列表")
    @GetMapping("/getUsers")
    @ApiOperation(value = "全部用户列表")
    @MyDataPermission(title = "供应商管理,营建对接启动,项目推进,用户管理,数据处理,批量变更干系人")
    public ResponseEntity<Object> getUsers() {
        return new ResponseEntity<>(userService.getUsers(), HttpStatus.OK);
    }


    @Log("用户列表")
    @GetMapping("/getUserJobList")
    @ApiOperation(value = "用户列表")
    @MyDataPermission(title = "供应商管理,营建对接启动,项目推进,用户管理,数据处理,批量变更干系人")
    public ResponseEntity<Object> getUserJobList(Long jobId) {
        return new ResponseEntity<>(userService.queryUsersByRole(jobId), HttpStatus.OK);
    }

    @Log("勘察工程人员")
    @GetMapping("/surveyEngineer")
    @ApiOperation(value = "勘察工程人员")
    @MyDataPermission(title = "供应商管理,营建对接启动,项目推进,用户管理,数据处理,批量变更干系人")
    public ResponseEntity<Object> surveyEngineer(@RequestParam("nickName") String nickName) {
        return new ResponseEntity<>(userService.surveyEngineer(nickName), HttpStatus.OK);
    }

    @Log("工程经理")
    @GetMapping("/engineerManager")
    @ApiOperation(value = "工程经理")
    @MyDataPermission(title = "供应商管理,营建对接启动,项目推进,用户管理,数据处理,批量变更干系人")
    public ResponseEntity<Object> engineerManager() {
        return new ResponseEntity<>(userService.getGcjl(), HttpStatus.OK);
    }

    @Log("勘察设计人员")
    @GetMapping("/surveyDesignEngineer")
    @ApiOperation(value = "勘察设计人员")
    @MyDataPermission(title = "供应商管理,营建对接启动,项目推进,用户管理,数据处理,批量变更干系人")
    public ResponseEntity<Object> surveyDesignEngineer(@RequestParam("nickName") String nickName) {
        return new ResponseEntity<>(userService.surveyDesignEngineer(nickName), HttpStatus.OK);
    }


    @Log("根据角色查询人员")
    @GetMapping("/getFindByRoleCode")
    @ApiOperation(value = "根据角色查询人员")
    @MyDataPermission(title = "供应商管理,营建对接启动,项目推进,用户管理,数据处理,批量变更干系人")
    public ResponseEntity<Object> getFindByRoleCode(@RequestParam("roleCodeList") String roleCode) {
        return new ResponseEntity<>(userService.findByRoleCodes(roleCode), HttpStatus.OK);
    }

    @Log("根据角色和城市查询人员")
    @GetMapping("/getFindByRoleAndCity")
    @ApiOperation(value = "根据角色查询人员")
    @MyDataPermission(title = "供应商管理,营建对接启动,项目推进,用户管理,数据处理,批量变更干系人")
    public ResponseEntity<Object> getFindByRoleAndCity(String roleCode, Long projectId) {
        return new ResponseEntity<>(userService.findByRoleCodesA(roleCode, projectId), HttpStatus.OK);
    }

    public ResponseEntity<Object> syncPassword() {
        return new ResponseEntity<>(userService.syncPass(), HttpStatus.OK);
    }

}
