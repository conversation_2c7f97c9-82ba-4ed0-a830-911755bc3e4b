/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.system.service.dto;

import com.bassims.annotation.Query;
import com.bassims.annotation.QueryPlus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018-11-23
 */
@Data
public class UserQueryCriteria implements Serializable {

    @Query
    private Long id;

    @Query(propName = "id", type = Query.Type.IN, joinName = "dept")
    private Set<Long> deptIds = new HashSet<>();


    @Query(blurry = "email,username,nickName")
    private String blurry;

    @Query(blurry = "nickName")
    private String nickName;

    @Query(blurry = "username")
    private String username;

    @Query(blurry = "email")
    private String email;


    @Query(blurry = "phone")
    private String phone;

    @Query(type = Query.Type.EQUAL, propName = "email")
    private String ssoEmail;

    @Query
    private Boolean enabled;

    private Long deptId;

//    @Query(type = Query.Type.BETWEEN)
//    private List<Timestamp> createTime;

    private Long roleId;

    @Query(blurry = "jobName")
    private String jobName;

    private Long jobId;

    @Query(type = Query.Type.IN, propName = "id")
    private List<Long> userIds;

    @Query(type = Query.Type.IN, propName = "id")
    private List<Long> userId;

    @Query(type = Query.Type.LESS_THAN, propName = "id", joinName = "roles")
    private Long allotRole;

    /*角色名称*/
    private String rolesName;


    private String gender;

    private String createTime;


    //@Query(propName = "id", type = Query.Type.IN, joinName = "roles")
    private Set<Long> rolesIds = new HashSet<>();

    private Set<Long> areaCodes;

    private Set<String> cityCompanys;

    private Long areaCode;

    private String cityCompany;


}
