/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.system.repository;

import com.bassims.modules.system.domain.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018-11-22
 */
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

    /**
     * 根据用户名查询
     * @param username 用户名
     * @return /
     */
    User findByUsername(String username);


    /**
     * 根据用户名查询
     * @param id 用户名
     * @return /
     */
    @Query(value = "select * from s_user_info where user_id = ?1",nativeQuery = true)
    User getByUserId(String id);

    @Query(value = "SELECT distinct u.* FROM s_user_info u " +
            "LEFT JOIN s_sys_users_roles ur " +
            "ON u.user_id = ur.user_id " +
            "LEFT JOIN s_sys_role r " +
            "ON ur.role_id = r.role_id " +
            "where u.is_delete is null and IF(?1 is not null, u.username like concat('%',?1,'%'), 1=1) " +
            "and IF(?2 is not null, r.`name` like concat('%',?2,'%'), 1=1) " +
            "and IF(?3 is not null, u.`nick_name` like concat('%',?3,'%'), 1=1) " +
            "and IF(?4 is not null, u.`gender` = ?4, 1=1) and IF(?5 is not null, u.`phone` like concat('%',?5,'%'), 1=1) " +
            "and IF(?6 is not null, u.`email` like concat('%',?6,'%'), 1=1) and IF(?7 is not null, u.`enabled` =?7, 1=1) " +
            "and  IF(?8 is not null, DATEDIFF(u.create_time,STR_TO_DATE(?8,'%Y-%m-%d'))   BETWEEN 0 and 1 , 1=1)" +
            "and  IF(?9 is not null, u.`job_name` like concat('%',?9,'%'), 1=1) ",countProjection = "u.user_id",nativeQuery = true)
    Page<User> findAll(String userName,String roleName,String nickName,String gender,String phone,String email,Boolean enable,String createTime,String jobName, Pageable pageable);


    /**
     * 根据用户名查询
     * @param username 用户名
     * @return /
     */
    @Query(value = "select * from s_user_info where username = ?1",nativeQuery = true)
    User getByUsername(String username);

    /**
     * 根据用户名查询
     * @param userName 用户名
     * @return /
     */
//    @Query(value = "select * from s_user_info where is_delete is null and IF(?1 is not null, username like concat('%',?1,'%'), 1=1) " +
//            "and IF(?2 is not null, enabled = ?2, 1=1 )", nativeQuery = true)
//    List<User> findByUserName(String userName, Boolean enabled);

    /**
     * 根据用户名查询
     * @param nickName 用户名
     * @return /
     */
    @Query(value = "select * from s_user_info where is_delete is null and nick_name like concat('%',?1,'%') and enabled = ?2", nativeQuery = true)
    List<User> findByEnabled(String nickName, Boolean enabled);

    @Query(value = "select * from s_user_info where is_delete is null and nick_name like concat('%',?1,'%')", nativeQuery = true)
    List<User> findByNickName2(String nickName);

    /**
     * 根据用户姓名查询
     * @param nickName 用户姓名
     * @return /
     */
    @Query(value = "select * from s_user_info where nick_name = ?1",nativeQuery = true)
    User findByNickName(String nickName);

    /**
     * 根据邮箱查询
     * @param email 邮箱
     * @return /
     */
    List<User> findByEmail(String email);

    /**
     * 根据手机号查询
     * @param phone 手机号
     * @return /
     */
    User findByPhone(String phone);

    /**
     * 修改密码
     * @param username 用户名
     * @param pass 密码
     * @param lastPasswordResetTime /
     */
    @Modifying
    @Query(value = "update s_user_info set password = ?2 , pwd_reset_time = ?3 where username = ?1",nativeQuery = true)
    void updatePass(String username, String pass, Date lastPasswordResetTime);

    /**
     * 修改邮箱
     * @param username 用户名
     * @param email 邮箱
     */
    @Modifying
    @Query(value = "update s_user_info set email = ?2 where username = ?1",nativeQuery = true)
    void updateEmail(String username, String email);

    /**
     * 根据角色查询用户
     * @param roleId /
     * @return /
     */
    @Query(value = "SELECT u.* FROM s_user_info u, s_sys_users_roles r WHERE" +
            " u.user_id = r.user_id AND r.role_id = ?1", nativeQuery = true)
    List<User> findByRoleId(Long roleId);

    /**
     * 根据角色中的部门查询
     * @param id /
     * @return /
     */
    @Query(value = "SELECT u.* FROM s_user_info u, s_sys_users_roles r, s_sys_roles_depts d WHERE " +
            "u.user_id = r.user_id AND r.role_id = d.role_id AND r.role_id = ?1 group by u.user_id", nativeQuery = true)
    List<User> findByDeptRoleId(Long id);

    /**
     * 根据菜单查询
     * @param id 菜单ID
     * @return /
     */
    @Query(value = "SELECT u.* FROM s_user_info u, s_sys_users_roles ur, s_sys_roles_menus rm WHERE " +
            "u.user_id = ur.user_id AND ur.role_id = rm.role_id AND rm.menu_id = ?1 group by u.user_id", nativeQuery = true)
    List<User> findByMenuId(Long id);

    /**
     * 根据Id删除
     * @param ids /
     */
    void deleteAllByIdIn(Set<Long> ids);

    /**
     * 根据岗位查询
     * @param ids /
     * @return /
     */
    @Query(value = "SELECT count(1) FROM s_user_info u, s_sys_users_jobs j WHERE u.user_id = j.user_id AND j.job_id IN ?1", nativeQuery = true)
    int countByJobs(Set<Long> ids);

    /**
     * 根据部门查询
     * @param deptIds /
     * @return /
     */
    @Query(value = "SELECT count(1) FROM s_user_info u WHERE u.dept_id IN ?1", nativeQuery = true)
    int countByDepts(Set<Long> deptIds);

    /**
     * 根据角色查询
     * @param ids /
     * @return /
     */
    @Query(value = "SELECT count(1) FROM s_user_info u, s_sys_users_roles r WHERE " +
            "u.user_id = r.user_id AND r.role_id in ?1", nativeQuery = true)
    int countByRoles(Set<Long> ids);

    @Query(value = "SELECT u.user_id FROM s_user_info u LEFT JOIN s_sys_users_roles r ON u.user_id = r.user_id " +
            "LEFT JOIN s_sys_role sr ON sr.role_id = r.role_id WHERE " +
            "sr.level = ?1", nativeQuery = true)
    List<Long> findByRoleLevel(long level);

    @Modifying
    @Query(value = "update s_user_info set enabled = ?1 where user_id = ?2",nativeQuery = true)
    void changeEnabled(Boolean enabled, Long id);


    /**
     * 根据角色查询用户
     * @param jobId /
     * @return /
     */
    @Query(value = "SELECT u.* FROM s_user_info u, s_sys_users_jobs j WHERE u.user_id = j.user_id AND j.job_id = ?1", nativeQuery = true)
    List<User> findByJobId(Long jobId);


    /**
     * 按用户名修改openId
     * @param username
     * @param openId
     */
    @Modifying
    @Query(value = "update s_user_info set open_id = ?2 where username = ?1",nativeQuery = true)
    void updateOpenId(String username, String openId);

    /**
     * 根据角色code查询
     * @param roleCodeList 角色code
     * @return /
     */
    @Query(value = "SELECT u.* FROM s_user_info u " +
            "LEFT JOIN s_sys_users_roles ur " +
            "ON u.user_id = ur.user_id " +
            "LEFT JOIN s_sys_role r " +
            "ON ur.role_id = r.role_id where u.enabled = 1 and r.role_code in ?1 and if(?2!='',u.nick_name like concat('%',?2,'%'),1=1)",nativeQuery = true)
    List<User> findByRoleCodeList(List<String> roleCodeList, String nickName);

    @Query(value = "SELECT u.* FROM s_user_info u " +
            "LEFT JOIN s_sys_users_roles ur " +
            "ON u.user_id = ur.user_id " +
            "LEFT JOIN s_sys_role r " +
            "ON ur.role_id = r.role_id where u.enabled = 1 and r.role_code in ?1 ",nativeQuery = true)
    List<User> findByRoleCodeList(List<String> roleCodeList);

    @Query(value = "SELECT u.* FROM s_user_info u " +
            "LEFT JOIN s_sys_users_roles ur " +
            "ON u.user_id = ur.user_id " +
            "LEFT JOIN s_sys_role r " +
            "ON ur.role_id = r.role_id where u.enabled = 1 and r.role_code = ?1 limit 1",nativeQuery = true)
    User findByRoleCode(String roleCode);


    @Query(value = "select *  from s_user_info where user_id in(select user_id from t_project_stakeholders where  shakeholder_status = 'in_term' AND project_id = ?2 " +
            " and IF(?3 is null, role_id in (select role_id from s_sys_role where FIND_IN_SET(role_code,?1)),user_id = ?3) )  " +
            " and enabled = 1 and is_delete is null and user_id !=1 ",nativeQuery = true)
    List<User> findUserByGroupRoleCode(String roleCode,Long projectId,String userId);

    @Query(value = "SELECT u.* FROM s_user_info u " +
            "LEFT JOIN s_sys_users_roles ur " +
            "ON u.user_id = ur.user_id " +
            "LEFT JOIN s_sys_role r " +
            "ON ur.role_id = r.role_id " +
            "LEFT JOIN s_sys_users_city uc " +
            "ON uc.user_id=u.user_id where u.enabled = 1 and r.role_code = ?1 and uc.area_code = ?2 GROUP BY u.user_id",nativeQuery = true)
    List<User> findByRoleCodeA(String roleCode,Long cityCode);



    @Query(value = "SELECT u.* FROM s_user_info u " +
            "LEFT JOIN s_sys_users_roles ur " +
            "ON u.user_id = ur.user_id " +
            "LEFT JOIN s_sys_role r " +
            "ON ur.role_id = r.role_id where u.enabled = 1 and  u.phone = ?1 and  r.role_code = ?2 limit 1",nativeQuery = true)
    User findByUserIdRoleCode( String phone,String roleCode);



    @Query(value = "SELECT u.* FROM s_user_info u " +
            "LEFT JOIN s_sys_users_roles ur " +
            "ON u.user_id = ur.user_id " +
            "LEFT JOIN s_sys_role r " +
            "ON ur.role_id = r.role_id where u.enabled = 1 and r.role_code = ?1",nativeQuery = true)
    List<User>  findByRoleCodes(String roleCode);


    @Query(value = "SELECT u.* FROM s_user_info u " +
            "where u.enabled = 1 and u.code = ?1 ",nativeQuery = true)
    User findByCode(String code);

    @Query(value = "SELECT u.* FROM s_user_info u  where u.enabled = 1 ",nativeQuery = true)
    List<User> getUsers();

    /**
     * 按
     * @param openId
     * @param phone
     * @return
     */
    @Query(value = "SELECT u.* FROM s_user_info u " +
            "where (u.open_id = ?1 or u.phone = ?2) limit 1 ",nativeQuery = true)
    User findUserByOpenIdOrPhone(String openId,String phone);

    /**
     *
     * @param userId 用户名
     * @param userStatus 状态
     */
    @Modifying
    @Query(value = "update s_user_info set user_status = ?2 where user_id = ?1",nativeQuery = true)
    void updateUserStatusById(Long userId, String userStatus);

    /**
     * 刪除用戶和城市关联数据
     * @param userId 用户名
     */
    @Modifying
    @Transactional
    @Query(value = "delete from  s_sys_users_city  where user_id = ?1",nativeQuery = true)
    void deleteCityRelationByUserId(Long userId);

    /**
     * 新增用户和城市关联数据
     * @param userId 用户名
     */
    @Modifying
    @Transactional
    @Query(value = "insert into s_sys_users_city(area_code,user_id) values(?2,?1)",nativeQuery = true)
    void addCityRelation(Long userId,Long areaCode);




    /**
     * 根据用户的战区查询对应的城市数据，并新增用户和城市关联数据
     */
    @Modifying
    @Query(value ="INSERT INTO `atour-con`.`s_sys_users_city` ( `area_code`, `user_id`, is_delete ) values(?1,?2,0)  ",nativeQuery = true)
    void addCityRelationByUserFirstDepartId(String areaCode,Long userId);

    /**
     * 给用户赋值全国的城市
     * @param userId 用户名
     */
    @Modifying
    @Query(value =  "SELECT  " +
            "CASE   " +
            "  WHEN  " +
            "    s_sys_area.area_code IS NOT NULL THEN  " +
            "      s_sys_area.area_code ELSE s_sys_area.area_code   " +
            "      END AS firstDepartName   " +
            "  FROM  " +
            "    s_user_info s_user_info  " +
            "    LEFT JOIN s_sys_area s_sys_area on s_sys_area.`level` != 2   " +
            "  WHERE  " +
            "    s_user_info.first_depart_id IS NOT NULL   " +
            "    AND s_user_info.user_id != 1 and s_user_info.user_id=?1  " +
            "  GROUP BY  firstDepartName; ",nativeQuery = true)
    List<BigInteger> addAllCityRelation(Long userId);


    /**
     * 根据用户IDs查询用户数据
     * @param userId /
     * @return /
     */
    @Query(value = "SELECT u.* FROM s_user_info u WHERE u.user_id in ?1", nativeQuery = true)
    List<User> findByUserIds(List<Long> userId);


    @Query(value = "SELECT user_id FROM s_user_info WHERE FIND_IN_SET(?1,job_id)", nativeQuery = true)
    List<BigInteger> findByJobId(String jobId);


    @Query(value = "SELECT * FROM s_user_info WHERE phone LIKE CONCAT('%',:phone,'%')", nativeQuery = true)
    List<User> selectByPhone(@Param("phone") String phone);
}
