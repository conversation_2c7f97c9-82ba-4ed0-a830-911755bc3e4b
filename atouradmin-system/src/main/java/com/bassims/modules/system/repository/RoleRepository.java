/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.system.repository;

import com.bassims.modules.system.domain.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018-12-03
 */
public interface RoleRepository extends JpaRepository<Role, Long>, JpaSpecificationExecutor<Role> {

    /**
     * 根据名称查询
     *
     * @param name /
     * @return /
     */
    Role findByName(String name);

    /**
     * 删除多个角色
     *
     * @param ids /
     */
    void deleteAllByIdIn(Set<Long> ids);

    /**
     * 根据用户ID查询
     *
     * @param id 用户ID
     * @return /
     */
    @Query(value = "SELECT r.*,u.is_manually_add as isManuallyAdd  FROM s_sys_role r, s_sys_users_roles u WHERE " +
            "r.role_id = u.role_id AND u.user_id = ?1", nativeQuery = true)
    Set<Role> findByUserId(Long id);

    /**
     * 解绑角色菜单
     *
     * @param id 菜单ID
     */
    @Modifying
    @Query(value = "delete from s_sys_roles_menus where menu_id = ?1", nativeQuery = true)
    void untiedMenu(Long id);

    /**
     * 根据部门查询
     *
     * @param deptIds /
     * @return /
     */
    @Query(value = "select count(1) from s_sys_role r, s_sys_roles_depts d where " +
            "r.role_id = d.role_id and d.dept_id in ?1", nativeQuery = true)
    int countByDepts(Set<Long> deptIds);

    /**
     * 根据菜单Id查询
     *
     * @param menuIds /
     * @return /
     */
    @Query(value = "SELECT r.* FROM s_sys_role r, s_sys_roles_menus m WHERE " +
            "r.role_id = m.role_id AND m.menu_id in ?1", nativeQuery = true)
    List<Role> findInMenuId(List<Long> menuIds);

    @Query(value = "SELECT * FROM s_sys_role WHERE level = ?1", nativeQuery = true)
    List<Role> findByLevel(long level);


    /**
     * 根据用户ID查询
     *
     * @param userId 用户ID
     * @return /
     */
    @Query(value = "SELECT r.* FROM s_sys_role r, s_sys_users_roles u WHERE " +
            "r.role_id = u.role_id AND u.user_id = ?1", nativeQuery = true)
    List<Role> findRolesByUserId(Long userId);

    /**
     * 根据role获取role信息
     *
     * @param roleId
     * @return
     */
    @Query(value = "SELECT r.* FROM s_sys_role r WHERE " +
            " r.role_id = ?1", nativeQuery = true)
    Role findRoleByRoleId(Long roleId);

    /**
     * 根据用户ID查询
     *
     * @param userId 用户ID
     * @return /
     */
    @Query(value = "SELECT u.role_id FROM  s_sys_users_roles u WHERE " +
            " u.user_id = ?1", nativeQuery = true)
    List<Long> findRoleIdsByUserId(Long userId);



    /**
     * 根据用户ID查询
     *
     * @param userId 用户ID
     * @return /
     */
    @Query(value = "SELECT s_sys_role.role_code FROM s_sys_users_roles s_sys_users_roles " +
            "left join s_sys_role s_sys_role on s_sys_role.role_id=s_sys_users_roles.role_id" +
            " WHERE  s_sys_users_roles.user_id = ?1", nativeQuery = true)
    List<String> findRoleCodesByUserId1(Long userId);

    @Query(value = "SELECT u.role_id FROM  s_sys_users_roles u WHERE " +
            " u.user_id = ?1", nativeQuery = true)
    List<BigInteger> findRoleIdsByUserIdA(Long userId);

    /**
     * 根据用户ID查询
     *
     * @param roleName 角色名称
     * @return /
     */
    @Query(value = "SELECT * FROM s_sys_role sr WHERE sr.name like concat('%',?1,'%')", nativeQuery = true)
    List<Role> findRoleIdsByRoleName(String roleName);


    @Query(value = "SELECT ps.role_code FROM t_project_stakeholders ps WHERE ps.project_id = ?2 AND ps.user_id = ?1", nativeQuery = true)
    List<String> findRoleCodesByUserId(Long currentUserId, Long projectId);

    @Query(value = "SELECT ps.role_id FROM t_project_stakeholders ps WHERE ps.project_id = ?2 AND ps.user_id = ?1", nativeQuery = true)
    List<Long> findRoleIdsByUserId(Long currentUserId, Long projectId);

    @Query(value = "SELECT u.user_id,u.role_id,sr.name,sr.role_code FROM s_sys_users_roles u LEFT JOIN s_sys_role sr ON u.role_id=sr.role_id WHERE u.user_id = ?1", nativeQuery = true)
    List<Role> findStakeholdersByUserId(Long currentUserId);

    @Query(value = "select role_id,role_code,`name`,atos_job_name,atos_job_id from s_sys_role where role_id in(select DISTINCT role_id from t_project_stakeholders  where project_id=?2 and user_id=?1 )", nativeQuery = true)
    Object[] findRolesByUserIdAndProjectId(Long userId,Long projectId);

    @Query(value = "SELECT sr.* FROM s_sys_role sr WHERE sr.role_code = ?1 ", nativeQuery = true)
    Role findRoleIdByCode(String roleCode);


    /**
     * 根据atos的岗位ID查询对应的 角色信息
     *
     * @param atosJobId /
     * @return /
     */
    @Query(value = "SELECT * FROM s_sys_role   WHERE FIND_IN_SET(?1,atos_job_id) ", nativeQuery = true)
    List<Role> queryAllRoleByAtosJobId(String atosJobId);

    @Query(value = "SELECT r.*  FROM s_user_info u LEFT JOIN s_sys_users_roles ur on u.user_id = ur.user_id" +
            " LEFT JOIN s_sys_role r ON ur.role_id = r.role_id WHERE u.user_id = ?1",nativeQuery = true)
    List<Role> getPermissionmode(Long userId);

    /**
     * 刪除用戶和角色关联数据
     * @param userId 用户名
     */
    @Modifying
    @Transactional
    @Query(value = "delete from  s_sys_users_roles  where user_id = ?1 and is_manually_add = ?2",nativeQuery = true)
    void deleteRoleRelationByUserId(Long userId,Integer isManuallyAdd);

    /**
     * 新增用户和城市关联数据
     * @param userId 用户名
     */
    @Modifying
    @Query(value = "insert into s_sys_users_roles(role_id,user_id,is_manually_add) values(?2,?1,?3)",nativeQuery = true)
    void addRoleRelation(Long userId,Long roleId,Integer isManuallyAdd);


    /**
     * 修改用户和城市关联数据
     * @param userId 用户名
     */
    @Modifying
    @Transactional
    @Query(value = "UPDATE `atour-con`.`s_sys_users_roles` SET `is_manually_add` = 1 WHERE `user_id` = ?1 " +
            "and  `role_id` not in ?2  ",nativeQuery = true)
    void upRoleRelation(Long userId,List<Long> roleIds);


    @Modifying
    @Transactional
    @Query(value = "UPDATE `atour-con`.`s_sys_users_roles` SET `is_manually_add` = 1 WHERE `user_id` in ?1",nativeQuery = true)
    void upUserRoleRelations(List<Long> userIds);


    @Query(value = "SELECT count(1) FROM s_sys_users_roles u LEFT JOIN s_sys_role sr ON u.role_id=sr.role_id WHERE u.user_id = ?1 and sr.role_code =?2", nativeQuery = true)
    Integer getCountByUserAndRoleCode(Long currentUserId,String roleCode);
}
