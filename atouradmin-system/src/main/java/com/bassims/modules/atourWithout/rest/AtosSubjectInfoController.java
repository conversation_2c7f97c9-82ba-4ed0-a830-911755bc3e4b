/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atourWithout.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.modules.atourWithout.domain.AtosSubjectInfo;
import com.bassims.modules.atourWithout.service.AtosSubjectInfoService;
import com.bassims.modules.atourWithout.service.dto.AtosSubjectInfoDto;
import com.bassims.modules.atourWithout.service.dto.AtosSubjectInfoQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-11-15
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_atos_subject_info管理")
@RequestMapping("/api/atosSubjectInfo")
public class AtosSubjectInfoController {

    private static final Logger logger = LoggerFactory.getLogger(AtosSubjectInfoController.class);

    private final AtosSubjectInfoService atosSubjectInfoService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, AtosSubjectInfoQueryCriteria criteria) throws IOException {
        atosSubjectInfoService.download(atosSubjectInfoService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<AtosSubjectInfoDto>>}
    */
    @GetMapping("/list")
    @Log("查询t_atos_subject_info")
    @ApiOperation("查询t_atos_subject_info")
    public ResponseEntity<Object> query(AtosSubjectInfoQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(atosSubjectInfoService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<AtosSubjectInfoDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询t_atos_subject_info")
    @ApiOperation("查询t_atos_subject_info")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(atosSubjectInfoService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增t_atos_subject_info")
    @ApiOperation("新增t_atos_subject_info")
    public ResponseEntity<Object> create(@Validated @RequestBody AtosSubjectInfo resources){
        return new ResponseEntity<>(atosSubjectInfoService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改t_atos_subject_info")
    @ApiOperation("修改t_atos_subject_info")
    public ResponseEntity<Object> update(@Validated @RequestBody AtosSubjectInfo resources){
        atosSubjectInfoService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_atos_subject_info")
    @ApiOperation("删除t_atos_subject_info")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        atosSubjectInfoService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }



    @PostMapping("/push")
    @Log("推送指定供应商")
    @ApiOperation("推送指定供应商")
    public ResponseEntity<Object> push(@RequestBody Long[] ids) {
        atosSubjectInfoService.push(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}