/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atourWithout.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.SupplierInfo;
import com.bassims.modules.atourWithout.domain.AtosSubjectInfo;
import com.bassims.modules.atourWithout.domain.AtosUserResponse;
import com.bassims.modules.atourWithout.service.dto.AtosSubjectInfoDto;
import com.bassims.modules.atourWithout.service.dto.AtosSubjectInfoQueryCriteria;
import com.bassims.modules.system.domain.User;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2023-11-15
**/
public interface AtosSubjectInfoService extends BaseService<AtosSubjectInfo> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(AtosSubjectInfoQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<AtosSubjectInfoDto>
    */
    List<AtosSubjectInfoDto> queryAll(AtosSubjectInfoQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param atosSubjectId ID
     * @return AtosSubjectInfoDto
     */
    AtosSubjectInfoDto findById(Long atosSubjectId);

    /**
    * 创建
    * @param resources /
    * @return AtosSubjectInfoDto
    */
    AtosSubjectInfoDto create(AtosSubjectInfo resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(AtosSubjectInfo resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<AtosSubjectInfoDto> all, HttpServletResponse response) throws IOException;

    AtosUserResponse pullUserInfoForAtos(User user , String tk);

    AtosUserResponse updateUserInfoForAtos(User user , String tk);

    AtosUserResponse pullSubjectInfoForAtos(SupplierInfo supplierInfo);

    AtosUserResponse pullUpdateSubjectInfoForAtos(SupplierInfo supplierInfo);
    AtosUserResponse pullUpdateStatusSubjectInfoForAtos(SupplierInfo supplierInfo);
    AtosUserResponse pullDeleteSubjectInfoForAtos(SupplierInfo supplierInfo);


    AtosUserResponse deleteUserInfoForAtos(User user ,int type);

    void push(Long[] ids);
}