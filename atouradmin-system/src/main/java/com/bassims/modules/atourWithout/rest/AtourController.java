package com.bassims.modules.atourWithout.rest;

import com.alibaba.fastjson.JSONObject;
import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.domain.LocalStorage;
import com.bassims.modules.atour.domain.HlmProjectInfoUpdateBrand;
import com.bassims.modules.atour.domain.HlmProjectInfoUpdateContract;
import com.bassims.modules.atour.domain.HlmProjectInfoUpdateRedLine;
import com.bassims.modules.atour.domain.ProjectInfoExpansion;
import com.bassims.modules.atour.service.ProjectInfoExpansionService;
import com.bassims.modules.atour.service.ProjectNodeInfoService;
import com.bassims.modules.atour.service.dto.ProjectInfoExpansionVO;
import com.bassims.modules.atour.service.dto.ProjectInfoPushCompletionAcceptanceDTO;
import com.bassims.modules.atour.service.dto.ProjectInfoPushFranchiseUserDTO;
import com.bassims.modules.atourWithout.domain.AtosUserRequest;
import com.bassims.modules.system.service.UserService;
import com.bassims.service.LocalStorageService;
import com.bassims.utils.DateUtil;
import com.bassims.utils.OSSClientUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

@Controller
@RequiredArgsConstructor
@RequestMapping("/api/atour")
public class AtourController {
    private static final Logger logger = LoggerFactory.getLogger(AtourController.class);


    private final ProjectInfoExpansionService projectInfoExpansionService;
    private final ProjectNodeInfoService projectNodeInfoService;
    private final OSSClientUtil ossClientUtil;
    private final LocalStorageService localStorageService;
    @Autowired
    private UserService userService;

    @ResponseBody
    @AnonymousAccess
    @Log("测试推送用户信息")
    @ApiOperation("测试推送用户信息")
    @PostMapping("/saveOrUpdateUserFromAtos")
    public String saveOrUpdateUserFromAtos(@Validated @RequestBody AtosUserRequest model ) throws IllegalAccessException {
        userService.saveOrUpdateUserFromAtos(model);
        return "测试推送用户信息";
    }

    @ResponseBody
    @AnonymousAccess
    @Log("测试xml参数")
    @ApiOperation("测试xml参数")
    @GetMapping("/syncProjectExpansion1")
    public List<ProjectInfoExpansion> syncProjectExpansio1(ProjectInfoExpansionVO expansion) throws IllegalAccessException {

        return projectInfoExpansionService.syncProjectExpansion1(expansion);
    }

    @ResponseBody
    @AnonymousAccess
    @Log("同步HLM系统里的项目数据")
    @ApiOperation("同步HLM系统里的项目数据")
    @PostMapping("/syncProjectExpansion")
    public JSONObject syncProjectExpansion(@Validated @RequestBody ProjectInfoExpansionVO expansion) throws IllegalAccessException {
        return projectInfoExpansionService.syncProjectExpansion(expansion);
    }

    @ResponseBody
    @AnonymousAccess
    @Log("重新更新项目的计划事件时间")
    @ApiOperation("重新更新项目的计划事件时间")
    @PostMapping("/syncProjectPlaneventRelation")
    public JSONObject syncProjectPlaneventRelation(@RequestParam(value = "projectId",required = true) Long projectId) throws IllegalAccessException {
        return projectInfoExpansionService.syncProjectPlaneventRelation(projectId);
    }

    @ResponseBody
    @AnonymousAccess
    @Log("同步HLM系统里的更新项目品牌变更信息")
    @ApiOperation("同步HLM系统里的更新项目品牌变更信息")
    @PostMapping("/syncUpdateBrandByChain")
    public JSONObject syncUpdateBrandByChain(@Validated @RequestBody HlmProjectInfoUpdateBrand expansion) {
        return projectInfoExpansionService.syncUpdateBrandByChain(expansion);
    }

    @ResponseBody
    @AnonymousAccess
    @Log("同步HLM系统里的更新项目红线变更信息")
    @ApiOperation("同步HLM系统里的更新项目红线变更信息")
    @PostMapping("/syncUpdateRedLineChange")
    public JSONObject syncUpdateRedLineChange(@Validated @RequestBody HlmProjectInfoUpdateRedLine expansion) {
        return projectInfoExpansionService.syncUpdateRedLineChange(expansion);
    }

    @ResponseBody
    @AnonymousAccess
    @Log("同步HLM系统里的更新项目报审合同信息")
    @ApiOperation("同步HLM系统里的更新项目报审合同信息")
    @PostMapping("/syncUpdateContract")
    public JSONObject syncUpdateContract(@Validated @RequestBody HlmProjectInfoUpdateContract expansion) {

        return projectInfoExpansionService.syncUpdateContract(expansion);
    }


    @ResponseBody
    @AnonymousAccess
    @Log("向HLM系统里的推送竣工验收的数据")
    @ApiOperation("向HLM系统里的推送竣工验收的数据")
    @GetMapping("/pushCompletionAcceptance")
    public String pushCompletionAcceptance(ProjectInfoPushCompletionAcceptanceDTO acceptanceDTO) throws JsonProcessingException {
        return projectNodeInfoService.pushCompletionAcceptance(null, acceptanceDTO);
    }


    @ResponseBody
    @AnonymousAccess
    @Log("向HLM系统里的推送营建对接人的数据")
    @ApiOperation("向HLM系统里的推送营建对接人的数据")
    @GetMapping("/pushBuildContacts")
    public String pushBuildContacts(ProjectInfoPushFranchiseUserDTO franchiseUserDTO) throws JsonProcessingException {
        return projectNodeInfoService.pushBuildContacts(null, franchiseUserDTO);
    }

    @ResponseBody
    @AnonymousAccess
    @Log("oss上传")
    @ApiOperation("oss上传")
    @PostMapping("/ossAttachmentUploading")
    public ResponseEntity<Object> ossAttachmentUploading(@RequestParam String nodeId, @RequestParam String name, @RequestParam("uploadFile") MultipartFile file) throws Exception {
        JSONObject json = new JSONObject();
        if (file.isEmpty()) {
            return new ResponseEntity<>("当前文件为空", HttpStatus.CREATED);
        }
        LocalDateTime localDateTime = LocalDateTime.now();
        String yyyyMMdd = DateUtil.localDateTimeFormat(localDateTime, "yyyyMMdd");
        LocalStorage localStorage = localStorageService.createOss(nodeId,name, file, Long.parseLong(yyyyMMdd));

        return new ResponseEntity<>(localStorage, HttpStatus.CREATED);

    }


    @ApiOperation("下载文件")
    @GetMapping(value = "/downloadFile")
    @AnonymousAccess
    public void download(Long localId, HttpServletResponse response, HttpServletRequest request,String isDrawing) throws IOException {
        localStorageService.downloadOss(localId, response, request,isDrawing);
    }


    @ResponseBody
    @AnonymousAccess
    @Log("同步竣工验收的数据")
    @ApiOperation("同步竣工验收的数据")
    @PostMapping("/syncCompletionAcceptance")
    public String syncCompletionAcceptance(ProjectInfoPushCompletionAcceptanceDTO acceptanceDTO) {
        return acceptanceDTO.toString();
    }

}
