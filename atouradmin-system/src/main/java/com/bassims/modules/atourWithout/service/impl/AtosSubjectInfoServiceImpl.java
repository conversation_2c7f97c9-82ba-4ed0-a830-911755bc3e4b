/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atourWithout.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.bassims.constant.bsEnum.AtourSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.SupplierInfo;
import com.bassims.modules.atour.repository.SupplierInfoRepository;
import com.bassims.modules.atour.service.SupplierInfoService;
import com.bassims.modules.atour.service.SupplierPmService;
import com.bassims.modules.atourWithout.domain.*;
import com.bassims.modules.system.domain.Area;
import com.bassims.modules.system.domain.Role;
import com.bassims.modules.system.domain.User;
import com.bassims.modules.system.repository.UserRepository;
import com.bassims.utils.SecurityUtils;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atourWithout.repository.AtosSubjectInfoRepository;
import com.bassims.modules.atourWithout.service.AtosSubjectInfoService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atourWithout.service.dto.AtosSubjectInfoDto;
import com.bassims.modules.atourWithout.service.dto.AtosSubjectInfoQueryCriteria;
import com.bassims.modules.atourWithout.service.mapstruct.AtosSubjectInfoMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import lombok.extern.log4j.Log4j2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2023-11-15
 **/
@Log4j2
@Service
public class AtosSubjectInfoServiceImpl extends BaseServiceImpl<AtosSubjectInfoRepository, AtosSubjectInfo> implements AtosSubjectInfoService {

    private static final Logger logger = LoggerFactory.getLogger(AtosSubjectInfoServiceImpl.class);

    @Value("${atos.path}")
    private String resourcePath;
    @Value("${atos.create}")
    private String createPath;
    @Value("${atos.update}")
    private String updatePath;
    @Value("${atos.subject-create}")
    private String subjectCreatePath;
    @Value("${atos.subject-update}")
    private String subjectUpdatePath;
    @Value("${atos.subject-update-status}")
    private String subjectUpdateStatusPath;

    @Value("${atos.delete}")
    private String deletePath;
    @Value("${atos.systemid-list}")
    private List<Integer> system_id_list;

    @Autowired
    private AtosSubjectInfoRepository atosSubjectInfoRepository;
    @Autowired
    private AtosSubjectInfoMapper atosSubjectInfoMapper;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private SupplierInfoRepository supplierInfoRepository;


    @Override
    public Map<String, Object> queryAll(AtosSubjectInfoQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<AtosSubjectInfo> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(AtosSubjectInfo.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", atosSubjectInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<AtosSubjectInfoDto> queryAll(AtosSubjectInfoQueryCriteria criteria) {
        return atosSubjectInfoMapper.toDto(list(QueryHelpPlus.getPredicate(AtosSubjectInfo.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AtosSubjectInfoDto findById(Long atosSubjectId) {
        AtosSubjectInfo atosSubjectInfo = Optional.ofNullable(getById(atosSubjectId)).orElseGet(AtosSubjectInfo::new);
        ValidationUtil.isNull(atosSubjectInfo.getAtosSubjectId(), getEntityClass().getSimpleName(), "atosSubjectId", atosSubjectId);
        return atosSubjectInfoMapper.toDto(atosSubjectInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AtosSubjectInfoDto create(AtosSubjectInfo resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setAtosSubjectId(snowflake.nextId());
        save(resources);
        return findById(resources.getAtosSubjectId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(AtosSubjectInfo resources) {
        AtosSubjectInfo atosSubjectInfo = Optional.ofNullable(getById(resources.getAtosSubjectId())).orElseGet(AtosSubjectInfo::new);
        ValidationUtil.isNull(atosSubjectInfo.getAtosSubjectId(), "AtosSubjectInfo", "id", resources.getAtosSubjectId());
        atosSubjectInfo.copy(resources);
        updateById(atosSubjectInfo);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long atosSubjectId : ids) {
            atosSubjectInfoRepository.deleteById(atosSubjectId);
        }
    }

    @Override
    public void download(List<AtosSubjectInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (AtosSubjectInfoDto atosSubjectInfo : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("atos系统主键", atosSubjectInfo.getAtosUserId());
            map.put("营建代理商或营建业主ID", atosSubjectInfo.getCompanyId());
            map.put("营建代理商或营建业主姓名", atosSubjectInfo.getCompanyName());
            map.put("营建代理商或营建业主手机号 ", atosSubjectInfo.getCompanyPhone());
            map.put("邮箱", atosSubjectInfo.getCompanyEmail());
            map.put("营建代理商或营建业主类型营建代理商:3002 营建业主:3003", atosSubjectInfo.getCompanyType());
            map.put("状态true:正常 / false:停用 ", atosSubjectInfo.getEnabled());
            map.put(" createTime", atosSubjectInfo.getCreateTime());
            map.put(" updateTime", atosSubjectInfo.getUpdateTime());
            map.put(" createBy", atosSubjectInfo.getCreateBy());
            map.put(" updateBy", atosSubjectInfo.getUpdateBy());
            map.put("是否可用", atosSubjectInfo.getIsEnabled());
            map.put("是否删除", atosSubjectInfo.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public AtosUserResponse pullUserInfoForAtos(User user, String tk) {
        AtosModelRequest atosModelRequest = new AtosModelRequest();
        AtosUserSendRequest atosUserSendRequest = new AtosUserSendRequest();
        AtosModelHeader atosModelHeader = new AtosModelHeader();
        atosModelHeader.setTk(tk);
        atosUserSendRequest.setTk(tk);
        atosUserSendRequest.setMobile(user.getPhone());
        atosUserSendRequest.setEmail(user.getEmail());
        atosUserSendRequest.setGender(Integer.getInteger(AtourSystemEnum.userGender.getKey(user.getGender())));
        atosUserSendRequest.setUser_name(user.getNickName());
        atosUserSendRequest.setUser_note("");
        //atosUserRequest.setUk(user.getTenantId());
        atosModelRequest.setHeader(atosModelHeader);
        atosModelRequest.setModel(atosUserSendRequest);
        //拼接用户信息
        String atosPath = resourcePath + createPath;
        //log.info("新建用户地址：{}", atosPath);
        //log.info("新建用户参数：{}", JSONUtil.toJsonStr(atosModelRequest));
        String post = HttpUtil.post(atosPath, JSONUtil.toJsonStr(atosModelRequest));
        //log.info("新建用户返回参数：{}", JSONUtil.toJsonStr(post));
        AtosUserResponse atosUserResponse = null;
        try {
            atosUserResponse = JSONUtil.toBean(post, AtosUserResponse.class);
        } catch (Exception e) {
            throw new BadRequestException("atos系统返回空信息：新建用户数据异常"+post);
        }
        //log.info("新建用户返回参数：{}", JSONUtil.toJsonStr(atosUserResponse));
        log.info("atos系统新建用户返回信息,address:{},send:{},result:{}", atosPath, JSONUtil.toJsonStr(atosModelRequest), JSONUtil.toJsonStr(post));
        if (null != atosUserResponse) {
            if (atosUserResponse.isSuccess()) {
                String uk = atosUserResponse.getData();
                user.setOpenId(uk);
                //更新用户uk
                userRepository.save(user);
                //成功
                return atosUserResponse;
            } else {
                throw new BadRequestException("atos系统新建用户返回错误信息：" + atosUserResponse.getMsg() + "，请联系atos系统管理员！");
            }

        } else {
            log.info("atos系统返回空信息：新建用户数据异常{}", post);
        }
        return null;
    }

    @Override
    public AtosUserResponse updateUserInfoForAtos(User user, String tk) {
        AtosModelRequest atosModelRequest = new AtosModelRequest();
        AtosUserSendRequest atosUserSendRequest = new AtosUserSendRequest();
        AtosModelHeader atosModelHeader = new AtosModelHeader();
        atosModelHeader.setTk(tk);
        atosUserSendRequest.setTk(tk);
        atosUserSendRequest.setMobile(user.getPhone());
        atosUserSendRequest.setEmail(user.getEmail());
        atosUserSendRequest.setGender(Integer.getInteger(AtourSystemEnum.userGender.getKey(user.getGender())));
        atosUserSendRequest.setUser_name(user.getNickName());
        atosUserSendRequest.setUser_note("");
        atosUserSendRequest.setUk(user.getOpenId());
        atosModelRequest.setHeader(atosModelHeader);
        atosModelRequest.setModel(atosUserSendRequest);
        //拼接用户信息
        String atosPath = resourcePath + updatePath;

        //log.info("更新用户地址：{}", atosPath);
        //log.info("更新用户参数：{}", JSONUtil.toJsonStr(atosModelRequest));
        String post = HttpUtil.post(atosPath, JSONUtil.toJsonStr(atosModelRequest));
        //log.info("更新用户返回参数：{}", JSONUtil.toJsonStr(post));
        AtosUserResponse atosUserResponse = JSONUtil.toBean(post, AtosUserResponse.class);
        //log.info("更新用户返回参数：{}", JSONUtil.toJsonStr(atosUserResponse));
        log.info("atos系统更新用户返回信息,address:{},send:{},result:{}", atosPath, JSONUtil.toJsonStr(atosModelRequest), JSONUtil.toJsonStr(post));
        if (null != atosUserResponse) {
            if (atosUserResponse.isSuccess()) {
                //成功
                return atosUserResponse;
            } else {
                //log.info("atos系统更新用户返回错误信息,address:{},send:{},result:{}",atosPath,JSONUtil.toJsonStr(atosModelRequest),JSONUtil.toJsonStr(post));
                throw new BadRequestException("atos系统更新用户返回错误信息：" + atosUserResponse.getMsg() + "，请联系atos系统管理员！");
            }

        } else {
            log.info("atos系统返回空信息：更新用户数据异常{}", post);
        }
        return null;
    }

    @Override
    public AtosUserResponse pullSubjectInfoForAtos(SupplierInfo supplierInfo) {
        AtosSubjectModelRequest model = new AtosSubjectModelRequest();
        //获取当前登录人
        String username = SecurityUtils.getCurrentUsername();
        if (StrUtil.isBlank(username)){
            username = "admin";
        }
        //int [] i= {21};
        AtosSubjectSendRequest atosSubjectSendRequest = new AtosSubjectSendRequest();
        atosSubjectSendRequest.setAdmin(supplierInfo.getContact());
        atosSubjectSendRequest.setAdmin_telephone(supplierInfo.getPhone());
        atosSubjectSendRequest.setCreated_by(username);
        atosSubjectSendRequest.setCreated_by_name(username);
        atosSubjectSendRequest.setTk(supplierInfo.getTkId());
        atosSubjectSendRequest.setTenant_name(supplierInfo.getSupNameCn());
        atosSubjectSendRequest.setTenant_type(0);
        atosSubjectSendRequest.setSystem_id_list(system_id_list);
        atosSubjectSendRequest.setTenant_note("acms系统添加");
        model.setModel(atosSubjectSendRequest);
        //拼接用户信息
        String atosPath = resourcePath + subjectCreatePath;
        //log.info("新建供应商地址：{}", atosPath);
        //log.info("新建供应商参数：{}", JSONUtil.toJsonStr(model));
        String post = HttpUtil.post(atosPath, JSONUtil.toJsonStr(model));
        //log.info("新建供应商返回参数：{}", JSONUtil.toJsonStr(post));
        AtosUserResponse atosUserResponse = null;
        try {
            atosUserResponse = JSONUtil.toBean(post, AtosUserResponse.class);
        } catch (Exception e) {
            log.info("atos系统返回空信息：新建供应商数据异常{}", post);
        }
        //log.info("新建供应商返回参数：{}", JSONUtil.toJsonStr(atosUserResponse));
        log.info("atos系统新增供应商返回信息,address:{},send:{},result:{}", atosPath, JSONUtil.toJsonStr(model), JSONUtil.toJsonStr(post));
        if (null != atosUserResponse) {
            if (atosUserResponse.isSuccess()) {
                String uk = atosUserResponse.getData();

                //成功
                return atosUserResponse;
            } else {
                //log.info("atos系统新增供应商返回错误信息,address:{},send:{},result:{}",atosPath,JSONUtil.toJsonStr(model),JSONUtil.toJsonStr(post));
                throw new BadRequestException("atos系统新增供应商返回错误信息:" + atosUserResponse.getMsg() + "，请联系atos系统管理员！");
            }

        } else {
            log.info("atos系统返回空信息：新建供应商数据异常{}", post);
        }
        return null;
    }

    @Override
    public AtosUserResponse pullUpdateSubjectInfoForAtos(SupplierInfo supplierInfo) {
        AtosSubjectModelRequest model = new AtosSubjectModelRequest();
        //获取当前登录人
        String username = SecurityUtils.getCurrentUsername();
        //int [] i= {21};
        AtosSubjectSendRequest atosSubjectSendRequest = new AtosSubjectSendRequest();
        atosSubjectSendRequest.setAdmin(supplierInfo.getContact());
        atosSubjectSendRequest.setAdmin_telephone(supplierInfo.getPhone());
        atosSubjectSendRequest.setCreated_by(username);
        atosSubjectSendRequest.setCreated_by_name(username);
        atosSubjectSendRequest.setTk(supplierInfo.getTkId());
        atosSubjectSendRequest.setTenant_name(supplierInfo.getSupNameCn());
        atosSubjectSendRequest.setTenant_type(0);
        atosSubjectSendRequest.setSystem_id_list(system_id_list);
        atosSubjectSendRequest.setTenant_note("acms系统编辑");
        model.setModel(atosSubjectSendRequest);
        //拼接用户信息
        String atosPath = resourcePath + subjectUpdatePath;
        //log.info("新建供应商地址：{}", atosPath);
        //log.info("新建供应商参数：{}", JSONUtil.toJsonStr(model));
        String post = HttpUtil.post(atosPath, JSONUtil.toJsonStr(model));
        //log.info("新建供应商返回参数：{}", JSONUtil.toJsonStr(post));
        AtosUserResponse atosUserResponse = JSONUtil.toBean(post, AtosUserResponse.class);
        //log.info("新建供应商返回参数：{}", JSONUtil.toJsonStr(atosUserResponse));
        log.info("atos系统编辑供应商返回信息,address:{},send:{},result:{}", atosPath, JSONUtil.toJsonStr(model), JSONUtil.toJsonStr(post));
        if (null != atosUserResponse) {
            if (atosUserResponse.isSuccess()) {
                String uk = atosUserResponse.getData();
                //成功
                return atosUserResponse;
            } else {
                //log.info("atos系统新增供应商返回错误信息,address:{},send:{},result:{}",atosPath,JSONUtil.toJsonStr(model),JSONUtil.toJsonStr(post));
                throw new BadRequestException("atos系统编辑供应商返回错误信息:" + atosUserResponse.getMsg() + "，请联系atos系统管理员！");
            }

        } else {
            log.info("atos系统返回空信息：编辑供应商数据异常{}", post);
        }
        return null;
    }

    @Override
    public AtosUserResponse pullUpdateStatusSubjectInfoForAtos(SupplierInfo supplierInfo) {
        AtosSubjectStatusModelRequest model = new AtosSubjectStatusModelRequest();
        //获取当前登录人
        String username = SecurityUtils.getCurrentUsername();
        //int [] i= {21};
        AtosSubjectStatusSendRequest sendRequest = new AtosSubjectStatusSendRequest();
        sendRequest.setTk(supplierInfo.getTkId());
        if (supplierInfo.getSupStatus().equals(AtourSystemEnum.SupStatusEnum.COOPERATION.getKey())) {
            sendRequest.setData_status(0);
        } else {
            sendRequest.setData_status(1);
        }
        model.setModel(sendRequest);
        //拼接用户信息
        String atosPath = resourcePath + subjectUpdateStatusPath;
        //log.info("新建供应商地址：{}", atosPath);
        //log.info("新建供应商参数：{}", JSONUtil.toJsonStr(model));
        String post = HttpUtil.post(atosPath, JSONUtil.toJsonStr(model));
        //log.info("新建供应商返回参数：{}", JSONUtil.toJsonStr(post));
        AtosUserResponse atosUserResponse = JSONUtil.toBean(post, AtosUserResponse.class);
        //log.info("新建供应商返回参数：{}", JSONUtil.toJsonStr(atosUserResponse));
        log.info("atos系统状态变更供应商返回信息,address:{},send:{},result:{}", atosPath, JSONUtil.toJsonStr(model), JSONUtil.toJsonStr(post));
        if (null != atosUserResponse) {
            if (atosUserResponse.isSuccess()) {
                String uk = atosUserResponse.getData();
                //成功
                return atosUserResponse;
            } else {
                //log.info("atos系统新增供应商返回错误信息,address:{},send:{},result:{}",atosPath,JSONUtil.toJsonStr(model),JSONUtil.toJsonStr(post));
                throw new BadRequestException("atos系统状态变更供应商返回错误信息:" + atosUserResponse.getMsg() + "，请联系atos系统管理员！");
            }

        } else {
            log.info("atos系统返回空信息：状态变更供应商数据异常{}", post);
        }
        return null;
    }

    @Override
    public AtosUserResponse pullDeleteSubjectInfoForAtos(SupplierInfo supplierInfo) {
        AtosSubjectStatusModelRequest model = new AtosSubjectStatusModelRequest();
        //获取当前登录人
        String username = SecurityUtils.getCurrentUsername();
        //int [] i= {21};
        AtosSubjectStatusSendRequest sendRequest = new AtosSubjectStatusSendRequest();
        sendRequest.setTk(supplierInfo.getTkId());
        sendRequest.setData_status(4);
        model.setModel(sendRequest);
        //拼接用户信息
        String atosPath = resourcePath + subjectUpdateStatusPath;
        //log.info("新建供应商地址：{}", atosPath);
        //log.info("新建供应商参数：{}", JSONUtil.toJsonStr(model));
        String post = HttpUtil.post(atosPath, JSONUtil.toJsonStr(model));
        //log.info("新建供应商返回参数：{}", JSONUtil.toJsonStr(post));
        AtosUserResponse atosUserResponse = JSONUtil.toBean(post, AtosUserResponse.class);
        //log.info("新建供应商返回参数：{}", JSONUtil.toJsonStr(atosUserResponse));
        log.info("atos系统删除供应商返回信息,address:{},send:{},result:{}", atosPath, JSONUtil.toJsonStr(model), JSONUtil.toJsonStr(post));
        if (null != atosUserResponse) {
            if (atosUserResponse.isSuccess()) {
                String uk = atosUserResponse.getData();
                //成功
                return atosUserResponse;
            } else {
                //log.info("atos系统新增供应商返回错误信息,address:{},send:{},result:{}",atosPath,JSONUtil.toJsonStr(model),JSONUtil.toJsonStr(post));
                throw new BadRequestException("atos系统删除供应商返回错误信息:" + atosUserResponse.getMsg() + "，请联系atos系统管理员！");
            }

        } else {
            log.info("atos系统返回空信息：删除供应商数据异常{}", post);
        }
        return null;
    }

    @Override
    public AtosUserResponse deleteUserInfoForAtos(User user, int type) {
        AtosUserDeleteSendRequest model = new AtosUserDeleteSendRequest();

        AtosUserDeleteRequest atosUserDeleteRequest = new AtosUserDeleteRequest();
        atosUserDeleteRequest.setUk(user.getOpenId());
        atosUserDeleteRequest.setData_status(type);
        model.setModel(atosUserDeleteRequest);
        //拼接用户信息
        String atosPath = resourcePath + deletePath;
        //log.info("删除用户地址：{}", atosPath);
        //log.info("删除用户参数：{}", JSONUtil.toJsonStr(model));
        String post = HttpUtil.post(atosPath, JSONUtil.toJsonStr(model));
        //log.info("删除用户返回参数：{}", JSONUtil.toJsonStr(post));
        AtosUserResponse atosUserResponse = null;
        try {
            atosUserResponse = JSONUtil.toBean(post, AtosUserResponse.class);
        } catch (Exception e) {
            throw new BadRequestException("atos系统返回空信息：删除用户数据异常{}" + post);
        }
        //log.info("删除用户返回参数：{}", JSONUtil.toJsonStr(atosUserResponse));
        log.info("atos系统删除用户返回信息,address:{},send:{},result:{}", atosPath, JSONUtil.toJsonStr(model), JSONUtil.toJsonStr(post));
        if (null != atosUserResponse) {
            if (atosUserResponse.isSuccess()) {
                String uk = atosUserResponse.getData();
                //成功
                return atosUserResponse;
            } else {
                throw new BadRequestException("atos系统删除用户返回错误信息：" + atosUserResponse.getMsg() + "，请联系atos系统管理员！");
            }

        } else {
            log.info("atos系统返回空信息：删除用户数据异常{}", post);
        }
        return null;
    }

    @Override
    public void push(Long[] ids) {
        if (ids != null && ids.length > 0){
            List<SupplierInfo> infoList = supplierInfoRepository.selectBatchIds(Arrays.asList(ids));
            if (infoList!= null && !infoList.isEmpty()) {
                infoList.forEach(this::pullSubjectInfoForAtos);
            }
        }

    }

}