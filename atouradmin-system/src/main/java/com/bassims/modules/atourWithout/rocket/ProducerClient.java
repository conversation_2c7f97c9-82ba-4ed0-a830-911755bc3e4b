package com.bassims.modules.atourWithout.rocket;

import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * @description:
 * @author: shiX
 * @date: 2023/11/15
 */
@Configuration
public class ProducerClient {

    @Value("${rocketmq.name-server}")
    private String serverAddress;

    @Value("${rocketmq.producer.group}")
    private String group;

    @Value("${rocketmq.consumer.access-key}")
    private String accessKey;

    @Value("${rocketmq.consumer.secret-key}")
    private String secretKey;

    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public ProducerBean buildProducer() {
        ProducerBean producer = new ProducerBean();
        //配置文件
        Properties properties = new Properties();
        // AccessKey 阿里云身份验证，在阿里云服务器管理控制台创建
        properties.setProperty(PropertyKeyConst.AccessKey, accessKey);
        // SecretKey 阿里云身份验证，在阿里云服务器管理控制台创建
        properties.setProperty(PropertyKeyConst.SecretKey, secretKey);
        // 设置 TCP 接入域名，进入控制台的实例管理页面的“获取接入点信息”区域查看
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, serverAddress);
        //设置发送超时时间，单位毫秒
        properties.setProperty(PropertyKeyConst.SendMsgTimeoutMillis, "3000");
        //设置组id
        properties.setProperty(PropertyKeyConst.GROUP_ID, group);
        producer.setProperties(properties);
        return producer;
    }

}
