/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.bsEnum.AtourSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.domain.vo.ProjectInfoSimple;
import com.bassims.modules.atour.domain.vo.ProjectStakeholdersVO;
import com.bassims.modules.atour.repository.*;
import com.bassims.modules.atour.service.*;
import com.bassims.modules.atour.service.dto.ProjectInfoExpansionDto;
import com.bassims.modules.atour.service.dto.ProjectStakeholdersDto;
import com.bassims.modules.atour.service.dto.ProjectStakeholdersQueryCriteria;
import com.bassims.modules.atour.service.dto.ProjectStakeholdersUpdateDto;
import com.bassims.modules.atour.service.mapstruct.ProjectStakeholdersMapper;
import com.bassims.modules.atour.util.NoteInfoMappingUtil;
import com.bassims.modules.system.domain.Role;
import com.bassims.modules.system.domain.User;
import com.bassims.modules.system.repository.DictDetailRepository;
import com.bassims.modules.system.repository.RoleRepository;
import com.bassims.modules.system.repository.UserRepository;
import com.bassims.modules.system.service.RoleService;
import com.bassims.modules.system.service.UserService;
import com.bassims.modules.system.service.dto.UserDto;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.SecurityUtils;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-03-29
 **/
@Service
public class ProjectStakeholdersServiceImpl extends BaseServiceImpl<ProjectStakeholdersRepository, ProjectStakeholders> implements ProjectStakeholdersService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectStakeholdersServiceImpl.class);

    @Autowired
    private ProjectStakeholdersRepository projectStakeholdersRepository;
    @Autowired
    private ProjectStakeholdersMapper projectStakeholdersMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private ProjectNodeInfoService projectNodeInfoService;
    @Autowired
    private ProjectTaskService projectTaskService;
    @Autowired
    private ProjectApproveDetailService projectApproveDetailService;
    @Autowired
    private ProjectStakeholdersService projectStakeholdersService;
    @Autowired
    private ProjectInfoService projectInfoService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private ProjectGroupRepository projectGroupRepository;
    @Autowired
    private ProjectNodeInfoRepository projectNodeInfoRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private ProjectInfoExpansionService projectInfoExpansionService;
    @Autowired
    private NoteInfoMappingUtil util;
    @Autowired
    private DictDetailRepository dictDetailRepository;
    @Autowired
    private SysUsersRolesRepository sysUsersRolesRepository;


    @Override
    public Map<String, Object> queryAll(ProjectStakeholdersQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<ProjectStakeholders> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectStakeholders.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectStakeholdersMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }


    @Override
    public Map<String, Object> queryList(ProjectStakeholdersQueryCriteria criteria, Pageable pageable) {
        //设置orderId为null
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (ObjectUtil.isEmpty(currentUserId)) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }

        Map<String, Object> map = new LinkedHashMap<>(2);
//        getPage(pageable);
        PageHelper.clearPage();
        PageInfo<ProjectStakeholdersDto> page = new PageInfo<>(projectStakeholdersRepository.getLists(criteria));
        List<ProjectStakeholdersDto> list = page.getList();
        if (CollectionUtil.isNotEmpty(list)) {
            criteria.setOrderIdNull("order_id");
            String isChange = "0";
            if (ObjectUtil.isNotEmpty(criteria.getRoleCode())
                    && criteria.getRoleCode().equals(AtourSystemEnum.engineeringRoleCodeEnum.YZXMJL.getKey())
                    && ObjectUtil.isNotEmpty(criteria.getProjectNo())) {
                ProjectInfoExpansionDto expansionDto = projectInfoExpansionService.queryHotelId(criteria.getProjectNo());
                if (ObjectUtil.isNotEmpty(expansionDto)) {
                    if (expansionDto.getOwnerProjectManager().equals(currentUserId.toString())) {
                        isChange = "1";
                    }
                }
            }
            if (isChange.equals("1")) {
                list.forEach(p -> {
                    p.setIsChange("1");

                    if (ObjectUtils.isNotEmpty(p.getBrandCode())) {
                        p.setBrandName(dictDetailRepository.getIdByNodeCodeRemark(p.getBrandCode()));
                    }
                    if (ObjectUtils.isNotEmpty(p.getProductCode())) {
                        p.setProductName(dictDetailRepository.getIdByNodeCodeRemark(p.getProductCode()));
                    }

                });
            }
        }
        map.put("content", list);
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectStakeholdersDto> queryAll(ProjectStakeholdersQueryCriteria criteria) {
        //设置orderId为null
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (ObjectUtil.isEmpty(currentUserId)) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }

        criteria.setOrderIdNull("order_id");
        criteria.setProjectId(projectNodeInfoService.getSubmeterProjectId(criteria.getProjectId()));
        String isChange = "0";
        if (ObjectUtil.isNotEmpty(criteria.getRoleCode())
                && criteria.getRoleCode().equals(AtourSystemEnum.engineeringRoleCodeEnum.YZXMJL.getKey())
                && ObjectUtil.isNotEmpty(criteria.getProjectNo())) {
            ProjectInfoExpansionDto expansionDto = projectInfoExpansionService.queryHotelId(criteria.getProjectNo());
            if (ObjectUtil.isNotEmpty(expansionDto)) {
                if (expansionDto.getOwnerProjectManager().equals(currentUserId.toString())) {
                    isChange = "1";
                }
            }
        }

        List<ProjectStakeholdersDto> result = projectStakeholdersRepository.getList(criteria);
        if (ObjectUtil.isNotEmpty(isChange) && isChange.equals("1")) {
            result.forEach(p -> {
                p.setIsChange("1");
            });
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectStakeholdersDto findById(Long stakeholderId) {
        ProjectStakeholders projectStakeholders = Optional.ofNullable(getById(stakeholderId)).orElseGet(ProjectStakeholders::new);
        ValidationUtil.isNull(projectStakeholders.getStakeholderId(), getEntityClass().getSimpleName(), "stakeholderId", stakeholderId);
        return projectStakeholdersMapper.toDto(projectStakeholders);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createStakeholders(ProjectStakeholdersVO resources) {
        if (ObjectUtil.isEmpty(resources.getProjectId())) {
            throw new BadRequestException("参数 projectId 不可为空");
        }
        Long currentUserId = SecurityUtils.getCurrentUserId();
//        List<Long> ids = instructionSheetRepository.getIdsByStakeholders(currentUserId, Boolean.FALSE);
        if (ObjectUtil.isEmpty(currentUserId)) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        ProjectInfo byId = projectInfoService.getById(resources.getProjectId());

        if (ObjectUtil.isNotEmpty(resources.getUserId()) && ObjectUtil.isNotEmpty(resources.getRoleCode())) {
            projectInfoService.createProjectStakeholders(resources.getProjectId(), resources.getRoleCode(), resources.getUserId(), null);
        } else {
            String[] roleCdes = new String[]{
                    AtourSystemEnum.engineeringRoleCodeEnum.YYJL.getKey(),
                    AtourSystemEnum.engineeringRoleCodeEnum.KYJL.getKey(),
                    AtourSystemEnum.engineeringRoleCodeEnum.CGXS.getKey(),
                    AtourSystemEnum.engineeringRoleCodeEnum.JFZXGXZC.getKey(),
                    AtourSystemEnum.engineeringRoleCodeEnum.JFZXGCFZR.getKey(),
                    AtourSystemEnum.engineeringRoleCodeEnum.JFZXJSFZR.getKey(),
                    AtourSystemEnum.engineeringRoleCodeEnum.JFZXBMFZR.getKey(),
                    AtourSystemEnum.engineeringRoleCodeEnum.KFZQFZR.getKey(),
                    AtourSystemEnum.engineeringRoleCodeEnum.KFFQFZR.getKey()
            };
            for (String roleCde : roleCdes) {
                final List<User> byCodes = userRepository.findByRoleCodeA(roleCde, byId.getCity());
                if (ObjectUtil.isNotEmpty(byCodes)) {
                    for (User byCode : byCodes) {
                        projectInfoService.createProjectStakeholders(resources.getProjectId(), roleCde, byCode.getId(), null);
                    }
                }
            }
        }
        return "已更新干系人";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectStakeholdersDto create(ProjectStakeholders resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setStakeholderId(snowflake.nextId());
        save(resources);
        return findById(resources.getStakeholderId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectStakeholders resources) {
        ProjectStakeholders projectStakeholders = Optional.ofNullable(getById(resources.getStakeholderId())).orElseGet(ProjectStakeholders::new);
        ValidationUtil.isNull(projectStakeholders.getStakeholderId(), "ProjectStakeholders", "id", resources.getStakeholderId());
        projectStakeholders.copy(resources);
        updateById(projectStakeholders);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStakeholders(ProjectStakeholders resources) {
        if (null == resources || null == resources.getStakeholderId()) {
            throw new BadRequestException("参数 stakeholderId 不可为空");
        }
        //开发经理是多人，获取当前被修改的人员
        Long developmentManager = null;
        //获取更新前的干系人数据
        ProjectStakeholders oldSta = getById(resources.getStakeholderId());
        if (oldSta.getUserId() == null) {
            Timestamp nowTime = new Timestamp(System.currentTimeMillis());
            resources.setJoinTime(nowTime);
            resources.setReason(null);
            resources.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
            updateById(resources);
            //特殊处理，工程经理变换时，变更项目主体数据的工程经理
            updateProjectNodeInfo(resources.getProjectId(), resources.getUserId(), resources.getRoleCode(), developmentManager);
        } else {
            Timestamp nowTime = new Timestamp(System.currentTimeMillis());
            //修改状态为离项
            oldSta.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS1.getKey());
            oldSta.setReason(resources.getReason());
            oldSta.setLeaveTime(nowTime);
            updateById(oldSta);
            //新增干系人
            resources.setStakeholderId(null);
            resources.setReason(null);
            resources.setJoinTime(nowTime);
            resources.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
            save(resources);
            //更新审批人
            //更新任务责任人
            // projectNodeInfoService.updateRoleCode(resources.getProjectId(), resources.getUserId(), oldSta.getUserId());
            //更新任务人
            projectTaskService.changeTaskUser(resources.getProjectId(), null, resources.getUserId(), oldSta.getUserId());
            //更新审批人
            projectApproveDetailService.changeApproveUser(resources.getProjectId(), null, resources.getUserId(), oldSta.getUserId());

            //设计单位存在的话，更新设计到已完成的三级看板的设计单位和设计师
            if (ObjectUtil.isNotEmpty(resources.getSupplierId())) {
                upSupplierByNodeCode(resources.getProjectId(), resources.getRoleCode(), resources.getUserId());
            }
            //开发经理
            if (AtourSystemEnum.engineeringRoleCodeEnum.KFJL.getKey().equals(resources.getRoleCode())) {
                developmentManager = oldSta.getUserId();
            }
            //特殊处理，工程经理变换时，变更项目主体数据的工程经理
            updateProjectNodeInfo(resources.getProjectId(), resources.getUserId(), resources.getRoleCode(), developmentManager);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStakeholdersInTime(ProjectStakeholders resources) {
        Date now = new Date();
        //判断是否重复操作
        String nowStr = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("change_id", resources.getStakeholderId());
        queryWrapper.eq("change_end_time", nowStr);
        if (count(queryWrapper) > 0) {
            throw new BadRequestException("该变更干系人已经存在变更请勿重复操作");
        }
        if (resources.getChangeStartTime() == null || resources.getChangeEndTime() == null) {
            this.updateStakeholders(resources);
            return;
        }
        //判断时间是否相等
        String strTime = DateUtil.format(resources.getChangeStartTime(), DatePattern.NORM_DATE_PATTERN);
        String endTime = DateUtil.format(resources.getChangeEndTime(), DatePattern.NORM_DATE_PATTERN);
        if (now.after(resources.getChangeStartTime()) && now.before(resources.getChangeEndTime())
                || nowStr.equals(strTime) || nowStr.equals(endTime)) {//当前时间在两者之间,直接更改
            //获取更新前的干系人数据
            ProjectStakeholders oldSta = getById(resources.getStakeholderId());
            resources.setChangeId(oldSta.getStakeholderId());
            this.updateStakeholders(resources);
            oldSta.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS3.getKey());
            updateById(oldSta);
        } else if (resources.getChangeStartTime().after(now)) { //当前时间在开始时间之前,创建一个离项的人员
            Timestamp nowTime = new Timestamp(System.currentTimeMillis());
            resources.setChangeId(resources.getStakeholderId());
            resources.setStakeholderId(null);
            resources.setReason(null);
            resources.setJoinTime(nowTime);
            resources.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS2.getKey());
            save(resources);
        } else {
            throw new BadRequestException("输入的时间有误");
        }
    }


    @Override
    public void batchUpdateStakeholders(ProjectStakeholdersUpdateDto resources) {
        //批量变更干系人
        //1.查询离职用户在所属项目中的干系人信息
        List<ProjectStakeholders> list = projectStakeholdersRepository.getListByProjectIdAndUserId(resources.getProjectId(), resources.getLeavingUser());
        for (ProjectStakeholders stakeholders : list) {
            stakeholders.setUserId(resources.getUserId());
            this.updateStakeholders(stakeholders);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(List<ProjectStakeholders> list) {
        if (CollectionUtil.isNotEmpty(list)) {
            for (ProjectStakeholders projectStakeholders : list) {
                this.updateStakeholders(projectStakeholders);
            }
        }
    }

    private void upSupplierByNodeCode(Long projectId, String roleCode, Long userId) {
        //查看未完成的【筹建启动会】【正式开工】【设计勘测】【确认设计单位】 二级任务
        String supplierByNodeCode = projectGroupRepository.getSupplierByNodeCode(projectId, JhSystemEnum.StakeholderUnitLevelEnum.SECOND_LEVLE.getSpec());
        if (ObjectUtil.isNotEmpty(supplierByNodeCode)) {
            util.initialize(projectId);
            List<Map<String, String>> nodeCodeList = JhSystemEnum.StakeholderUnitEnum.getNodeCodeList(roleCode, supplierByNodeCode);
            for (Map<String, String> stringMap : nodeCodeList) {
                for (String s : stringMap.keySet()) {
                    //修改设计单位
                    LambdaUpdateWrapper<ProjectNodeInfo> wrapper = Wrappers.lambdaUpdate(ProjectNodeInfo.class)
                            .eq(ProjectNodeInfo::getProjectId, projectId)
                            .eq(ProjectNodeInfo::getNodeCode, s);
                    ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectOne(wrapper);
                    nodeInfo.setRemark(roleCode);
                    int update = projectNodeInfoRepository.updateById(nodeInfo);
                    //修改设计师
                    LambdaUpdateWrapper<ProjectNodeInfo> wrapper1 = Wrappers.lambdaUpdate(ProjectNodeInfo.class)
                            .eq(ProjectNodeInfo::getProjectId, projectId)
                            .eq(ProjectNodeInfo::getNodeCode, stringMap.get(s));
                    ProjectNodeInfo nodeInfo1 = projectNodeInfoRepository.selectOne(wrapper1);
                    nodeInfo1.setRemark(userId + "");
                    int update1 = projectNodeInfoRepository.updateById(nodeInfo1);
                }
            }
        }
    }


    @Override
    public void deleteAll(Long[] ids) {
        for (Long stakeholderId : ids) {
            projectStakeholdersRepository.deleteById(stakeholderId);
        }
    }

    @Override
    public void download(List<ProjectStakeholdersDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectStakeholdersDto projectStakeholders : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("项目id", projectStakeholders.getProjectId());
            map.put("用户id", projectStakeholders.getUserId());
            map.put("角色id", projectStakeholders.getRoleId());
            map.put("角色名称", projectStakeholders.getRoleName());
            map.put("加入时间", projectStakeholders.getJoinTime());
            map.put("状态", projectStakeholders.getShakeholderStatus());
            map.put("离开时间", projectStakeholders.getLeaveTime());
            map.put("原因", projectStakeholders.getReason());
            map.put("是否是审批角色", projectStakeholders.getIsApprove());
            map.put("是否不展示", projectStakeholders.getIsNotshow());
            map.put(" createTime", projectStakeholders.getCreateTime());
            map.put(" createBy", projectStakeholders.getCreateBy());
            map.put(" updateTime", projectStakeholders.getUpdateTime());
            map.put(" updateBy", projectStakeholders.getUpdateBy());
            map.put("是否可用", projectStakeholders.getIsEnabled());
            map.put("是否删除", projectStakeholders.getIsDelete());
            map.put("角色code", projectStakeholders.getRoleCode());
            map.put("下拉列表角色名称", projectStakeholders.getDownCode());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public String getTaskPersonByRoleCode(String roleCodes, Long orderId) {
        List<String> list = Lists.newArrayList();
        Arrays.stream(roleCodes.split(",")).forEach(s -> {
            list.add(s);
        });
        String userName = "";
        for (String roleCode : list) {
            LambdaQueryWrapper<ProjectStakeholders> lambdaQueryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class);
            lambdaQueryWrapper.eq(ProjectStakeholders::getRoleCode, roleCode)
                    .eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey())
                    .eq(ProjectStakeholders::getOrderId, orderId);
            List<ProjectStakeholders> stakeholderslist = projectStakeholdersService.list(lambdaQueryWrapper);
            if (stakeholderslist.size() > 0) {
                for (ProjectStakeholders one : stakeholderslist) {
                    if (one != null && null != one.getUserId()) {
                        UserDto UserDto = userService.findById(one.getUserId());
                        if (null != UserDto) {
                            if (ObjectUtil.isNotEmpty(UserDto.getEnabled()) && !UserDto.getEnabled()) {
                                continue;
                            }
                            if (userName == "") {
                                userName = projectNodeInfoService.changeUserName(UserDto, UserDto.getUsername());
                            } else {
                                userName = userName + "," + projectNodeInfoService.changeUserName(UserDto, UserDto.getUsername());
                            }

                        }
                    }
                }
            }
        }
        return userName;
    }

    /**
     * 特殊处理，工程经理变换时，变更项目主体数据的工程经理
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateProjectNodeInfo(Long projectId, Long userId, String roleCode, Long developmentManager) {
        if (projectId == null || userId == null) {
            return;
        }
        LambdaQueryWrapper<ProjectInfo> lambdaQueryWrapper = Wrappers.lambdaQuery(ProjectInfo.class);
        lambdaQueryWrapper.eq(ProjectInfo::getProjectId, projectId)
                .eq(ProjectInfo::getIsDelete, false);
        //获得主体相关数据
        ProjectInfo projectInfo = projectInfoService.getOne(lambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(projectInfo)) {

            //变更项目经理
            if (JhSystemEnum.JobEnum.GCJL.getKey().equals(roleCode)) {
                projectInfo.setProjectManager(userId);
            }
            //机电工程师
            if (AtourSystemEnum.engineeringRoleCodeEnum.JDGCS.getKey().equals(roleCode)) {
                projectInfo.setMechanicalAndElectricalEngineer(userId);
            }
            // 弱电工程师
            if (AtourSystemEnum.engineeringRoleCodeEnum.RDGCS.getKey().equals(roleCode)) {
                projectInfo.setWeakCurrentEngineer(userId);
            }
            //软装设计
            if (AtourSystemEnum.engineeringRoleCodeEnum.RZSJ.getKey().equals(roleCode)) {
                projectInfo.setSoftDecorationDesign(userId);
            }
            //设计单位（设计师）
            if (JhSystemEnum.JobEnum.SJFZR.getKey().equals(roleCode)) {
                projectInfo.setDesigner(userId);
            }
            //飞行质检人员
            if (AtourSystemEnum.engineeringRoleCodeEnum.FXZJRY.getKey().equals(roleCode)) {
                projectInfo.setFlightQualityInspectionPersonnel(userId);
            }
            //竣工验收人员
            if (AtourSystemEnum.engineeringRoleCodeEnum.JGYSRY.getKey().equals(roleCode)) {
                projectInfo.setCompletionAcceptancePersonnel(userId);
            }
            //采购销售
            if (AtourSystemEnum.engineeringRoleCodeEnum.CGXS.getKey().equals(roleCode)) {
                projectInfo.setProcurementMarketing(userId);
            }
            //设计共管人员
            if (AtourSystemEnum.engineeringRoleCodeEnum.SJGGRY.getKey().equals(roleCode)) {
                projectInfo.setDesignCoManagementPersonnel(userId);
            }
            //营建区域负责人
            if (AtourSystemEnum.engineeringRoleCodeEnum.YJQYFZR.getKey().equals(roleCode)) {
                projectInfo.setConstructionAreaLeader(userId);
            }
            //开发经理
            if (AtourSystemEnum.engineeringRoleCodeEnum.KFJL.getKey().equals(roleCode)) {
                projectInfo.setDevelopmentManager(projectInfo.getDevelopmentManager().replace(developmentManager.toString(), userId.toString()));
            }
            //开发分区负责人
            if (AtourSystemEnum.engineeringRoleCodeEnum.KFFQFZR.getKey().equals(roleCode)) {
                projectInfo.setDevelopmentZoneLeader(userId);
            }
            //开发战区负责人
            if (AtourSystemEnum.engineeringRoleCodeEnum.KFZQFZR.getKey().equals(roleCode)) {
                projectInfo.setDevelopmentTheaterLeader(userId);
            }
            //特许经理
            if (AtourSystemEnum.engineeringRoleCodeEnum.TXJL.getKey().equals(roleCode)) {
                projectInfo.setFranchiseManager(userId);
            }

            //运营经理
            if (AtourSystemEnum.engineeringRoleCodeEnum.YYJL.getKey().equals(roleCode)) {
                projectInfo.setOperationsManager(userId);
            }
            //开业经理
            if (AtourSystemEnum.engineeringRoleCodeEnum.KYJL.getKey().equals(roleCode)) {
                projectInfo.setOpeningManager(userId);
            }
            //运营战区负责人
            if (AtourSystemEnum.engineeringRoleCodeEnum.YYZQFZR.getKey().equals(roleCode)) {
                projectInfo.setOperationsTheaterLeader(userId);
            }
            projectInfoService.updateById(projectInfo);
        }
    }

    /**
     * 用户选的角色，插入干系人表
     */
    @Override
    public Boolean insertStakeholders(StakeholdersReq resources) {
        //查询role
//        List<StakeholdersRoleReq> roles = roleService.selectRoleByCode(resources.getRoleCode());

//        获取当前用户id，并判断用户是否存在
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
//        查询当前用户,并获取对应干系人的roleId
        List<Long> roleIds = roleRepository.findRoleIdsByUserId(currentUserId);

        //根据当前用户，获取对应干系人
        List<Role> roles = roleRepository.findStakeholdersByUserId(currentUserId);

        List<ProjectStakeholders> projectStakeholdersList = new ArrayList<>();

        //用户输入roleCode计数
        int count = 0;
//        outerLoop:
        for (Role role : roles) {
            for (String roleCode : resources.getRoleCode()) {
                if (resources.getRoleCode().size() == count) {
//                    退出所有循环
                    break;
                }
                ProjectStakeholders projectStakeholders = new ProjectStakeholders();
                projectStakeholders.setProjectId(resources.getProjectId());
                projectStakeholders.setUserId(currentUserId);
                projectStakeholders.setRoleId(role.getId());
                projectStakeholders.setRoleName(role.getName());
                projectStakeholders.setJoinTime(new Timestamp(System.currentTimeMillis()));
                projectStakeholders.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
                projectStakeholders.setIsApprove(false);
                projectStakeholders.setIsDelete(false);
                projectStakeholders.setRoleCode(roleCode);
                projectStakeholders.setCreateBy(role.getCreateBy());
                //插入t_project_stakeholders
                projectStakeholdersRepository.insert(projectStakeholders);
                count++;
                projectStakeholdersList.add(projectStakeholders);
            }
        }
        if (count == 0) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public List<String> selectRolesByUserId(Long userId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("user_id", userId);
        queryWrapper.groupBy("role_name");
        queryWrapper.select("role_name");
        List<String> roleNames = listObjs(queryWrapper);
        return roleNames;
    }

    // 变更干系人到时间变为在项
    @Override
    @Transactional
    public void stakeholderInTerm() {
        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
        String now = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
        //查询离项的人员，并且时间快到的
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.le("change_start_time", now);
        queryWrapper.ge("change_end_time", now);
        queryWrapper.eq("shakeholder_status", JhSystemEnum.StakeholderStatusEnum.STA_STATUS2.getKey());
        List<ProjectStakeholders> stakeholders = list(queryWrapper);
        for (ProjectStakeholders stakeholderPrepareInTerm : stakeholders) {
            Long noTermChangeId = stakeholderPrepareInTerm.getChangeId();
            ProjectStakeholders projectStakeholdersPrepareNoTerm = getById(noTermChangeId);
            projectStakeholdersPrepareNoTerm.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS3.getKey());
            //更新在项变为离项
            updateById(projectStakeholdersPrepareNoTerm);
            //更新未到期变为在项
            stakeholderPrepareInTerm.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
            stakeholderPrepareInTerm.setJoinTime(nowTime);
            updateById(stakeholderPrepareInTerm);
        }
    }

    //变更短期干系人到时间变为离项
    @Override
    @Transactional
    public void stakeholderNoTerm() {
        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
        String now = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
        //查询在项的人员，并且时间到的
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("shakeholder_status", JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
        queryWrapper.lt("change_end_time", now);
        List<ProjectStakeholders> stakeholders = list(queryWrapper);
        for (ProjectStakeholders stakeholderPrepareNoTerm : stakeholders) {
            //查询对应准备在的
            ProjectStakeholders projectStakeholdersPrepareInTerm = getById(stakeholderPrepareNoTerm.getChangeId());
            projectStakeholdersPrepareInTerm.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
            //更新离项变为在项
            updateById(projectStakeholdersPrepareInTerm);
            //更新在项变为离项
            stakeholderPrepareNoTerm.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS1.getKey());
            stakeholderPrepareNoTerm.setLeaveTime(nowTime);
            updateById(stakeholderPrepareNoTerm);
        }
    }

    //增加干系人和现长角色
    @Override
    @Transactional
    public void saveOrUpdateByPmsDepartmentId(User user, String pmsDepartmentId) {
        //酒店ID ,查询项目ID
        Long projectNo = Long.valueOf(pmsDepartmentId);
        LambdaQueryWrapper<ProjectInfo> wrapper = Wrappers.lambdaQuery(ProjectInfo.class);
        wrapper.eq(ProjectInfo::getProjectNo, projectNo);
        wrapper.last("limit 1");
        ProjectInfo serviceOne = projectInfoService.getOne(wrapper);
        if (ObjectUtils.isNotEmpty(serviceOne)) {


            Role role = roleRepository.findRoleIdByCode("xz");
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("role_id", role.getId());
            queryWrapper.eq("user_id", user.getId());
            long count = sysUsersRolesRepository.selectCount(queryWrapper);
            //没有现长角色就添加
            if (count == 0) {
                SysUsersRoles roles = new SysUsersRoles();
                roles.setUserId(user.getId());
                roles.setRoleId(role.getId());
                roles.setIsManuallyAdd("1");
                sysUsersRolesRepository.insert(roles);
            }
            //增加干系人
            queryWrapper.clear();
            queryWrapper.eq("project_id", pmsDepartmentId);
//        queryWrapper.eq("user_id", user.getId());
            queryWrapper.eq("shakeholder_status", JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
            queryWrapper.eq("role_code", "xz");
            if (count(queryWrapper) == 0) {

                //现长
                projectInfoService.createProjectStakeholders(serviceOne.getProjectId(), AtourSystemEnum.engineeringRoleCodeEnum.XZ.getKey(), user.getId(), null);

                //增加竣工验收-派驻现长
                util.initialize(serviceOne.getProjectId());
                queryWrapper.clear();
                queryWrapper.eq("project_id", serviceOne.getProjectId());
                queryWrapper.eq("node_code", "eng-00129021");
                List<ProjectNodeInfo> nodeInfos = projectNodeInfoRepository.selectList(queryWrapper);
                if (nodeInfos.size() > 0) {
                    ProjectNodeInfo nodeInfo = nodeInfos.get(0);
                    nodeInfo.setRemark(user.getId().toString());
                    projectNodeInfoRepository.updateById(nodeInfo);
                }

                //增加派驻现长任务中的信息
                util.initialize(serviceOne.getProjectId());
                queryWrapper.clear();
                queryWrapper.eq("project_id", serviceOne.getProjectId());
                queryWrapper.eq("node_code", "eng-00121002");
                nodeInfos = projectNodeInfoRepository.selectList(queryWrapper);
                if (nodeInfos.size() > 0) {
                    ProjectNodeInfo nodeInfo = nodeInfos.get(0);
                    nodeInfo.setRemark(user.getId().toString());
                    projectNodeInfoRepository.updateById(nodeInfo);
                }
                //增加派驻现长任务中的信息
                util.initialize(serviceOne.getProjectId());
                queryWrapper.clear();
                queryWrapper.eq("project_id", serviceOne.getProjectId());
                queryWrapper.eq("node_code", "eng-00121003");
                nodeInfos = projectNodeInfoRepository.selectList(queryWrapper);
                if (nodeInfos.size() > 0) {
                    ProjectNodeInfo nodeInfo = nodeInfos.get(0);
                    nodeInfo.setRemark(user.getPhone().toString());
                    projectNodeInfoRepository.updateById(nodeInfo);
                }
                //增加派驻现长任务中的信息
                util.initialize(serviceOne.getProjectId());
                queryWrapper.clear();
                queryWrapper.eq("project_id", serviceOne.getProjectId());
                queryWrapper.eq("node_code", "eng-00121004");
                nodeInfos = projectNodeInfoRepository.selectList(queryWrapper);
                if (nodeInfos.size() > 0) {
                    ProjectNodeInfo nodeInfo = nodeInfos.get(0);
                    nodeInfo.setRemark(user.getEmail().toString());
                    projectNodeInfoRepository.updateById(nodeInfo);
                }
            }
        }
    }

    @Override
    @Transactional
    public void changeRollBack(Long stakeholderId) {
        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
        ProjectStakeholders stakeholders = getById(stakeholderId);
        if (stakeholders.getChangeId() == null) {
            throw new BadRequestException("不可变更");
        }
        ProjectStakeholders beChangeStakeholders = getById(stakeholders.getChangeId());
        //把当前的干系人改为离职
        stakeholders.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS1.getKey());
        stakeholders.setLeaveTime(nowTime);
        updateById(stakeholders);
        //把被变更的干系人还原在职
        beChangeStakeholders.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
        updateById(beChangeStakeholders);
    }
}