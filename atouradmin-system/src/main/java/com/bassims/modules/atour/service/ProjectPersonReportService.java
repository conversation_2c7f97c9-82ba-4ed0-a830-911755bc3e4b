package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.ProjectPersonReport;
import com.bassims.modules.atour.service.dto.ProjectPersonReportQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 工程运维人员报表
 *
 * <AUTHOR>
 * @date 2023/03/13
 */
public interface ProjectPersonReportService extends BaseService<ProjectPersonReport> {
    /**
     * 下载
     *
     * @param response 响应
     * @param criteria 标准
     */
    void download(HttpServletResponse response, ProjectPersonReportQueryCriteria criteria) throws IOException;

    /**
     * 查询时间
     *
     * @param criteria 标准
     * @param pageable
     * @return {@link Object}
     */
    Object queryTime(ProjectPersonReportQueryCriteria criteria, Pageable pageable);
}
