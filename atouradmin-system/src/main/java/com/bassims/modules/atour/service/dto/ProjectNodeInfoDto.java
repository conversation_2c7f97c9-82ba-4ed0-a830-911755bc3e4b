/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import cn.hutool.core.lang.tree.Tree;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.bassims.modules.atour.domain.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.alibaba.fastjson.annotation.JSONField;
import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-03-24
**/
@Data
public class ProjectNodeInfoDto implements Serializable {

    /** 节点id */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private String nodeId;

    private String templateCode;

    /** 模板主键 */
    private Long oneTemplateId;
    /** 模板主键 */
    private Long twoNodeId;

    /** 模板主键 */
    private String templateId;

    /** 项目id */
    private String projectId;

    /**
     * 项目编码
     */
    private String processCode;

    private String projectGroupId;

    /** 队列id */
    private String templateQueueId;

    /** 父节点 */
    private String parentId;

    /** 项目版本号 */
    private String projectVersion;

    /** 节点编码 */
    private String nodeCode;

    /** 节点名称 */
    private String nodeName;

    /** 计划开始时间 */
    @JSONField(format = "yyyy-MM-dd")
    private Date planStartDate;

    /** 计划结束时间 */
     @JSONField(format = "yyyy-MM-dd")
    private Date planEndDate;

    /** 预估开始日期 */
    @JSONField(format = "yyyy-MM-dd")
    private Date predictStartDate;

    /** 预估结束日期 */
    @JSONField(format = "yyyy-MM-dd")
    private Date predictEndDate;

    /** 计划需要完成天数 */
    private Integer planDay;

    /** 实际完成时间 */
    @JSONField(format = "yyyy-MM-dd")
    private Date actualEndDate;

    /** 提醒天数 */
    private Integer noticeDay;

    /** 延期天数 */
    private Integer delayDay;

    /** 节点序号 */
    private Integer nodeWbs;

    /** 前置任务配置[{"type":"FS","wbs":11},{"type":"SS","wbs":12}] */
    private String frontWbsConfig;

    /** 是否是关键节点 */
    private Boolean isKey;

    /** 关键节点前置任务 */
    private String keyFrontWbs;

    /** 子节点排序 */
    private double nodeIndex;

    /** 节点等级 */
    private Integer nodeLevel;

    /** 节点类型 */
    private String nodeType;

    /** 节点状态 */
    private String nodeStatus;

    /** 已完成按钮 */
    private Boolean nodeIsfin;

    /** 小程序标志 */
    private String icon;

    /** 节点备注 */
    private String remark;

    private Boolean isDelete;

    /** 创建时间 */
    private Timestamp createTime;

    /** 创建人 */
    private String createBy;

    /** 修改时间 */
    private Timestamp updateTime;

    /** 修改人 */
    private String updateBy;

    /** 是否可用 */
    private Boolean isEnabled;

    /**总工期*/
    private Integer totalDay;

    /** 开始标志（甘特图） */
    private String startSign;

    /** 结束标志（甘特图） */
    private Integer endSign;

    private String jobCode;

    /** 下拉列表角色名称 */
    private String downCode;
    private Boolean isMobile;
    private String relationCode;
    private String relationType;
    private String useCase;
    private String roleCode;
    private String roleName;
    private String isEdit;
    private String seat;
    /**是否换行*/
    private Boolean isWrap;

    private String progress;

    private Boolean isDelay;
    /**
     * 阶段状态
     */
    private String  phaseStatus;

    /** 读权限 */
    private Boolean isRead;

    /** 写权限 */
    private Boolean isWrite;

    /** 隐藏权限 */
    private Boolean isHidden;
    /**
     * 有审批模板
     */
    private Long approveMatrixId;

    private List<Tree<String>> children;
    /**
     * 用于提交保存的list
     */
    private List<ProjectNodeInfoDto> list;

    private String simpleName;

    private Boolean isOpen;

    private String templateGroupId;

    /** 公式 */
    private String formula;

    /** 影响的code */
    private String formulaCode;
    /**
     * 是否有第三方审批
     */
    private Boolean isThird;


    /**
     * 用户添加的版本
     */
    private Integer addedVersion;

    /**
     * 同组最后的code
     */
    private String lastCode;

    /**
     * 用以标识用户添加的块
     */
    private String pieceIndex;

    /**
     * 用于传用户增加的节点块的字符串nodeId
     */
    private String fileId;


    /**
     * 预付明细表格
     */
    private RepayDto repay;

    /**是否不需要任务信息*/
    private Boolean isNotTask;


    /*业主计划完成时间*/
    private String ownerPlansEndDate;


    /*是否展示（0展示、1不展示）*/
    private Integer isShow;


    /*查询当前是否存在未完成的紧前任务，存在就为true*/
    private boolean aBooleanUrgentTask;

    /*如果存在紧前任务，存返回信息*/
    private String urgentTaskMsg;


    /*查询紧前状态  1只查看紧前状态  */
    private Integer queryCompactStatus;
    /*如果存在紧前任务，存显示信息*/
    private String urgentTaskShowMsg;

    private String fileCheckFormat;

    /*子级进度*/
    private String sublevelSchedule;

    /*项目计划事件的计划开始时间*/
    private String planeventPlanStartDate;

    /*项目计划事件的计划结束时间*/
    private String planeventPlanEndDate;

    private List<OrderDetailDto> orderDetails;

    /**
     * 合同管理基本信息-大仓
     */
    private List<ContractManageDto> contractManages;

    /**
     * 费用信息-大仓
     */
    private List<FeeInfoDto> feeInfos;

    /**
     * 费用信息-大仓
     */
    private List<FeeInfoDto> otherFeeInfos;

    /**
     * 审定单-大仓
     */
    private List<ApprovedFormDto> approvedForms;

    /**
     * 竣工验收照片信息-大仓
     */
    private List<CompletionAcceptancePictureDto> completionAcceptancePictures;

    /**
     * 擅自施工
     */
    private List<UnauthorizedConstruction> unauthorizedConstructions;


    /**
     * 施工照片
     */
    private List<ConstructionPhotograph> constructionPhotographs;


    /**
     * 质量管理
     */
    private List<QualityControl> qualityControls;


    /**
     *  项目进度
     */
    private List<ProjectGroup> nodeInfoDtos;


    /**
     *  设计交底问题汇总
     */
    private List<ProjectDisclosureProblem> projectDisclosureProblems;
    /**
     *  现场特殊情况暂无法解决事项
     */
    private List<ProjectInsolubleMatter> projectInsolubleMatter;
    /**
     *  擅自施工与图纸不符部分
     */
    private List<ProjectUnauthorizedConstruction> projectUnauthorizedConstructions;

    /**
     *  项目特殊情况说明表
     */
    private List<ProjectSpecialCaseDescription> projectSpecialCaseDescription;

//    审图额外走【审图处理】的按钮，不走【保存、提交】
//    /**
//     *  项目审图配置表（关于深化、设计审图列表的数据存储）
//     */
//    private List<ProjectGroupExpand> projectGroupExpands;


    /**
     *  项目特殊情况说明表
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "项目的特许商特殊需求表")
    private List<ProjectSpecialNeedsFranchisors> projectSpecialNeedsFranchisors;


    /**
     *  样板间确定
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "样板间确定")
    private List<ProjectRoom> projectRoomSure;

    /**
     *  物资管理材料表
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "物资管理材料表")
    private List<ProjectMaterialManagement> projectMaterialManagement;

    /**
     *  证照管理表
     */
    private List<ProjectCateInfo> projectCateInfoList;

    /**竣工常规项目自检表*/
    private List<QualityControl> routineSelfInspection;

    /**竣工系统自检表*/
    private List<ProjectSystemSelfInspection> projectSystemSelfInspection;

    /**弱电设施设备信息表*/
    private List<ProjectWeakCurrent> projectWeakCurrent;

    /*轮次标识*/
    private String roundMarking;
    /*轮次标识*/
    private String roundMarkingStr;

    /*阶段名称*/
    private String phaseName;

    /*阶段名称*/
    private String createByNickName;

    private List<SupplierPm> supplierPmList;
}
