/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.SupplierCase;
import com.bassims.modules.atour.domain.SupplierInfo;
import com.bassims.modules.atour.domain.vo.SupplierManagerParam;
import com.bassims.modules.atour.domain.vo.SupplierManagerVo;
import com.bassims.modules.atour.service.dto.SupplierInfoDto;
import com.bassims.modules.atour.service.dto.SupplierInfoQueryCriteria;
import com.bassims.modules.atourWithout.domain.AtosSubjectRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2022-09-15
**/
public interface SupplierInfoService extends BaseService<SupplierInfo> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(SupplierInfoQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<SupplierInfoDto>
    */
    List<SupplierInfoDto> queryAll(SupplierInfoQueryCriteria criteria);

    /**
     * 查询所有数据不分页（供接口调用）
     * @param criteria 条件参数
     * @return List<SupplierInfoDto>
     */
    List<SupplierInfoDto> queryAllNeedData(SupplierInfoQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return SupplierInfoDto
     */
    SupplierInfoDto findById(Long id);

    /**
     * 创建
     * @param resources /
     * @return SupplierInfoDto
     */
    SupplierInfoDto saveSupplierInfo(SupplierInfo resources);

    /**
    * 创建
    * @param resources /
    * @return SupplierInfoDto
    */
    SupplierInfoDto create(SupplierInfo resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(SupplierInfo resources);
    /**
     * 编辑
     * @param resources /
     */
    void updateStatus(SupplierInfo resources);

    /**
     * 删除
     * @param resources /
     */
    void deleteStatus(SupplierInfo resources);


    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<SupplierInfoDto> all, HttpServletResponse response) throws IOException;

    void downloadNew(List<SupplierInfoDto> all, HttpServletResponse response) throws IOException;

    /**
     * 根据厂商名称查询信息
     * @param supplierName 条件参数
     * @return List<SupplierInfoDto>
     */
    List<SupplierInfoDto> supplierByName(String supplierName);

    /**
     * 根据厂商名称查询信息
     * @param supplierName 条件参数
     * @return List<SupplierInfoDto>
     */
    SupplierInfoDto supplierInfoByName(String supplierName);

    /**
     * 查询厂商管理员信息
     * @return List<SupplierManagerVo>
     */
    List<SupplierManagerVo> supplierManagerRelate();

    /**
     * 关联查询厂商信息
     * @return List<SupplierInfoDto>
     */
    List<SupplierInfoDto> getSupplierInfo();

    /**
     * 保存厂商关联信息
     * @return List<SupplierInfoDto>
     */
    void relateSave(SupplierManagerParam supplierManagerParam);

    /**
     * 根据厂商名称查询物料厂商信息
     * @param supplierName 条件参数
     * @return List<SupplierInfoDto>
     */
    List<SupplierInfoDto> supplierFirstByName(String supplierName);


    /**
     * 查询所有数据不分页 供应商下拉
     * @param criteria 条件参数
     * @return List<SupplierInfoDto>
     */
    List<SupplierInfoDto> getSupplierList(SupplierInfoQueryCriteria criteria);

    void saveOrUpdateFromAtos(AtosSubjectRequest atosSubjectRequest);

    void deleteFromAtos(AtosSubjectRequest atosSubjectRequest);

    Boolean supplierImport(MultipartFile file) throws IOException;

    void downloadTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException ;

    Long getByName(String name);

    List<SupplierCase> caseList(Long supplierId);
}