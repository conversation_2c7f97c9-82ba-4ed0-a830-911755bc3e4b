/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.modules.atour.domain.ApproveTemplate;
import com.bassims.modules.atour.service.ApproveTemplateDetailService;
import com.bassims.modules.atour.service.ApproveTemplateService;
import com.bassims.modules.atour.service.ProjectTemplateNoticeRelationService;
import com.bassims.modules.atour.service.dto.ApproveTemplateDetailDto;
import com.bassims.modules.atour.service.dto.ApproveTemplateQueryCriteria;
import com.bassims.modules.atour.service.dto.ProjectTemplateNoticeRelationDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-03-30
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "审批模版管理")
@RequestMapping("/api/approveTemplate")
public class ApproveTemplateController {

    private static final Logger logger = LoggerFactory.getLogger(ApproveTemplateController.class);

    private final ApproveTemplateService approveTemplateService;

    private final ApproveTemplateDetailService approveTemplateDetailService;

    private final ProjectTemplateNoticeRelationService projectTemplateNoticeRelationService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @MyDataPermission(title = "系统管理")
    public void download(HttpServletResponse response, ApproveTemplateQueryCriteria criteria) throws IOException {
        approveTemplateService.download(approveTemplateService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity <List<ApproveTemplateDto>>}
    */
    @GetMapping("/list")
    @Log("查询审批模版")
    @ApiOperation("查询审批模版")
    @MyDataPermission(title = "系统管理")
    public ResponseEntity<Object> query(ApproveTemplateQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(approveTemplateService.queryAll(criteria,pageable), HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity <ApproveTemplateDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询审批模版")
    @ApiOperation("查询审批模版")
    @MyDataPermission(title = "系统管理")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(approveTemplateService.findById(id), HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增审批模版")
    @ApiOperation("新增审批模版")
    @MyDataPermission(title = "系统管理")
    public ResponseEntity<Object> create(@Validated @RequestBody ApproveTemplate resources){
        return new ResponseEntity<>(approveTemplateService.create(resources), HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改审批模版")
    @ApiOperation("修改审批模版")
    @MyDataPermission(title = "系统管理")
    public ResponseEntity<Object> update(@Validated @RequestBody ApproveTemplate resources){
        approveTemplateService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除审批模版")
    @ApiOperation("删除审批模版")
    @MyDataPermission(title = "系统管理")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        approveTemplateService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(value = "/appTemplateAll")
    @Log("查询所有审批模板")
    @ApiOperation("查询所有审批模板")
    @MyDataPermission(title = "系统管理")
    public ResponseEntity<Object> getTemplateAll(){
        return new ResponseEntity<>(approveTemplateService.getApproveTemplate(), HttpStatus.OK);
    }



    @GetMapping(value = "/appTemplateTitle")
    @Log("查询审批模板抬头数据")
    @ApiOperation("查询审批模板抬头数据")
    @MyDataPermission(title = "审批配置")
    public ResponseEntity<Object> getApproveTemplateTitle(Long templateId){
        return new ResponseEntity<>(approveTemplateService.getApproveTemplateTitle(templateId), HttpStatus.OK);
    }

    @GetMapping(value = "/appTemplateDetail")
    @Log("查询所有审批模板子数据")
    @ApiOperation("查询所有审批模板子数据")
    @MyDataPermission(title = "系统管理")
    public ResponseEntity<Object> getAppTemplateDetail(Long templateId){
        return new ResponseEntity<>(approveTemplateDetailService.getApproveTemplateDetail(templateId), HttpStatus.OK);
    }


    @PostMapping(value = "/updateApproveDetail")
    @Log("更新子审批模板")
    @ApiOperation("更新子审批模板")
    public ResponseEntity<Object> updateApproveDetail(@Validated @RequestBody ApproveTemplateDetailDto approveTemplateDetailDto){
        return new ResponseEntity<>(approveTemplateDetailService.saveApproveTemplateDetail(approveTemplateDetailDto), HttpStatus.OK);
    }


    @GetMapping("/appNotiRelation")
    @Log("获取审批关联消息任务")
    @ApiOperation("获取审批关联消息任务")
    public ResponseEntity<Object> appNotiRelation(Long appTemplateDetailId) {
        return new ResponseEntity<>(projectTemplateNoticeRelationService.getAppNoticeTemplate(appTemplateDetailId) , HttpStatus.OK);
    }

    @PostMapping("/saveAppNoti")
    @Log("审批消息配置保存")
    @ApiOperation("审批消息配置保存")
    public ResponseEntity<Object> saveAppNoti(@Validated @RequestBody List<ProjectTemplateNoticeRelationDto> resources){
        return new ResponseEntity<>(projectTemplateNoticeRelationService.saveAppNotiRelation(resources), HttpStatus.CREATED);
    }

    @PostMapping("/deleteAppNoti")
    @Log("审批消息配置删除")
    @ApiOperation("审批消息配置删除")
    public ResponseEntity<Object> deleteAppNoti(@Validated @RequestBody ProjectTemplateNoticeRelationDto resource){
        return new ResponseEntity<>(projectTemplateNoticeRelationService.deleteAppNotiRelation(resource), HttpStatus.OK);
    }


}