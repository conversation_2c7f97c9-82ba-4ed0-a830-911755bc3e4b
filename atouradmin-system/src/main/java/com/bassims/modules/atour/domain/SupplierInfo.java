/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.*;
import com.bassims.modules.atour.service.dto.ProjectCompletionReceiptListDto;
import com.bassims.modules.atour.service.dto.SupplierPmDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2022-09-15
 **/
@Data
@TableName(value = "t_supplier_info")
public class SupplierInfo implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "厂商ID")
    private Long id;

    @TableField(value = "supplier_num")
    @ApiModelProperty(value = "厂商NO")
    private String supplierNum;

    @TableField(value = "supplier_version_id")
    @ApiModelProperty(value = "supplierVersionId")
    private Long supplierVersionId;

    @TableField(value = "supplier_code")
    @ApiModelProperty(value = "厂商编号")
    private String supplierCode;

    @TableField(value = "sup_name_cn")
    @ApiModelProperty(value = "厂商中文名称")
    private String supNameCn;

    @TableField(value = "sup_name_en")
    @ApiModelProperty(value = "厂商英文名称")
    private String supNameEn;

    @TableField(value = "sup_short_name")
    @ApiModelProperty(value = "厂商简称(用來存放安心筹开单位)")
    private String supShortName;

    @TableField(value = "sup_province")
    @ApiModelProperty(value = "省份")
    private Long supProvince;

    @TableField(value = "sup_city")
    @ApiModelProperty(value = "城市")
    private String supCity;

    @TableField(value = "sup_post_code")
    @ApiModelProperty(value = "supPostCode")
    private String supPostCode;

    @TableField(value = "comp_reg_date")
    @ApiModelProperty(value = "厂商注册日期")
    private Timestamp compRegDate;

    @TableField(value = "comp_reg_addr")
    @ApiModelProperty(value = "厂商注册地址")
    private String compRegAddr;

    @TableField(value = "effect_status")
    @ApiModelProperty(value = "effectStatus")
    private String effectStatus;

    @TableField(value = "version_status")
    @ApiModelProperty(value = "versionStatus")
    private String versionStatus;

    @TableField(value = "sup_status")
    @ApiModelProperty(value = "厂商状态")
    private String supStatus;

    @TableField(value = "is_disposable")
    @ApiModelProperty(value = "是否一次性厂商")
    private String isDisposable;

    @TableField(value = "contact")
    @ApiModelProperty(value = "联系人(公司法人)")
    private String contact;

    @TableField(value = "phone")
    @ApiModelProperty(value = "联系电话(公司法人)")
    private String phone;

    @TableField(value = "email")
    @ApiModelProperty(value = "邮箱(公司法人)")
    private String email;

    @TableField(value = "fax")
    @ApiModelProperty(value = "传真(公司法人)")
    private String fax;

    @TableField(value = "bizlic_exp_date")
    @ApiModelProperty(value = "bizlicExpDate")
    private Timestamp bizlicExpDate;

    @TableField(value = "spp_exp_date")
    @ApiModelProperty(value = "sppExpDate")
    private Timestamp sppExpDate;

    @TableField(value = "tax_reg_attr")
    @ApiModelProperty(value = "税务登记属性")
    private String taxRegAttr;

    @TableField(value = "tax_sale_rate")
    @ApiModelProperty(value = "taxSaleRate")
    private String taxSaleRate;

    @TableField(value = "tax_service_rate")
    @ApiModelProperty(value = "税率(服务)")
    private String taxServiceRate;

    @TableField(value = "trc_exp_date")
    @ApiModelProperty(value = "trcExpDate")
    private Timestamp trcExpDate;

    @TableField(value = "logistics_terms")
    @ApiModelProperty(value = "logisticsTerms")
    private String logisticsTerms;

    @TableField(value = "parent_id")
    @ApiModelProperty(value = "parentId")
    private Long parentId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @TableField(value = "is_active")
    @ApiModelProperty(value = "是否激活")
    private Integer isActive;

    @TableField(value = "CREATE_USER", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private Long createUser;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "UPDATE_USER", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新人")
    private Long updateUser;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "IS_DELETE")
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    @TableField(value = "business_license")
    @ApiModelProperty(value = "营业执照")
    private String businessLicense;
    @TableField(value = "business_exp_date")
    @ApiModelProperty(value = "营业执照开始时间-结束时间")
    private String businessExpDate;

    @TableField(value = "service_start_date")
    @ApiModelProperty(value = "服务开始时间")
    private Timestamp serviceStartDate;

    @TableField(value = "service_scope")
    @ApiModelProperty(value = "服务范围")
    private String serviceScope;

    //    @TableField(value = "service_area")
    @ApiModelProperty(value = "服务区域")
    @TableField(exist = false)
//    @JoinTable(name = "s_sys_users_city",
//            joinColumns = {@JoinColumn(name = "user_id",referencedColumnName = "user_id")},
//            inverseJoinColumns = {@JoinColumn(name = "area_code",referencedColumnName = "area_code")})
    private Set<Long> serviceAreas;

    @TableField(value = "supplier_starbucks_rating")
    @ApiModelProperty(value = "评级")
    private String supplierStarbucksRating;

    @TableField(value = "avg_quality_score")
    @ApiModelProperty(value = "平均质量评分")
    private BigDecimal avgQualityScore;

    @TableField(value = "finish_amount")
    @ApiModelProperty(value = "累计已完成项目量数")
    private Integer finishAmount;

    @TableField(value = "doing_amount")
    @ApiModelProperty(value = "在建项目数")
    private Integer doingAmount;

    @TableField(value = "bank_account_name")
    @ApiModelProperty(value = "开户名字(中文)")
    private String bankAccountName;

    @TableField(value = "bank_name_cn")
    @ApiModelProperty(value = "银行名字(中文，银行全称)")
    private String bankNameCn;

    @TableField(value = "bank_name_en")
    @ApiModelProperty(value = "银行名字(英文)")
    private String bankNameEn;

    @TableField(value = "bank_branch_cn")
    @ApiModelProperty(value = "银行支行名字(中文)")
    private String bankBranchCn;

    @TableField(value = "bank_branch_city_cn")
    @ApiModelProperty(value = "银行支行城市(中文)")
    private String bankBranchCityCn;

    @TableField(value = "bank_account_number")
    @ApiModelProperty(value = "账户号")
    private String bankAccountNumber;

    @TableField(value = "tax_number")
    @ApiModelProperty(value = "税号")
    private String taxNumber;

    @TableField(value = "market_type")
    @ApiModelProperty(value = "大盘类型(用来存放公司简介)")
    private String marketType;

    @TableField(value = "referrer")
    @ApiModelProperty(value = "推荐人(存放承接品牌，多个,隔开)")
    private String referrer;

    @TableField(value = "contact_way")
    @ApiModelProperty(value = "联系方式")
    private String contactWay;

    @TableField(value = "pay_retention_money")
    @ApiModelProperty(value = "已缴纳质保金(元)")
    private BigDecimal payRetentionMoney;

    @TableField(value = "refund_warranty_deposit")
    @ApiModelProperty(value = "已退还质保金(元)")
    private BigDecimal refundWarrantyDeposit;

    @TableField(value = "is_first_material_supplier")
    @ApiModelProperty(value = "是否甲供材单位")
    private Boolean isFirstMaterialSupplier;

    @TableField(value = "is_test")
    @ApiModelProperty(value = "是否需要测试")
    private Boolean isTest;

    @TableField(value = "legal_entity")
    @ApiModelProperty(value = "厂商所属法人公司")
    private String legalEntity;

    @TableField(value = "project_convenor")
    @ApiModelProperty(value = "联系人(项目召集人)")
    private String projectConvenor;

    @TableField(value = "project_convenor_phone")
    @ApiModelProperty(value = "联系电话(项目召集人)")
    private String projectConvenorPhone;

    @TableField(value = "project_convenor_email")
    @ApiModelProperty(value = "邮箱(项目召集人)")
    private String projectConvenorEmail;

    @TableField(value = "decoration_qualification")
    @ApiModelProperty(value = "安全施工许可")
    private String decorationQualification;

    @TableField(value = "decoration_exp_date")
    @ApiModelProperty(value = "安全施工许可开始-结束时间")
    private String decorationExpDate;

    @TableField(value = "electromechanical_qualification")
    @ApiModelProperty(value = "资质文件")
    private String electromechanicalQualification;

    @TableField(value = "electromechanical_qualification_type")
    @ApiModelProperty(value = "资质文件类型")
    private String electromechanicalQualificationType;
    @TableField(value = "electromechanical_exp_date")
    @ApiModelProperty(value = "资质文件开始-结束时间")
    private String electromechanicalExpDate;

//    @TableField(value = "spl_exp_date")
//    @ApiModelProperty(value = "安全生产许可证有效期到期日")
//    private Timestamp splExpDate;

    @TableField(value = "tral_exp_date")
    @ApiModelProperty(value = "税务登记证有效期到期日")
    private Timestamp tralExpDate;

    @TableField(value = "service_content")
    @ApiModelProperty(value = "厂商工程范围")
    private String serviceContent;

    @TableField(value = "project_manager_amount")
    @ApiModelProperty(value = "批准项目经理数")
    private Integer projectManagerAmount;

    @TableField(value = "is_used")
    @ApiModelProperty(value = "是否使用")
    private Integer isUsed;

    @TableField(value = "project_convenor_fax")
    @ApiModelProperty(value = "传真(项目召集人)")
    private String projectConvenorFax;

    @TableField(value = "other_licence_exp_date")
    @ApiModelProperty(value = "其他施工资质证照有效期到期日")
    private Timestamp otherLicenceExpDate;

    @TableField(value = "change_desc")
    @ApiModelProperty(value = "变更说明")
    private String changeDesc;

    @TableField(value = "postal_code")
    @ApiModelProperty(value = "厂商邮编")
    private String postalCode;

    @TableField(value = "pay_term")
    @ApiModelProperty(value = "付款条件")
    private String payTerm;

    @TableField(value = "supplier_address")
    @ApiModelProperty(value = "厂商地址")
    private String supplierAddress;

    @TableField(value = "user_id")
    @ApiModelProperty(value = "厂商管理员id")
    private String userId;

    @TableField(value = "contract_no")
    @ApiModelProperty(value = "合同号")
    private String contractNo;

    @TableField(value = "file_id")
    @ApiModelProperty(value = "附件id")
    private String fileId;


    @TableField(value = "platform_provider_not")
    @ApiModelProperty(value = "是否平台供应商（码值platform_provider_not）")
    private String platformProviderNot;

    @TableField(value = "supplier_type")
    @ApiModelProperty(value = "供应商类型（码值）")
    private String supplierType;

    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @TableField(exist = false)
    private String roleName;

    @TableField(value = "tk_id")
    @ApiModelProperty(value = "atos主键id")
    private String tkId;

    @TableField(value = "status")
    @ApiModelProperty(value = "审批状态")
    private String status;

    @TableField(exist = false)
    @ApiModelProperty(value = "案例介绍JSON")
    private String caseStudy;

    @TableField(exist = false)
    @ApiModelProperty(value = "员工")
    List<SupplierPmDto> pmList;

    public void copy(SupplierInfo source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }
}