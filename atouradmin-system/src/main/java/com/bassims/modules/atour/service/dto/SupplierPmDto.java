/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2022-09-15
 **/
public class SupplierPmDto implements Serializable {

    /** 主键 */
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /**
     * 厂商Id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long supplierId;

    /**
     * 项目经理名称 1
     */
    private String pmName;

    /**
     * 项目经理评级
     */
    private String pmGrade;

    /**
     * 参与服务日期
     */
    private Timestamp joinDate;

    /**
     * 离开日期
     */
    private Timestamp leaveDate;

    /**
     * 联系电话 1
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 更新人
     */
    private Long updateUser;

    /**
     * 更新时间
     */
    private Timestamp updateTime;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 项目经理工程范围
     */
    private String projectScope;

    /**
     * 可承担项目数
     */
    private String planAmount;

    /**
     * 已派案数
     */
    private String assignAmount;

    /**
     * 尚可承担项目数
     */
    private String availableAmount;

    /**
     * 累计已完成项目量数
     */
    private String finishAmount;

    /**
     * 在建项目数
     */
    private Integer doingAmount;

    /**
     * 在建项目拟完工日
     */
    private Timestamp completionDate;

    /**
     * 本财年质量评分
     */
    private String currentQualityScore;

    /**
     * 平均质量评分
     */
    private String avgQualityScore;

    /**
     * 考评得分
     */
    private String evaluationScore;

    /**
     * 考评开始时间
     */
    private Date evaluationStartTime;

    /**
     * 考评结束时间
     */
    private Date evaluationEndTime;

    /**
     * 是否使用
     */
    private Boolean isUsed;

    /**
     * 是否激活
     */
    private Boolean isActive;

    /**
     * 财年
     */
    private String fisyear;

    /**
     * 本财年总派案数
     */
    private Integer totalAssignAmount;

    /**
     * 本财年总在建项目数
     */
    private Integer totalDoingAmount;

    /**
     * 本财年总已完成项目数
     */
    private Integer totalFinishAmount;

    private List<Object> evaluation;

    /**
     * 大区id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer parganaId;

    private String pmStatus;

    /**
     * 供应商人员角色(码值)
     */
    private String supplierPersonnelRole;
    /**
     * 供应商人员角色(码值)数组格式，方便前端转换
     */
    private String[] supplierPersonnelRoles;


    /**
     * 用户名
     */
    private String pmUserName;

    /**
     * 性别 1
     */
    private String pmGender;

    /**
     * 服务区域
     */
    private Set<Long> pmServiceAreas;


    /**供应商名称*/
    private String supNameCn;

    /**是否推送到atos系统 0推送 1不推送*/
    private String isPullUserInfoForAtos;


//    审批状态
    private String status;

    private String availableAmountStart;

    private String availableAmountEnd;

    public void setId(Long id) {
        this.id = id;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public void setPmName(String pmName) {
        this.pmName = pmName;
    }

    public void setPmGrade(String pmGrade) {
        this.pmGrade = pmGrade;
    }

    public void setJoinDate(Timestamp joinDate) {
        this.joinDate = joinDate;
    }

    public void setLeaveDate(Timestamp leaveDate) {
        this.leaveDate = leaveDate;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public void setProjectScope(String projectScope) {
        this.projectScope = projectScope;
    }

    public void setPlanAmount(String planAmount) {
        this.planAmount = planAmount;
    }

    public void setAssignAmount(String assignAmount) {
        this.assignAmount = assignAmount;
    }

    public void setAvailableAmount(String availableAmount) {
        this.availableAmount = availableAmount;
    }

    public void setFinishAmount(String finishAmount) {
        this.finishAmount = finishAmount;
    }

    public void setDoingAmount(Integer doingAmount) {
        this.doingAmount = doingAmount;
    }

    public void setCompletionDate(Timestamp completionDate) {
        this.completionDate = completionDate;
    }

    public void setCurrentQualityScore(String currentQualityScore) {
        this.currentQualityScore = currentQualityScore;
    }

    public void setAvgQualityScore(String avgQualityScore) {
        this.avgQualityScore = avgQualityScore;
    }

    public void setEvaluationScore(String evaluationScore) {
        this.evaluationScore = evaluationScore;
    }

    public void setEvaluationStartTime(Date evaluationStartTime) {
        this.evaluationStartTime = evaluationStartTime;
    }

    public void setEvaluationEndTime(Date evaluationEndTime) {
        this.evaluationEndTime = evaluationEndTime;
    }

    public void setIsUsed(Boolean used) {
        isUsed = used;
    }

    public void setIsActive(Boolean active) {
        isActive = active;
    }

    public void setFisyear(String fisyear) {
        this.fisyear = fisyear;
    }

    public void setTotalAssignAmount(Integer totalAssignAmount) {
        this.totalAssignAmount = totalAssignAmount;
    }

    public void setTotalDoingAmount(Integer totalDoingAmount) {
        this.totalDoingAmount = totalDoingAmount;
    }

    public void setTotalFinishAmount(Integer totalFinishAmount) {
        this.totalFinishAmount = totalFinishAmount;
    }

    public void setEvaluation(List<Object> evaluation) {
        this.evaluation = evaluation;
    }

    public void setParganaId(Integer parganaId) {
        this.parganaId = parganaId;
    }

    public void setPmStatus(String pmStatus) {
        this.pmStatus = pmStatus;
    }

    public void setSupplierPersonnelRole(String supplierPersonnelRole) {
        if (this.supplierPersonnelRole == null) {
            this.supplierPersonnelRole = supplierPersonnelRole;
        }
    }

    public void setSupplierPersonnelRoles(String[] supplierPersonnelRoles) {
        if (supplierPersonnelRoles != null) {
            this.supplierPersonnelRole = Arrays.toString(supplierPersonnelRoles).replaceAll(" ","").replace("[", "").replace("]", "");
        }
        this.supplierPersonnelRoles = supplierPersonnelRoles;
    }

    public void setPmUserName(String pmUserName) {
        this.pmUserName = pmUserName;
    }

    public void setPmGender(String pmGender) {
        this.pmGender = pmGender;
    }

    public void setPmServiceAreas(Set<Long> pmServiceAreas) {
        this.pmServiceAreas = pmServiceAreas;
    }

    public void setSupNameCn(String supNameCn) {        this.supNameCn = supNameCn;    }

    public Long getId() {
        return id;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public String getPmName() {
        return pmName;
    }

    public String getPmGrade() {
        return pmGrade;
    }

    public Timestamp getJoinDate() {
        return joinDate;
    }

    public Timestamp getLeaveDate() {
        return leaveDate;
    }

    public String getPhone() {
        return phone;
    }

    public String getEmail() {
        return email;
    }

    public String getRemark() {
        return remark;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public String getProjectScope() {
        return projectScope;
    }

    public String getPlanAmount() {
        return planAmount;
    }

    public String getAssignAmount() {
        return assignAmount;
    }

    public String getAvailableAmount() {
        return availableAmount;
    }

    public String getFinishAmount() {
        return finishAmount;
    }

    public Integer getDoingAmount() {
        return doingAmount;
    }

    public Timestamp getCompletionDate() {
        return completionDate;
    }

    public String getCurrentQualityScore() {
        return currentQualityScore;
    }

    public String getAvgQualityScore() {
        return avgQualityScore;
    }

    public String getEvaluationScore() {
        return evaluationScore;
    }

    public Date getEvaluationStartTime() {
        return evaluationStartTime;
    }

    public Date getEvaluationEndTime() {
        return evaluationEndTime;
    }

    public Boolean getIsUsed() {
        return isUsed;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public String getFisyear() {
        return fisyear;
    }

    public Integer getTotalAssignAmount() {
        return totalAssignAmount;
    }

    public Integer getTotalDoingAmount() {
        return totalDoingAmount;
    }

    public Integer getTotalFinishAmount() {
        return totalFinishAmount;
    }

    public List<Object> getEvaluation() {
        return evaluation;
    }

    public Integer getParganaId() {
        return parganaId;
    }

    public String getPmStatus() {
        return pmStatus;
    }

    public String getSupplierPersonnelRole() {
        return supplierPersonnelRole;
    }

    public String[] getSupplierPersonnelRoles() {
        if (this.supplierPersonnelRole != null) {
            return this.supplierPersonnelRole.split(",");
        }
        return supplierPersonnelRoles;
    }

    public String getPmUserName() {
        return pmUserName;
    }

    public String getPmGender() {
        return pmGender;
    }

    public Set<Long> getPmServiceAreas() {
        return pmServiceAreas;
    }

    public String getSupNameCn() {        return supNameCn;    }


    public void setIsPullUserInfoForAtos(String isPullUserInfoForAtos) {
        this.isPullUserInfoForAtos = isPullUserInfoForAtos;
    }

    public String getIsPullUserInfoForAtos() {
        return isPullUserInfoForAtos;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAvailableAmountStart() {
        return availableAmountStart;
    }

    public void setAvailableAmountStart(String availableAmountStart) {
        this.availableAmountStart = availableAmountStart;
    }

    public String getAvailableAmountEnd() {
        return availableAmountEnd;
    }

    public void setAvailableAmountEnd(String availableAmountEnd) {
        this.availableAmountEnd = availableAmountEnd;
    }
}