/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-03-16
**/
@Data
@TableName(value="project_license_report")
public class ProjectLicenseReport implements Serializable {

    @ApiModelProperty(value = "门店编码")
    private String storeNo;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "现场工程")
    private String engineer;

    @ApiModelProperty(value = "图纸审核意见书")
    private String drawIdea;

    @ApiModelProperty(value = "施工许可证")
    private String buildPermit;

    @ApiModelProperty(value = "消防竣工验收合格证")
    private String firePass;

    @ApiModelProperty(value = "开检证")
    private String openCheck;

    public void copy(ProjectLicenseReport source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}