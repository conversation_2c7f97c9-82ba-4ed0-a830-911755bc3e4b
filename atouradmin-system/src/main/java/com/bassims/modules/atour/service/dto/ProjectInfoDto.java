/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.dto;

import cn.hutool.core.lang.tree.Tree;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.bassims.modules.atour.domain.ProjectSpotCheck;
import com.bassims.modules.atour.domain.ProjectVisaFiling;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.alibaba.fastjson.annotation.JSONField;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2022-03-24
 **/
@Data
public class ProjectInfoDto implements Serializable {

    /** 项目id */
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    /** hlm系统过来的项目ID*/
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer projectHlmId;

    /** 品牌id */
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long brandId;

    /** 模板id */
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long templateId;

    /** 模板code */
    private String templateCode;

    /**
     * 门店id
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long storeId;

    /**
     * 门店版本
     */
    private String storeVersion;

    /**
     * 项目编码
     */
    private String projectNo;

    /**
     * 项目版本号
     */
    private String projectVersion;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 经营性质
     */
    private String businessNature;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店编码
     */
    private String storeNo;

    /**
     * 门店类型
     */
    private String storeType;

    /**
     * 设计形象
     */
    private String designImage;

    /**
     * 大区value
     */
    private String region;

    /**
     * 城市公司value
     */
    private String cityCompany;

    /** 省份id */
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long province;

    /**
     * 省份名称
     */
    private String provinceName;

    /** 城市id */
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long city;

    /**
     * 城市名称
     */
    private String cityName;

    /** 区县id */
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long county;

    /**
     * 区县名称
     */
    private String countyName;

    /**
     * 详细地址（不包括省市县）
     */
    private String projectAddress;

    /** 商圈id */
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long tradeId;

    /**
     * 立项时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private Date establishTime;

    /**
     * 区域经理
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long regionNetManager;

    /**
     * 工程经理
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long constructionManager;

    /**
     * 总部经理
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long engineerDirector;

    /**
     * 运营战区负责人
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long operationsTheaterLeader;
    private String operationsTheaterLeaderName;
    private String operationsTheaterName;

    /**
     * 开业经理
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long openingManager;
    private String openingManagerName;

    /**
     * 运营经理
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long operationsManager;
    private String operationsManagerName;

    /**
     * 特许经理
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long franchiseManager;
    private String franchiseManagerName;

    /**
     * 开发战区负责人
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long developmentTheaterLeader;
    private String developmentTheaterLeaderName;
    private String developmentTheaterName;

    /**
     * 开发分区负责人
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long developmentZoneLeader;
    private String developmentZoneLeaderName;

    /**
     * 开发经理 12/6 改成多个逗号拼接
     */
    private String developmentManager;

    /**
     * 营建区域负责人
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long constructionAreaLeader;
    private String constructionAreaLeaderName;
    private String purchase;

    /**
     * 项目经理
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectManager;

    /**
     * 设计师
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long designer;
    private String designerName;

    /**
     * 机电工程师
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long mechanicalAndElectricalEngineer;
    private String mechanicalAndElectricalEngineerName;
    private String softDesigner;

    /**
     * 弱电工程师
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long weakCurrentEngineer;
    private String weakCurrentEngineerName;

    /**
     * 设计共管人员
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long designCoManagementPersonnel;

    /**
     * 弱电验收人员
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long weakCurrentAcceptancePersonnel;

    /**
     * 机电验收人员
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long mechanicalAndElectricalAcceptancePersonnel;

    /**
     * 飞行质检人员
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long flightQualityInspectionPersonnel;

    /**
     * 竣工验收人员
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long completionAcceptancePersonnel;

    /**
     * 交付中心-共享支持
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long deliveryCenterSharedSupport;

    /**
     * 业主
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long owner;

    /**
     * 设计单位
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long designUnit;

    private String designUnitName;
    //公区装饰设计师
    private String areaDesigner;
    private String areaDesignUnit;

    /**
     * 采购营销
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long procurementMarketing;

    /**
     * 交付中心-工程负责人
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long deliveryCenterEngineeringLeader;

    /**
     * 交付中心-技术负责人
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long deliveryCenterTechnicalLeader;

    /**
     * 交付中心-部门负责人
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long deliveryCenterDepartmentHead;

    /**
     * 装修设计师
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long decorationDesigner;

    /**
     * 机电设计师
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long mechanicalAndElectricalDesigner;
    private String mechanicalAndElectricalDesignerName;
    private String mechanicalAndElectricalDesignerUnit;
    private String electricalDesignerName;
    private String electricalDesignerUnit;

    /**
     * 软装设计
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long softDecorationDesign;

    /**
     * 总部网发
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long generalNetwork;

    /**
     * 城市公司财务
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long regionaFinanceManager;

    /**
     * 项目计划开始时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private Date projectPlanStart;

    /**
     * 项目计划结束时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private Date projectPlanEnd;

    /**
     * 项目实际结束时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private Date projectActualEnd;
    @JSONField(format = "yyyy-MM-dd")
    private Date projectActualStart;

    /**
     * 计划进场日期
     */
    @JSONField(format = "yyyy-MM-dd")
    private Date planApproachDate;

    /**
     * 计划竣工日期
     */
    @JSONField(format = "yyyy-MM-dd")
    private Date planOverDate;

    /**
     * 项目创建日期
     */
    @JSONField(format = "yyyy-MM-dd")
    private Date projectCreateDate;

    /**
     * 实际开业日期
     */
    private String actualOpenDate;

    /**
     * 是否开业
     */
    private Boolean isOpen;

    /**
     * 是否立项
     */
    private Boolean isCreate;

    /**
     * 总计面积
     */
    private BigDecimal totalArea;

    /**
     * 使用面积
     */
    private BigDecimal usedArea;

    /**
     * 装修面积
     */
    private BigDecimal decorateArea;

    /**
     * 项目类型
     */
    private String projectType;

    /**
     * 项目状态
     */
    private String projectStatus;

    /**
     * 当前任务阶段
     */
    private String taskPhase;

    /**
     * 是否逾期
     */
    private Boolean isOverdue;
    private String overdue;

    /**
     * 结算任务阶段
     */
    private String accountPhase;

    /**
     * 结算是否逾期
     */
    private Boolean accountOverdue;

    /**
     * 项目备注
     */
    private String remark;

    /**
     * 是否生效
     */
    private Boolean isActive;

    /**
     * 是否删除
     */
    private Boolean isDelete;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 修改时间
     */
    private Timestamp updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否可用
     */
    private Boolean isEnabled;

    private String enabled;
    /**
     * mobile端的进度条
     */
    List<Tree<String>> nodeList;
    /**
     * 项目创建时
     */
    List<Long> uploadList;

    private String createDate;

    /**
     * 总部网发
     */
    private String generalNetworkName;

    /**
     * 城市公司财务
     */
    private String regionaFinanceManagerName;

    /**
     * 城市公司网发经理
     */
    private String regionNetManagerName;

    /**
     * SE营建经理
     */
    private String constructionManagerName;

    //施工单位
    private String construction;
    //客房装饰设计师
    private String  decorationDesignerName;
    /**
     * 项目经理
     */
    private String projectManagerName;
    /**
     * 导入number
     */
    private int no;

    /**
     * 是否申请指令单
     */
    private Boolean isSheet;

    /**
     * 指令单id
     */
    private Long sheetId;

    /**
     * 装修等级
     */
    private String decorateGrade;

    private String nodeCode;

    private String nodeName;


    /**
     * 楼层
     */
    private String floor;

    /**
     * 设计定位
     */
    private String designPosition;


    /**
     * 城市公司名称
     */
    private String cityCompanyName;

    /**
     * 设计师名称
     */
    private String designName;
    /**
     * 改造与闭店用masterid
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long storeMasterId;

    /**
     * 决算费用
     */
    private BigDecimal finalSettlementExpenses;

    /**
     * 标识
     */
    private String flag;

    /**
     * 备注1
     */
    private String note1;

    /**
     * 备注2
     */
    private String note2;

    /**
     * 备注3
     */
    private String note3;

    /**
     * 备注4
     */
    private String note4;

    /**
     * 备注5
     */
    private String note5;

    /**
     * 中心负责人
     */
    private List<Long> centerManager;

    /**
     * 风险管控
     */
    private List<Long> riskController;

    /**
     * 设计经理
     */
    private List<Long> designManager;

    /**
     * 设计总监
     */
    private List<Long> designDirector;

    /**
     * 工程财务
     */
    private List<Long> constructionFinance;

    /**
     * 施工厂商
     */
    private List<Long> firmOwner;

    /**
     * 节点表实际名称
     */
    private String nodeTableName;

    /**
     * 分配状态
     */
    private String allocationStatus;


    /*业主项目经理/业主对接人*/
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long ownerProjectManager;

    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 品牌编码
     */
    private String brandCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 签约房量
     */
    private Integer roomsNumber;

    /**
     * 签约日期
     */
    @JSONField(format = "yyyy-MM-dd")
    private Date effectiveDate;
    /**
     * 楼层
     */
    private Integer numberFloorLevels;


    /**
     * 项目任务阶段code
     */
    private String projectTaskPhase;

    private String deepenNodeTask;


    /**
     * 是否为保密项目 1=是 0=否 默认为0
     */
    private Integer securityProject;

    /**
     * 计划开工日期
     **/
    @JSONField(format = "yyyy-MM-dd")
    private Date plannedStartDate;

    /**
     * 调整开工日期
     **/
    @JSONField(format = "yyyy-MM-dd")
    private Date adjustingThestartDate;

    /**
     * 实际开工日期
     **/
    @JSONField(format = "yyyy-MM-dd")
    private Date actualcommencementDate;

    /**
     * 计划完工日期
     **/
    @JSONField(format = "yyyy-MM-dd")
    private Date plannedCompletionDate;

    /**
     * 调整完工日期
     **/
    @JSONField(format = "yyyy-MM-dd")
    private Date adjustCompletionDate;

    /**
     * 实际完工日期
     **/
    @JSONField(format = "yyyy-MM-dd")
    private Date actualCompletionDate;

    /**
     * 计划开业日期
     **/
    @JSONField(format = "yyyy-MM-dd")
    private Date planOpenDate;

    /**
     * 调整开业日期
     **/
    @JSONField(format = "yyyy-MM-dd")
    private Date adiustingTheOpeningDate;
//
//    /** 实际开业日期 **/
//    private Date actualopenDate;


    /**
     * 质量管理<发起【工程问题整改模板】模版，并存数据>
     */
    private QualityControlDto qualityControlDto;


    /**
     * 【竣工自检任务】<发起【竣工系统自检】模版，并存数据>
     */
    private ProjectSystemSelfInspectionDto projectSystemSelfInspection;

    /**
     * 【竣工自检任务】<发起【竣工常规项目自检】模版，并存数据>
     */
    private QualityControlDto routineSelfInspection;

    /**
     * 【样板间验收】<发起【工程问题整改模板】模版，并存数据>
     */
    private ProjectTableNodeInfoDto projectTableNodeInfo;


    /**施工日志*/
    private ProjectConstructionLogDto projectConstructionLog;

    /**签证报备*/
    private ProjectVisaFiling projectVisaFiling;


    /**工程抽查*/
    private ProjectSpotCheck projectSpotCheck;

    /**焕新店*/
    private ProjectTemplateCodeDto projectRefreshShopRotationDto;

    /**
     * 筹建启动是否有提交
     */
    private boolean prepareSubmitted;


    /**项目启动时间*/
    @JSONField(format = "yyyy-MM-dd")
    private Date projectStartTime;

    @ApiModelProperty(value = "HBG-供应链事业部VP")
    private String supplyChainBusinessUnit;

    @ApiModelProperty(value = "法务经理")
    private String legalManager;

    @ApiModelProperty(value = "共享支持设计负责人")
    private String sharedSupportDesignLead;

    @ApiModelProperty(value = "住宿运营负责人")
    private String headAccommodationOperations;

    @ApiModelProperty(value = "运维负责人")
    private String headOperationMaintenance;


    @ApiModelProperty(value = "工程节点")
    private String engineeringNode;

    @ApiModelProperty(value = "设计节点")
    private String designNode;


    @ApiModelProperty(value = "工程任务")
    private String engineeringNodeTask;

    @ApiModelProperty(value = "设计任务")
    private String designNodeTask;

    /*阶段节点名称（工程、设计）*/
    private String stageNodeName;


    /* 用户在该项目中的角色 */
    private String roleName;
}