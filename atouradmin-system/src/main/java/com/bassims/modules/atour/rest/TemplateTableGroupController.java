/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.modules.atour.domain.TemplateTableGroup;
import com.bassims.modules.atour.service.TemplateTableGroupService;
import com.bassims.modules.atour.service.dto.TemplateTableGroupDto;
import com.bassims.modules.atour.service.dto.TemplateTableGroupQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2023-10-15
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "系统列表分组模版管理")
@RequestMapping("/api/templateTableGroup")
public class TemplateTableGroupController {

    private static final Logger logger = LoggerFactory.getLogger(TemplateTableGroupController.class);

    private final TemplateTableGroupService templateTableGroupService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @MyDataPermission(title = "物资管理,配置管理")
    public void download(HttpServletResponse response, TemplateTableGroupQueryCriteria criteria) throws IOException {
        templateTableGroupService.download(templateTableGroupService.queryAll(criteria), response);
    }

    /**
     * @real_return {@link ResponseEntity<List<TemplateTableGroupDto>>}
     */
    @GetMapping("/list")
    @Log("查询系统列表分组模版")
    @ApiOperation("查询系统列表分组模版")
    @MyDataPermission(title = "物资管理,配置管理")
    public ResponseEntity<Object> query(TemplateTableGroupQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(templateTableGroupService.queryAll(criteria, pageable), HttpStatus.OK);
    }


    /**
     * @real_return {@link ResponseEntity<List<TemplateTableGroupDto>>}
     */
    @GetMapping("/getNodeList")
    @Log("根据查询条件查询列表数据")
    @ApiOperation("根据查询条件查询列表数据")
    @MyDataPermission(title = "物资管理,配置管理")
    public ResponseEntity<Object> getNodeList(TemplateTableGroupQueryCriteria criteria) {
        return new ResponseEntity<>(templateTableGroupService.getNodeList(criteria), HttpStatus.OK);
    }

    @GetMapping("/getVersion")
    @Log("弱电验收申请选择验收标准版本")
    @ApiOperation("弱电验收申请选择验收标准版本")
    public ResponseEntity<Object> getVerision(TemplateTableGroupQueryCriteria criteria) {
        return new ResponseEntity<>(templateTableGroupService.getVersion(criteria), HttpStatus.OK);
    }

    @GetMapping("/getCompleteVersion")
    @Log("竣工验收申请选择验收标准版本")
    @ApiOperation("竣工验收申请选择验收标准版本")
    public ResponseEntity<Object> getCompleteVersion(TemplateTableGroupQueryCriteria criteria) {
        return new ResponseEntity<>(templateTableGroupService.getCompleteVersion(criteria), HttpStatus.OK);
    }


    /**
     * @real_return {@link ResponseEntity<TemplateTableGroupDto>}
     */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询系统列表分组模版")
    @ApiOperation("查询系统列表分组模版")
    @MyDataPermission(title = "物资管理,配置管理")
    public ResponseEntity<Object> query(@PathVariable Long id) {
        return new ResponseEntity<>(templateTableGroupService.findById(id), HttpStatus.OK);
    }

    @PostMapping("/saveOrup")
    @Log("新增系统列表分组模版")
    @ApiOperation("新增系统列表分组模版")
    public ResponseEntity<Object> create(@Validated @RequestBody TemplateTableGroup resources) {
        return new ResponseEntity<>(templateTableGroupService.saveOrup(resources), HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改系统列表分组模版")
    @ApiOperation("修改系统列表分组模版")
 //   @MyDataPermission(title = "证照模板,设计院工作内容模板,装饰勘测报告模板,设计自检表模板,设计样板间验收模板,安全文明施工模板")
    public ResponseEntity<Object> update(@Validated @RequestBody TemplateTableGroup resources) {
        templateTableGroupService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除系统列表分组模版")
    @ApiOperation("删除系统列表分组模版")
//    @MyDataPermission(title = "证照模板,设计院工作内容模板,装饰勘测报告模板,设计自检表模板,设计样板间验收模板,安全文明施工模板")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        templateTableGroupService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}