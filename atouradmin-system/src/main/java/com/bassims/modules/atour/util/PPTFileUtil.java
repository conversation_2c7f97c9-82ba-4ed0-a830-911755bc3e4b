package com.bassims.modules.atour.util;

import cn.hutool.core.util.ObjectUtil;
import com.bassims.utils.OSSClientUtil;
import com.drew.imaging.ImageMetadataReader;
import com.drew.metadata.Metadata;
import com.drew.metadata.exif.ExifIFD0Directory;
import com.itextpdf.awt.geom.AffineTransform;
import com.sun.imageio.plugins.jpeg.JPEGMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.sl.usermodel.PictureData;
import org.apache.poi.xslf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.ResourceUtils;
import org.w3c.dom.Node;

import java.awt.*;
import java.awt.image.AffineTransformOp;
import java.io.*;
import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.metadata.IIOMetadata;
import javax.imageio.metadata.IIOMetadataNode;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2024/11/6 16:27
 */
@Slf4j
@Component
public class PPTFileUtil {

    @Autowired
    private OSSClientUtil ossClientUtil;

//    public static void main(String[] args) {
//        String modelPath = "template/亚朵验收报告 V20241021.pptx";
//        String fileName = "template/亚朵验收报告 V2024102101.pptx";
//
//
////        // 打开PPTX文件
////        InputStream fis = null;
////        try {
//////            fis = ResourceUtils.getURL(modelPath).openStream();
////            ClassPathResource classPathResource = new ClassPathResource(modelPath);
////            fis = classPathResource.getInputStream();
////
////            XMLSlideShow ppt = new XMLSlideShow(fis);
//////            makePPT(2,ppt,getTextDataMap());
////            // 将新的ppt写入到指定的文件中
////            FileOutputStream outputStream = new FileOutputStream(fileName);
////            ppt.write(outputStream);
////
////            outputStream.close();
////            fis.close();
////        } catch (IOException e) {
////            e.printStackTrace();
////        }
//
//    }

    public void makePPT(Integer page, XMLSlideShow ppt, Map<String, Object> textDataMap) {
        //获取幻灯片对象
        XSLFSlide slide = null;
        slide = getShapes(page - 1, ppt);
        if (page == 1) {
            sideShow1(slide, textDataMap, 0);
        } else if (page == 2) {
            sideShow2(slide, textDataMap);
        } else {
            if (ObjectUtil.isNotEmpty(textDataMap.get("textPic")) && ObjectUtil.isEmpty(textDataMap.get("textPicLeft")) && ObjectUtil.isEmpty(textDataMap.get("textPicRight"))) {
                sideShow2(slide, textDataMap);
                sideShow3(slide, ppt, textDataMap);
            } else {
                sideShow1(slide, textDataMap, 1);
                sideShow2(slide, textDataMap);
                if (ObjectUtil.isNotEmpty(textDataMap.get("textPicLeft"))) {
                    sideShowTextPicLeft(slide, ppt, textDataMap);
                }
                if (ObjectUtil.isNotEmpty(textDataMap.get("textPicRight"))) {
                    sideShowTextPicRight(slide, ppt, textDataMap);
                }

            }
        }
    }

    //填充普通文本框
    public void sideShow1(XSLFSlide slide, Map<String, Object> textDataMap, Integer type) {
        fillDateToPPT(slide, textDataMap, type);
    }


    //填充表格数据
    public void sideShow2(XSLFSlide slide, Map<String, Object> textDataMap) {
        fillTableDataToPPT(textDataMap, slide);
    }


    //填充图片
    public void sideShow3(XSLFSlide slide, XMLSlideShow ppt, Map<String, Object> textDataMap) {
        fillImageToPPT(ppt, slide, textDataMap.get("textPic") + "", 50, 95, 410, 265);
    }

    //填充图片左侧图
    public void sideShowTextPicLeft(XSLFSlide slide, XMLSlideShow ppt, Map<String, Object> textDataMap) {
        fillImageToPPT(ppt, slide, textDataMap.get("textPicLeft") + "", 50, 95, 410, 300);
    }


    //填充图片右侧图
    public void sideShowTextPicRight(XSLFSlide slide, XMLSlideShow ppt, Map<String, Object> textDataMap) {
        fillImageToPPT(ppt, slide, textDataMap.get("textPicRight") + "", 500, 95, 410, 300);
    }

    //ppt幻灯片对象
    private XSLFSlide getShapes(Integer page, XMLSlideShow ppt) {
        return ppt.getSlides().get(page);
    }

    /**
     * 替换文本数据到ppt
     *
     * @param textDataMap 文本数据
     * @param slide       幻灯片对象
     */
    private void fillDateToPPT(XSLFSlide slide, Map<String, Object> textDataMap, Integer type) {
        for (XSLFShape shape : slide.getShapes()) {
            if (shape instanceof XSLFTextShape) {
                XSLFTextShape textShape = (XSLFTextShape) shape;
                for (XSLFTextParagraph textParagraph : textShape.getTextParagraphs()) {
                    for (XSLFTextRun textRun : textParagraph.getTextRuns()) {
                        String text = textRun.getRawText();
                        if (!text.equals("\\n") && text.contains("$")) {
                            Object resultText = textDataMap.get(text);
                            textRun.setText(resultText + "");
                        }
                    }
                }
            }
        }
    }


    /**
     * 根据集合循环填充表格数据
     *
     * @param textDataMap 文本数据地图
     * @param slide       幻灯片对象
     */
    private void fillTableDataToPPT(Map<String, Object> textDataMap, XSLFSlide slide) {
        for (XSLFShape shape : slide.getShapes()) {
            if (shape instanceof XSLFTable) {
                XSLFTable table = (XSLFTable) shape;
                for (int row = 0; row < table.getNumberOfRows(); row++) {
                    for (int colum = 0; colum < table.getNumberOfColumns(); colum++) {
                        // 遍历单元格匹配修改内容
                        XSLFTableCell cell = table.getCell(row, colum);
                        for (XSLFTextParagraph textParagraph : cell.getTextParagraphs()) {
                            for (XSLFTextRun textRun : textParagraph.getTextRuns()) {
                                String text = textRun.getRawText();
                                if (!text.equals("\\n") && text.contains("$")) {
                                    String resultText = textDataMap.get(text.trim()) + "";
                                    textRun.setText(resultText);
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    /**
     * 设置字体样式
     *
     * @param pptFontStyle
     */
    private void setFontStyle(PPTFontStyle pptFontStyle) {
        if (ObjectUtils.isEmpty(pptFontStyle)) {
            return;
        }
        XSLFTextRun textRun = null;
        if (!ObjectUtils.isEmpty(pptFontStyle.getCell())) {
            XSLFTextParagraph paragraph = pptFontStyle.getCell().addNewTextParagraph();
            textRun = paragraph.addNewTextRun();
        } else {
            textRun = pptFontStyle.getTextRun();
        }

        if (!ObjectUtils.isEmpty(pptFontStyle.getText())) {
            textRun.setText(pptFontStyle.getText());
        }
        if (!ObjectUtils.isEmpty(pptFontStyle.getFontSize())) {
            textRun.setFontSize(pptFontStyle.getFontSize());
        }
        if (!ObjectUtils.isEmpty(pptFontStyle.getFontFamily())) {
            textRun.setFontFamily(pptFontStyle.getFontFamily());
        }
        if (!ObjectUtils.isEmpty(pptFontStyle.getFontColor())) {
            textRun.setFontColor(pptFontStyle.getFontColor());
        }
    }

    /**
     * @param ppt      ppt对象
     * @param slide    幻灯片对象
     * @param fileName 图片地址
     * @param x        x轴
     * @param y        y轴
     * @param width    宽
     * @param height   高
     *///填充图片
    private static Object mylock = new Object();
    private void fillImageToPPT(XMLSlideShow ppt, XSLFSlide slide, String fileName, Integer x, Integer y, Integer width, Integer height) {
        if (ObjectUtils.isEmpty(fileName)) {
            return;
        }
        BufferedInputStream newinputStream = null;
        File newFile = null;
        // 填充展位图片
        try (BufferedInputStream inputStream = ossClientUtil.getFileInputStream(fileName);
             BufferedInputStream inputStream2 = ossClientUtil.getFileInputStream(fileName)){

            //把从OSS服务器读到的文件流缓存到本地生成一个新的改变分辨率的文件
            Thread currentThread = Thread.currentThread();
            newFile = resizeImage(fileName,inputStream,inputStream2,(new Date().getTime()+"_"+ currentThread.getId()+"."+fileName.substring(fileName.lastIndexOf(".") + 1)),width,height);
            newinputStream = new BufferedInputStream(new FileInputStream(newFile));
            PictureData.PictureType format = null;
            switch (fileName.toLowerCase().substring(fileName.lastIndexOf(".") + 1))
            {
                case "jpg": format = PictureData.PictureType.JPEG;break;
                case "png": format = PictureData.PictureType.PNG;break;
                case "gif": format = PictureData.PictureType.GIF;break;
                case "bmp": format = PictureData.PictureType.BMP;break;
                default: format = PictureData.PictureType.JPEG;
            }
            synchronized (mylock){
                XSLFPictureData pictureData = ppt.addPicture(newinputStream, format);
                XSLFPictureShape pictureShape = slide.createPicture(pictureData);
                pictureShape.setAnchor(new Rectangle(x, y, width, height));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            if(newinputStream!=null){try{newinputStream.close();}catch (Exception e){}}
            if(newFile.exists()){try{newFile.delete();}catch (Exception e){}}
        }
    }

    /**
     * 重新绘制成低分辨率的图片 ljp
     */
    private File resizeImage(String inputImageName, InputStream inputStream,InputStream inputStream2, String outputImagePath, int targetWidth, int targetHeight) throws IOException {
//        File inputFile = new File(inputImagePath);

        int orientation = 0;
        String fileEnd = inputImageName.substring(inputImageName.lastIndexOf(".") + 1).toLowerCase();
        if("jpg".equals(fileEnd)||"jpeg".equals(fileEnd)){
            orientation = getOrientation(inputStream);
        }
        BufferedImage inputImage = ImageIO.read(inputStream2);
        inputImage = retateImage(inputImage,orientation);
        // 创建一个新的缓冲图片
        BufferedImage outputImage = new BufferedImage(targetWidth, targetHeight, inputImage.getType());

        // 绘制并调整图片大小
        outputImage.getGraphics().drawImage(inputImage.getScaledInstance(targetWidth, targetHeight, BufferedImage.SCALE_SMOOTH), 0, 0, null);

        // 写入新图片到文件
        File outputFile = new File(outputImagePath);
        ImageIO.write(outputImage, inputImageName.substring(inputImageName.lastIndexOf(".") + 1), outputFile); // 假设输出格式为jpg

        return outputFile;
    }

    /**
     * 获取JPG图片的方向信息
     * @return
     */
    private int getOrientation(InputStream inputStream2){
        int orientation = 0;
        try{
            //读取图片元数据
            Metadata metadata = ImageMetadataReader.readMetadata(inputStream2);
            ExifIFD0Directory directory = metadata.getFirstDirectoryOfType(ExifIFD0Directory.class);
            if(directory!=null&&directory.containsTag(ExifIFD0Directory.TAG_ORIENTATION)){
                orientation = directory.getInt(ExifIFD0Directory.TAG_ORIENTATION);
            }
            return orientation;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    /**
     * 本剧图片的方向信息给图片转向
     * @param inputImage
     * @param orientation
     * @return
     */
    private BufferedImage retateImage(BufferedImage inputImage,int orientation){
        if(orientation==6){
            java.awt.geom.AffineTransform transform = new java.awt.geom.AffineTransform();
            transform.rotate(Math.toRadians(90),inputImage.getWidth()/2,inputImage.getHeight()/2);
            java.awt.image.AffineTransformOp op= new java.awt.image.AffineTransformOp(transform, AffineTransformOp.TYPE_NEAREST_NEIGHBOR);
            return op.filter(inputImage,null);
        }else if(orientation==3){
            java.awt.geom.AffineTransform transform = new java.awt.geom.AffineTransform();
            transform.rotate(Math.toRadians(180),inputImage.getWidth()/2,inputImage.getHeight()/2);
            java.awt.image.AffineTransformOp op= new java.awt.image.AffineTransformOp(transform, AffineTransformOp.TYPE_NEAREST_NEIGHBOR);
            return op.filter(inputImage,null);
        }else if(orientation==8){
            java.awt.geom.AffineTransform transform = new java.awt.geom.AffineTransform();
            transform.rotate(Math.toRadians(-90),inputImage.getWidth()/2,inputImage.getHeight()/2);
            java.awt.image.AffineTransformOp op= new java.awt.image.AffineTransformOp(transform, AffineTransformOp.TYPE_NEAREST_NEIGHBOR);
            return op.filter(inputImage,null);
        }
        return inputImage;
    }

    public static void main(String[] args) {
        try {
            PPTFileUtil util = new PPTFileUtil();
            util.resizeImage("D:\\222.jpeg",new FileInputStream("D:\\222.jpeg") ,new FileInputStream("D:\\222.jpeg"),"D:\\test333.jpeg", 800, 600); // 调整分辨率为800x600
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

// //填充图片
//    private void fillImageToPPT() throws IOException {
//        // 这里能读到这个流，但是是找不到这个文件的
//        ClassPathResource classPathResource = new ClassPathResource("template/亚朵验收报告 V20241021.pptx");
//
//        // 打开PPTX文件
//        InputStream fis = classPathResource.getInputStream();
//
//
//        Presentation ppt = new Presentation();
//        ppt.loadFromFile("D:\\project\\ces\\atour-admin\\atouradmin-system\\src\\main\\resources\\template\\亚朵验收报告 V20241021.pptx");
//
//        //获取第一张幻灯片
//        ISlide slide = ppt.getSlides().get(0);
//
//        //添加视频文件到指定位置
//        slide.getShapes().appendVideoMedia("mda-njqbvdg1h8kdqez1.mp4",new Rectangle(550, 100, 180, 100));
//        //添加音频文件到指定位置
////        slide.getShapes().appendAudioMedia("Myheartwillgoon.mp3",620, 300, true);
//
//        //保存文档
//        ppt.saveToFile("result.pptx",FileFormat.PPTX_2010);
//
//    }


    /**
     * 添加一个新幻灯片
     */
    public void addSlide(XMLSlideShow ppt) {
        XSLFSlide slide = ppt.createSlide();

    }


}
