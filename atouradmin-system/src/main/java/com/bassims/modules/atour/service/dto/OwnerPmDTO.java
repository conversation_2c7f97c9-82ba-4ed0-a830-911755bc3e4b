/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 业主方-特许商对接人信息（手机号、姓名、邮箱）
 * @date: 2023/11/2
 */
@Data
public class OwnerPmDTO implements Serializable {

    /**
     * 用户名
     */
    private String username;

    /**
     * 手机号
     */
    private String mobile;
    /**
     * 邮箱
     */
    private String email;
}