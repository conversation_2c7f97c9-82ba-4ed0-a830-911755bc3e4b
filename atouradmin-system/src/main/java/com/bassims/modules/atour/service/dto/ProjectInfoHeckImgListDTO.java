/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description / 风险管理
 * @date 2023-10-30
 **/
@Data
public class ProjectInfoHeckImgListDTO implements Serializable {


    /*风险管理类型 1, "消防联动"2, "消防广播"3, "喷淋水系统"4, "消防水系统"5, "三方通话"6, "梯控"7, "监控"8, "门禁"  */
    private Integer type;

    /*验收结果 */
    private ProjectInfoHeckImgListDTO checkResult;

    /*一项否决扣分项目(正整数) 给分数 */
    private Integer rejectDeductScore;

    /*检查项名称      */
    private String checkItem;

    /*验收照片列表      */
    private String checkImgList;

}