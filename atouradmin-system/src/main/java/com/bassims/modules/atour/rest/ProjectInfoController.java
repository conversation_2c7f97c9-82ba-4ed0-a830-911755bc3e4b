/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.base.QueryRequest;
import com.bassims.constant.bsEnum.KidsSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.domain.vo.DownloadDocVo;
import com.bassims.modules.atour.service.ProjectInfoExpansionService;
import com.bassims.modules.atour.service.ProjectInfoService;
import com.bassims.modules.atour.service.dto.ProjectInfoDto;
import com.bassims.modules.atour.service.dto.ProjectInfoInsertStaDto;
import com.bassims.modules.atour.service.dto.ProjectInfoQueryCriteria;
import com.bassims.modules.atour.service.dto.ProjectTemplateCodeDto;
import com.bassims.modules.system.domain.TemplateConfig;
import com.bassims.modules.system.service.TemplateConfigService;
import com.bassims.utils.FileUtil;
import com.bassims.utils.excelutil.ExcelUtils;
import com.bassims.utils.excelutil.ValuePosition;
import com.bassims.utils.wordutil.Contract;
import com.bassims.utils.wordutil.NoticeToProceed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-03-24
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "项目主表管理")
@RequestMapping("/api/projectInfo")
public class ProjectInfoController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectInfoController.class);

    @Autowired
    private final ProjectInfoService projectInfoService;
    private final TemplateConfigService templateConfigService;
    private final ProjectInfoExpansionService projectInfoExpansionService;


    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ProjectInfoQueryCriteria criteria) throws IOException {
        projectInfoService.downloadNew(response);
    }

    /**
     * @real_return {@link ResponseEntity <List<ProjectInfoDto>>}
     */
    @GetMapping("/list")
//    @Log("查询项目主表")
    @ApiOperation("查询项目主表")
    @MyDataPermission(title = "营建对接启动,项目推进")
    public ResponseEntity<Object> query(ProjectInfoQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(projectInfoService.queryAll(criteria, pageable), HttpStatus.OK);
    }


    @GetMapping("/queryChangeStakeholders")
    @Log("查询项目批量更换干系人列表")
    @ApiOperation("查询项目批量更换干系人列表")
    public ResponseEntity<Object> queryChangeStakeholders(ProjectInfoQueryCriteria criteria) {
        return new ResponseEntity<>(projectInfoService.queryChangeStakeholders(criteria), HttpStatus.OK);
    }


    /**
     * @real_return {@link ResponseEntity <List<ProjectInfoDto>>}
     */
    @GetMapping("/adjustList")
    @Log("查询项目主表")
    @ApiOperation("查询项目主表")
    public ResponseEntity<Object> adjustQuery(ProjectInfoQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(projectInfoService.queryAdjustAll(criteria, pageable), HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity <List<ProjectInfoDto>>}
     */
    @GetMapping("/promotionlist")
    @Log("查询项目推进主表")
    @ApiOperation("查询项目推进主表")
    public ResponseEntity<Object> promotionquery(ProjectInfoQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(projectInfoService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity <List<ProjectInfoDto>>}
     */
    @GetMapping("/deletionlist")
    @Log("查询项目删除主表")
    @ApiOperation("查询项目删除主表")
    public ResponseEntity<Object> deletionquery(ProjectInfoQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(projectInfoService.queryAllForDelete(criteria, pageable), HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity <List<ProjectInfoDto>>}
     */
    @GetMapping("/adjustmentlist")
    @Log("查询信息调整主表")
    @ApiOperation("查询信息调整主表")
    public ResponseEntity<Object> adjustmentquery(ProjectInfoQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(projectInfoService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity <List<ProjectInfoDto>>}
     */
    @GetMapping("/projectTaskList")
    @Log("查询项目任务列表")
    @ApiOperation("查询项目任务列表")
    public ResponseEntity<Object> TaskQuery(ProjectInfoQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(projectInfoService.queryTask(criteria, pageable), HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity <ProjectInfoDto>}
     */
    @GetMapping("/taskQuery")
    @Log("查看任务")
    @ApiOperation("查看任务")
    public ResponseEntity<Object> taskQuery(Long projectId) {
        return new ResponseEntity<>(projectInfoService.taskQueryByProjectId(projectId), HttpStatus.OK);
    }


    @GetMapping("/mobilelist")
    @Log("mobile查询项目主表")
    @ApiOperation("mobile查询项目主表")
    public ResponseEntity<Object> mobilequery(ProjectInfoQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(projectInfoService.queryAllForMobile(criteria, pageable), HttpStatus.OK);
    }

    @GetMapping("/complatelist")
    @Log("竣工查询项目主表")
    @ApiOperation("竣工查询项目主表")
    public ResponseEntity<Object> complatequery(ProjectInfoQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(projectInfoService.queryAllForComplateData(criteria, pageable), HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity <ProjectInfoDto>}
     */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询项目主表")
    @ApiOperation("查询项目主表")
    public ResponseEntity<Object> query(@PathVariable Long id) {
        return new ResponseEntity<>(projectInfoService.findById(id), HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增项目主表")
    @ApiOperation("新增项目主表")
    public ResponseEntity<Object> create(@Validated @RequestBody ProjectInfo resources) {
        return new ResponseEntity<>(projectInfoService.create(resources), HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改项目主表")
    @ApiOperation("修改项目主表")
    public ResponseEntity<Object> update(@Validated @RequestBody ProjectInfo resources) {
        projectInfoService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除项目主表")
    @ApiOperation("删除项目主表")
    public ResponseEntity<Object> delete(@RequestBody ProjectInfoQueryCriteria criteria) {
        projectInfoService.deleteFlag(criteria.getId(), criteria.getDeleteFlag());
        return new ResponseEntity<>(HttpStatus.OK);
    }

//    @GetMapping("/createOldProject")
//    @Log("批量创建项目")
//    @ApiOperation("批量创建项目")
//    public ResponseEntity<Object> createOldProject() {
//        return new ResponseEntity<>(projectInfoService.createOldProject(), HttpStatus.CREATED);
//    }

    /**
     * postman测试saveProjectStakeholdersUsers方法
     */
    @PostMapping("/saveProjectStakeholdersUsers")
    @Log("用户输入干系人")
    @ApiOperation("用户输入干系人")
    public ResponseEntity<Object> saveProjectStakeholdersUsers(@Validated @RequestBody StakeholdersReq resources) throws IllegalAccessException {
        return new ResponseEntity<>(projectInfoService.saveProjectStakeholdersUsers(resources), HttpStatus.CREATED);
    }

    /***
     * ,ljp 项目启动 新开店
     * @param resources
     * @return
     * @throws IllegalAccessException
     */
    @PostMapping("/createProjectSta")
    @Log("筹建启动项目保存干系人")
    @ApiOperation("筹建启动项目保存干系人")
    public ResponseEntity<Object> createProjectStakeholder(@Validated @RequestBody ProjectInfoInsertStaDto resources) throws IllegalAccessException {
        return new ResponseEntity<>(projectInfoService.createProjectSta(resources), HttpStatus.CREATED);
    }


    @PostMapping("/saveRefreshShopRotation")
    @Log("焕新店新增轮次")
    @ApiOperation("焕新店新增轮次")
    public ResponseEntity<Object> saveRefreshShopRotation(@Validated @RequestBody ProjectTemplateCodeDto dto){
        return new ResponseEntity<>(projectInfoService.saveRefreshShopRotation(dto),HttpStatus.CREATED);
    }

    @PostMapping("/createProject")
    @Log("项目立项")
    @ApiOperation("项目立项")
    public ResponseEntity<Object> createProject(@Validated @RequestBody ProjectInfoDto resources, String... conditions) throws IllegalAccessException {
        return new ResponseEntity<>(projectInfoService.findTemplateByConditionCode(resources, conditions), HttpStatus.CREATED);
    }

//    @PostMapping("/newProjectTime")
//    @Log("修改项目的计划开始时间")
//    @ApiOperation("修改项目的计划开始时间")
//    public ResponseEntity<Object> newProjectTime(@RequestParam(value = "projectId",required = true) Long projectId,@RequestParam(value = "startTime",required = false) String startTime) {
//        return new ResponseEntity<>(projectInfoService.newProjectTime(projectId, startTime), HttpStatus.CREATED);
//    }

    @PostMapping("/getConditionsRelation")
    @Log("获取condition")
    @ApiOperation("获取condition")
    public ResponseEntity<Object> getConditionsRelation() throws IllegalAccessException {
        return new ResponseEntity<>(projectInfoService.getConditionsRelation(), HttpStatus.CREATED);
    }

    @GetMapping("/insertProjectUser")
    @Log("导入当前用户的角色信息")
    @ApiOperation("导入当前用户的角色信息")
    public ResponseEntity<Object> insertProjectUser() throws IllegalAccessException {
        return new ResponseEntity<>(projectInfoService.insertProjectUser(), HttpStatus.OK);
    }

    @PostMapping("/createAdjustProject")
    @Log("小调整项目立项")
    @ApiOperation("小调整项目立项")
    public ResponseEntity<Object> createAdjustProject(@Validated @RequestBody ProjectInfoDto resources) {
        return new ResponseEntity<>(projectInfoService.createAdjustProject(resources), HttpStatus.CREATED);
    }

    /**
     * ,ljp 加载项目的基本信息
     * @param id
     * @return
     */
    @GetMapping(value = "/title")
    @Log("通过Id查询项目主表(title)")
    @ApiOperation("查询项目主表(title)")
    @MyDataPermission
    public ResponseEntity<Object> queryTitle(Long id) {
        return new ResponseEntity<>(projectInfoService.findByIdForTitle(id), HttpStatus.OK);
    }

    @GetMapping(value = "/total")
    @Log("获取首页统计")
    @ApiOperation("获取首页统计")
    public ResponseEntity<Object> getTotalStatistics() {
        return new ResponseEntity<>(projectInfoService.getTotalStatistics(), HttpStatus.OK);
    }

    @GetMapping(value = "/overDuePie")
    @Log("获取逾期饼图统计")
    @ApiOperation("获取逾期饼图统计")
    public ResponseEntity<Object> getOverDuePie(String date) {
        return new ResponseEntity<>(projectInfoService.getOverDuePercent(date), HttpStatus.OK);
    }

    @GetMapping(value = "/phasePie")
    @Log("获取项目饼图统计")
    @ApiOperation("获取项目饼图统计")
    public ResponseEntity<Object> getPhasePie(String date) {
        return new ResponseEntity<>(projectInfoService.getPhasePercent(date), HttpStatus.OK);
    }

    @GetMapping(value = "/phasePieWithProjectType")
    @Log("获取项目阶段饼图统计")
    @ApiOperation("获取项目阶段饼图统计")
    public ResponseEntity<Object> getPhasePieWithProjectType(@RequestParam(required = false) String projectType) {
        return new ResponseEntity<>(projectInfoService.getPhasePercentWithProjectType(projectType), HttpStatus.OK);
    }

    @GetMapping(value = "/pieByProjectType")
    @Log("获取项目类型饼图统计")
    @ApiOperation("获取项目类型饼图统计")
    public ResponseEntity<Object> getpieByProjectType() {
        return new ResponseEntity<>(projectInfoService.getPercentByProjectType(), HttpStatus.OK);
    }

    @GetMapping(value = "/cityProjectSum")
    @Log("获取城市项目统计")
    @ApiOperation("获取城市项目统计")
    public ResponseEntity<Object> getCityProjectStatistics() {
        return new ResponseEntity<>(projectInfoService.getCityProjectStatistics(), HttpStatus.OK);
    }

    @GetMapping(value = "/cityStoreSum")
    @Log("获取分城市门店汇总")
    @ApiOperation("获取城市门店汇总")
    public ResponseEntity<Object> getCityStoreStatistics() {
        return new ResponseEntity<>(projectInfoService.getCityStoreProjectStatistics(), HttpStatus.OK);
    }

    @GetMapping(value = "/totalAccount")
    @Log("获取结算首页统计")
    @ApiOperation("获取结算首页统计")
    public ResponseEntity<Object> getTotalStatisticsAccount() {
        return new ResponseEntity<>(projectInfoService.getTotalStatisticsAccount(), HttpStatus.OK);
    }


    @GetMapping(value = "/overDuePieAccount")
    @Log("获取结算逾期饼图统计")
    @ApiOperation("获取结算逾期饼图统计")
    public ResponseEntity<Object> getAccountOverDuePie(String date) {
        return new ResponseEntity<>(projectInfoService.getAccountOverDuePercent(date), HttpStatus.OK);
    }

    @GetMapping(value = "/phasePieAccount")
    @Log("获取结算项目饼图统计")
    @ApiOperation("获取结算项目饼图统计")
    public ResponseEntity<Object> getAccountPhasePie(String date) {
        return new ResponseEntity<>(projectInfoService.getAccountPhasePercent(date), HttpStatus.OK);
    }

    @GetMapping(value = "/cityProjectAccountSum")
    @Log("获取城市饼图项目统计")
    @ApiOperation("获取城市饼图项目统计")
    public ResponseEntity<Object> getCityProjectAccountStatistics() {
        return new ResponseEntity<>(projectInfoService.getCityProjectAccountStatistics(), HttpStatus.OK);
    }

    @Log("导入项目创建信息")
    @ApiOperation(value = "导入项目创建信息")
    @PostMapping("/importProject")
    public ResponseEntity<Object> importProject(MultipartFile file) throws Exception {
        return new ResponseEntity<>(projectInfoService.importProjectExcel(file), HttpStatus.OK);
    }

    @GetMapping("/getStoreOrProject")
    @Log("获取门店或项目信息")
    @ApiOperation("获取门店或项目信息")
    public ResponseEntity<Object> getStoreOrProject(ProjectInfoQueryCriteria criteria) {
        return new ResponseEntity<>(projectInfoService.getProjectInfoByStoreNoAndName(criteria), HttpStatus.OK);
    }

    @GetMapping("/getConstructionManagerAndPhone")
    @Log("获取项目工程经理和电话")
    @ApiOperation("获取项目工程经理和电话")
    public ResponseEntity<Object> getConstructionManagerAndPhone(Long projectId) {
        return new ResponseEntity<>(projectInfoService.getConstructionManagerAndPhone(projectId), HttpStatus.OK);
    }

    @GetMapping("/getDistinctProjectByStoreNoAndName")
    @Log("获取门店或项目信息")
    @ApiOperation("获取门店或项目信息")
    public ResponseEntity<Object> getDistinctProjectByStoreNoAndName(ProjectInfoQueryCriteria criteria) {
        return new ResponseEntity<>(projectInfoService.getProjectInfoByStoreNoAndName(criteria), HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity <List<ProjectInfoDto>>}
     */
    @GetMapping("/getProjectInfo")
    @Log("根据门店编号和项目类型查询未申请指令单的项目信息")
    @ApiOperation("根据门店编号和项目类型查询未申请指令单的项目信息")
    public ResponseEntity<Object> queryNoSheetList(ProjectInfoQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(projectInfoService.queryNoSheetList(criteria, pageable), HttpStatus.OK);
    }

    @GetMapping("/getDrawingInfo")
    @Log("查询主档图纸信息")
    @ApiOperation("查询主档图纸信息")
    public ResponseEntity<Object> getDrawingInfo(Long drawingId) {
        return new ResponseEntity<>(projectInfoService.getDrawingInfo(drawingId), HttpStatus.OK);
    }

    /**
     * 导入资料返回信息
     *
     * @param queryRequest
     * @return
     * @throws Exception
     */
    @PostMapping("/importProjectAndReturn")
    @Log("导入资料返回信息")
    @ApiOperation("导入资料返回信息")
    public ResponseEntity<Object> importProject(QueryRequest queryRequest) throws Exception {
        String nodeCode = queryRequest.getNodeCode();
        Integer sheetStart = KidsSystemEnum.TemplateConfigType.CFT001.getSheetStart();
        String configType = KidsSystemEnum.TemplateConfigType.CFT001.getValue();
        if (JhSystemEnum.NodeCodeSEEnum.NODE_402.getKey().equals(nodeCode)) {
            configType = KidsSystemEnum.TemplateConfigType.CFT002.getValue();
            sheetStart = KidsSystemEnum.TemplateConfigType.CFT002.getSheetStart();
        } else if (JhSystemEnum.NodeCodeSEEnum.NODE_113.getKey().equals(nodeCode)) {
            configType = KidsSystemEnum.TemplateConfigType.CFT003.getValue();
            sheetStart = KidsSystemEnum.TemplateConfigType.CFT003.getSheetStart();
        } else if (JhSystemEnum.NodeCodeSEEnum.NODE_142.getKey().equals(nodeCode)) {
            configType = KidsSystemEnum.TemplateConfigType.CFT004.getValue();
            sheetStart = KidsSystemEnum.TemplateConfigType.CFT004.getSheetStart();
        }

        TemplateConfig byConfigType = templateConfigService.findByConfigType(configType);
        if (byConfigType == null) {
            throw new BadRequestException("请先配置导入模板");
        }
        List<ValuePosition> valuePositionList = ExcelUtils.importFile(queryRequest.getFile(), byConfigType.getContent(), KidsSystemEnum.TemplateConfigType.CFT001.getValue(), sheetStart);
        return new ResponseEntity<>(valuePositionList, HttpStatus.OK);
    }

    /**
     * 下载项目信息
     *
     * @param response
     * @param criteria
     * @return
     * @throws Exception
     */
    @GetMapping("/downloadProjectByNode")
    @Log("下载项目信息")
    @ApiOperation("下载项目信息")
    public void downloadProjectByNode(HttpServletResponse response, ProjectInfoQueryCriteria criteria) {
        projectInfoService.downloadProjectByNode(response, criteria);
    }


    /**
     * 导出doc
     *
     * @param request
     * @param response
     * @return
     */
    @PostMapping("/downloadDoc")
    @Log("导出doc")
    @ApiOperation("导出doc")
    public void downloadDoc(@RequestBody ProjectInfoDto resources, HttpServletRequest request, HttpServletResponse response, DownloadDocVo downloadDocVo) throws IOException {
        /*NoticeToProceed noticeToProceed = new NoticeToProceed();
        noticeToProceed.setCompany("南京八金木科技有限公司");
        noticeToProceed.setProjectName("测试word文档生成是否可行是否可行是否可行是否可行是否可行");
        noticeToProceed.setStartTime("2022年10月31日");
        noticeToProceed.setProjectAddress("南京八金木科技有限公司小小工位1号位");
        noticeToProceed.setProjectDetail("测试word文档生成是否可行是否可行是否可行是否可行是否可行，这不是在测试呢么");
        noticeToProceed.setEndTime("2022年10月31日");
        noticeToProceed.setProjectContent("1、放一个文档，测试 2、再生成一个文档，测试");
        noticeToProceed.setDesigner("小熊同学");
        noticeToProceed.setDesignerPhone("18013835427");
        noticeToProceed.setDesignBoss("小张同学");
        noticeToProceed.setDesignBossPhone("15203997213");
        noticeToProceed.setSendTime("2022年10月31日");
        noticeToProceed.setManagerCount("20");
        noticeToProceed.setPm("小徐同学、小谭同学、小熊同学");
        noticeToProceed.setManagers("小张同学、小徐同学、小谭同学、小熊同学");
        noticeToProceed.setConstructionerCount("10");
        noticeToProceed.setReturnTime("2022年10月31日");
        downloadDocVo.setNoticeToProceed(noticeToProceed);
        downloadDocVo.setDocType(KidsSystemEnum.DocType.NoticeToProceed.getValue());
        String path = wordUtils.createDoc("noticeToProceed.ftl", noticeToProceed);
        */
        //拿到参数，到表中查到数据，将数据塞进contract


        Long aLong = resources.getProjectId();
        String nodeCode = resources.getNodeCode();


//        String projectId = request.getParameter("projectId");
//        Long aLong = Long.valueOf(projectId);
//        String nodeCode = request.getParameter("nodeCode");


        //施工派案
        if ("con-00103".equals(nodeCode)) {
            //调用
            Contract contract = templateConfigService.downloadDoc(aLong, nodeCode);
            // =========入参结束=========
            downloadDocVo.setContract(contract);
            downloadDocVo.setDocType(KidsSystemEnum.DocType.Contract.getValue());
        } else if ("con-00104".equals(nodeCode)) {
            NoticeToProceed noticeToProceed = templateConfigService.downloadOpenWorkDoc(aLong, nodeCode);
            downloadDocVo.setNoticeToProceed(noticeToProceed);
            downloadDocVo.setDocType(KidsSystemEnum.DocType.NoticeToProceed.getValue());
        }
//        downloadDocVo.setDocType();
        // 这里是调用的接口，接口中的参数是传入的，无须再入参
        String filePath = projectInfoService.getFilePath(downloadDocVo);
        File file = new File(filePath);
        FileUtil.downloadFile(request, response, file, false);
    }

    @GetMapping("/test")
    @Log("导出doc")
    @ApiOperation("导出doc")
    public void test(String nodeCode) {
        projectInfoService.test(nodeCode);
    }


    @GetMapping("/getDynamicMaskClick")
    @Log("项目动态列表蒙版点击")
    @ApiOperation("项目动态蒙版点击")
    public ResponseEntity<Object> getDynamicMaskClick(Long projectId, String nodeCode) {
        return new ResponseEntity<>(projectInfoService.getDynamicMaskClick(projectId, nodeCode), HttpStatus.CREATED);
    }

    @PostMapping("/saveDynamicMaskClick")
    @Log("项目动态列表蒙版保存")
    @ApiOperation("项目动态列表蒙版保存")
    public ResponseEntity<Object> saveDynamicMaskClick(@Validated @RequestBody List<ProjectTableNodeInfo> projectTableNodeInfos) {
        return new ResponseEntity<>(projectInfoService.saveDynamicMaskClick(projectTableNodeInfos), HttpStatus.CREATED);
    }

    @GetMapping("/getDynamicMaskRectificationList")
    @Log("整改清单")
    @ApiOperation("整改清单")
    public ResponseEntity<Object> getDynamicMaskRectificationList(Long projectId, Long parentId,String nodeCode) {
        return new ResponseEntity<>(projectInfoService.getDynamicMaskRectificationList(projectId, parentId,nodeCode), HttpStatus.CREATED);
    }


    @PostMapping("/saveDrawingReviewData")
    @Log("项目审图数据的保存")
    @ApiOperation("项目审图数据的保存")
    public ResponseEntity<Object> saveDrawingReviewData(@Validated @RequestBody List<ProjectInfoAbarbeitung> projectTableNodeInfos) {
        return new ResponseEntity<>(projectInfoService.saveDrawingReviewData(projectTableNodeInfos), HttpStatus.CREATED);
    }

    @GetMapping("/getDrawingReviewData")
    @Log("项目审图数据的查看（问题集合）")
    @ApiOperation("项目审图数据的查看（问题集合）")
    public ResponseEntity<Object> getDrawingReviewData(Long projectId,String nodeCode,String nodeId) {
        return new ResponseEntity<>(projectInfoService.getDrawingReviewData(projectId,nodeCode,nodeId), HttpStatus.CREATED);
    }

    @GetMapping("/getApproveRejectedVersions")
    @Log("项目审批拒绝的版本")
    @ApiOperation("项目审批拒绝的版本")
    public ResponseEntity<Object> getApproveRejectedVersions(Long projectId,String approveId) {
        return new ResponseEntity<>(projectInfoService.getApproveRejectedVersions(projectId,approveId), HttpStatus.CREATED);
    }

    @GetMapping("/getDrawingReviewDataVersions")
    @Log("审图版本里的审图数据（问题集合）")
    @ApiOperation("审图版本里的审图数据（问题集合）")
    public ResponseEntity<Object> getDrawingReviewDataVersions(Long projectId,String nodeCode,String nodeId) {
        return new ResponseEntity<>(projectInfoService.getDrawingReviewDataVersions(projectId,nodeCode,nodeId), HttpStatus.CREATED);
    }

    @GetMapping("/getMaskPopupClick")
    @Log("项目蒙版弹窗点击(资料)")
    @ApiOperation("项目蒙版弹窗点击(资料)")
    public ResponseEntity<Object> getMaskPopupClick(Long projectId, String nodeCode) {
        return new ResponseEntity<>(projectInfoService.getMaskPopupClick(projectId, nodeCode), HttpStatus.CREATED);
    }



    @PostMapping("/saveProjectSheJi")
    @Log("点击创建项目客房设计阶段")
    @ApiOperation("点击创建项目客房设计阶段")
    public ResponseEntity<Object> saveProjectSheJi(@Validated @RequestBody ProjectInfoInsertStaDto resources) throws IllegalAccessException {
        return new ResponseEntity<>(projectInfoService.saveProjectSheJi(resources), HttpStatus.CREATED);
    }

    @GetMapping("/queryUnqualifiedDownloadState")
    @Log("查询文件是否生成")
    @ApiOperation("查询文件是否生成")
    public ResponseEntity<Object> createProjectSup() throws InterruptedException {
        return new ResponseEntity<>(projectInfoService.queryUnqualifiedDownloadState(), HttpStatus.CREATED);
    }

    @GetMapping("/downloadProject")
    @Log("下载项目信息")
    @ApiOperation("下载项目信息")
    public void downloadProject(HttpServletRequest request,HttpServletResponse response) throws IOException {
        projectInfoService.downloadProject(request, response);
    }



}
