/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bassims.modules.atour.domain.vo.ProjectInfoSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-09-26
**/
@Data
@TableName(value="t_instruction_sheet")
public class InstructionSheet implements Serializable {

    @TableId(value = "sheet_id")
    @ApiModelProperty(value = "主键")
    private Long sheetId;

    @TableField(value = "apply_no")
    @ApiModelProperty(value = "申请单号")
    private String applyNo;

    @TableField(value = "name")
    @ApiModelProperty(value = "申请人姓名")
    private String name;

    @TableField(value = "code")
    @ApiModelProperty(value = "申请人工号")
    private String code;

    @TableField(value = "dept")
    @ApiModelProperty(value = "申请人部门")
    private String dept;

    @TableField(value = "job")
    @ApiModelProperty(value = "申请人岗位")
    private String job;

    @TableField(value = "`rank`")
    @ApiModelProperty(value = "申请人职级")
    private String rank;

    @TableField(value = "phone")
    @ApiModelProperty(value = "联系人电话")
    private String phone;

    @TableField(value = "apply_date")
    @ApiModelProperty(value = "申请日期")
    private Timestamp applyDate;

    @TableField(value = "store_no")
    @ApiModelProperty(value = "门店编码")
    private String storeNo;

    @TableField(value = "store_name")
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @TableField(value = "sheet_type")
    @ApiModelProperty(value = "指令单类型")
    private String sheetType;

    @TableField(value = "sheet_subclass")
    @ApiModelProperty(value = "指令单小类")
    private String sheetSubclass;

    @TableField(value = "fee")
    @ApiModelProperty(value = "费用")
    private BigDecimal fee;

    @TableField(value = "content")
    @ApiModelProperty(value = "请示内容")
    private String content;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人")
    private Long createBy;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新人")
    private Long updateBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否使用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @TableField(value = "is_visa")
    @ApiModelProperty(value = "是否签证")
    private Boolean isVisa;

    @TableField(value = "is_submit")
    @ApiModelProperty(value = "是否已提交")
    private Boolean isSubmit;

    @TableField(value = "project_type")
    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @TableField(value = "visa_id")
    @ApiModelProperty(value = "签证id")
    private Long visaId;

    @TableField(exist = false)
    private List<ProjectInfo> projectInfos;

    @TableField(value = "area_manager")
    @ApiModelProperty(value = "区域经理")
    private Long areaManager;

    @TableField(value = "design_manager")
    @ApiModelProperty(value = "设计经理")
    private Long designManager;

    @TableField(exist = false)
    @ApiModelProperty(value = "2级节点projectGroupId")
    private Long projectGroupId;

    @TableField(value = "file_id")
    @ApiModelProperty(value = "上传的文件id")
    private Long fileId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "关联的项目id")
    private Long projectId;

    @TableField(exist = false)
    private String nodeCode;

    @TableField(exist = false)
    private Long nodeId;

    @TableField(exist = false)
    private List<ProjectInfoSimple> simpleProjects;

    @TableField(exist = false)
    private String remark;

    @TableField(exist = false)
    private String roleCode;

    @TableField(exist = false)
    private String nodeStatus;

    @TableField(exist = false)
    private Boolean nodeIsfin;

    @TableField(exist = false)
    private Boolean nodeIsSubmit;

    @TableField(exist = false)
    private String actualEndDate;

    @TableField(exist = false)
    private String projectName;

    @TableField(exist = false)
    @ApiModelProperty(value = "是否显示")
    private Boolean isHidden;

    @TableField(exist = false)
    @ApiModelProperty(value = "是否只读")
    private Boolean isRead;

    @TableField(exist = false)
    @ApiModelProperty(value = "是否可写")
    private Boolean isWrite;

    public void copy(InstructionSheet source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}