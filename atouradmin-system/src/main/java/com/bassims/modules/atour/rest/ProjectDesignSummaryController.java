package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.service.ProjectDesignSummaryService;
import com.bassims.modules.atour.service.dto.ProjectDesignSummaryQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 设计师设计进度汇总
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "设计师设计进度汇总报表")
@RequestMapping("/api/projectDesignSummary")
public class ProjectDesignSummaryController {

    @Autowired
    private ProjectDesignSummaryService projectDesignSummaryService;

    @Log("导出报表")
    @ApiOperation("导出报表")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ProjectDesignSummaryQueryCriteria criteria) throws IOException {
        projectDesignSummaryService.download(response,criteria);
    }

    @Log("查询报表")
    @ApiOperation("查询报表")
    @GetMapping(value = "/queryAll")
    public ResponseEntity<Object> queryAll( ProjectDesignSummaryQueryCriteria criteria, Pageable pageable){
        return ResponseEntity.ok(projectDesignSummaryService.queryAll(criteria,pageable));
    }
}
