/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.domain.vo.CityProjectStatisticsVo;
import com.bassims.modules.atour.domain.vo.DownloadDocVo;
import com.bassims.modules.atour.domain.vo.ProjectPhaseCountVo;
import com.bassims.modules.atour.domain.vo.ProjectTypeCountVo;
import com.bassims.modules.atour.service.dto.*;
import com.bassims.modules.system.domain.Role;
import com.bassims.modules.system.domain.User;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务接口
 * @date 2022-03-24
 **/
public interface ProjectInfoService extends BaseService<ProjectInfo> {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(ProjectInfoQueryCriteria criteria, Pageable pageable);

    /**通过离职用户查询对应的项目*/
    List<ProjectInfoDto>  queryChangeStakeholders(ProjectInfoQueryCriteria criteria);

    /**
     * 查询小调整数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAdjustAll(ProjectInfoQueryCriteria criteria, Pageable pageable);

    /**
     * 查询带已删除数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAllForDelete(ProjectInfoQueryCriteria criteria, Pageable pageable);

    /**
     * 查询带结算数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryTask(ProjectInfoQueryCriteria criteria, Pageable pageable);

    /**
     * 通过projectId处理项目结算
     *
     * @param ProjectId 条件
     * @return ProjectInfoDto
     */
    ProjectInfoDto taskQueryByProjectId(Long ProjectId);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<ProjectInfoDto>
     */
    List<ProjectInfoDto> queryAll(ProjectInfoQueryCriteria criteria);

    /**
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> queryAllForMobile(ProjectInfoQueryCriteria criteria, Pageable pageable);

    /**
     * 竣工资料
     *
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> queryAllForComplateData(ProjectInfoQueryCriteria criteria, Pageable pageable);

    /**
     * 根据ID查询
     *
     * @param projectId ID
     * @return ProjectInfoDto
     */
    ProjectInfoDto findById(Long projectId);

    public ProjectInfoDto findById(Long projectId, String nodeCode);

    /**
     * 创建
     *
     * @param resources /
     * @return ProjectInfoDto
     */
    ProjectInfoDto create(ProjectInfo resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(ProjectInfo resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Long[] ids);

    /**
     * 导出数据
     *
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<ProjectInfoDto> all, HttpServletResponse response) throws IOException;
    void downloadNew(HttpServletResponse response) throws IOException;

    /**
     * 起老系统项目
     *
     * @return
     */
    Boolean createSupplierProject(JhSystemEnum.SupplierTypeEnum typeEnum, Long id);

    /**
     * 创建项目
     *
     * @param resources
     * @return
     */
    Boolean createProject(ProjectInfoDto resources, List<TemplateCollection> templateCollections) throws IllegalAccessException;

    Boolean createProjectSta(ProjectInfoInsertStaDto resources);

    Boolean saveRefreshShopRotation(ProjectTemplateCodeDto dto);
    /**
     * 重新设定项目各个节点的计划开始时间和计划结束时间
     * @return
     */
//    boolean newProjectTime(Long projectId, String startTime);
    List<TemplateCollection>  findTemplateByConditionCode(ProjectInfoDto resources, String... conditions) throws IllegalAccessException;

    List<ProjectTableNodeInfo> getDynamicMaskClick(Long projectId, String nodeCode);

    Boolean saveDynamicMaskClick(List<ProjectTableNodeInfo> projectTableNodeInfos);

    List<ProjectTableNodeInfo> getDynamicMaskRectificationList(Long projectId, Long parentId,String nodeCode);

    /**
     * 创建项目
     *
     * @param resources
     * @return
     */
    Boolean createAdjustProject(ProjectInfoDto resources);

    /**
     * 获取项目信息（项目抬头组件使用）
     *
     * @param projectId
     * @return
     */
    ProjectInfoDto findByIdForTitle(Long projectId);

    /**
     * 软删除
     *
     * @param id
     * @param deleteFlag
     */
    void deleteFlag(Long id, String deleteFlag);

    /**
     * 获取所有需要的数字值
     *
     * @return
     */
    Map<String, Integer> getTotalStatistics();

    ProjectNodePhaseDto getOverDuePercent(String date);

    ProjectNodePhaseDto getPhasePercent(String date);

    ProjectPhaseCountVo getPhasePercentWithProjectType(String projectType);

    ProjectTypeCountVo getPercentByProjectType();

    List<CityProjectStatisticsDto> getCityProjectStatistics();

    List<CityProjectStatisticsVo> getCityStoreProjectStatistics();

    void createProjectStakeholders(Long projectId, String roleName, Long userid, String downCode);

    Boolean updateAllProject();

    //插入系统所需干系人
    void createIsNotShowSta(Long projectId, Long city, String roleCode, Boolean isNotShow);

    /**
     * 结算首页total
     *
     * @return
     */
    Map<String, Integer> getTotalStatisticsAccount();


    ProjectAccountNodePhaseDto getAccountOverDuePercent(String date);

    ProjectAccountNodePhaseDto getAccountPhasePercent(String date);


    List<CityProjectAccountStatisticsDto> getCityProjectAccountStatistics();

    Map<String, Object> importProjectExcel(MultipartFile file) throws Exception;

    List<ProjectInfoDto> getProjectInfoByStore(ProjectInfoQueryCriteria criteria);

    Map<String, String> getConstructionManagerAndPhone(Long projectId);

    List<ProjectInfoDto> getProjectInfoByStoreNoAndName(ProjectInfoQueryCriteria criteria);

    Map<String, Object> queryNoSheetList(ProjectInfoQueryCriteria criteria, Pageable pageable);

    Role getRolePermission(Long currentUserId);

    /**
     * 获取doc文件全路径
     *
     * @param downloadDocVo
     * @return
     */
    String getFilePath(DownloadDocVo downloadDocVo);

    void test(String nodeCode);

    void downloadProjectByNode(HttpServletResponse response, ProjectInfoQueryCriteria criteria);

    MasterDrawingInfoDto getDrawingInfo(Long drawId);

    /**
     * 存入审批人/操作人
     */
    void saveProjectStakeholders(ProjectInfo resources) throws IllegalAccessException;

    Boolean saveProjectStakeholdersUsers(StakeholdersReq resources);

    User insertProjectUser();

    List<ConditionsRelation> getConditionsRelation();

    Boolean saveDrawingReviewData(List<ProjectInfoAbarbeitung> projectTableNodeInfos);


    List<ProjectInfoAbarbeitung> getDrawingReviewData(Long projectId, String nodeCode, String nodeId);

    List<ProjectNodeInfoApprovalRejection> getApproveRejectedVersions(Long projectId, String approveId);

    List<ProjectInfoAbarbeitungApprovalRejection> getDrawingReviewDataVersions(Long projectId, String nodeCode, String nodeId);


    Map<String, List<ProjectMaterialManagement>> getMaskPopupClick(Long projectId, String nodeCode);

    Boolean saveProject(TemplateCollection collection, Long projectId, String projectType, ProjectInfoDto resources
            , Long createProjectProjectGroupId, ProjectInfo projectInfo, String projectType1, Long storeMasterParam, ProjectInfoExpansion developmentSystem) throws IOException;

    /**
     * 获取该项目的所有计划节点和对应的时间节点
     * @param projectId
     * @return
     */
    List<ProjectNodeInfoDto> getProgressTimeByProjectId(Long projectId);

    Boolean saveProjectSheJi(ProjectInfoInsertStaDto resources);

    Boolean queryUnqualifiedDownloadState() throws InterruptedException;

    void downloadProject(HttpServletRequest request, HttpServletResponse response) throws IOException;
}
