/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service;

import cn.hutool.core.lang.tree.Tree;
import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.service.dto.*;
import com.bassims.modules.system.service.dto.UserDto;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务接口
 * @date 2022-03-24
 **/
public interface ProjectNodeInfoService extends BaseService<ProjectNodeInfo> {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(ProjectNodeInfoQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<ProjectNodeInfoDto>
     */
    List<ProjectNodeInfoDto> queryAll(ProjectNodeInfoQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param nodeId ID
     * @return ProjectNodeInfoDto
     */
    ProjectNodeInfoDto findById(Long nodeId);



    /**
     * 创建
     *
     * @param resources /
     * @return ProjectNodeInfoDto
     */
    ProjectNodeInfoDto create(ProjectNodeInfo resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(ProjectNodeInfo resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Long[] ids);

    /**
     * 导出数据
     *
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<ProjectNodeInfoDto> all, HttpServletResponse response) throws IOException;

    /**
     * 点击看板进入2、3级详情
     *
     * @param projectId
     * @param useCase
     * @param isMobile
     * @param deep
     * @return
     */
    List<Tree<String>> projectTree(Long projectId, String useCase, Boolean isMobile, int deep);

    Long getSubmeterProjectId(Long projectId);

    List<Tree<String>> projectTree1(Long projectId, String useCase, Boolean isMobile, int deep, Long templateId, String nodeCode, Long nodeId, String roundMarking);
    List<Tree<String>> projectTree2(Long projectId, String useCase, Boolean isMobile, int deep, Long templateId, String nodeCode, Long nodeId, String roundMarking);

    List<List<Tree<String>>> projectTreeForBoard(Long projectId, String useCase, Boolean isMobile, int deep);
    List<List<Tree<String>>> projectTreeForBoardNew(Long projectId, String useCase, Boolean isMobile, int deep);

    /**
     * 进度条接口方法
     *
     * @param projectId
     * @param useCase
     * @param isMobile
     * @return
     */
    ProjectNodeForProgress projectTreeProgress(Long projectId, String useCase, Boolean isMobile);

    ProjectNodeInfoDto projectTreeByNodeCode(Long projectId, String nodeCode, String useCase, Boolean isMobile,String roundMarking);

    String changeUserName(UserDto userDto, String username);

    List<ProjectNodeInfo> updateData(List<ProjectNodeInfo> list, Boolean isCommit);

    Boolean createUserAddData(List<ProjectNodeInfoDto> list);

    ProjectNodeInfoDto submit(ProjectNodeInfoDto projectNodeInfoD);

    ProjectNodeInfoDto adjustSubmit(ProjectNodeInfoDto projectNodeInfoD);

    Map<String,List> projectTreeForSchedule(Long projectId);

    void updateRoleCode(Long projectId, Long userid, Long oldUserId);
    //回退当前节点状态
    Boolean fallbackStatus(ProjectNodeInfoDto projectNodeInfoDto);

    void updateGrantt(ProjectNodeInfo projectNodeInfo, String type);

    //更新节点状态
    void updateNodeStatus(ProjectApprove projectApprove);

    //更新当前节点状态，开下一个节点
    void updateGroupStatusNext(ProjectGroup projectGroup);

    //拒绝 更新当前提交状态，回退
    void updateGroupStatusBack(ProjectGroup projectGroup);

    /**
     * 根据项目id、父级id查询
     *
     * @param projectId
     * @param parentId
     * @return
     */
    List<ProjectNodeInfoDto> getMeasureAreaValue(Long projectId, Long parentId);

    //小调整完成不生成审批，直接完成节点
    ProjectNodeInfoDto submitNoApprove(ProjectNodeInfoDto projectNodeInfoD);


    Map<String, Object> getQueryTimeNode(String projectId);

    String pushCompletionAcceptance(ProjectGroup projectGroup, ProjectInfoPushCompletionAcceptanceDTO acceptanceDTO) throws JsonProcessingException;

    String pushBuildContacts(ProjectGroup projectGroup, ProjectInfoPushFranchiseUserDTO franchiseUserDTO) ;

    ProjectNodeInfo getThreeRoleCode(Long projectId,String parentId,String roleCode);

    List<ProjectNodeInfo> getConventionalInformation(Long projectId);

    List<ProjectNodeInfo>  getDeepeningList(Long projectId);

    List<ProjectNodeInfo> documentManage(Long projectId);

    List<ProjectNodeInfo> getEngineeringRectificationIssues(Long projectId);

    Map<String, Object> getNodeIdByPdf(Long projectId);

    void broughtOutXmjlToCol(ProjectNodeInfo nodeInfo, Long projectId);

    void saveCompletionReceiptByNewVersion(Long projectId);

    void updateRemark(ProjectNodeInfo projectNodeInfo);


}