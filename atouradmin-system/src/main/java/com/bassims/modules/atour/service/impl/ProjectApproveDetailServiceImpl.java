/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.domain.ProjectApproveGroup;
import com.bassims.modules.atour.repository.ProjectApproveDetailRepository;
import com.bassims.modules.atour.repository.ProjectApproveGroupRepository;
import com.bassims.modules.atour.repository.ProjectInfoRepository;
import com.bassims.modules.atour.repository.ProjectParentGroupRepository;
import com.bassims.modules.atour.service.*;
import com.bassims.modules.atour.service.dto.*;
import com.bassims.modules.atour.service.mapstruct.ProjectApproveDetailMapper;
import com.bassims.modules.atour.service.mapstruct.ProjectGroupMapper;
import com.bassims.modules.system.domain.Role;
import com.bassims.modules.system.repository.RoleRepository;
import com.bassims.modules.system.service.UserService;
import com.bassims.modules.system.service.dto.UserDto;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-03-28
 **/
@Service
public class ProjectApproveDetailServiceImpl extends BaseServiceImpl<ProjectApproveDetailRepository, ProjectApproveDetail> implements ProjectApproveDetailService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectApproveDetailServiceImpl.class);

    @Autowired
    private ProjectApproveDetailRepository projectApproveDetailRepository;
    @Autowired
    private ProjectApproveDetailMapper projectApproveDetailMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private ProjectAppTemplateService projectAppTemplateService;
    @Autowired
    private ProjectInfoRepository projectInfoRepository;
    @Autowired
    private ProjectTaskService projectTaskService;
    @Autowired
    private ProjectStakeholdersService projectStakeholdersService;
    @Autowired
    private ProjectApproveGroupRepository projectApproveGroupRepository;
    @Autowired
    private ProjectParentGroupRepository projectParentGroupRepository;
    @Autowired
    private ProjectApproveService projectApproveService;
    @Autowired
    private ProjectNodeInfoService projectNodeInfoService;
    @Autowired
    private ProjectGroupMapper projectGroupMapper;
    @Autowired
    private ProjectTemplateApproveRelationService projectTemplateApproveRelationService;
    @Autowired
    private ApproveTemplateDetailService approveTemplateDetailService;

    @Override
    public Map<String, Object> queryAll(ProjectApproveDetailQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<ProjectApproveDetail> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectApproveDetail.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectApproveDetailMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectApproveDetailDto> queryAll(ProjectApproveDetailQueryCriteria criteria) {
        List<ProjectApproveDetailDto> list = projectApproveDetailMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectApproveDetail.class, criteria)));
        for (ProjectApproveDetailDto dto : list) {
            if (null != dto.getApproveUser()) {
                UserDto UserDto = userService.findById(dto.getApproveUser());
                if (null != UserDto) {
                    dto.setApproveUserName(UserDto.getUsername());
                }
                Role roleByRoleId = roleRepository.findRoleByRoleId(Long.parseLong(dto.getApproveRole()));
                if (roleByRoleId != null) {
                    dto.setApproveRoleName(roleByRoleId.getName());
                }
            }
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectApproveDetailDto findById(Long approveDetailId) {
        ProjectApproveDetail projectApproveDetail = Optional.ofNullable(getById(approveDetailId)).orElseGet(ProjectApproveDetail::new);
        ValidationUtil.isNull(projectApproveDetail.getApproveDetailId(), getEntityClass().getSimpleName(), "approveDetailId", approveDetailId);
        return projectApproveDetailMapper.toDto(projectApproveDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectApproveDetailDto create(ProjectApproveDetail resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setApproveDetailId(snowflake.nextId());
        save(resources);
        return findById(resources.getApproveDetailId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectApproveDetail resources) {
        ProjectApproveDetail projectApproveDetail = Optional.ofNullable(getById(resources.getApproveDetailId())).orElseGet(ProjectApproveDetail::new);
        ValidationUtil.isNull(projectApproveDetail.getApproveDetailId(), "ProjectApproveDetail", "id", resources.getApproveDetailId());
        projectApproveDetail.copy(resources);
        updateById(projectApproveDetail);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long approveDetailId : ids) {
            projectApproveDetailRepository.deleteById(approveDetailId);
        }
    }

    @Override
    public void download(List<ProjectApproveDetailDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectApproveDetailDto projectApproveDetail : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("审批主键", projectApproveDetail.getApproveId());
            map.put("项目审批模板主键", projectApproveDetail.getApproveNodeId());
            map.put("审批组主键", projectApproveDetail.getApproveGroupId());
            map.put("审批子模板主键", projectApproveDetail.getApproveTemplateDetailId());
            map.put("父节点id", projectApproveDetail.getParentId());
            map.put("是否有子集", projectApproveDetail.getHasChild());
            map.put("审批角色", projectApproveDetail.getApproveRole());
            map.put("审批角色", projectApproveDetail.getApproveRoleName());
            map.put("审批人id", projectApproveDetail.getApproveUser());
            map.put("审批状态", projectApproveDetail.getApproveStatus());
            map.put("审批结果", projectApproveDetail.getApproveResult());
            map.put("审批时间", projectApproveDetail.getApproveEnd());
            map.put("审批意见", projectApproveDetail.getApproveOption());
            map.put("审批排序", projectApproveDetail.getApproveIndex());
            map.put("该审批角色是否可以修改", projectApproveDetail.getIsModifiable());
            map.put("可修改的字段code", projectApproveDetail.getModifiableCode());
            map.put(" createTime", projectApproveDetail.getCreateTime());
            map.put(" createBy", projectApproveDetail.getCreateBy());
            map.put(" updateTime", projectApproveDetail.getUpdateTime());
            map.put(" updateBy", projectApproveDetail.getUpdateBy());
            map.put("是否可用", projectApproveDetail.getIsEnabled());
            map.put("是否删除", projectApproveDetail.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    /*@Override
    public Boolean createApproveGroup(ProjectGroupDto projectGroupDto, ProjectApprove projectApprove, ProjectParentGroup bigGroup, String level) {
        LambdaQueryWrapper<ProjectAppTemplate> appTemplateLambdaQueryWrapper= Wrappers.lambdaQuery(ProjectAppTemplate.class);
        appTemplateLambdaQueryWrapper.eq(ProjectAppTemplate::getNodeId,projectGroupDto.getProjectGroupId())
        .eq(ProjectAppTemplate::getApproveLevel,level)
        .orderBy(true,true,ProjectAppTemplate::getApproveIndex);
        List<ProjectAppTemplate> list1 = projectAppTemplateService.list(appTemplateLambdaQueryWrapper);
        Map<String, List<ProjectAppTemplate>> collect = list1.stream().collect(Collectors.groupingBy(ProjectAppTemplate::getApproveGroup));
        String parentGroup = "0";
        for (Map.Entry<String, List<ProjectAppTemplate>> entry : collect.entrySet()) {
            Long parentGroupId=0L;
            ProjectParentGroup projectParentGroup=new ProjectParentGroup();
            Boolean flag=Boolean.FALSE;
            if((!parentGroup.equals(entry.getKey()))&&"1".equals(level)){
                flag=Boolean.TRUE;
                Snowflake snow = IdUtil.getSnowflake(1, 1);
                parentGroup = entry.getKey();
                parentGroupId=snow.nextId();
                projectParentGroup.setParentGroup(parentGroup);
                projectParentGroup.setParentGroupId(parentGroupId);
                projectParentGroup.setApproveId(projectApprove.getApproveId());
                projectParentGroup.setProjectId(projectGroupDto.getProjectId() == null ? null : Long.parseLong(projectGroupDto.getProjectId()));
                projectParentGroup.setOrderId(projectGroupDto.getOrderId() == null ? null :Long.parseLong(projectGroupDto.getOrderId()));
                projectParentGroup.setNodeId(Long.parseLong(projectGroupDto.getProjectGroupId()));
                projectParentGroup.setApproveStatus(JhSystemEnum.approveStatusEnum.IN_APPROVE.getKey());
                projectParentGroup.setIsDelete(Boolean.FALSE);
            }

        *//*}
        Iterator<Map.Entry<String, List<ProjectAppTemplate>>> entries = collect.entrySet().iterator();
        while (entries.hasNext()) {
            //按组插入
            Map.Entry<String, List<ProjectAppTemplate>> entry = entries.next();*//*
            List<ProjectAppTemplate> value = entry.getValue();
            Snowflake snowflake = IdUtil.getSnowflake(1, 1);
            Long approveGroupId=snowflake.nextId();
            ProjectApproveGroup projectApproveGroup=new ProjectApproveGroup();
            projectApproveGroup.setApproveGroupId(approveGroupId);
            projectApproveGroup.setApproveId(projectApprove.getApproveId());
            projectApproveGroup.setApproveGroup(value.get(0).getApproveGroup());
            projectApproveGroup.setApproveLevel(value.get(0).getApproveLevel());
            projectApproveGroup.setApproveBegin(value.get(0).getApproveBegin());
            projectApproveGroup.setApproveMode(value.get(0).getApproveMode());
            projectApproveGroup.setProjectId(projectGroupDto.getProjectId() == null ? null : Long.parseLong(projectGroupDto.getProjectId()));
            projectApproveGroup.setOrderId(projectGroupDto.getOrderId() == null ? null :Long.parseLong(projectGroupDto.getOrderId()));
            projectApproveGroup.setNodeId(Long.parseLong(projectGroupDto.getProjectGroupId()));
            if("1".equals(projectApproveGroup.getApproveLevel())&&"1".equals(projectApproveGroup.getApproveGroup())){
                projectApproveGroup.setApproveStatus(JhSystemEnum.approveStatusEnum.IN_APPROVE.getKey());
                projectApproveGroup.setApproveResult(JhSystemEnum.approveResultEnum.UNDER_APPROVE.getKey());
            }

            for(ProjectAppTemplate detail:value){
                Role one = roleRepository.getOne(Long.parseLong(detail.getApproveRole()));
                LambdaQueryWrapper<ProjectStakeholders> stakeholdersLambdaQueryWrapper= Wrappers.lambdaQuery(ProjectStakeholders.class);
                if(projectGroupDto.getOrderId()!=null){
                    stakeholdersLambdaQueryWrapper.eq(ProjectStakeholders::getOrderId,projectGroupDto.getOrderId())
                            .eq(ProjectStakeholders::getRoleCode,one.getRoleCode())
                            .eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
                }else {
                    stakeholdersLambdaQueryWrapper.eq(ProjectStakeholders::getProjectId,projectGroupDto.getProjectId()).isNull(ProjectStakeholders::getOrderId)
                            .eq(ProjectStakeholders::getRoleCode,one.getRoleCode())
                            .eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
                }

                List<ProjectStakeholders> list = projectStakeholdersService.list(stakeholdersLambdaQueryWrapper);

                if(list.size()==0){
                    throw new BadRequestException("当前项目没有配置"+one.getName());
                }
                for(ProjectStakeholders projectStakeholders:list){
                    Role role= projectInfoRepository.getRoleByRoleCode(projectStakeholders.getRoleCode());
                    ProjectApproveDetail projectApproveDetail=new ProjectApproveDetail();

                    projectApproveDetail.setApproveId(projectApprove.getApproveId());
                    projectApproveDetail.setApproveStatus(JhSystemEnum.approveStatusEnum.IN_APPROVE.getKey());
                    projectApproveDetail.setApproveResult(JhSystemEnum.approveResultEnum.UNDER_APPROVE.getKey());
                    projectApproveDetail.setApproveUser(projectStakeholders.getUserId());
                    projectApproveDetail.setApproveRole(role.getId().toString());
                    projectApproveDetail.setApproveRoleName(role.getName());
                    projectApproveDetail.setApproveNodeId(detail.getApproveNodeId());
                    projectApproveDetail.setApproveGroupId(approveGroupId);
                    projectApproveDetail.setApproveNodeId(detail.getApproveNodeId());
                    projectApproveDetail.setApproveIndex(detail.getApproveIndex());
                    projectApproveDetail.setApproveTemplateDetailId(detail.getApproveTemplateDetailId());
                    projectApproveDetail.setParentId(detail.getParentId());
                    projectApproveDetail.setHasChild(detail.getHasChild());
                    projectApproveDetail.setIsModifiable(detail.getIsModifiable());
                    projectApproveDetail.setModifiableCode(detail.getModifiableCode());

                    projectApproveDetailRepository.insert(projectApproveDetail);
                    if("1".equals(detail.getApproveLevel())){
                        if(JhSystemEnum.approveBeginEnum.ALL_EXCUTE.getKey().equals(detail.getApproveBegin())&&"1".equals(detail.getApproveGroup())){
                            //创建待办
                            ProjectApproveDetailDto projectApproveDetailDto = projectApproveDetailMapper.toDto(projectApproveDetail);
                            projectApproveDetailDto.setApproveRole(role.getId().toString());
                            projectApproveDetailDto.setApproveRoleName(role.getName());
                            //todo 待办
                            //projectTaskService.createAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK,projectApproveDetailDto);
                           // projectTaskService.createNoticeAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK,projectApproveDetailDto);
                        }else if(JhSystemEnum.approveBeginEnum.IN_ORDER.getKey().equals(detail.getApproveBegin())&&detail.getApproveIndex()==1){
                            //创建待办
                            ProjectApproveDetailDto projectApproveDetailDto = projectApproveDetailMapper.toDto(projectApproveDetail);
                            projectApproveDetailDto.setApproveRole(role.getId().toString());
                            projectApproveDetailDto.setApproveRoleName(role.getName());
                            projectTaskService.createAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK,projectApproveDetailDto);
//                            projectTaskService.createNoticeAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK,projectApproveDetailDto);
                        }
                    }

                }
                if(detail.getHasChild()!=null&&detail.getHasChild()){
                    createApproveGroup( projectGroupDto,projectApprove, projectParentGroup, "2");
                }

            }
            if(flag&&"1".equals(level)){

                projectParentGroupRepository.insert(projectParentGroup);
                projectApproveGroup.setParentGroupId(parentGroupId);
                flag=Boolean.FALSE;
            }else if("2".equals(level)){
                projectApproveGroup.setParentGroupId(bigGroup.getParentGroupId());
            }else {
                projectApproveGroup.setParentGroupId(parentGroupId);
            }

            projectApproveGroupRepository.insert(projectApproveGroup);

        }
        return null;
    }
*/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createApproveGroup(ProjectGroupDto projectGroupDto, ProjectApprove projectApprove, ProjectParentGroup bigGroup, String level) {

        //24.09.03修改：获取审批信息位置由立项位置改到提交位置
        //1.获取当前任务对应的审批主信息的主键id
        List<ProjectTemplateApproveRelationDto> appRelation = projectTemplateApproveRelationService.getAppRelation(Long.valueOf(projectGroupDto.getTemplateId()), Long.valueOf(projectGroupDto.getTemplateGroupId()));
        Long approveTemplateId = appRelation.stream().map(ProjectTemplateApproveRelationDto::getApproveMatrixId).findFirst().orElse(null);
        //2.根据审批主键id找到关联的审批角色信息
        if (null == approveTemplateId) {
            return Boolean.FALSE;
        }
        Map<String, List<ApproveTemplateDetailDto>> collect = approveTemplateDetailService.getApproveTemplateDetail(approveTemplateId).stream().collect(Collectors.groupingBy(ApproveTemplateDetailDto::getApproveGroup));

        /*LambdaQueryWrapper<ProjectAppTemplate> appTemplateLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectAppTemplate.class);
        appTemplateLambdaQueryWrapper.eq(ProjectAppTemplate::getNodeId, projectGroupDto.getProjectGroupId())
                .eq(ProjectAppTemplate::getApproveLevel, level)
                .orderBy(true, true, ProjectAppTemplate::getApproveIndex);
        List<ProjectAppTemplate> list1 = projectAppTemplateService.list(appTemplateLambdaQueryWrapper);

        Map<String, List<ProjectAppTemplate>> collect = list1.stream().collect(Collectors.groupingBy(ProjectAppTemplate::getApproveGroup));*/
        String parentGroup = "0";
        for (Map.Entry<String, List<ApproveTemplateDetailDto>> entry : collect.entrySet()) {
            Long parentGroupId = 0L;
            ProjectParentGroup projectParentGroup = new ProjectParentGroup();
            Boolean flag = Boolean.FALSE;
            if ((!parentGroup.equals(entry.getKey())) && "1".equals(level)) {
                flag = Boolean.TRUE;
                Snowflake snow = IdUtil.getSnowflake(1, 1);
                parentGroup = entry.getKey();
                parentGroupId = snow.nextId();
                projectParentGroup.setParentGroup(parentGroup);
                projectParentGroup.setParentGroupId(parentGroupId);
                projectParentGroup.setApproveId(projectApprove.getApproveId());
                projectParentGroup.setProjectId(projectGroupDto.getProjectId() == null ? null : Long.parseLong(projectGroupDto.getProjectId()));
                projectParentGroup.setOrderId(projectGroupDto.getOrderId() == null ? null : Long.parseLong(projectGroupDto.getOrderId()));
                projectParentGroup.setNodeId(Long.parseLong(projectGroupDto.getProjectGroupId()));
                projectParentGroup.setApproveStatus(JhSystemEnum.approveStatusEnum.IN_APPROVE.getKey());
                projectParentGroup.setIsDelete(Boolean.FALSE);
            }

        /*}
        Iterator<Map.Entry<String, List<ProjectAppTemplate>>> entries = collect.entrySet().iterator();
        while (entries.hasNext()) {
            //按组插入
            Map.Entry<String, List<ProjectAppTemplate>> entry = entries.next();*/
            List<ApproveTemplateDetailDto> value = entry.getValue();
            Snowflake snowflake = IdUtil.getSnowflake(1, 1);
            Long approveGroupId = snowflake.nextId();
            ProjectApproveGroup projectApproveGroup = new ProjectApproveGroup();
            projectApproveGroup.setApproveGroupId(approveGroupId);
            projectApproveGroup.setApproveId(projectApprove.getApproveId());
            projectApproveGroup.setApproveGroup(value.get(0).getApproveGroup());
            projectApproveGroup.setApproveLevel(value.get(0).getApproveLevel());
            projectApproveGroup.setApproveBegin(value.get(0).getApproveBegin());
            projectApproveGroup.setApproveMode(value.get(0).getApproveMode());
            projectApproveGroup.setProjectId(projectGroupDto.getProjectId() == null ? null : Long.parseLong(projectGroupDto.getProjectId()));
            projectApproveGroup.setOrderId(projectGroupDto.getOrderId() == null ? null : Long.parseLong(projectGroupDto.getOrderId()));
            projectApproveGroup.setNodeId(Long.parseLong(projectGroupDto.getProjectGroupId()));
            if ("1".equals(projectApproveGroup.getApproveLevel()) && "1".equals(projectApproveGroup.getApproveGroup())) {
                projectApproveGroup.setApproveStatus(JhSystemEnum.approveStatusEnum.IN_APPROVE.getKey());
                projectApproveGroup.setApproveResult(JhSystemEnum.approveResultEnum.UNDER_APPROVE.getKey());
            }

            for (ApproveTemplateDetailDto detail : value) {
                Role one = roleRepository.findRoleByRoleId(Long.parseLong(detail.getApproveRole()));

                List<ProjectStakeholders> list = new ArrayList<>();
                List<ProjectStakeholders> listLeave = new ArrayList<>();
                if (one.getRoleCode().equals(JhSystemEnum.RoleCodeEnum.CJGLY.getKey())) {
                    List<UserDto> dtoList = userService.findByRoleCodes(one.getRoleCode());
                    for (UserDto userDto : dtoList) {
                        ProjectStakeholders projectStakeholders = new ProjectStakeholders();
                        projectStakeholders.setUserId(userDto.getId());
                        projectStakeholders.setRoleCode(one.getRoleCode());
                        list.add(projectStakeholders);
                    }
                } else {
                    LambdaQueryWrapper<ProjectStakeholders> stakeholdersLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class);
                    //查询离项人员 干系人更换审批问题bug 修复 2023/08/04 陆
                    LambdaQueryWrapper<ProjectStakeholders> stakeholdersLambdaLeave = Wrappers.lambdaQuery(ProjectStakeholders.class);
                    if (projectGroupDto.getOrderId() != null) {
                        stakeholdersLambdaQueryWrapper.eq(ProjectStakeholders::getOrderId, projectGroupDto.getOrderId())
                                .eq(ProjectStakeholders::getRoleCode, one.getRoleCode())
                                .eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());

                        stakeholdersLambdaLeave.eq(ProjectStakeholders::getProjectId, projectGroupDto.getProjectId()).isNull(ProjectStakeholders::getOrderId)
                                .eq(ProjectStakeholders::getRoleCode, one.getRoleCode())
                                .eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS1.getKey());
                    } else {
                        stakeholdersLambdaQueryWrapper.eq(ProjectStakeholders::getProjectId, projectNodeInfoService.getSubmeterProjectId(Long.valueOf(projectGroupDto.getProjectId()))).isNull(ProjectStakeholders::getOrderId)
                                .eq(ProjectStakeholders::getRoleCode, one.getRoleCode())
                                .eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());

                        stakeholdersLambdaLeave.eq(ProjectStakeholders::getProjectId, projectGroupDto.getProjectId()).isNull(ProjectStakeholders::getOrderId)
                                .eq(ProjectStakeholders::getRoleCode, one.getRoleCode())
                                .eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS1.getKey());
                    }
                    list = projectStakeholdersService.list(stakeholdersLambdaQueryWrapper);
                    listLeave = projectStakeholdersService.list(stakeholdersLambdaLeave);

                }
                int approveIndex = 1;

                if (list.size() == 0) {
                    throw new BadRequestException("当前项目没有配置" + one.getName());
                }
                // 查询之前同节点是否有审批，如果有，则判断审批人是否已审批，如果已经审批通过，则此次创建审批详情时要将之前审批通过的结果进行同步
//                List<Long> approveUserIds = null;
//                Map<Long, List<ProjectApproveDetail>> projectApproveDetailMap = new HashMap<>();
//                LambdaQueryWrapper<ProjectApprove> projectApproveLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApprove.class);
//                projectApproveLambdaQueryWrapper.eq(ProjectApprove::getNodeId, projectApprove.getNodeId());
//                projectApproveLambdaQueryWrapper.orderBy(true, false, ProjectApprove::getCreateTime);
//                  List<ProjectApprove> projectApproveList = projectApproveService.list(projectApproveLambdaQueryWrapper);
//                  List<ProjectApprove> projectApproveList = projectApproveService.queryInfoByNodeId(projectApprove.getNodeId());
//
//                if (CollUtil.isNotEmpty(projectApproveList)) {
//                    // 获取最新的审核
//                    ProjectApprove lastProjectApprove = projectApproveList.get(0);
//                    // 获取审核通过详情
//                    LambdaQueryWrapper<ProjectApproveDetail> projectApproveDetailLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class);
//                    projectApproveDetailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveId, lastProjectApprove.getApproveId());
////                    projectApproveDetailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveResult, JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey());
//                    List<ProjectApproveDetail> projectApproveDetailList = list(projectApproveDetailLambdaQueryWrapper);
//                    if (ObjectUtil.isNotEmpty(projectApproveDetailList)) {
//                        projectApproveDetailMap = projectApproveDetailList.stream().collect(Collectors.groupingBy(u -> ObjectUtil.isEmpty(u.getApproveUser()) ? Long.valueOf("0") : u.getApproveUser()));
//                        List<ProjectApproveDetail> collect1 = projectApproveDetailList.stream().filter(e -> JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey().equals(e.getApproveResult()) && e.getApproveIndex() != null).collect(Collectors.toList());
//                        if (CollUtil.isNotEmpty(collect1)) {
//                            Integer maxIndex = projectApproveDetailList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getApproveIndex()) && JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey().equals(e.getApproveResult())).map(ProjectApproveDetail::getApproveIndex).max(Integer::compareTo).get();
//                            // approveUserIds = projectApproveDetailList.stream().map(ProjectApproveDetail::getApproveUser).collect(Collectors.toList());
//                            approveIndex = maxIndex + 1;
//                        }
//                    }
//                }

                for (ProjectStakeholders projectStakeholders : list) {
//                    logger.info("=============projectStakeholders:{}",projectStakeholders);
                    ProjectStakeholders projectStakeholdersLeave = new ProjectStakeholders();
                    Role role = projectInfoRepository.getRoleByRoleCode(projectStakeholders.getRoleCode());
                    ProjectApproveDetail projectApproveDetail = new ProjectApproveDetail();
                    //如果以前存在已审批通过的审批履历，那么把这些【审批通过】的记录重新赋值给最新的审批记录上  但是亚朵不需要这样的需求所以注释掉
//                    if (!projectApproveDetailMap.isEmpty()) {
//                        //是否存在离项已审批
//                        List<ProjectApproveDetail> projectApproveDetailListLeave= new ArrayList<>();
//                        List<ProjectApproveDetail> projectApproveDetailList = projectApproveDetailMap.get(projectStakeholders.getUserId());
//                        //如果项目更换为新的职位负责人则进行之前的审批详情表数据查询
//                        if (CollUtil.isNotEmpty(listLeave)){
//                            for (ProjectStakeholders stakeholders : listLeave) {
//                                if (CollUtil.isNotEmpty(projectApproveDetailListLeave)){
//                                    continue;
//                                }
//                                if (CollUtil.isNotEmpty(projectApproveDetailMap.get(stakeholders.getUserId()))){
//                                    BeanUtils.copyProperties(stakeholders,projectStakeholdersLeave);
//                                    projectApproveDetailListLeave = projectApproveDetailMap.get(stakeholders.getUserId());
//                                }
//
//                            }
//                        }
////                        Map<String, ProjectApproveDetail> collect1 = projectApproveDetailList.stream().collect(Collectors.toMap(ProjectApproveDetail::getApproveRole, e -> e));
////                        ProjectApproveDetail projectApproveDetail1 = Optional.ofNullable(collect1.get(projectStakeholders.getRoleId().toString())).orElseGet(ProjectApproveDetail::new);
//                        if (ObjectUtil.isNotEmpty(projectApproveDetailList)) {
////                            List<ProjectApproveDetail> projectApproveDetailList = projectApproveDetailMap.get(projectStakeholders.getUserId());
//                            Map<String, ProjectApproveDetail> collect1 = projectApproveDetailList.stream().collect(Collectors.toMap(ProjectApproveDetail::getApproveRole, e -> e));
//                            ProjectApproveDetail projectApproveDetail1 = Optional.ofNullable(collect1.get(projectStakeholders.getRoleId().toString())).orElseGet(ProjectApproveDetail::new);
////                            projectApproveDetail.setApproveStatus(JhSystemEnum.approveStatusEnum.APPROVE_COMPLETE.getKey());
////                            projectApproveDetail.setApproveResult(JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey());
////                            projectApproveDetail.setApproveOption(JhSystemEnum.approveResultEnum.APPROVE_PASS.getSpec());
//                            String roleId = projectStakeholders.getRoleId().toString();
//                            if (roleId.equals(projectApproveDetail1.getApproveRole()) && JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey().equals(projectApproveDetail1.getApproveResult())) {
//                                projectApproveDetail.setApproveStatus(JhSystemEnum.approveStatusEnum.APPROVE_COMPLETE.getKey());
//                                projectApproveDetail.setApproveResult(JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey());
//                                projectApproveDetail.setApproveOption(projectApproveDetail1.getApproveOption());
//                                projectApproveDetail.setApproveEnd(projectApproveDetail1.getApproveEnd());
//
//                            }else if (roleId.equals(projectApproveDetail1.getApproveRole())){
//                                projectApproveDetail.setApproveStatus(JhSystemEnum.approveStatusEnum.IN_APPROVE.getKey());
//                                projectApproveDetail.setApproveResult(JhSystemEnum.approveResultEnum.UNDER_APPROVE.getKey());
//                            }else{
//                                continue;
//                            }
//                        }else if(CollUtil.isNotEmpty(projectApproveDetailListLeave)){
//                            String roleIdLeaveUse = projectStakeholdersLeave.getRoleId().toString();
//                            Map<String, ProjectApproveDetail> collect1 = projectApproveDetailListLeave.stream().collect(Collectors.toMap(ProjectApproveDetail::getApproveRole, e -> e));
//                            ProjectApproveDetail projectApproveDetail1 = Optional.ofNullable(collect1.get(roleIdLeaveUse)).orElseGet(ProjectApproveDetail::new);
////                            projectApproveDetail.setApproveStatus(JhSystemEnum.approveStatusEnum.APPROVE_COMPLETE.getKey());
////                            projectApproveDetail.setApproveResult(JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey());
////                            projectApproveDetail.setApproveOption(JhSystemEnum.approveResultEnum.APPROVE_PASS.getSpec());
////                            String roleId = projectStakeholders.getRoleId().toString();
//                            if (roleIdLeaveUse.equals(projectApproveDetail1.getApproveRole()) && JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey().equals(projectApproveDetail1.getApproveResult())) {
//                                projectApproveDetail.setApproveStatus(JhSystemEnum.approveStatusEnum.APPROVE_COMPLETE.getKey());
//                                projectApproveDetail.setApproveResult(JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey());
//                                projectApproveDetail.setApproveOption(projectApproveDetail1.getApproveOption());
//                                projectApproveDetail.setApproveEnd(projectApproveDetail1.getApproveEnd());
//
//                            }else if (roleIdLeaveUse.equals(projectApproveDetail1.getApproveRole())){
//                                projectApproveDetail.setApproveStatus(JhSystemEnum.approveStatusEnum.IN_APPROVE.getKey());
//                                projectApproveDetail.setApproveResult(JhSystemEnum.approveResultEnum.UNDER_APPROVE.getKey());
//                            }else{
//                                continue;
//                            }
//                        }
//                       // 1
//                        else {
//                            continue;
//                        }
////                        approveIndex = projectApproveDetail1.getApproveIndex() + 1;
//                    } else {
                    projectApproveDetail.setApproveStatus(JhSystemEnum.approveStatusEnum.PENDING_APPROVAL.getKey());
                    projectApproveDetail.setApproveResult(JhSystemEnum.approveResultEnum.PENDING_APPROVAL.getKey());
//                    }
                    projectApproveDetail.setApproveId(projectApprove.getApproveId());
                    projectApproveDetail.setApproveUser(projectStakeholders.getUserId());
                    projectApproveDetail.setApproveRole(role.getId().toString());
                    projectApproveDetail.setApproveRoleName(role.getName());
                    //projectApproveDetail.setApproveNodeId(detail.getApproveNodeId());
                    projectApproveDetail.setApproveGroupId(approveGroupId);
                    //projectApproveDetail.setApproveNodeId(detail.getApproveNodeId());
                    projectApproveDetail.setApproveIndex(detail.getApproveIndex());
                    projectApproveDetail.setApproveTemplateDetailId(detail.getApproveTemplateDetailId());
                    projectApproveDetail.setParentId(detail.getParentId());
                    projectApproveDetail.setHasChild(detail.getHasChild());
                    projectApproveDetail.setIsModifiable(detail.getIsModifiable());
                    projectApproveDetail.setModifiableCode(detail.getModifiableCode());
                    projectApproveDetail.setModifiableCodeTextarea(detail.getModifiableCodeTextarea());

                    projectApproveDetail.setIsShow(detail.getIsShow());
                    projectApproveDetail.setIsExistOpenCondition(detail.getIsExistOpenCondition());
                    projectApproveDetail.setApprovalOpeningConditionNodeCode(detail.getApprovalOpeningConditionNodeCode());
                    projectApproveDetail.setApprovalOpeningConditionValue(detail.getApprovalOpeningConditionValue());

                    projectApproveDetailRepository.insert(projectApproveDetail);
                    //级别等于1，判断需不需要出审批
                    if ("1".equals(detail.getApproveLevel())) {
                        if (JhSystemEnum.approveBeginEnum.ALL_EXCUTE.getKey().equals(detail.getApproveBegin()) && "1".equals(detail.getApproveGroup())) {
                            //创建待办
                            ProjectApproveDetailDto projectApproveDetailDto = projectApproveDetailMapper.toDto(projectApproveDetail);
                            projectApproveDetailDto.setApproveRole(role.getId().toString());
                            projectApproveDetailDto.setApproveRoleName(role.getName());
                            //todo 待办
                            //审批同时执行的待办
                            projectTaskService.createAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK, projectApproveDetailDto);

                            //需要审批，修改审批记录状态为 审批中
                            projectApproveDetail.setApproveStatus(JhSystemEnum.approveStatusEnum.IN_APPROVE.getKey());
                            projectApproveDetail.setApproveResult(JhSystemEnum.approveResultEnum.UNDER_APPROVE.getKey());

                            // projectTaskService.createNoticeAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK,projectApproveDetailDto);
                        } else if (JhSystemEnum.approveBeginEnum.IN_ORDER.getKey().equals(detail.getApproveBegin()) && detail.getApproveIndex() == approveIndex) {
                            //创建待办
                            ProjectApproveDetailDto projectApproveDetailDto = projectApproveDetailMapper.toDto(projectApproveDetail);
                            logger.info("=============projectApproveDetailDto:{}", projectApproveDetailDto);
                            projectApproveDetailDto.setApproveRole(role.getId().toString());
                            projectApproveDetailDto.setApproveRoleName(role.getName());
                            projectTaskService.createAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK, projectApproveDetailDto);

                            //需要审批，修改审批记录状态为 审批中
                            projectApproveDetail.setApproveStatus(JhSystemEnum.approveStatusEnum.IN_APPROVE.getKey());
                            projectApproveDetail.setApproveResult(JhSystemEnum.approveResultEnum.UNDER_APPROVE.getKey());

//                            projectTaskService.createNoticeAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK,projectApproveDetailDto);
                        }
                        projectApproveDetailRepository.updateById(projectApproveDetail);
                    }

                }
                if (detail.getHasChild() != null && detail.getHasChild()) {
                    createApproveGroup(projectGroupDto, projectApprove, projectParentGroup, "2");
                }

            }
            if (flag && "1".equals(level)) {

                projectParentGroupRepository.insert(projectParentGroup);
                projectApproveGroup.setParentGroupId(parentGroupId);
                flag = Boolean.FALSE;
            } else if ("2".equals(level)) {
                projectApproveGroup.setParentGroupId(bigGroup.getParentGroupId());
            } else {
                projectApproveGroup.setParentGroupId(parentGroupId);
            }

            projectApproveGroupRepository.insert(projectApproveGroup);

        }
        return null;
    }


    @Override
    public Boolean createOrderApproveGroup(ProjectGroupDto projectGroupDto, ProjectApprove projectApprove, ProjectParentGroup bigGroup, String level) {
        LambdaQueryWrapper<ProjectAppTemplate> appTemplateLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectAppTemplate.class);
        appTemplateLambdaQueryWrapper.eq(ProjectAppTemplate::getNodeId, projectGroupDto.getProjectGroupId())
                .eq(ProjectAppTemplate::getApproveLevel, level)
                .orderBy(true, true, ProjectAppTemplate::getApproveIndex);
        List<ProjectAppTemplate> list1 = projectAppTemplateService.list(appTemplateLambdaQueryWrapper);
        Map<String, List<ProjectAppTemplate>> collect = list1.stream().collect(Collectors.groupingBy(ProjectAppTemplate::getApproveGroup));
        String parentGroup = "0";
        for (Map.Entry<String, List<ProjectAppTemplate>> entry : collect.entrySet()) {
            Long parentGroupId = 0L;
            ProjectParentGroup projectParentGroup = new ProjectParentGroup();
            Boolean flag = Boolean.FALSE;
            if ((!parentGroup.equals(entry.getKey())) && "1".equals(level)) {
                flag = Boolean.TRUE;
                Snowflake snow = IdUtil.getSnowflake(1, 1);
                parentGroup = entry.getKey();
                parentGroupId = snow.nextId();
                projectParentGroup.setParentGroup(parentGroup);
                projectParentGroup.setParentGroupId(parentGroupId);
                projectParentGroup.setApproveId(projectApprove.getApproveId());
                projectParentGroup.setOrderId(projectApprove.getOrderId());
                projectParentGroup.setProjectId(projectGroupDto.getProjectId() == null ? null : Long.parseLong(projectGroupDto.getProjectId()));
                projectParentGroup.setNodeId(Long.parseLong(projectGroupDto.getProjectGroupId()));
                projectParentGroup.setApproveStatus(JhSystemEnum.approveStatusEnum.IN_APPROVE.getKey());
                projectParentGroup.setIsDelete(Boolean.FALSE);
            }

        /*}
        Iterator<Map.Entry<String, List<ProjectAppTemplate>>> entries = collect.entrySet().iterator();
        while (entries.hasNext()) {
            //按组插入
            Map.Entry<String, List<ProjectAppTemplate>> entry = entries.next();*/
            List<ProjectAppTemplate> value = entry.getValue();
            Snowflake snowflake = IdUtil.getSnowflake(1, 1);
            Long approveGroupId = snowflake.nextId();
            ProjectApproveGroup projectApproveGroup = new ProjectApproveGroup();
            projectApproveGroup.setApproveGroupId(approveGroupId);
            projectApproveGroup.setApproveId(projectApprove.getApproveId());
            projectApproveGroup.setApproveGroup(value.get(0).getApproveGroup());
            projectApproveGroup.setApproveLevel(value.get(0).getApproveLevel());
            projectApproveGroup.setApproveBegin(value.get(0).getApproveBegin());
            projectApproveGroup.setApproveMode(value.get(0).getApproveMode());
            projectApproveGroup.setProjectId(projectGroupDto.getProjectId() == null ? null : Long.parseLong(projectGroupDto.getProjectId()));
            projectApproveGroup.setOrderId(projectApprove.getOrderId());
            projectApproveGroup.setNodeId(Long.parseLong(projectGroupDto.getProjectGroupId()));
            if ("1".equals(projectApproveGroup.getApproveLevel()) && "1".equals(projectApproveGroup.getApproveGroup())) {
                projectApproveGroup.setApproveStatus(JhSystemEnum.approveStatusEnum.IN_APPROVE.getKey());
                projectApproveGroup.setApproveResult(JhSystemEnum.approveResultEnum.UNDER_APPROVE.getKey());
            }

            for (ProjectAppTemplate detail : value) {
                Role one = roleRepository.findRoleByRoleId(Long.parseLong(detail.getApproveRole()));
                LambdaQueryWrapper<ProjectStakeholders> stakeholdersLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class);
                stakeholdersLambdaQueryWrapper.eq(ProjectStakeholders::getOrderId, projectGroupDto.getOrderId())
                        .eq(ProjectStakeholders::getRoleCode, one.getRoleCode())
                        .eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
                List<ProjectStakeholders> list = projectStakeholdersService.list(stakeholdersLambdaQueryWrapper);

                if (list.size() == 0) {
                    throw new BadRequestException("当前项目没有配置" + one.getName());
                }
                for (ProjectStakeholders projectStakeholders : list) {
                    Role role = projectInfoRepository.getRoleByRoleCode(projectStakeholders.getRoleCode());
                    ProjectApproveDetail projectApproveDetail = new ProjectApproveDetail();

                    projectApproveDetail.setApproveId(projectApprove.getApproveId());
                    projectApproveDetail.setApproveStatus(JhSystemEnum.approveStatusEnum.IN_APPROVE.getKey());
                    projectApproveDetail.setApproveResult(JhSystemEnum.approveResultEnum.UNDER_APPROVE.getKey());
                    projectApproveDetail.setApproveUser(projectStakeholders.getUserId());
                    projectApproveDetail.setApproveRole(role.getId().toString());
                    projectApproveDetail.setApproveNodeId(detail.getApproveNodeId());
                    projectApproveDetail.setApproveGroupId(approveGroupId);
                    projectApproveDetail.setApproveNodeId(detail.getApproveNodeId());
                    projectApproveDetail.setApproveIndex(detail.getApproveIndex());
                    projectApproveDetail.setApproveTemplateDetailId(detail.getApproveTemplateDetailId());
                    projectApproveDetail.setParentId(detail.getParentId());
                    projectApproveDetail.setHasChild(detail.getHasChild());

                    projectApproveDetailRepository.insert(projectApproveDetail);
                    if ("1".equals(detail.getApproveLevel())) {
                        if (JhSystemEnum.approveBeginEnum.ALL_EXCUTE.getKey().equals(detail.getApproveBegin()) && "1".equals(detail.getApproveGroup())) {
                            //创建待办
                            ProjectApproveDetailDto projectApproveDetailDto = projectApproveDetailMapper.toDto(projectApproveDetail);
                            projectApproveDetailDto.setApproveRole(role.getId().toString());
                            projectApproveDetailDto.setApproveRoleName(role.getName());
                            //todo 待办
                            // projectTaskService.createAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK,projectApproveDetailDto);
                            // projectTaskService.createNoticeAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK,projectApproveDetailDto);
                        } else if (JhSystemEnum.approveBeginEnum.IN_ORDER.getKey().equals(detail.getApproveBegin()) && detail.getApproveIndex() == 1) {
                            //创建待办
                            ProjectApproveDetailDto projectApproveDetailDto = projectApproveDetailMapper.toDto(projectApproveDetail);
                            projectApproveDetailDto.setApproveRole(role.getId().toString());
                            projectApproveDetailDto.setApproveRoleName(role.getName());
                            projectTaskService.createAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK, projectApproveDetailDto);
                            //  projectTaskService.createNoticeAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK,projectApproveDetailDto);
                        }
                    }


                }
                if (detail.getHasChild() != null && detail.getHasChild()) {
                    createApproveGroup(projectGroupDto, projectApprove, projectParentGroup, "2");
                }


            }
            if (flag && "1".equals(level)) {

                projectParentGroupRepository.insert(projectParentGroup);
                projectApproveGroup.setParentGroupId(parentGroupId);
                flag = Boolean.FALSE;
            } else if ("2".equals(level)) {
                projectApproveGroup.setParentGroupId(bigGroup.getParentGroupId());
            } else {
                projectApproveGroup.setParentGroupId(parentGroupId);
            }

            projectApproveGroupRepository.insert(projectApproveGroup);

        }
        return null;
    }

    @Override
    public Boolean changeApproveUser(Long projectId, Long orderId, Long userId, Long oldUserId) {
        Boolean flag = false;
        //查询旧审批人所有未审核任务
        List<ProjectApproveDetail> list = projectApproveDetailRepository.getApproveDetailByUser(projectId, orderId, JhSystemEnum.approveStatusEnum.APPROVE_COMPLETE.getKey(), oldUserId);
        //更新所有审批为新人
        if (list.size() > 0) {
            for (ProjectApproveDetail approveDetail : list) {
                approveDetail.setApproveUser(userId);

            }
            flag = this.saveOrUpdateBatch(list);
        }
        return flag;
    }

    @Override
    public Boolean changeApproveOrderUser(Long orderId, Long userId, Long oldUserId) {
        Boolean flag = false;
        //查询旧审批人所有未审核任务
        List<ProjectApproveDetail> list = projectApproveDetailRepository.getApproveDetailByOrderUser(orderId, JhSystemEnum.approveStatusEnum.APPROVE_COMPLETE.getKey(), oldUserId);
        //更新所有审批为新人
        if (list.size() > 0) {
            for (ProjectApproveDetail approveDetail : list) {
                approveDetail.setApproveUser(userId);

            }
            flag = this.saveOrUpdateBatch(list);
        }
        return flag;
    }


}