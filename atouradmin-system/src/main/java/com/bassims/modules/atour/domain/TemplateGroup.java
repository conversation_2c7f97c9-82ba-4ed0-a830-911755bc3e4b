/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-08-18
**/
@Data
@TableName(value="t_template_group")
public class TemplateGroup implements Serializable {

    @TableId(value = "template_group_id",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "模板组主键")
    private Long templateGroupId;

    @TableField(value = "template_queue_id")
    @ApiModelProperty(value = "二级id")
    private Long templateQueueId;


    @TableField(value = "template_id")
    @ApiModelProperty(value = "模板主键")
    private Long templateId;

    @TableField(value = "template_code")
    @ApiModelProperty(value = "templateCode")
    private String templateCode;

    @TableField(value = "parent_id")
    @ApiModelProperty(value = "父节点")
    private Long parentId;

    @TableField(value = "node_name")
    @ApiModelProperty(value = "名称")
    private String nodeName;

    @TableField(value = "node_code")
    @ApiModelProperty(value = "节点编码")
    private String nodeCode;

    @TableField(value = "task_flag")
    @ApiModelProperty(value = "标识是否已经配置了输出条件")
    private String taskFlag;

    @TableField(value = "node_index")
    @ApiModelProperty(value = "子节点下标")
    private double nodeIndex;

    @TableField(value = "node_wbs")
    @ApiModelProperty(value = "节点序号")
    private Integer nodeWbs;

    @TableField(value = "front_wbs_config")
    @ApiModelProperty(value = "前置任务配置")
    private String frontWbsConfig;

    @TableField(value = "front_finish_wbs")
    @ApiModelProperty(value = "前置完成条件")
    private String frontFinishWbs;

    @TableField(value = "is_key")
    @ApiModelProperty(value = "是否是关键节点")
    private Boolean isKey;

    @TableField(value = "key_front_wbs")
    @ApiModelProperty(value = "关键节点前置任务")
    private String keyFrontWbs;

    @TableField(value = "plan_day")
    @ApiModelProperty(value = "计划需要完成天数")
    private Integer planDay;

    @TableField(value = "node_level")
    @ApiModelProperty(value = "节点等级")
    private Integer nodeLevel;

    @TableField(value = "node_type")
    @ApiModelProperty(value = "节点类型")
    private String nodeType;

    @TableField(value = "notice_day")
    @ApiModelProperty(value = "提醒天数")
    private Integer noticeDay;

    @TableField(value = "use_case")
    @ApiModelProperty(value = "使用场景")
    private String useCase;

    @TableField(value = "is_third")
    @ApiModelProperty(value = "是否有第三方审批")
    private Boolean isThird;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

    @TableField(value = "total_day")
    @ApiModelProperty(value = "总工期")
    private Integer totalDay;

    @TableField(value = "is_mobile")
    @ApiModelProperty(value = "是否是手机端")
    private Boolean isMobile;

    @TableField(value = "role_code")
    @ApiModelProperty(value = "责任人角色code")
    private String roleCode;

    @TableField(value = "remark")
    @ApiModelProperty(value = "remark")
    private String remark;

    @TableField(value = "icon")
    @ApiModelProperty(value = "小程序标志")
    private String icon;

    @TableField(value = "is_not_task")
    @ApiModelProperty(value = "是否不需要任务信息")
    private Boolean isNotTask;


    @TableField(value = "is_show")
    @ApiModelProperty(value = "是否展示（0展示、1不展示）")
    private Integer isShow;


    @TableField(value = "one_template_id")
    @ApiModelProperty(value = "关联的模版的一级template_id")
    private Long oneTemplateId;

    @TableField(value = "two_template_id")
    @ApiModelProperty(value = "关联的模版的二级template_id")
    private Long twoTemplateId;

    @TableField(value = "stencil_level")
    @ApiModelProperty(value = "模版级别,码值stencil_level")
    private String stencilLevel;

    @TableField(value = "message")
    @ApiModelProperty(value = "是否发送消息：false-不发；true-发")
    private Boolean message;

    @TableField(value = "is_planned_event")
    @ApiModelProperty(value = "是否有计划事件(0没有计划事件1、有计划事件)")
    private Integer isPlannedEvent;


    @TableField(value = "carbon_copy_role_code")
    @ApiModelProperty(value = "抄送角色")
    private String carbonCopyRoleCode;

    @TableField(value = "template_type")
    @ApiModelProperty(value = "模板类型")
    private String templateType;

    public void copy(TemplateGroup source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}