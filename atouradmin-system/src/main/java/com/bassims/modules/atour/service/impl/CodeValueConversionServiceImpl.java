package com.bassims.modules.atour.service.impl;

import com.bassims.constant.bsEnum.KidsSystemEnum;
import com.bassims.modules.atour.service.CodeValueConversionService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class CodeValueConversionServiceImpl implements CodeValueConversionService {

    /**
     * 城市公司转换
     */
    @Override
    public String getCityCompany(String value) {
        String cityCompany = "";
        if (KidsSystemEnum.CityCompanyEnum.NANJING.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.NANJING.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.YANGZHEN.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.YANGZHEN.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.TONGTAI.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.TONGTAI.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.CHANGZHOU.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.CHANGZHOU.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.WUXI.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.WUXI.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.SUZHOU.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.SUZHOU.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.YANCHENG.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.YANCHENG.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.XULIAN.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.XULIAN.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.SUHUAI.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.SUHUAI.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.ANHUI.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.ANHUI.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.ZHEJIANG.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.ZHEJIANG.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.SHANGHAI.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.SHANGHAI.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.HUNAN.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.HUNAN.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.HUBEI.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.HUBEI.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.JIANGXI.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.JIANGXI.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.FUJIAN.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.FUJIAN.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.SHANDONG.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.SHANDONG.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.HENAN.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.HENAN.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.HEBEI.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.HEBEI.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.TIANJIN.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.TIANJIN.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.LIAONING.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.LIAONING.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.SHANXI.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.SHANXI.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.CHONGQING.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.CHONGQING.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.GUANGDONG.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.GUANGDONG.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.GUANGXI.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.GUANGXI.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.SICHUAN.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.SICHUAN.getCityCompanyName();
        }
        if (KidsSystemEnum.CityCompanyEnum.YUNGUI.getCityCompany().equals(value)){
            cityCompany = KidsSystemEnum.CityCompanyEnum.YUNGUI.getCityCompanyName();
        }

        return cityCompany;
    }


    /**
     * 订单状态转换
     */
    @Override
    public String getOrderStatus(String value) {
        String orderStatus = "";
        if (KidsSystemEnum.OrderStatus.ORDER_CREATING.getValue().equals(value)){
            orderStatus = KidsSystemEnum.OrderStatus.ORDER_CREATING.getLabel();
        }
        if (KidsSystemEnum.OrderStatus.ORDER_APPROVAL.getValue().equals(value)){
            orderStatus = KidsSystemEnum.OrderStatus.ORDER_APPROVAL.getLabel();
        }
        if (KidsSystemEnum.OrderStatus.ORDER_CHOICE.getValue().equals(value)){
            orderStatus = KidsSystemEnum.OrderStatus.ORDER_CHOICE.getLabel();
        }
        if (KidsSystemEnum.OrderStatus.ORDER_RETURN.getValue().equals(value)){
            orderStatus = KidsSystemEnum.OrderStatus.ORDER_RETURN.getLabel();
        }
        if (KidsSystemEnum.OrderStatus.ORDER_CANCEL.getValue().equals(value)){
            orderStatus = KidsSystemEnum.OrderStatus.ORDER_CANCEL.getLabel();
        }
        if (KidsSystemEnum.OrderStatus.ORDER_CANCELING.getValue().equals(value)){
            orderStatus = KidsSystemEnum.OrderStatus.ORDER_CANCELING.getLabel();
        }
        if (KidsSystemEnum.OrderStatus.ORDER_DELIVER.getValue().equals(value)){
            orderStatus = KidsSystemEnum.OrderStatus.ORDER_DELIVER.getLabel();
        }
        if (KidsSystemEnum.OrderStatus.ORDER_ACCEPTANCE.getValue().equals(value)){
            orderStatus = KidsSystemEnum.OrderStatus.ORDER_ACCEPTANCE.getLabel();
        }
        if (KidsSystemEnum.OrderStatus.ORDER_ACCEPTED.getValue().equals(value)){
            orderStatus = KidsSystemEnum.OrderStatus.ORDER_ACCEPTED.getLabel();
        }
        if (KidsSystemEnum.OrderStatus.ORDER_SETTLEMENT.getValue().equals(value)){
            orderStatus = KidsSystemEnum.OrderStatus.ORDER_SETTLEMENT.getLabel();
        }
        if (KidsSystemEnum.OrderStatus.ORDER_PAYMENT.getValue().equals(value)){
            orderStatus = KidsSystemEnum.OrderStatus.ORDER_PAYMENT.getLabel();
        }
        if (KidsSystemEnum.OrderStatus.ORDER_FINISH.getValue().equals(value)){
            orderStatus = KidsSystemEnum.OrderStatus.ORDER_FINISH.getLabel();
        }
        return orderStatus;
    }


    /**
     * 项目类型转换
     */
    @Override
    public String getProjectType(String value) {
        String projectType = "";
        if (KidsSystemEnum.ProjectTypeEnum.NEW.getValue().equals(value)) {
            projectType = KidsSystemEnum.ProjectTypeEnum.NEW.getLabel();
        }
        if (KidsSystemEnum.ProjectTypeEnum.MAJOR.getValue().equals(value)) {
            projectType = KidsSystemEnum.ProjectTypeEnum.MAJOR.getLabel();
        }
        if (KidsSystemEnum.ProjectTypeEnum.MINOR.getValue().equals(value)) {
            projectType = KidsSystemEnum.ProjectTypeEnum.MINOR.getLabel();
        }
        if (KidsSystemEnum.ProjectTypeEnum.CLOSE.getValue().equals(value)) {
            projectType = KidsSystemEnum.ProjectTypeEnum.CLOSE.getLabel();
        }
        if (KidsSystemEnum.ProjectTypeOEnum.REFORM.getValue().equals(value)) {
            projectType = KidsSystemEnum.ProjectTypeOEnum.REFORM.getLabel();
        }
        if (KidsSystemEnum.ProcessType.Adjust.getValue().equals(value)) {
            projectType = KidsSystemEnum.ProcessType.Adjust.getLabel();
        }
        return projectType;
    }


    /**
     * 费用类型转换
     */
    @Override
    public String getCostType(String value) {
        String costType = "";
        if (KidsSystemEnum.CostType.NEW_FEE.getValue().equals(value)){
            costType = KidsSystemEnum.CostType.NEW_FEE.getLabel();
        }
        if (KidsSystemEnum.CostType.REFORM_FEE.getValue().equals(value)){
            costType = KidsSystemEnum.CostType.REFORM_FEE.getLabel();
        }
        if (KidsSystemEnum.CostType.CURRENT_FEE.getValue().equals(value)){
            costType = KidsSystemEnum.CostType.CURRENT_FEE.getLabel();
        }
        if (KidsSystemEnum.CostType.SPECIAL_PROCUREMENT.getValue().equals(value)){
            costType = KidsSystemEnum.CostType.SPECIAL_PROCUREMENT.getLabel();
        }
        if (KidsSystemEnum.CostType.STORE_SPECIAL.getValue().equals(value)){
            costType = KidsSystemEnum.CostType.STORE_SPECIAL.getLabel();
        }
        return costType;
    }


    /**
     * 设计定位类型转换
     */
    @Override
    public String getDesignPosition(String value) {
        String designPosition = "";
        if (KidsSystemEnum.DesignPosition.STANDARD_STORE.getValue().equals(value)){
            designPosition = KidsSystemEnum.DesignPosition.STANDARD_STORE.getLabel();
        }
        if (KidsSystemEnum.DesignPosition.FLAGSHIP_STORE.getValue().equals(value)){
            designPosition = KidsSystemEnum.DesignPosition.FLAGSHIP_STORE.getLabel();
        }
        if (KidsSystemEnum.DesignPosition.PREFERRED_STORE.getValue().equals(value)){
            designPosition = KidsSystemEnum.DesignPosition.PREFERRED_STORE.getLabel();
        }
        return designPosition;
    }
}
