/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.repository.*;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.service.OrderStockService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.OrderStockDto;
import com.bassims.modules.atour.service.dto.OrderStockQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.OrderStockMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-09-16
**/
@Service
public class OrderStockServiceImpl extends BaseServiceImpl<OrderStockRepository,OrderStock> implements OrderStockService {

    private static final Logger logger = LoggerFactory.getLogger(OrderStockServiceImpl.class);

    @Autowired
    private OrderStockRepository orderStockRepository;
    @Autowired
    private OrderStockMapper orderStockMapper;
    @Autowired
    private MtoBaseMaterielRepository mtoBaseMaterielRepository;
    @Autowired
    private SupplierInfoRepository supplierInfoRepository;
    @Autowired
    private SupplierMaterielInfoRepository supplierMaterielInfoRepository;
    @Autowired
    private MtoClassRepository mtoClassRepository;

    @Override
    public Map<String,Object> queryAll(OrderStockQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<OrderStock> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(OrderStock.class, criteria)));
        List<OrderStock> list = page.getList();
        for (OrderStock orderStock : list) {
            queryClass(orderStock);
        }
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", orderStockMapper.toDto(list));
        map.put("totalElements", page.getTotal());
        return map;
    }

    private OrderStock queryClass(OrderStock orderStock){
        MtoBaseMateriel mtoBaseMateriel = Optional.ofNullable(queryMaterielInfo(orderStock.getMaterielNo(), orderStock.getSupplierNum())).orElseGet(MtoBaseMateriel::new);
        MtoClass mtoClass = Optional.ofNullable(mtoClassRepository.selectById(mtoBaseMateriel.getClassId())).orElseGet(MtoClass::new);
        mtoBaseMateriel.setLevelClass2(mtoClass.getName());
        MtoClass mtoClass1 = Optional.ofNullable(mtoClassRepository.selectById(mtoClass.getParentId())).orElseGet(MtoClass::new);
        mtoBaseMateriel.setLevelClass1(mtoClass1.getName());
        orderStock.setMtoBaseMateriel(mtoBaseMateriel);
        return orderStock;
    }

    //根据物料编号和厂商编号查找唯一物料信息
    private MtoBaseMateriel queryMaterielInfo(String materielNo,String supplierNum){
        LambdaQueryWrapper queryWrapper = Wrappers.lambdaQuery(SupplierInfo.class)
                .eq(SupplierInfo::getSupplierNum,supplierNum);
        SupplierInfo supplierInfo = Optional.ofNullable(supplierInfoRepository.selectOne(queryWrapper)).orElseGet(SupplierInfo::new);
        LambdaQueryWrapper queryWrapper2 = Wrappers.lambdaQuery(SupplierMaterielInfo.class)
                .eq(SupplierMaterielInfo::getSupplierId,supplierInfo.getId())
                .eq(SupplierMaterielInfo::getMaterielCode,materielNo);
        List<SupplierMaterielInfo> list = supplierMaterielInfoRepository.selectList(queryWrapper2);
        MtoBaseMateriel mtoBaseMateriel = new MtoBaseMateriel();
        if (ObjectUtils.isNotEmpty(list) && list.size()>0){
            mtoBaseMateriel = mtoBaseMaterielRepository.selectById(list.get(0).getMaterielId());
        }
        return mtoBaseMateriel;
    }

    @Override
    public List<OrderStockDto> queryAll(OrderStockQueryCriteria criteria){
        return orderStockMapper.toDto(list(QueryHelpPlus.getPredicate(OrderStock.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderStockDto findById(Long id) {
        OrderStock orderStock = Optional.ofNullable(getById(id)).orElseGet(OrderStock::new);
        ValidationUtil.isNull(orderStock.getId(),getEntityClass().getSimpleName(),"id",id);
//        MtoBaseMateriel mtoBaseMateriel = queryMaterielInfo(orderStock.getMaterielNo(), orderStock.getSupplierNum());
//        orderStock.setMtoBaseMateriel(mtoBaseMateriel);
        orderStock = queryClass(orderStock);
        return orderStockMapper.toDto(orderStock);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderStockDto create(OrderStock resources) {
//        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
//        resources.setId(snowflake.nextId());
        save(resources);
        return findById(resources.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(OrderStock resources) {
        OrderStock orderStock = Optional.ofNullable(getById(resources.getId())).orElseGet(OrderStock::new);
        ValidationUtil.isNull( orderStock.getId(),"OrderStock","id",resources.getId());
        orderStock.copy(resources);
        updateById(orderStock);
    }

    @Override
    public void deleteAll(String[] ids) {
        for (String materielNo : ids) {
            orderStockRepository.deleteById(materielNo);
        }
    }

    @Override
    public void download(List<OrderStockDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (OrderStockDto orderStock : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("预约单库存", orderStock.getOrderStock());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void incrStock(String materielNo,String supplierNum, BigDecimal orderNum) {
        orderStockRepository.incrStock(materielNo,supplierNum,orderNum);
    }

    @Override
    public void decrStock(String materielNo,String supplierNum, BigDecimal orderNum) {
        orderStockRepository.decrStock(materielNo,supplierNum,orderNum);
    }
}