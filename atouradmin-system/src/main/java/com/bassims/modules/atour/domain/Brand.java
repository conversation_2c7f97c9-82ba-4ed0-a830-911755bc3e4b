package com.bassims.modules.atour.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *  品牌
 */
@Data
@TableName(value="s_brand")
@Accessors(chain = true)
public class Brand {
    @TableId(value = "brand_id")
    @ApiModelProperty(value = "品牌id")
    private Long brandId;

    @TableField(value = "region")
    @ApiModelProperty(value = "关联品牌名称")
    private String region;

    @TableField(value = "brand_name")
    @ApiModelProperty(value = "品牌名称")
    private String brandName;
}
