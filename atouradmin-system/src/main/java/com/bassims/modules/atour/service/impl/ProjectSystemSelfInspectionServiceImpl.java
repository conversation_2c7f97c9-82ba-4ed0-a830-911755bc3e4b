/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.domain.ProjectSystemSelfInspection;
import com.bassims.modules.atour.domain.TemplateSystemSelfInspection;
import com.bassims.modules.atour.repository.ProjectSystemSelfInspectionRepository;
import com.bassims.modules.atour.service.ProjectSystemSelfInspectionService;
import com.bassims.modules.atour.service.TemplateSystemSelfInspectionService;
import com.bassims.modules.atour.service.dto.ProjectSystemSelfInspectionDto;
import com.bassims.modules.atour.service.dto.ProjectSystemSelfInspectionQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.ProjectSystemSelfInspectionMapper;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-01-08
**/
@Service
public class ProjectSystemSelfInspectionServiceImpl extends BaseServiceImpl<ProjectSystemSelfInspectionRepository,ProjectSystemSelfInspection> implements ProjectSystemSelfInspectionService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectSystemSelfInspectionServiceImpl.class);

    @Autowired
    private ProjectSystemSelfInspectionRepository projectSystemSelfInspectionRepository;
    @Autowired
    private ProjectSystemSelfInspectionMapper projectSystemSelfInspectionMapper;
    @Autowired
    private TemplateSystemSelfInspectionService templateSystemSelfInspectionService;

    @Override
    public Map<String,Object> queryAll(ProjectSystemSelfInspectionQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<ProjectSystemSelfInspection> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectSystemSelfInspection.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectSystemSelfInspectionMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectSystemSelfInspectionDto> queryAll(ProjectSystemSelfInspectionQueryCriteria criteria){
        return projectSystemSelfInspectionMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectSystemSelfInspection.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectSystemSelfInspectionDto findById(Long systemSelfInspectionId) {
        ProjectSystemSelfInspection projectSystemSelfInspection = Optional.ofNullable(getById(systemSelfInspectionId)).orElseGet(ProjectSystemSelfInspection::new);
        ValidationUtil.isNull(projectSystemSelfInspection.getSystemSelfInspectionId(),getEntityClass().getSimpleName(),"systemSelfInspectionId",systemSelfInspectionId);
        return projectSystemSelfInspectionMapper.toDto(projectSystemSelfInspection);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectSystemSelfInspectionDto create(ProjectSystemSelfInspection resources) {
        save(resources);
        return findById(resources.getSystemSelfInspectionId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectSystemSelfInspection resources) {
        ProjectSystemSelfInspection projectSystemSelfInspection = Optional.ofNullable(getById(resources.getSystemSelfInspectionId())).orElseGet(ProjectSystemSelfInspection::new);
        ValidationUtil.isNull( projectSystemSelfInspection.getSystemSelfInspectionId(),"ProjectSystemSelfInspection","id",resources.getSystemSelfInspectionId());
        projectSystemSelfInspection.copy(resources);
        updateById(projectSystemSelfInspection);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long systemSelfInspectionId : ids) {
            projectSystemSelfInspectionRepository.deleteById(systemSelfInspectionId);
        }
    }

    @Override
    public void download(List<ProjectSystemSelfInspectionDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectSystemSelfInspectionDto projectSystemSelfInspection : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("项目ID", projectSystemSelfInspection.getProjectId());
            map.put("节点编码", projectSystemSelfInspection.getNodeCode());
            map.put("自检项目名称", projectSystemSelfInspection.getSelfProjectName());
            map.put("自检内容", projectSystemSelfInspection.getSelfContent());
            map.put("结论", projectSystemSelfInspection.getConclusion());
            map.put("说明", projectSystemSelfInspection.getExplains());
            map.put("创建时间", projectSystemSelfInspection.getCreateTime());
            map.put("创建人", projectSystemSelfInspection.getCreateBy());
            map.put("更新时间", projectSystemSelfInspection.getUpdateTime());
            map.put("更新人", projectSystemSelfInspection.getUpdateBy());
            map.put("是否可用", projectSystemSelfInspection.getIsEnabled());
            map.put("是否删除", projectSystemSelfInspection.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<ProjectSystemSelfInspectionDto> getList(ProjectSystemSelfInspectionQueryCriteria criteria) {
        final LambdaQueryWrapper<ProjectSystemSelfInspection> wrapper = Wrappers.lambdaQuery(ProjectSystemSelfInspection.class)
                .eq(ProjectSystemSelfInspection::getProjectId, criteria.getProjectId())
                .eq(ProjectSystemSelfInspection::getNodeCode, criteria.getNodeCode());
        final List<ProjectSystemSelfInspection> projectSystemSelfInspections = projectSystemSelfInspectionRepository.selectList(wrapper);
        final List<TemplateSystemSelfInspection> list = templateSystemSelfInspectionService.list();
        final List<ProjectSystemSelfInspection> systemSelfInspections= new ArrayList<>();
        projectSystemSelfInspections.stream().forEach(p->{
           if (list.stream().map(TemplateSystemSelfInspection::getSelfContent).collect(Collectors.toList()).contains(p.getSelfContent())){
               p.setNotDelete("1");
           }else{
               p.setNotDelete("0");
           }
           systemSelfInspections.add(p);
        });

        return projectSystemSelfInspectionMapper.toDto(systemSelfInspections);
    }

    @Override
    public void deleteByIds(List<Long> collect) {
        projectSystemSelfInspectionRepository.deleteByIds(collect);
    }
}