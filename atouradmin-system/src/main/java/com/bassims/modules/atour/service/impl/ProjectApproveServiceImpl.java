/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.bsEnum.AtourSystemEnum;
import com.bassims.constant.bsEnum.KidsSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum.approveResultEnum;
import com.bassims.constant.jhEnum.JhSystemEnum.approveStatusEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.ProjectApproveGroup;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.domain.vo.ApproveDetailVo;
import com.bassims.modules.atour.domain.vo.ApproveNodeInfoVo;
import com.bassims.modules.atour.repository.*;
import com.bassims.modules.atour.requestParam.KwApproveResponse;
import com.bassims.modules.atour.requestParam.KwWorkFlowApproveDetail;
import com.bassims.modules.atour.service.*;
import com.bassims.modules.atour.service.dto.*;
import com.bassims.modules.atour.service.mapstruct.*;
import com.bassims.modules.atour.util.NoteInfoMappingUtil;
import com.bassims.modules.atourWithout.service.AtosSubjectInfoService;
import com.bassims.modules.system.domain.Role;
import com.bassims.modules.system.domain.User;
import com.bassims.modules.system.repository.RoleRepository;
import com.bassims.modules.system.repository.UserRepository;
import com.bassims.modules.system.service.UserService;
import com.bassims.modules.system.service.dto.UserDto;
import com.bassims.utils.*;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-03-28
 **/
@Slf4j
@Service
public class ProjectApproveServiceImpl extends BaseServiceImpl<ProjectApproveRepository, ProjectApprove> implements ProjectApproveService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectApproveServiceImpl.class);

    @Autowired
    private ProjectApproveRepository projectApproveRepository;
    @Autowired
    private ProjectApproveDetailRepository projectApproveDetailRepository;
    @Autowired
    private ProjectApproveMapper projectApproveMapper;
    @Autowired
    private ProjectApproveDetailMapper projectApproveDetailMapper;
    @Autowired
    private ProjectStakeholdersService projectStakeholdersService;
    @Autowired
    private OrderInfoRepository orderInfoRepository;
    @Autowired
    private OrderStockService orderStockService;
    @Autowired
    private OrderDetailRepository orderDetailRepository;
    @Autowired
    private SupplierMaterielInfoRepository supplierMaterielInfoRepository;
    @Autowired
    private SupplierInfoRepository supplierInfoRepository;
    @Autowired
    private SupplierPmRepository supplierPmRepository;
    @Autowired
    private SupplierPmService supplierPmService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private ProjectNodeInfoService projectNodeInfoService;
    @Autowired
    private ProjectNodeInfoRepository projectNodeInfoRepository;
    @Autowired
    private OrderNodeInfoService orderNodeInfoService;
    @Autowired
    private ProjectTaskService projectTaskService;
    @Autowired
    private ProjectTaskInfoRepository projectTaskInfoRepository;
    @Autowired
    private ProjectApproveDetailService projectApproveDetailService;
    @Autowired
    private ProjectApproveGroupRepository projectApproveGroupRepository;
    @Autowired
    private ProjectParentGroupRepository projectParentGroupRepository;
    @Autowired
    private ProjectGroupMapper projectGroupMapper;
    @Autowired
    private ProjectGroupService projectGroupService;
    @Autowired
    private ProjectGroupRepository projectGroupRepository;
    @Autowired
    private OrderNodeInfoRepository orderNodeInfoRepository;
    @Autowired
    private BudgetVersionService budgetVersionService;
    @Autowired
    private ProjectInfoService projectInfoService;
    @Autowired
    private StoreMasterInfoService storeMasterInfoService;
    @Autowired
    private OutsourceVersionService outsourceVersionService;
    @Autowired
    private SpecialProjectRepository specialProjectRepository;
    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Autowired
    private SpecialProjectMapper specialProjectMapper;
    @Autowired
    private OutsourceVersionDetailService outsourceVersionDetailService;
    @Autowired
    private ProjectApproveService projectApproveService;
    @Autowired
    private OrderNodeInfoMapper orderNodeInfoMapper;
    @Autowired
    private OrderInfoService orderInfoService;

    @Autowired
    private SurveyReportHistoryVersionService surveyReportHistoryVersionService;

    @Autowired
    private ProjectInfoAbarbeitungService projectInfoAbarbeitungService;
    @Autowired
    private ProjectNodeInfoApprovalRejectionService infoApprovalRejectionService;

    @Autowired
    private ProjectNoticeService projectNoticeService;

    @Autowired
    private ProjectGroupExpandService projectGroupExpandService;


    @Autowired
    private ProjectApproveGroupService projectApproveGroupService;


    @Autowired
    private ProjectTemplateApproveRelationService projectTemplateApproveRelationService;
    @Autowired
    private ApproveTemplateDetailService approveTemplateDetailService;
    @Autowired
    private ProjectCompletionReceiptService projectCompletionReceiptService;
    @Autowired
    private ProjectCompletionReceiptRepository projectCompletionReceiptRepository;
    @Autowired
    private AtosSubjectInfoService atosSubjectInfoService;

    @Autowired
    private NoteInfoMappingUtil util;

    @Override
    public Map<String, Object> queryAll(ProjectApproveQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<ProjectApprove> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectApprove.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectApproveMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectApproveDto> queryAll(ProjectApproveQueryCriteria criteria) {
        List<ProjectApproveDto> list = projectApproveMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectApprove.class, criteria)));
        for (ProjectApproveDto dto : list) {
            if (null != dto.getSubmitUser()) {
                UserDto UserDto = userService.findById(dto.getSubmitUser());
                if (null != UserDto) {
                    dto.setSubmitUserName(UserDto.getUsername());
                }
                List<Role> rolesByUserId = roleRepository.findRolesByUserId(dto.getSubmitUser());
                if (rolesByUserId.size() != 0) {
                    dto.setSubmitRoleName(rolesByUserId.get(0).getName());
                }
            }
        }
        //TODO 需要转换人名


        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectApproveDto findById(Long approveId) {
        ProjectApprove projectApprove = Optional.ofNullable(getById(approveId)).orElseGet(ProjectApprove::new);
        ValidationUtil.isNull(projectApprove.getApproveId(), getEntityClass().getSimpleName(), "approveId", approveId);
        return projectApproveMapper.toDto(projectApprove);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectApproveDto create(ProjectApprove resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setApproveId(snowflake.nextId());
        save(resources);
        return findById(resources.getApproveId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectApprove resources) {
        ProjectApprove projectApprove = Optional.ofNullable(getById(resources.getApproveId())).orElseGet(ProjectApprove::new);
        ValidationUtil.isNull(projectApprove.getApproveId(), "ProjectApprove", "id", resources.getApproveId());
        projectApprove.copy(resources);
        updateById(projectApprove);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long approveId : ids) {
            projectApproveRepository.deleteById(approveId);
        }
    }

    @Override
    public void download(List<ProjectApproveDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectApproveDto projectApprove : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("项目id", projectApprove.getProjectId());
            map.put("审批类型", projectApprove.getApproveType());
            map.put("审批状态", projectApprove.getApproveStatus());
            map.put("审批结果", projectApprove.getApproveResult());
            map.put("审批开始时间", projectApprove.getApproveStart());
            map.put("审批结束时间", projectApprove.getApproveEnd());
            map.put("提交人", projectApprove.getSubmitUser());
            map.put("是否重报", projectApprove.getIsAnew());
            map.put("重报原因", projectApprove.getAnewReason());
            map.put("审批对应的节点id", projectApprove.getNodeId());
            map.put("创建时间", projectApprove.getCreateTime());
            map.put("创建人", projectApprove.getCreateBy());
            map.put("更新时间", projectApprove.getUpdateTime());
            map.put("更新人", projectApprove.getUpdateBy());
            map.put("是否可用", projectApprove.getIsEnabled());
            map.put("是否删除", projectApprove.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createApprove(ProjectGroupDto projectGroupDto) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        // Long approveMatrixId = appRelation.getApproveMatrixId();
        //查找当前的审批节点数据
        /*LambdaQueryWrapper<ApproveTemplateDetail> templateDetailLambdaQueryWrapper= Wrappers.lambdaQuery(ApproveTemplateDetail.class);
        templateDetailLambdaQueryWrapper.eq(ApproveTemplateDetail::getApproveTemplateId,approveMatrixId)
        .eq(ApproveTemplateDetail::getIsDelete,Boolean.FALSE)
        .orderBy(true,true,ApproveTemplateDetail::getApproveNum)
        .orderBy(true,true,ApproveTemplateDetail::getApproveIndex);
        List<ApproveTemplateDetail> approveTemplateDetails = approveTemplateDetailRepository.selectList(templateDetailLambdaQueryWrapper);*/


        ProjectApprove projectApprove = new ProjectApprove();
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        Long approveId = snowflake.nextId();
        projectApprove.setApproveId(approveId);
        projectApprove.setProjectId(projectGroupDto.getProjectId() == null ? null : Long.parseLong(projectGroupDto.getProjectId()));
        projectApprove.setOrderId(projectGroupDto.getOrderId() == null ? null : Long.parseLong(projectGroupDto.getOrderId()));
        projectApprove.setApproveStatus(approveStatusEnum.IN_APPROVE.getKey());
        projectApprove.setApproveResult(approveResultEnum.UNDER_APPROVE.getKey());
        projectApprove.setNodeId(Long.parseLong(projectGroupDto.getProjectGroupId()));
        projectApprove.setSubmitUser(currentUserId);
        projectApprove.setIsDelete(false);
        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
        projectApprove.setApproveStart(nowTime);
        projectApproveDetailService.createApproveGroup(projectGroupDto, projectApprove, new ProjectParentGroup(), "1");

        projectApproveRepository.insert(projectApprove);

        ProjectGroup projectGroup = projectGroupMapper.toEntity(projectGroupDto);
//        projectTaskService.generateTodoTask(projectGroup);

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createOrderApprove(ProjectGroupDto projectGroupDto) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
//        Long currentUserId = 1L;
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        // Long approveMatrixId = appRelation.getApproveMatrixId();
        //查找当前的审批节点数据
        /*LambdaQueryWrapper<ApproveTemplateDetail> templateDetailLambdaQueryWrapper= Wrappers.lambdaQuery(ApproveTemplateDetail.class);
        templateDetailLambdaQueryWrapper.eq(ApproveTemplateDetail::getApproveTemplateId,approveMatrixId)
        .eq(ApproveTemplateDetail::getIsDelete,Boolean.FALSE)
        .orderBy(true,true,ApproveTemplateDetail::getApproveNum)
        .orderBy(true,true,ApproveTemplateDetail::getApproveIndex);
        List<ApproveTemplateDetail> approveTemplateDetails = approveTemplateDetailRepository.selectList(templateDetailLambdaQueryWrapper);*/


        ProjectApprove projectApprove = new ProjectApprove();
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        Long approveId = snowflake.nextId();
        projectApprove.setApproveId(approveId);
        projectApprove.setProjectId(projectGroupDto.getProjectId() == null ? null : Long.parseLong(projectGroupDto.getProjectId()));
        //todo 修改
        projectApprove.setOrderId(Long.parseLong(projectGroupDto.getOrderId()));
        projectApprove.setApproveStatus(approveStatusEnum.IN_APPROVE.getKey());
        projectApprove.setApproveResult(approveResultEnum.UNDER_APPROVE.getKey());
        projectApprove.setNodeId(Long.parseLong(projectGroupDto.getProjectGroupId()));
        projectApprove.setSubmitUser(currentUserId);
        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
        projectApprove.setApproveStart(nowTime);
        projectApprove.setIsDelete(false);
        projectApproveDetailService.createOrderApproveGroup(projectGroupDto, projectApprove, new ProjectParentGroup(), "1");

        projectApproveRepository.insert(projectApprove);

        return null;
    }


    @Override
    public boolean estimate(ProjectNodeInfoDto nodeInfo) {
        boolean flag = Boolean.FALSE;
        LambdaQueryWrapper<ProjectApprove> templateLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApprove.class);
        templateLambdaQueryWrapper.eq(ProjectApprove::getNodeId, nodeInfo.getProjectGroupId());

//        ProjectApprove projectApprove = projectApproveRepository.selectOne(templateLambdaQueryWrapper);
        List<ProjectApprove> projectApproves = projectApproveRepository.selectList(templateLambdaQueryWrapper);
        if (projectApproves.size() == 0 || projectApproves == null) {
            return flag = Boolean.FALSE;
        }
        for (ProjectApprove projectApprove : projectApproves) {
            String approveStatus = projectApprove.getApproveStatus();
            if (approveStatusEnum.IN_APPROVE.getKey().equals(approveStatus)) {
                return flag = Boolean.TRUE;
            }
        }
        return flag = Boolean.FALSE;
    }

    @Override
    public boolean estimate(OrderNodeInfoDto nodeInfo) {
        boolean flag = Boolean.FALSE;
        LambdaQueryWrapper<ProjectApprove> templateLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApprove.class);
        templateLambdaQueryWrapper
                .eq(ProjectApprove::getNodeId, nodeInfo.getProjectGroupId());

        List<ProjectApprove> projectApproves = projectApproveRepository.selectList(templateLambdaQueryWrapper);

        if (projectApproves.size() == 0) {
            return flag = Boolean.FALSE;
        }
        for (ProjectApprove projectApprove : projectApproves) {
            String approveStatus = projectApprove.getApproveStatus();
            if (approveStatusEnum.IN_APPROVE.getKey().equals(approveStatus)) {
                flag = Boolean.TRUE;
            }
        }

        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createWorkFlowApprove(ProjectGroupDto projectGroupDto) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        ProjectApprove projectApprove = new ProjectApprove();
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        Long approveId = snowflake.nextId();
        projectApprove.setApproveId(approveId);
        projectApprove.setProjectId(projectGroupDto.getProjectId() == null ? null : Long.parseLong(projectGroupDto.getProjectId()));
        projectApprove.setOrderId(projectGroupDto.getOrderId() == null ? null : Long.parseLong(projectGroupDto.getOrderId()));
        projectApprove.setApproveStatus(approveStatusEnum.IN_APPROVE.getKey());
        projectApprove.setApproveResult(approveResultEnum.UNDER_APPROVE.getKey());
        projectApprove.setNodeId(Long.parseLong(projectGroupDto.getProjectGroupId()));
        projectApprove.setSubmitUser(currentUserId);
        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
        projectApprove.setApproveStart(nowTime);
        // projectApproveDetailService.createApproveGroup(projectGroupDto, projectApprove, new ProjectParentGroup(), "1");

        projectApproveRepository.insert(projectApprove);

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public KwApproveResponse createWorkFlowApproveDetail(KwWorkFlowApproveDetail kwWorkFlowApproveDetail) {
        log.info("审批回调参数：{}", JSONObject.toJSONString(kwWorkFlowApproveDetail));
        KwApproveResponse kwApproveResponse = new KwApproveResponse();
        //查询
        if (ObjectUtil.hasNull(kwWorkFlowApproveDetail)) {
            kwApproveResponse.setSuccess("false");
            kwApproveResponse.setCode("-1");
            kwApproveResponse.setMsg("参数不能为空");
            return kwApproveResponse;
        }
        String businessCode = kwWorkFlowApproveDetail.getBusinessCode();
        String processInstanceId = kwWorkFlowApproveDetail.getProcessInstanceId();
        Long nodeId = projectGroupService.getIdByNodeApplyNo(businessCode);
        if (ObjectUtils.isEmpty(nodeId)) {
            nodeId = projectGroupService.getIdByReportApplyNo(businessCode);
            if (ObjectUtil.isEmpty(nodeId)) {
                nodeId = Long.valueOf(businessCode);
            }
        }
        LambdaQueryWrapper<ProjectApprove> approveLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApprove.class)
                .eq(ProjectApprove::getNodeId, nodeId)
                .eq(ProjectApprove::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey());
        ProjectApprove one = this.getOne(approveLambdaQueryWrapper);
        if (one == null) {
            kwApproveResponse.setSuccess("false");
            kwApproveResponse.setCode("-1");
            kwApproveResponse.setMsg("未查询到审批");
            return kwApproveResponse;
        }
        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
        ProjectApproveDetail projectApproveDetail = new ProjectApproveDetail();
        projectApproveDetail.setApproveId(one.getApproveId());
        String empCode = kwWorkFlowApproveDetail.getUserId();
        UserDto userDto = userService.findByCode(empCode);
        if (userDto != null) {
            projectApproveDetail.setApproveUser(userDto.getId());
            projectApproveDetail.setEmpCode(empCode);
        } else {
            projectApproveDetail.setEmpCode(empCode);
        }
        projectApproveDetail.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
        //审批状态，需要转码再存储
        approveResultEnum approveResultEnum = JhSystemEnum.approveResultEnum.approveResultPhase(kwWorkFlowApproveDetail.getType());
        projectApproveDetail.setApproveEnd(nowTime);
        projectApproveDetail.setApproveResult(approveResultEnum.getKey());
        projectApproveDetail.setApproveOption(StringUtils.isNotEmpty(kwWorkFlowApproveDetail.getComment()) ? kwWorkFlowApproveDetail.getComment() : approveResultEnum.getSpec());
        //projectApproveDetail.setApproveRole(role.getId().toString());
        //projectApproveDetail.setApproveRoleName(role.getName());
        int insert = projectApproveDetailRepository.insert(projectApproveDetail);
        //存流程实例id
        LambdaUpdateWrapper groupUpdate = Wrappers.lambdaUpdate(ProjectGroup.class)
                .eq(ProjectGroup::getProjectGroupId, nodeId)
                .set(ProjectGroup::getProcessInstanceId, processInstanceId);
        projectGroupService.update(groupUpdate);
        if ("true".equals(kwWorkFlowApproveDetail.getNextEndTask())) {
            //更新审批状态
            kwApproveComplate(projectApproveDetail, one);

            if (JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey().equals(approveResultEnum.getKey()) || JhSystemEnum.approveResultEnum.APPROVE_DIS.getKey().equals(approveResultEnum.getKey())) {
                // 下个节点开启
                ProjectGroup projectGroup = projectGroupService.getById(nodeId);
//                projectNodeInfoService.updateNodeStatus(one);
                if (one.getOrderId() != null) {
                    //如果是订单的节点，修改订单状态
                    NodeInfoDto nodeInfoDto = new NodeInfoDto();
                    nodeInfoDto.setOrderId(one.getOrderId());
                    nodeInfoDto.setNodeCode(projectGroup.getNodeCode());
                    nodeInfoDto.setType(KidsSystemEnum.OperationType.FINISH.getValue());
                    orderInfoService.updateStatus(nodeInfoDto);
                    orderNodeInfoService.updateOrderGroupStatusNext(projectGroup);

                } else {
                    projectNodeInfoService.updateGroupStatusNext(projectGroup);
                }
            }
        } else {
            if (JhSystemEnum.approveResultEnum.APPROVE_REFUSE.getKey().equals(approveResultEnum.getKey()) || JhSystemEnum.approveResultEnum.APPROVE_REJECT.getKey().equals(approveResultEnum.getKey())) {
                //更新审批状态
                kwApproveComplate(projectApproveDetail, one);

                //重新打开当前节点
//                ProjectGroup projectGroup = projectGroupService.getById(Long.valueOf(businessCode));
                projectNodeInfoService.updateNodeStatus(one);
            }
        }

        if (insert != 0) {
            kwApproveResponse.setSuccess("true");
            kwApproveResponse.setCode("0");
            kwApproveResponse.setMsg("成功");
        }

        return kwApproveResponse;
    }

    private void kwApproveComplate(ProjectApproveDetail detail, ProjectApprove approve) {
        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
        //审批状态，需要转码再存储
        approve.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
        approve.setApproveResult(detail.getApproveResult());
        approve.setApproveEnd(nowTime);
        update(approve);

        ProjectApproveGroup currentGroup = Optional.ofNullable(getCurrentGroup(detail)).orElseGet(ProjectApproveGroup::new);
        currentGroup.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
        currentGroup.setApproveResult(detail.getApproveResult());
        updateCurrentGroup(currentGroup);

        Long parentGroupId = currentGroup.getParentGroupId();
        ProjectParentGroup projectParentGroup = Optional.ofNullable(projectParentGroupRepository.selectById(parentGroupId)).orElseGet(ProjectParentGroup::new);
        projectParentGroup.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
        projectParentGroup.setApproveResult(detail.getApproveResult());
        updateCurrentParentGroup(projectParentGroup);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitApprove(ProjectApproveDto projectApproveDto) {
        Boolean flag = Boolean.FALSE;
        //发起人
        Long initiatUser = SecurityUtils.getCurrentUserId();
        //审批人
        Long approveUser = projectApproveDto.getApproveUser();
        List<String> userRoleCodes = roleRepository.findRoleCodesByUserId1(approveUser);

        Long nodeId = projectApproveDto.getNodeId();
        SupplierInfo info = supplierInfoRepository.selectById(projectApproveDto.getProjectId());
        if (info != null) {
//            info.setId(projectApproveDto.getProjectId());
            info.setStatus(projectApproveDto.getApproveResult());
            if (approveResultEnum.APPROVE_PASS.getKey().equals(projectApproveDto.getApproveResult())) {
                supplierPmRepository.update(null, Wrappers.lambdaUpdate(SupplierPm.class)
                        .eq(SupplierPm::getSupplierId, projectApproveDto.getProjectId())
                        .set(SupplierPm::getStatus, approveResultEnum.APPROVE_PASS.getKey()));
                atosSubjectInfoService.pullSubjectInfoForAtos(info);
                List<User> pmList = supplierPmRepository.getUserListBySupplierId(info.getId());
                if (pmList != null && ! pmList.isEmpty()) {
                    pmList.forEach(p->{
                        String tkId = info.getTkId();
                        atosSubjectInfoService.pullUserInfoForAtos(p, tkId);
                    });
                }
            }
            supplierInfoRepository.updateById(info);
//            return flag;
        }
        SupplierPm pm = supplierPmRepository.selectById(projectApproveDto.getProjectId());
        if (pm != null) {
            pm.setStatus(projectApproveDto.getApproveResult());
            supplierPmRepository.updateById(pm);
            if (approveResultEnum.APPROVE_PASS.getKey().equals(projectApproveDto.getApproveResult())) {
                //调用接口同步用户
                //根据供应商信息新增用户
                SupplierInfo supplierInfo = supplierInfoRepository.selectById(pm.getSupplierId());
                User user = userRepository.getByUserId(pm.getId().toString());
                String tkId = supplierInfo.getTkId();
                atosSubjectInfoService.pullUserInfoForAtos(user, tkId);
            }
        }

        String approveOption = projectApproveDto.getApproveOption();
        String approveResult = projectApproveDto.getApproveResult();
        Timestamp nowTime = new Timestamp(System.currentTimeMillis());


        if (ObjectUtil.isNotEmpty(projectApproveDto.getApproveNodeInfoVo())) {
            //获取当前项目任务的有条件开启审批
            this.upAppOpenConditio(projectApproveDto.getProjectId(), projectApproveDto.getNodeId(), projectApproveDto.getApproveNodeInfoVo());
        }


        LambdaQueryWrapper projectGroupQuery = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getProjectGroupId, nodeId);
        ProjectGroup projectGroup = Optional.ofNullable(projectGroupRepository.selectOne(projectGroupQuery)).orElseGet(ProjectGroup::new);
        String nodeCode = ObjectUtils.isNotEmpty(projectGroup) ? projectGroup.getNodeCode() : null;
        //查询当前节点是否需要审批
        LambdaQueryWrapper<ProjectApprove> approveLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApprove.class);
        approveLambdaQueryWrapper.eq(ProjectApprove::getNodeId, nodeId)
                .eq(ProjectApprove::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey());
        ProjectApprove projectApprove = projectApproveRepository.selectOne(approveLambdaQueryWrapper);
        if (projectApprove != null) {
            projectApproveDto.setApproveId(projectApprove.getApproveId());
            projectApproveDto.setProjectId(projectApprove.getProjectId());
            if (approveStatusEnum.IN_APPROVE.getKey().equals(projectApprove.getApproveStatus())) {
                LambdaQueryWrapper<ProjectApproveDetail> detailLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class);
                //当前登录人存在 业务管理员权限，查询当前未完成的审批
                if (userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.YWGLY.getKey())) {
                    detailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveId, projectApprove.getApproveId())
//                            .eq(ProjectApproveDetail::getApproveUser, approveUser)
                            .and(wrapper -> wrapper.eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.PENDING_APPROVAL.getKey())
                                    .or().eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey()));
                } else {
                    //当前登录人不存在业务管理员权限，查询当前用户未完成的审批
                    detailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveId, projectApprove.getApproveId())
                            .eq(ProjectApproveDetail::getApproveUser, approveUser)
                            .and(wrapper -> wrapper.eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.PENDING_APPROVAL.getKey())
                                    .or().eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey()));
                }
                //此处后期需要关联审批逻辑修改
                List<ProjectApproveDetail> projectApproveDetailList = projectApproveDetailRepository.selectList(detailLambdaQueryWrapper);
                if (CollUtil.isNotEmpty(projectApproveDetailList)) {
                    List<ProjectApproveDetail> collect = projectApproveDetailList.stream().sorted(Comparator.comparing(ProjectApproveDetail::getApproveIndex)).collect(Collectors.toList());
                    ProjectApproveDetail projectApproveDetail = collect.get(0);
                    if (projectApproveDetail != null && projectApproveDetail.getApproveUser() != 0 && !userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.YWGLY.getKey())) {
                        if (approveResultEnum.APPROVE_PASS.getKey().equals(approveResult)) {
//                            //审批通过   判断是否是最后一人审批
                            Boolean lastNodeInGroupExamine = isLastNodeInGroupExamine(projectApproveDetail);
                            if (lastNodeInGroupExamine) {
                                //判断竣工验收申请任务，资料审核不通过时，不允许审批通过
                                if ("eng-00129".equals(nodeCode)) {
                                    this.applicationCompletionAcceptance(projectApproveDto.getProjectId(), projectApproveDto);
                                }
                            }
                            //判断竣工验收整改 ，不合格 不允许审批通过 ，存在承诺项 不允许审批通过，只能选择有条件通过
                            if ("eng-00135".equals(nodeCode)) {
                                this.determineCompletionAcceptance(projectApproveDto.getProjectId(), projectApproveDto, projectApproveDetail);

                            }
                            //当前二级的图纸状态为 -- 或者 审批拒绝，暂时无法审批通过，需重新选择图纸状态
                            this.getDrawingStatus(projectApproveDto.getProjectId(), nodeCode);


                            //通过
                            //更新原始数据
                            updateDateList(projectApproveDto);
                            projectApproveDetail.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
                            projectApproveDetail.setApproveResult(approveResult);
                            projectApproveDetail.setApproveOption(StringUtils.isNotEmpty(approveOption) ? approveOption : approveResultEnum.APPROVE_PASS.getSpec());
                            projectApproveDetail.setApproveUser(approveUser);
                            projectApproveDetail.setApproveEnd(nowTime);
                            projectApproveDetailRepository.updateById(projectApproveDetail);
                            //查找已出任务，更新为已完成
                            LambdaQueryWrapper<ProjectTaskInfo> projectTaskInfoLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTaskInfo.class);
                            projectTaskInfoLambdaQueryWrapper.eq(ProjectTaskInfo::getNodeId, nodeId)
                                    .eq(ProjectTaskInfo::getUserId, approveUser)
                                    .eq(ProjectTaskInfo::getJobId, projectApproveDetail.getApproveRole());
                            List<ProjectTaskInfo> projectTaskInfos = projectTaskInfoRepository.selectList(projectTaskInfoLambdaQueryWrapper);
                            if (projectTaskInfos.size() > 0) {
                                for (ProjectTaskInfo projectTaskInfo : projectTaskInfos) {
                                    if ((projectTaskInfo.getNodeName().contains("审批"))) {

                                        projectTaskInfo.setTaskStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
                                        projectTaskInfo.setPlanEndDate(new java.sql.Date(nowTime.getTime()));
                                        projectTaskInfoRepository.updateById(projectTaskInfo);
                                    }
                                }
                            }

                            //更新当前index下其他节点
                            LambdaQueryWrapper<ProjectApproveDetail> otherDetailWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class);
                            otherDetailWrapper.eq(ProjectApproveDetail::getApproveGroupId, projectApproveDetail.getApproveGroupId())
                                    .eq(ProjectApproveDetail::getApproveIndex, projectApproveDetail.getApproveIndex())
                                    .and(wrapper -> wrapper.eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.PENDING_APPROVAL.getKey())
                                            .or().eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey()));
//                                    .eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey());
                            //此处后期需要关联审批逻辑修改
                            List<ProjectApproveDetail> projectApproveDetails = projectApproveDetailRepository.selectList(otherDetailWrapper);
                            if (projectApproveDetails.size() > 0) {
                                for (ProjectApproveDetail detail : projectApproveDetails) {
                                    LambdaQueryWrapper<ProjectTaskInfo> otherTaskWrapper = Wrappers.lambdaQuery(ProjectTaskInfo.class);
                                    otherTaskWrapper.eq(ProjectTaskInfo::getNodeId, nodeId)
                                            .eq(ProjectTaskInfo::getUserId, detail.getApproveUser())
                                            .eq(ProjectTaskInfo::getJobId, detail.getApproveRole());
                                    List<ProjectTaskInfo> otherTaskInfos = projectTaskInfoRepository.selectList(otherTaskWrapper);
                                    if (otherTaskInfos.size() > 0) {
                                        for (ProjectTaskInfo projectTaskInfo : otherTaskInfos) {
                                            if ((projectTaskInfo.getNodeName().contains("审批"))) {
                                                projectTaskInfo.setTaskStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
                                                projectTaskInfo.setPlanEndDate(new java.sql.Date(nowTime.getTime()));
                                                projectTaskInfoRepository.updateById(projectTaskInfo);
                                            }
                                        }
                                    }
                                }
                                //删除
                                projectApproveDetailRepository.delete(otherDetailWrapper);
                            }
                            //获取当前流程结构
                            ProjectApproveGroup currentGroup = getCurrentGroup(projectApproveDetail);

                            if (JhSystemEnum.approveModeEnum.ALL_PASS.getKey().equals(currentGroup.getApproveMode())) {
                                //全部通过
                                ProjectApproveGroup childGroup = hasChildNode(projectApproveDetail);
                                if (childGroup.getApproveGroupId() != null) {
                                    //有子节点
                                    childGroup.setApproveStatus(approveStatusEnum.IN_APPROVE.getKey());
                                    childGroup.setApproveResult(approveResultEnum.UNDER_APPROVE.getKey());
                                    updateCurrentGroup(childGroup);
                                    //子节点发待办
                                    sendChildGroupTask(childGroup);
                                    //查找已出任务，更新为审批中
                                    LambdaQueryWrapper<ProjectTaskInfo> projectTaskInfoLambdaQueryWrapper1 = Wrappers.lambdaQuery(ProjectTaskInfo.class);
                                    projectTaskInfoLambdaQueryWrapper.eq(ProjectTaskInfo::getNodeId, nodeId)
                                            .eq(ProjectTaskInfo::getUserId, approveUser)
                                            .eq(ProjectTaskInfo::getJobId, projectApproveDetail.getApproveRole());
                                    List<ProjectTaskInfo> projectTaskInfos1 = projectTaskInfoRepository.selectList(projectTaskInfoLambdaQueryWrapper1);
                                    if (projectTaskInfos.size() > 0) {
                                        for (ProjectTaskInfo projectTaskInfo : projectTaskInfos1) {
                                            if ((projectTaskInfo.getNodeName().contains("审批"))) {
                                                projectTaskInfo.setTaskStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS2.getKey());
                                                projectTaskInfo.setPlanEndDate(new java.sql.Date(nowTime.getTime()));
                                                projectTaskInfoRepository.updateById(projectTaskInfo);
                                            }
                                        }
                                    }
                                }
                                //没有子节点
//                                Boolean lastNodeInGroup = isLastNodeInGroup(projectApproveDetail);
                                lastNodeInGroupExamine = isLastNodeInGroupExamine(projectApproveDetail);
                                if (lastNodeInGroupExamine) {
                                    //最后一个子节点
                                    //更新当前group状态
                                    currentGroup.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
                                    currentGroup.setApproveResult(approveResultEnum.APPROVE_PASS.getKey());
                                    //更新当前group
                                    int i = updateCurrentGroup(currentGroup);
                                    //更新当前节点的所有detail节点
                                    updateGroupDetail(currentGroup);
                                    //更新当前父group
                                    ProjectParentGroup currentParentGroup = getCurrentParentGroup(projectApproveDetail);
                                    updateCurrentParentGroup(currentParentGroup);
                                    //看所有父group是否结束，若结束，更新审批
                                    ProjectApprove projectApproveAll = getProjectApprove(currentParentGroup);
                                    //当前parent组完成后开启下一组
                                    startNextParentGroup(projectApproveAll, currentParentGroup);
                                    updateProjectApprove(projectApproveAll);
                                } else {
                                    //不是最后一个节点
                                    sendGroupNextTask(projectApproveDto, projectApproveDetail);
                                }

                            } else if (JhSystemEnum.approveModeEnum.ONE_PASS_PASS.getKey().equals(currentGroup.getApproveMode())) {
                                //一个通过则通过
                                ProjectApproveGroup childGroup = hasChildNode(projectApproveDetail);
                                if (childGroup.getApproveGroupId() != null) {
                                    //有子节点
                                    childGroup.setApproveStatus(approveStatusEnum.IN_APPROVE.getKey());
                                    childGroup.setApproveResult(approveResultEnum.UNDER_APPROVE.getKey());
                                    updateCurrentGroup(childGroup);
                                    //子节点发待办
                                    sendChildGroupTask(childGroup);

                                }
                                //没有子节点
                                //更新当前group状态
                                currentGroup.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
                                currentGroup.setApproveResult(approveResultEnum.APPROVE_PASS.getKey());
                                //更新当前group
                                int i = updateCurrentGroup(currentGroup);
                                //更新当前节点的所有detail节点
                                updateGroupDetail(currentGroup);
                                //更新当前父group
                                ProjectParentGroup currentParentGroup = getCurrentParentGroup(projectApproveDetail);
                                updateCurrentParentGroup(currentParentGroup);
                                //看所有父group是否结束，若结束，更新审批
                                ProjectApprove projectApproveAll = getProjectApprove(currentParentGroup);
                                //当前parent组完成后开启下一组
                                startNextParentGroup(projectApproveAll, currentParentGroup);
                                updateProjectApprove(projectApproveAll);

                            }

//                            if (ObjectUtil.isNotEmpty(projectApproveDto.getAcceptor()) && ObjectUtil.isNotEmpty(projectApproveDto.getAcceptanceDate())) {
//                                //通过后，如果存在验收人和验收日期，存进对应的三级字段里
//                                this.updateAcceptor(projectApproveDto);
//                            }

                            //发送消息通知
                            projectNoticeService.generateNotice(nodeId, projectGroup, JhSystemEnum.MessageTemplate.MB1000006, initiatUser);
                        } else if (approveResultEnum.APPROVE_REFUSE.getKey().equals(approveResult)) {
                            //审批拒绝
                            projectApproveDetail.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
                            projectApproveDetail.setApproveResult(approveResult);
                            projectApproveDetail.setApproveOption(StringUtils.isNotEmpty(approveOption) ? approveOption : approveResultEnum.APPROVE_REFUSE.getSpec());
                            projectApproveDetail.setApproveUser(approveUser);
                            projectApproveDetail.setApproveEnd(nowTime);
                            projectApproveDetailRepository.updateById(projectApproveDetail);
                            //查找已出代办任务，更新为已完成
                            LambdaQueryWrapper<ProjectTaskInfo> projectTaskInfoLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTaskInfo.class);
                            projectTaskInfoLambdaQueryWrapper.eq(ProjectTaskInfo::getNodeId, nodeId)
                                    .eq(ProjectTaskInfo::getUserId, approveUser);
                            List<ProjectTaskInfo> projectTaskInfos = projectTaskInfoRepository.selectList(projectTaskInfoLambdaQueryWrapper);
                            if (projectTaskInfos.size() > 0) {
                                for (ProjectTaskInfo projectTaskInfo : projectTaskInfos) {
                                    if ((projectTaskInfo.getNodeName().contains("审批"))) {
                                        projectTaskInfo.setTaskStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
                                        projectTaskInfo.setPlanEndDate(new java.sql.Date(nowTime.getTime()));
                                        projectTaskInfoRepository.updateById(projectTaskInfo);
                                    }
                                }
                            }
                            //更新当前index下其他节点
                            LambdaQueryWrapper<ProjectApproveDetail> otherDetailWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class);
                            otherDetailWrapper.eq(ProjectApproveDetail::getApproveGroupId, projectApproveDetail.getApproveGroupId())
                                    .eq(ProjectApproveDetail::getApproveIndex, projectApproveDetail.getApproveIndex())
                                    .eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey());
                            //此处后期需要关联审批逻辑修改
                            List<ProjectApproveDetail> projectApproveDetails = projectApproveDetailRepository.selectList(otherDetailWrapper);
                            if (projectApproveDetails.size() > 0) {
                                for (ProjectApproveDetail detail : projectApproveDetails) {
                                    LambdaQueryWrapper<ProjectTaskInfo> otherTaskWrapper = Wrappers.lambdaQuery(ProjectTaskInfo.class);
                                    otherTaskWrapper.eq(ProjectTaskInfo::getNodeId, nodeId)
                                            .eq(ProjectTaskInfo::getUserId, detail.getApproveUser());
                                    List<ProjectTaskInfo> otherTaskInfos = projectTaskInfoRepository.selectList(otherTaskWrapper);
                                    if (otherTaskInfos.size() > 0) {
                                        for (ProjectTaskInfo projectTaskInfo : otherTaskInfos) {
                                            if ((projectTaskInfo.getNodeName().contains("审批"))) {
                                                projectTaskInfo.setTaskStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
                                                projectTaskInfo.setPlanEndDate(new java.sql.Date(nowTime.getTime()));
                                                projectTaskInfoRepository.updateById(projectTaskInfo);
                                            }
                                        }
                                    }
                                }
                                //删除
                                projectApproveDetailRepository.delete(otherDetailWrapper);
                            }
                            //获取当前流程结构
                            ProjectApproveGroup currentGroup = getCurrentGroup(projectApproveDetail);

                            if (JhSystemEnum.approveModeEnum.ALL_PASS.getKey().equals(currentGroup.getApproveMode())) {
                                //全部通过
                                ProjectApproveGroup childGroup = hasChildNode(projectApproveDetail);

                                //没有子节点

                                //更新当前group状态
                                currentGroup.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
                                currentGroup.setApproveResult(approveResultEnum.APPROVE_REFUSE.getKey());
                                //更新当前group
                                int i = updateCurrentGroup(currentGroup);
                                //更新所有相关的子流程(需写方法)
                                updateAllChildGroup(projectApproveDetail);
                                //更新当前父group
                                ProjectParentGroup currentParentGroup = getCurrentParentGroup(projectApproveDetail);
                                updateCurrentParentGroup(currentParentGroup);
                                //看所有父group是否结束，若结束，更新审批
                                ProjectApprove projectApproveAll = getProjectApprove(currentParentGroup);
                                updateProjectApprove(projectApproveAll);


                                //所有审批组的审批拒绝
                                projectApproveDto.setApproveOption(StringUtils.isNotEmpty(approveOption) ? approveOption : approveResultEnum.APPROVE_REFUSE.getSpec());
                                updateAllApproveDetail(projectApproveDto, projectApprove);
                                updateAllApproveGroup(projectApproveDto, projectApprove);
                                updateAllParentGroup(projectApproveDto, projectApprove);


                            } else if (JhSystemEnum.approveModeEnum.ONE_PASS_PASS.getKey().equals(currentGroup.getApproveMode())) {

                                //一个通过则通过
                                ProjectApproveGroup childGroup = hasChildNode(projectApproveDetail);
                                if (childGroup.getApproveGroupId() != null) {
                                    //有子节点
                                    childGroup.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
                                    childGroup.setApproveResult(approveResultEnum.UNDER_APPROVE.getKey());
                                    updateCurrentGroup(childGroup);
                                }
                                //没有子节点
                                Boolean lastNodeInGroup = isLastNodeInGroup(projectApproveDetail);
                                if (lastNodeInGroup) {
                                    //最后一个子节点
                                    //更新当前group状态
                                    currentGroup.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
                                    currentGroup.setApproveResult(approveResultEnum.APPROVE_REFUSE.getKey());
                                    //更新当前group
                                    int i = updateCurrentGroup(currentGroup);
                                    //更新当前节点的所有detail节点
                                    updateGroupDetail(currentGroup);

                                    //更新当前父group
                                    ProjectParentGroup currentParentGroup = getCurrentParentGroup(projectApproveDetail);
                                    updateCurrentParentGroup(currentParentGroup);
                                    //看所有父group是否结束，若结束，更新审批
                                    ProjectApprove projectApproveAll = getProjectApprove(currentParentGroup);
                                    updateProjectApprove(projectApproveAll);

                                } else {
                                    //不是最后一个节点
                                    sendGroupNextTask(projectApproveDto, projectApproveDetail);
                                }

                            }

                            //装饰勘测报告拒绝处理
                            if (JhSystemEnum.twoNodeCodeEnum.NODE_DES103.getKey().equals(nodeCode)) {
                                //被拒绝后，把勘测报告的数据赋值给勘测报告履历的数据
                                surveyReportHistoryVersionService.createSurveyReportHistoryVersion(projectApprove);
                            }
//                            //修改订单状态
//                            if (JhSystemEnum.NodeCodeEnum.NODE_127.getKey().equals(nodeCode)) {
//                                orderNodeInfoService.updateAdvanceStatus(projectApproveDto.getOrderId(), "back");
//                            }
//
//                            //预算拒绝处理
//                            if (JhSystemEnum.NodeCodeSEEnum.NODE_401.getKey().equals(nodeCode)) {
//                                budgetVersionService.createBudgetVersion(projectApprove);
//                            }
//                            //发包拒绝处理
//                            if (JhSystemEnum.NodeCodeSEEnum.NODE_402.getKey().equals(nodeCode)) {
//                                outsourceVersionService.createOutsourceVersion(projectApprove);
//                            }

                            //竣工验收整改 拒绝处理
                            if (AtourSystemEnum.EngineeringNodeTow.ENG00135.getKey().equals(nodeCode)) {
                                //被拒绝后，把竣工验收整改 的验收单 审批意见，存进审批意见记录字段中 ： 审批人 审批状态 审批意见
                                projectCompletionReceiptService.keepRecordsAuditComments(projectApprove);
                            }

                            //审批拒绝后
                            infoApprovalRejectionService.createAbarbeitung(projectApproveDto);
                            //发送消息通知
                            projectNoticeService.generateNotice(nodeId, projectGroup, JhSystemEnum.MessageTemplate.MB1000005, initiatUser);
                        }


                    } else {
                        //若是业务管理员角色
                        if (userRoleCodes.contains(JhSystemEnum.RoleCodeEnum.YWGLY.getKey())) {
                            //进行后续操作
                            if (approveResultEnum.APPROVE_PASS.getKey().equals(approveResult)) {
                                //通过
                                projectApproveDto.setApproveOption(StringUtils.isNotEmpty(approveOption) ? approveOption : approveResultEnum.APPROVE_PASS.getSpec());
                                updateAllApproveDetail(projectApproveDto, projectApprove);
                                updateAllApproveGroup(projectApproveDto, projectApprove);
                                updateAllParentGroup(projectApproveDto, projectApprove);
                                //发送消息通知
                                projectNoticeService.generateNotice(nodeId, projectGroup, JhSystemEnum.MessageTemplate.MB1000006, initiatUser);
                            } else if (approveResultEnum.APPROVE_REFUSE.getKey().equals(approveResult)) {
                                //拒绝
                                projectApproveDto.setApproveOption(StringUtils.isNotEmpty(approveOption) ? approveOption : approveResultEnum.APPROVE_REFUSE.getSpec());
                                updateAllApproveDetail(projectApproveDto, projectApprove);
                                updateAllApproveGroup(projectApproveDto, projectApprove);
                                updateAllParentGroup(projectApproveDto, projectApprove);
                                //发送消息通知
                                projectNoticeService.generateNotice(nodeId, projectGroup, JhSystemEnum.MessageTemplate.MB1000005, initiatUser);
                            }

                        }
                    }
                }
            }

        } else {
            //不需要审批

        }
      /*  LambdaQueryWrapper<ProjectApprove> approveLambdaQueryWrapper=Wrappers.lambdaQuery(ProjectApprove.class);
        approveLambdaQueryWrapper.eq(ProjectApprove::getNodeId,nodeId)
                .eq(ProjectApprove::getApproveStatus,JhSystemEnum.approveStatusEnum.IN_APPROVE.getKey());
        List<ProjectApprove> projectApproves = projectApproveRepository.selectList(approveLambdaQueryWrapper);
        if(projectApproves.size()>0){
            for(ProjectApprove projectApprove:projectApproves){
                //有审批中的审批
                if(JhSystemEnum.approveStatusEnum.IN_APPROVE.getKey().equals(projectApprove.getApproveStatus())){
                    LambdaQueryWrapper<ProjectApproveDetail> detailLambdaQueryWrapper=Wrappers.lambdaQuery(ProjectApproveDetail.class);
                    detailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveId,projectApprove.getApproveId());
                          //  .eq(ProjectApproveDetail::getApproveUser,approveUser);
                    //此处后期需要关联审批逻辑修改
                    ProjectApproveDetail projectApproveDetail = projectApproveDetailRepository.selectOne(detailLambdaQueryWrapper);
                    if(projectApproveDetail!=null&&projectApproveDetail.getApproveUser()!=0){
                        if(JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey().equals(approveResult)){
                            projectApproveDetail.setApproveStatus(JhSystemEnum.approveStatusEnum.APPROVE_COMPLETE.getKey());
                            projectApproveDetail.setApproveResult(approveResult);
                            projectApproveDetail.setApproveOption(approveOption);
                            projectApproveDetail.setApproveUser(approveUser);
                            projectApproveDetail.setApproveEnd(nowTime);
                            projectApproveDetailRepository.updateById(projectApproveDetail);
                            projectApprove.setApproveStatus(JhSystemEnum.approveStatusEnum.APPROVE_COMPLETE.getKey());
                            projectApprove.setApproveResult(approveResult);
                            projectApprove.setApproveEnd(nowTime);
                            projectApproveRepository.updateById(projectApprove);
                            //查找已出任务，更新为已完成
                            LambdaQueryWrapper<ProjectTaskInfo> projectTaskInfoLambdaQueryWrapper=Wrappers.lambdaQuery(ProjectTaskInfo.class);
                            projectTaskInfoLambdaQueryWrapper.eq(ProjectTaskInfo::getNodeId,nodeId);
                            List<ProjectTaskInfo> projectTaskInfos = projectTaskInfoRepository.selectList(projectTaskInfoLambdaQueryWrapper);
                            if(projectTaskInfos.size()>0){
                                for(ProjectTaskInfo projectTaskInfo:projectTaskInfos){
                                    if((projectTaskInfo.getNodeName().contains("审批"))){

                                        projectTaskInfo.setTaskStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
                                        projectTaskInfoRepository.updateById(projectTaskInfo);
                                    }
                                }
                            }
                            flag=Boolean.TRUE;
                        }else if(JhSystemEnum.approveResultEnum.APPROVE_REFUSE.getKey().equals(approveResult)){
                            projectApproveDetail.setApproveStatus(JhSystemEnum.approveStatusEnum.APPROVE_COMPLETE.getKey());
                            projectApproveDetail.setApproveResult(approveResult);
                            projectApproveDetail.setApproveOption(approveOption);
                            projectApproveDetail.setApproveUser(approveUser);
                            projectApproveDetail.setApproveEnd(nowTime);
                            projectApproveDetailRepository.updateById(projectApproveDetail);
                            projectApprove.setApproveStatus(JhSystemEnum.approveStatusEnum.APPROVE_COMPLETE.getKey());
                            projectApprove.setApproveResult(approveResult);
                            projectApprove.setApproveEnd(nowTime);
                            projectApproveRepository.updateById(projectApprove);
                            //回退所有当前节点状态
                            ProjectNodeInfoDto projectNodeInfoDto=new ProjectNodeInfoDto();
                            projectNodeInfoDto.setNodeId(nodeId.toString());
                            projectNodeInfoDto.setProjectId(projectApprove.getProjectId().toString());
                            projectNodeInfoService.fallbackStatus(projectNodeInfoDto);
                            //查找已出任务，更新为已完成
                            LambdaQueryWrapper<ProjectTaskInfo> projectTaskInfoLambdaQueryWrapper=Wrappers.lambdaQuery(ProjectTaskInfo.class);
                            projectTaskInfoLambdaQueryWrapper.eq(ProjectTaskInfo::getNodeId,projectNodeInfoDto.getNodeId());
                            List<ProjectTaskInfo> projectTaskInfos = projectTaskInfoRepository.selectList(projectTaskInfoLambdaQueryWrapper);
                            if(projectTaskInfos.size()>0){
                                for(ProjectTaskInfo projectTaskInfo:projectTaskInfos){
                                    if((projectTaskInfo.getNodeName().contains("审批"))){

                                        projectTaskInfo.setTaskStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
                                        projectTaskInfoRepository.updateById(projectTaskInfo);
                                    }
                                }
                            }
                            flag=Boolean.TRUE;
                        }
                    }
                }
            }
        }*/

        return flag;
    }

    //查询当前项目任务
    public void upAppOpenConditio(Long projectId, Long nodeId, List<ApproveNodeInfoVo> approveNodeInfoVo) {
//        Map<String, String> collect = approveNodeInfoVo.stream().collect(Collectors.toMap(ProjectNodeInfo::getNodeCode, p -> ObjectUtil.isNotEmpty(p.getRemark()) ? p.getRemark() : "", (v1, v2) -> v1));
        Map<String, String> collect = approveNodeInfoVo.stream().collect(Collectors.toMap(ApproveNodeInfoVo::getNodeCode, p -> ObjectUtil.isNotEmpty(p.getRemark()) ? p.getRemark() : "", (v1, v2) -> v1));
        List<ProjectApproveDetail> openCondition = projectApproveRepository.getApproveDetailOpenCondition(projectId, nodeId);


        for (ProjectApproveDetail approveDetail : openCondition) {
            String s = collect.get(approveDetail.getApprovalOpeningConditionNodeCode());
            if (ObjectUtil.isNotEmpty(s)) {
                if (approveDetail.getApprovalOpeningConditionValue().equals(s)) {
                    //当前审批满足开启条件
                    approveDetail.setIsShow(Boolean.FALSE);
                    projectApproveDetailRepository.updateById(approveDetail);
                } else {
                    //通过
                    //当前审批不满足开启条件，自动完成该审批
                    approveDetail.setApproveStatus("approve_complete");
                    approveDetail.setApproveResult("approve_pass");
                    projectApproveDetailRepository.updateById(approveDetail);

                    //更新当前group状态
                    //获取当前流程结构
                    ProjectApproveGroup currentGroup = getCurrentGroup(approveDetail);
                    //更新当前group状态
                    currentGroup.setApproveStatus(JhSystemEnum.approveStatusEnum.APPROVE_COMPLETE.getKey());
                    currentGroup.setApproveResult(JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey());
                    //更新当前group
                    int i = updateCurrentGroup(currentGroup);
                    //更新当前节点的所有detail节点
                    updateGroupDetail(currentGroup);

                    //更新当前父group
                    ProjectParentGroup currentParentGroup = getCurrentParentGroup(approveDetail);
                    updateCurrentParentGroup(currentParentGroup);
                }
            }
        }
    }

    private void applicationCompletionAcceptance(Long projectId, ProjectApproveDto projectApproveDto) {
        //判断资料审核是否存在不通过选择，存在则不允许审批通过
        String[] nodeCode = {"eng-00129191", "eng-00129195"};
        util.initialize(projectNodeInfoService.getSubmeterProjectId(Long.valueOf(projectId)));
        LambdaQueryWrapper wrapperq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId)
                .like(ProjectNodeInfo::getNodeCode, "eng-00129")
                .eq(ProjectNodeInfo::getNodeName, "资料审核")
                .notIn(ProjectNodeInfo::getNodeCode, nodeCode)
                .and(wrapper -> wrapper.isNull(ProjectNodeInfo::getRemark)
                        .or().eq(ProjectNodeInfo::getRemark, "not_go"));
        // .or().eq(ProjectNodeInfo::getRemark, "conditional_passage")    有条件通过，也可审批通过
        Long aLong = projectNodeInfoRepository.selectCount(wrapperq);
        if (aLong > 0) {
            throw new BadRequestException("审批失败！A类资料审核不允许为空或者不通过，请重新选择！");
        }

        LambdaQueryWrapper wrapperL = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId)
                .like(ProjectNodeInfo::getNodeCode, "eng-00129")
                .eq(ProjectNodeInfo::getNodeName, "资料审核")
                .in(ProjectNodeInfo::getNodeCode, nodeCode)
                .and(wrapper -> wrapper.isNull(ProjectNodeInfo::getRemark)
                        .or().eq(ProjectNodeInfo::getRemark, "not_go"));
        Long aLong1 = projectNodeInfoRepository.selectCount(wrapperL);
        if (aLong1 > 0) {
            throw new BadRequestException("审批失败，B类资料审核不允许为空或者不通过，请重新选择！");
        }

    }

    private void determineCompletionAcceptance(Long projectId, ProjectApproveDto projectApproveDto, ProjectApproveDetail projectApproveDetail) {
        if (ObjectUtil.isNotEmpty(projectApproveDetail.getApproveRole()) && projectApproveDetail.getApproveRole().equals("1407639269599150140")) {
            String[] nodeCodes = {"eng-00135024", "eng-00135029"};
            LambdaQueryWrapper wrapperL = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectApproveDto.getProjectId())
                    .like(ProjectNodeInfo::getNodeCode, "eng-00135")
                    .eq(ProjectNodeInfo::getNodeName, "资料审核")
                    .in(ProjectNodeInfo::getNodeCode, nodeCodes)
                    .and(wrapper -> wrapper.isNull(ProjectNodeInfo::getRemark)
                            .or().eq(ProjectNodeInfo::getRemark, "not_go")
                            .or().eq(ProjectNodeInfo::getRemark, "conditional_passage"));
            Long aLong1 = projectNodeInfoRepository.selectCount(wrapperL);
            if (aLong1 > 0) {
                throw new BadRequestException("审批失败！资料审核必须为通过，请重新选择！");
            }
        }

        List<ApproveNodeInfoVo> approveNodeInfoVo = projectApproveDto.getApproveNodeInfoVo();
        if (approveNodeInfoVo == null || approveNodeInfoVo.size() == 0) {
            throw new BadRequestException("审批失败！");
        }

        for (ApproveNodeInfoVo nodeInfoVo : approveNodeInfoVo) {
            if ("eng-00135011".equals(nodeInfoVo.getNodeCode()) && "not_passed".equals(nodeInfoVo.getRemark())) {
                throw new BadRequestException("审批失败！竣工验收数据“不通过”，请重新选择！");
            }
        }
        //不合格 不允许审批通过
        //存在承诺项 不允许审批通过，只能选择有条件通过
        LambdaQueryWrapper wrapperq = Wrappers.lambdaQuery(ProjectCompletionReceipt.class)
                .eq(ProjectCompletionReceipt::getProjectId, projectId)
                .eq(ProjectCompletionReceipt::getAcceptance, "unqualified")
                .isNull(ProjectCompletionReceipt::getApprovalStatus);
        Long aLong = projectCompletionReceiptRepository.selectCount(wrapperq);
        if (aLong > 0) {
            throw new BadRequestException("审批失败！请先进行【整改审核】在审批！");
        }

        Map<String, Long> statusMap = projectCompletionReceiptService.groupApprovalStatus(projectId);
        //获取不合格的次数
        Long unqualified = statusMap.get("0");
        if (unqualified != null && unqualified > 0) {
            throw new BadRequestException("审批失败！竣工验收整改存在“不合格”数据！");
        }
        //获取承诺项的次数,存在承诺项数据只能选择有条件通过
        Long commitmentItem = statusMap.get("2");
        if (commitmentItem != null && commitmentItem > 0) {
            //获取复核状态信息
            if (approveNodeInfoVo == null || approveNodeInfoVo.size() == 0) {
                throw new BadRequestException("审批失败！竣工验收存在承诺项数据只能选择“有条件通过”，请重新选择！");
            }
            for (ApproveNodeInfoVo nodeInfoVo : approveNodeInfoVo) {
                if ("eng-00135011".equals(nodeInfoVo.getNodeCode()) && !"conditional_passage".equals(nodeInfoVo.getRemark())) {
                    throw new BadRequestException("审批失败！竣工验收存在承诺项数据只能选择“有条件通过”，请重新选择！");
                }
            }
        }
    }


    private void getDrawingStatus(Long projectId, String nodeCode) {
        //审批通过的时候，判断有没有当前二级任务有没有审图列表，有审图列表在判断状态

        List<String> drawingStatus = new ArrayList<>();
        drawingStatus.add(AtourSystemEnum.ReviewDrawingStatus.NULL.getKey());
        drawingStatus.add(AtourSystemEnum.ReviewDrawingStatus.APPROVAL_REJECTION.getKey());

        //查询当前项目的二级任务中是否存在审图审批拒绝 或者 -- 的数据
        LambdaQueryWrapper<ProjectGroupExpand> queryWrapper = Wrappers.lambdaQuery(ProjectGroupExpand.class);
        queryWrapper.eq(ProjectGroupExpand::getProjectId, projectId)
                .eq(ProjectGroupExpand::getGroupNodeCode, nodeCode)
                .in(ProjectGroupExpand::getDrawingStatus, drawingStatus);
        Long aLong = projectGroupExpandService.count(queryWrapper);
        if (aLong > 0) {
            throw new BadRequestException("当前图纸状态未选择或审批拒绝，暂无法审批通过！请重新选择图纸状态");
        }
    }

//
//    private void updateAcceptor(ProjectApproveDto projectApproveDto) {
//        LambdaQueryWrapper<ProjectNodeInfo> infoLambdaQueryWrapper1 = Wrappers.lambdaQuery(ProjectNodeInfo.class)
//                .eq(ProjectNodeInfo::getProjectId, projectApproveDto.getProjectId())
//                .eq(ProjectNodeInfo::getNodeCode, JhSystemEnum.threeNodeCodeEnum.NODE_ENG129056.getKey());
//        ProjectNodeInfo one = projectNodeInfoService.getOne(infoLambdaQueryWrapper1);
//        one.setRemark(projectApproveDto.getAcceptor());
//        projectNodeInfoService.update(one);
//
//        //把验收人插入到【竣工验收】的负责人【干系人】里
//        if (ObjectUtil.isNotEmpty(projectApproveDto.getAcceptor())) {
//            projectInfoService.createProjectStakeholders(projectApproveDto.getProjectId(),
//                    AtourSystemEnum.engineeringRoleCodeEnum.JGYSRY.getKey(), Long.valueOf(projectApproveDto.getAcceptor()),null);
//        }
//
//        LambdaQueryWrapper<ProjectNodeInfo> infoLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
//                .eq(ProjectNodeInfo::getProjectId, projectApproveDto.getProjectId())
//                .eq(ProjectNodeInfo::getNodeCode, JhSystemEnum.threeNodeCodeEnum.NODE_ENG129057.getKey());
//        ProjectNodeInfo one1 = projectNodeInfoService.getOne(infoLambdaQueryWrapper);
//        one1.setRemark(projectApproveDto.getAcceptanceDate());
//        projectNodeInfoService.update(one1);
//    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean withdrawSubmit(ProjectApproveDetailDto projectApproveDetailDto) {
        //审批撤回
        ProjectGroup projectGroup = projectGroupRepository.selectById(projectApproveDetailDto.getNodeId());
        if (!projectGroup.getNodeStatus().equals(JhSystemEnum.NodeStatusEnum.NODE_STATUS2.getKey())) {
            throw new BadRequestException("当前审批记录不支持撤回，任务不是【审批中】状态！");
        }

        //查询当前审批操作人发送给下一级审批人的代办信息，且代办创建时间大于审批修改时间
        LambdaQueryWrapper<ProjectTaskInfo> wrapper = Wrappers.lambdaQuery(ProjectTaskInfo.class);
        wrapper.eq(ProjectTaskInfo::getNodeId, projectApproveDetailDto.getNodeId());
        wrapper.like(ProjectTaskInfo::getNodeName, "审批");
        wrapper.like(ProjectTaskInfo::getCreateBy, projectApproveDetailDto.getApproveUser());
        wrapper.ge(ProjectTaskInfo::getCreateTime, projectApproveDetailDto.getUpdateTime());
        List<ProjectTaskInfo> taskInfos = projectTaskInfoRepository.selectList(wrapper);
        boolean b = taskInfos.stream().anyMatch(task -> KidsSystemEnum.TaskStatusEnum.FINISH.getValue().equals(task.getTaskStatus()));
        //如果发送给下一级审批人的代办信息被完成，则当前不允许撤回
        if (b) {
            throw new BadRequestException("当前审批记录不支持撤回，已有审批操作完成！");
        }

        //获取该任务审批代办记录的创建时间大于当前审批记录操作时间 的角色ID
        List<Long> collect = taskInfos.stream().map(ProjectTaskInfo::getJobId).collect(Collectors.toList());


        ProjectApproveGroup approveGroup = projectApproveGroupRepository.selectById(projectApproveDetailDto.getApproveGroupId());

        //3.修改下一个分组的 getApproveStatus  getApproveResult 为空
        LambdaUpdateWrapper groupNextUpdateWrapper = Wrappers.lambdaUpdate(ProjectApproveGroup.class)
                .eq(ProjectApproveGroup::getApproveId, approveGroup.getApproveId())
                .eq(ProjectApproveGroup::getApproveGroup, Integer.valueOf(approveGroup.getApproveGroup()) + 1)
                .set(ProjectApproveGroup::getApproveStatus, null)
                .set(ProjectApproveGroup::getApproveResult, null);
        projectApproveGroupService.update(groupNextUpdateWrapper);

        //4.修改下一个分组的审批详情为 待审批状态
//        LambdaQueryWrapper groupNextQueryWrapper = Wrappers.lambdaQuery(ProjectApproveGroup.class)
//                .eq(ProjectApproveGroup::getApproveId, approveGroup.getApproveId())
//                .eq(ProjectApproveGroup::getApproveGroup, Integer.valueOf(approveGroup.getApproveGroup()) + 1);
//        ProjectApproveGroup one = projectApproveGroupService.getOne(groupNextQueryWrapper);
        if (ObjectUtil.isNotEmpty(collect)) {
            LambdaUpdateWrapper groupDetailNextUpdateWrapper = Wrappers.lambdaUpdate(ProjectApproveDetail.class)
                    .eq(ProjectApproveDetail::getApproveId, approveGroup.getApproveId())
                    .in(ProjectApproveDetail::getApproveRole, collect)
                    .set(ProjectApproveDetail::getApproveStatus, approveStatusEnum.PENDING_APPROVAL.getKey())
                    .set(ProjectApproveDetail::getApproveResult, approveResultEnum.PENDING_APPROVAL.getKey());
            projectApproveDetailService.update(groupDetailNextUpdateWrapper);
        }

        //1.修改当前需撤回的审批详情状态为 【审批中】
        LambdaUpdateWrapper updateWrapper = Wrappers.lambdaUpdate(ProjectApproveDetail.class)
                .eq(ProjectApproveDetail::getApproveDetailId, projectApproveDetailDto.getApproveDetailId())
                .set(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey())
                .set(ProjectApproveDetail::getApproveResult, approveResultEnum.UNDER_APPROVE.getKey())
                .set(ProjectApproveDetail::getApproveEnd, null)
                .set(ProjectApproveDetail::getApproveOption, null);
        projectApproveDetailService.update(updateWrapper);

        //2.修改当前需撤回的审批分组状态为【审批中】
        LambdaUpdateWrapper groupUpdateWrapper = Wrappers.lambdaUpdate(ProjectApproveGroup.class)
                .eq(ProjectApproveGroup::getApproveGroupId, projectApproveDetailDto.getApproveGroupId())
                .set(ProjectApproveGroup::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey())
                .set(ProjectApproveGroup::getApproveResult, approveResultEnum.UNDER_APPROVE.getKey());
        projectApproveGroupService.update(groupUpdateWrapper);

        //4.删除发送的审批代办
        projectTaskInfoRepository.delete(wrapper);

        return true;
    }

    @Override
    public Boolean isApprovePass(String nodeCode, Long projectId) {
        if (ObjectUtils.isEmpty(projectId)) {
            return true;
        }
        LambdaQueryWrapper groupQuery = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getProjectId, projectId)
                .eq(ProjectGroup::getNodeCode, nodeCode);
        ProjectGroup group = Optional.ofNullable(projectGroupService.getOne(groupQuery)).orElseGet(ProjectGroup::new);
        if (ObjectUtils.isNotEmpty(group.getProjectGroupId())) {
            LambdaQueryWrapper approveQuery = Wrappers.lambdaQuery(ProjectApprove.class)
                    .eq(ProjectApprove::getProjectId, projectId)
                    .eq(ProjectApprove::getNodeId, group.getProjectGroupId());
//            ProjectApprove approve = Optional.ofNullable(getOne(approveQuery)).orElseGet(ProjectApprove::new);
            List<ProjectApprove> projectApproveList = list(approveQuery);
            List<ProjectApprove> collect = projectApproveList.stream().filter(approve -> ObjectUtils.isNotEmpty(approve.getApproveResult()) && ObjectUtils.isNotEmpty(approve.getApproveStatus())
                    && approveResultEnum.APPROVE_PASS.getKey().equals(approve.getApproveResult())
                    && approveStatusEnum.APPROVE_COMPLETE.getKey().equals(approve.getApproveStatus())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                return true;
            }
            /*if (ObjectUtils.isNotEmpty(approve.getApproveResult()) && ObjectUtils.isNotEmpty(approve.getApproveStatus())
                    && JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey().equals(approve.getApproveResult())
                    && JhSystemEnum.approveStatusEnum.APPROVE_COMPLETE.getKey().equals(approve.getApproveStatus())) {
                return true;
            }*/
        }
        return false;
    }


    @Override
    public ApproveDetailVo isApprove(ProjectApproveDto projectApproveDto) {
        ApproveDetailVo approveDetailVo = new ApproveDetailVo();
        Boolean flag = Boolean.FALSE;
        //判断当前登录人是否在审批中
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        Long nodeId = projectApproveDto.getNodeId();
        if (nodeId == null || nodeId == 0) {
            //todo需修改
            // throw new BadRequestException("审批节点为空，请重新提交请求");
        }
        //查询当前节点是否需要审批
        LambdaQueryWrapper<ProjectApprove> approveLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApprove.class);
        approveLambdaQueryWrapper.eq(ProjectApprove::getNodeId, nodeId)
                .eq(ProjectApprove::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey());
        ProjectApprove projectApprove = projectApproveRepository.selectOne(approveLambdaQueryWrapper);
        if (projectApprove != null) {
            //有审批中的审批 查找审批group
            if (approveStatusEnum.IN_APPROVE.getKey().equals(projectApprove.getApproveStatus())) {
                LambdaQueryWrapper<ProjectApproveGroup> groupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveGroup.class);
                groupLambdaQueryWrapper.eq(ProjectApproveGroup::getNodeId, nodeId)
                        .eq(ProjectApproveGroup::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey());
                List<ProjectApproveGroup> projectApproveGroups = Optional.ofNullable(projectApproveGroupRepository.selectList(groupLambdaQueryWrapper)).orElseGet(LinkedList::new);
                if (projectApproveGroups.size() > 0) {
                    for (ProjectApproveGroup projectApproveGroup : projectApproveGroups) {
                        if (JhSystemEnum.approveBeginEnum.ALL_EXCUTE.getKey().equals(projectApproveGroup.getApproveBegin())) {
                            LambdaQueryWrapper<ProjectApproveDetail> detailLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class);
                            if (ObjectUtil.isNotEmpty(projectApproveDto.getApproveRole())) {
                                detailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveId, projectApprove.getApproveId())
                                        .eq(ProjectApproveDetail::getApproveUser, currentUserId)
                                        .eq(ProjectApproveDetail::getApproveRole, projectApproveDto.getApproveRole())
                                        .eq(ProjectApproveDetail::getApproveGroupId, projectApproveGroup.getApproveGroupId())
                                        .eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey());
                            } else {
                                detailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveId, projectApprove.getApproveId())
                                        .eq(ProjectApproveDetail::getApproveUser, currentUserId)
                                        .eq(ProjectApproveDetail::getApproveGroupId, projectApproveGroup.getApproveGroupId())
                                        .eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey())
                                        .last("limit 1");
                            }
                            ProjectApproveDetail projectApproveDetail = projectApproveDetailRepository.selectOne(detailLambdaQueryWrapper);
                            if (ObjectUtils.isNotEmpty(projectApproveDetail) && ObjectUtils.isNotEmpty(projectApproveDetail.getApproveUser())
                                    && projectApproveDetail.getApproveUser() != 0) {
                                flag = Boolean.TRUE;
                                approveDetailVo.setFlag(flag);
                                Boolean isModifiable = false;
                                if (ObjectUtil.isNotEmpty(projectApproveDetail.getIsModifiable())) {
                                    if (projectApproveDetail.getIsModifiable() == 1) {
                                        isModifiable = true;
                                    }
                                }
//                                Boolean isModifiable = projectApproveDetail.getIsModifiable();
                                approveDetailVo.setIsModifiable(projectApproveDetail.getIsModifiable());
                                String modifiableCode = projectApproveDetail.getModifiableCode();
                                if (isModifiable != null && isModifiable && ObjectUtil.isNotEmpty(modifiableCode)) {
                                    //查询所有的code所对应的node
                                    String[] split = modifiableCode.split(",");
                                    List<ApproveNodeInfoVo> nodeInfoVos = new ArrayList<>();
                                    for (int i = 0; i < split.length; i++) {
                                        Long orderId = projectApproveDto.getOrderId();
                                        Long projectId = projectApproveDto.getProjectId();
                                        String code = split[i];
                                        ApproveNodeInfoVo approveNodeInfoVo = new ApproveNodeInfoVo();
                                        if (orderId != null) {
                                            LambdaQueryWrapper<OrderNodeInfo> nodeInfoLambdaQueryWrapper = Wrappers.lambdaQuery(OrderNodeInfo.class);
                                            nodeInfoLambdaQueryWrapper.eq(OrderNodeInfo::getOrderId, orderId).eq(OrderNodeInfo::getNodeCode, code);
                                            OrderNodeInfo orderNodeInfo = orderNodeInfoRepository.selectOne(nodeInfoLambdaQueryWrapper);
                                            BeanUtils.copyProperties(orderNodeInfo, approveNodeInfoVo);
                                            approveNodeInfoVo.setOrderId(orderId.toString());
                                            approveNodeInfoVo.setNodeId(orderNodeInfo.getNodeId().toString());
                                            System.out.println(code);
                                            nodeInfoVos.add(approveNodeInfoVo);
                                        } else if (projectId != null) {
                                            LambdaQueryWrapper<ProjectNodeInfo> nodeInfoLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class);
                                            nodeInfoLambdaQueryWrapper.eq(ProjectNodeInfo::getProjectId, projectId).eq(ProjectNodeInfo::getNodeCode, code);
                                            ProjectNodeInfo projectNodeInfo = Optional.ofNullable(projectNodeInfoService.getOne(nodeInfoLambdaQueryWrapper)).orElseGet(ProjectNodeInfo::new);
                                            BeanUtils.copyProperties(projectNodeInfo, approveNodeInfoVo);
                                            approveNodeInfoVo.setProjectId(projectId.toString());
                                            approveNodeInfoVo.setNodeId(projectNodeInfo.getNodeId().toString());
                                            nodeInfoVos.add(approveNodeInfoVo);
                                        }
                                    }
                                    approveDetailVo.setApproveNodeInfoVo(nodeInfoVos);
                                }

                            }
                        } else if (JhSystemEnum.approveBeginEnum.IN_ORDER.getKey().equals(projectApproveGroup.getApproveBegin())) {
                            LambdaQueryWrapper<ProjectApproveDetail> detailLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class);
                            detailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveId, projectApprove.getApproveId())
                                    .eq(ProjectApproveDetail::getApproveGroupId, projectApproveGroup.getApproveGroupId())
                                    .eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey())
                                    .orderBy(true, true, ProjectApproveDetail::getApproveIndex);
                            List<ProjectApproveDetail> projectApproveDetails = Optional.ofNullable(projectApproveDetailRepository.selectList(detailLambdaQueryWrapper)).orElseGet(LinkedList::new);
                            Map<Integer, List<ProjectApproveDetail>> collect = projectApproveDetails.stream().collect(Collectors.groupingBy(d -> ObjectUtil.isEmpty(d.getApproveIndex()) ? 0 : d.getApproveIndex()));
                            if (MapUtils.isEmpty(collect)) {
                                collect = new HashMap<>();
                            }
                            Iterator<Map.Entry<Integer, List<ProjectApproveDetail>>> iterator = collect.entrySet().iterator();
                            Map.Entry<Integer, List<ProjectApproveDetail>> integerListEntry = null;
                            while (iterator.hasNext()) {
                                integerListEntry = iterator.next();
                                break;
                            }
//                            Map.Entry<Integer, List<ProjectApproveDetail>> integerListEntry = collect.entrySet().stream().findFirst().get();
                            if (ObjectUtils.isNotEmpty(integerListEntry)) {
                                for (ProjectApproveDetail projectApproveDetail : integerListEntry.getValue()) {
                                    if (currentUserId.equals(projectApproveDetail.getApproveUser())) {
                                        flag = Boolean.TRUE;
                                        approveDetailVo.setFlag(flag);
                                        Boolean isModifiable = false;
                                        if (ObjectUtil.isNotEmpty(projectApproveDetail.getIsModifiable()) && projectApproveDetail.getIsModifiable() == 1) {
                                            isModifiable = true;
                                        }
//                                        Boolean isModifiable = projectApproveDetail.getIsModifiable();
                                        approveDetailVo.setIsModifiable(projectApproveDetail.getIsModifiable());
                                        if (ObjectUtil.isNotEmpty(isModifiable) && isModifiable && ObjectUtil.isNotEmpty(projectApproveDetail.getModifiableCode())) {
                                            //查询所有的code所对应的node
                                            String modifiableCode = projectApproveDetail.getModifiableCode();
                                            String[] split = modifiableCode.split(",");
                                            List<ApproveNodeInfoVo> nodeInfoVos = new ArrayList<>();
                                            for (int i = 0; i < split.length; i++) {
                                                Long orderId = projectApproveDto.getOrderId();
                                                Long projectId = projectApproveDto.getProjectId();
                                                String code = split[i];
                                                ApproveNodeInfoVo approveNodeInfoVo = new ApproveNodeInfoVo();
                                                if (orderId != null) {
                                                    LambdaQueryWrapper<OrderNodeInfo> nodeInfoLambdaQueryWrapper = Wrappers.lambdaQuery(OrderNodeInfo.class);
                                                    nodeInfoLambdaQueryWrapper.eq(OrderNodeInfo::getOrderId, orderId).eq(OrderNodeInfo::getNodeCode, code);
                                                    OrderNodeInfo orderNodeInfo = Optional.ofNullable(orderNodeInfoRepository.selectOne(nodeInfoLambdaQueryWrapper)).orElseGet(OrderNodeInfo::new);
                                                    BeanUtils.copyProperties(orderNodeInfo, approveNodeInfoVo);
                                                    approveNodeInfoVo.setOrderId(orderId.toString());
                                                    approveNodeInfoVo.setNodeId(orderNodeInfo.getNodeId().toString());
                                                    System.out.println(code);
                                                    if (ObjectUtils.isNotEmpty(code) && JhSystemEnum.NodeCodeEnum.NODE_90902.getKey().equals(code)) {
                                                        LambdaQueryWrapper orderDetailQueryWrapper = Wrappers.lambdaQuery(OrderDetail.class)
                                                                .eq(OrderDetail::getOrderId, orderId);
                                                        List<OrderDetail> orderDetailList = orderDetailRepository.selectList(orderDetailQueryWrapper);
                                                        for (OrderDetail od : orderDetailList) {
                                                            if (ObjectUtils.isNotEmpty(od.getSubtotal())) {
                                                                od.setApprovedNum(od.getSubtotal());
                                                            }
                                                        }
                                                        approveNodeInfoVo.setOrderDetails(orderDetailMapper.toDto(orderDetailList));
                                                    }
                                                    //特殊项目
                                                    if (JhSystemEnum.NodeCodeEnum.NODE_90904.getKey().equals(code)) {
                                                        LambdaQueryWrapper specialProjectQueryWrapper = Wrappers.lambdaQuery(SpecialProject.class)
                                                                .eq(SpecialProject::getOrderId, orderId)
                                                                .eq(SpecialProject::getIsDelete, false);
                                                        List<SpecialProject> specialProjectList = specialProjectRepository.selectList(specialProjectQueryWrapper);
                                                        for (SpecialProject sp : specialProjectList) {
                                                            if (ObjectUtils.isNotEmpty(sp.getNum())) {
                                                                sp.setApprovedNum(sp.getNum());
                                                            }
                                                        }
                                                        approveNodeInfoVo.setSpecialProjects(specialProjectMapper.toDto(specialProjectList));
                                                    }

                                                    nodeInfoVos.add(approveNodeInfoVo);

                                                } else if (projectId != null) {
                                                    LambdaQueryWrapper<ProjectNodeInfo> nodeInfoLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class);
                                                    nodeInfoLambdaQueryWrapper.eq(ProjectNodeInfo::getProjectId, projectId).eq(ProjectNodeInfo::getNodeCode, code);
                                                    ProjectNodeInfo projectNodeInfo = Optional.ofNullable(projectNodeInfoService.getOne(nodeInfoLambdaQueryWrapper)).orElseGet(ProjectNodeInfo::new);
                                                    BeanUtils.copyProperties(projectNodeInfo, approveNodeInfoVo);
                                                    approveNodeInfoVo.setProjectId(projectId.toString());
                                                    approveNodeInfoVo.setNodeId(ObjectUtil.isNotEmpty(projectNodeInfo.getNodeId()) ? projectNodeInfo.getNodeId().toString() : "");
                                                    nodeInfoVos.add(approveNodeInfoVo);
                                                }
                                            }
                                            approveDetailVo.setApproveNodeInfoVo(nodeInfoVos);

                                        }

                                        break;
                                    }
                                }
                            }

                        }
                    }

                }

               /* LambdaQueryWrapper<ProjectApproveDetail> detailLambdaQueryWrapper=Wrappers.lambdaQuery(ProjectApproveDetail.class);
                detailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveId,projectApprove.getApproveId())
                        .eq(ProjectApproveDetail::getApproveUser,currentUserId);
                //此处后期需要关联审批逻辑修改
                ProjectApproveDetail projectApproveDetail = projectApproveDetailRepository.selectOne(detailLambdaQueryWrapper);
                if(projectApproveDetail!=null&&projectApproveDetail.getApproveUser()!=0){
                    flag=Boolean.TRUE;
                }else{*/
                //查找业务管理员(业务员给临时审批权限)
                List<Role> rolesByUserId = Optional.ofNullable(roleRepository.findRolesByUserId(currentUserId)).orElseGet(LinkedList::new);
                if (rolesByUserId.size() > 0) {
                    Role role = rolesByUserId.get(0);
                    if (JhSystemEnum.JobEnum.YWGLY.getKey().equals(role.getRoleCode())) {
                        flag = Boolean.TRUE;
                        approveDetailVo.setFlag(flag);
                    }
                }
                // }


            }
        }
        //return null;

        return approveDetailVo;
    }

    @Override
    public List<ProjectApproveDto> listSubmitAndDetail(ProjectApproveQueryCriteria criteria) {
        //获取当前登录人信息
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        //当前用户在项目中没有角色的时候，查询是否是超级管理员和业务管理员角色
        final List<String> codes = roleRepository.findRoleCodesByUserId1(currentUserId);
        if (ObjectUtil.isNotEmpty(criteria.getNodeId())) {


            ProjectGroup byId = projectGroupService.getById(criteria.getNodeId());
            LambdaQueryWrapper queryApprove = Wrappers.lambdaQuery(ProjectApprove.class)
                    .eq(ProjectApprove::getNodeId, criteria.getNodeId())
                    .eq(ProjectApprove::getIsDelete, false)
                    .orderByDesc(ProjectApprove::getCreateTime);
            List<ProjectApproveDto> list = Optional.ofNullable(projectApproveMapper.toDto(projectApproveRepository.selectList(queryApprove))).orElseGet(LinkedList::new);
            if (ObjectUtil.isNotEmpty(list)) {
                //获取当前任务审批代办已完成的记录
                Map<String, List<ProjectTaskInfo>> collect = new HashMap<>();
                //最新一轮的审批 = 0
                int latestApproval = 0;
                for (ProjectApproveDto dto : list) {
                    if (latestApproval == 0 && dto.getApproveStatus().equals(approveStatusEnum.IN_APPROVE.getKey())) {
                        //获取当前任务的审批代办
                        LambdaQueryWrapper<ProjectTaskInfo> wrapper = Wrappers.lambdaQuery(ProjectTaskInfo.class);
                        wrapper.eq(ProjectTaskInfo::getNodeId, dto.getNodeId());
                        wrapper.like(ProjectTaskInfo::getNodeName, "审批");
                        wrapper.eq(ProjectTaskInfo::getTaskStatus, KidsSystemEnum.TaskStatusEnum.FINISH.getValue());
                        wrapper.ge(ProjectTaskInfo::getCreateTime, dto.getCreateTime());
                        List<ProjectTaskInfo> taskInfos = projectTaskInfoRepository.selectList(wrapper);

                        //获取当前任务已完成审批代办的发送人
                        collect = taskInfos.stream().collect(Collectors.groupingBy(a -> a.getCreateBy().split(":")[1]));
                    }
                    List<ProjectNodeInfoApprovalRejection> versions = projectInfoService.getApproveRejectedVersions(dto.getProjectId(), dto.getApproveId().toString());
                    if (ObjectUtil.isNotEmpty(versions)) {
                        dto.setABooleanVersions(true);
                        dto.setVersions(versions);
                    }

                    if (null != dto.getSubmitUser()) {
                        UserDto UserDto = userService.findById(dto.getSubmitUser());
                        if (null != UserDto) {
                            dto.setSubmitUserName(UserDto.getUsername());
                            if (StringUtils.isNotEmpty(UserDto.getUsername()) && !UserDto.getUsername().equals(UserDto.getNickName())) {
                                dto.setSubmitUserName(UserDto.getUsername() + "-" + UserDto.getNickName());
                            }
                        }
                        List<Role> rolesByUserId = Optional.ofNullable(roleRepository.findRolesByUserId(dto.getSubmitUser())).orElseGet(LinkedList::new);
                        if (rolesByUserId.size() != 0) {
                            //拼接角色
                            // dto.setSubmitRoleName(rolesByUserId.get(0).getName());
                            dto.setSubmitRoleName(rolesByUserId.stream().map(Role::getName).collect(Collectors.joining(",")));
                        }
                    }
                    LambdaQueryWrapper queryDetail = Wrappers.lambdaQuery(ProjectApproveDetail.class)
                            .eq(ProjectApproveDetail::getApproveId, dto.getApproveId()).orderByAsc(ProjectApproveDetail::getApproveIndex);
                    List<ProjectApproveDetailDto> detailList = projectApproveDetailMapper.toDto(projectApproveDetailRepository.selectList(queryDetail));
                    //是否拒绝
                    boolean isTurnDown = false;
                    List<ProjectApproveDetailDto> detailLists = new ArrayList<>();
                    for (ProjectApproveDetailDto detailDto : detailList) {
                        if (isTurnDown || (ObjectUtil.isNotEmpty(detailDto.getApproveOption()) && detailDto.getApproveOption().equals("系统结束"))) {
                            //获取在【审批状态-撤回】后面的审批记录 以及 【系统结束】的审批记录
                            detailLists.add(detailDto);
                            continue;
                        }

                        if (approveStatusEnum.APPROVE_COMPLETE.getKey().equals(detailDto.getApproveStatus())
                                && approveResultEnum.APPROVE_REFUSE.getKey().equals(detailDto.getApproveResult())
                        ) {
                            isTurnDown = true;
                        }
                        if (latestApproval == 0) {
                            //当前审批完成 且审批通过
                            if (detailDto.getApproveStatus().equals(approveStatusEnum.APPROVE_COMPLETE.getKey())
                                    && detailDto.getApproveResult().equals(approveResultEnum.APPROVE_PASS.getKey())) {
                                //查询当前审批通过的审批人发送的代办消息是否有被完成
                                List<ProjectTaskInfo> taskInfos = collect.get(detailDto.getApproveUser().toString());
                                if (ObjectUtil.isNotEmpty(byId) && byId.getNodeStatus().equals(JhSystemEnum.NodeStatusEnum.NODE_STATUS2.getKey()) &&
                                        ObjectUtil.isEmpty(taskInfos) &&
                                        (currentUserId.equals(detailDto.getApproveUser())
                                                || codes.contains(JhSystemEnum.RoleCodeEnum.YWGLY.getKey())
                                                || codes.contains(JhSystemEnum.RoleCodeEnum.CJGLY.getKey()))
                                ) {
                                    //1.当前二级任务审批中，
                                    // 2.'审批通过的审批人'发送的代办没有被完成，
                                    // 3.当前登录人是'当前审批通过的审批人',或者当前登录人是超级管理员或业务管理员，给 下一人是否审批赋值0未审批
                                    detailDto.setIsNextApproval(0);
                                }
                            }
                        }
                        if (null != detailDto.getApproveUser()) {
                            UserDto UserDto = userService.findById(detailDto.getApproveUser());
                            if (null != UserDto) {
                                detailDto.setApproveUserName(UserDto.getUsername());
                                if (StringUtils.isNotEmpty(UserDto.getUsername()) && !UserDto.getUsername().equals(UserDto.getNickName())) {
                                    detailDto.setApproveUserName(UserDto.getUsername() + "-" + UserDto.getNickName());
                                }
                            }
                            if (ObjectUtil.isNotEmpty(detailDto.getApproveRole())) {
                                Role roleByRoleId = roleRepository.findRoleByRoleId(Long.parseLong(detailDto.getApproveRole()));
                                if (roleByRoleId != null) {
                                    // detailDto.setApproveRoleName(rolesByUserId.get(0).getName());
                                    detailDto.setApproveRoleName(roleByRoleId.getName());
                                }
                            }
                        } else {
                            detailDto.setApproveUserName(detailDto.getEmpCode());
                        }
                    }
                    //清除掉【系统结束】的审批记录，不显示在列表中
                    detailList.removeAll(detailLists);
                    dto.setProjectApproveDetailDtos(detailList);
                    latestApproval = latestApproval + 1;
                }
            } else {
                //1.获取当前任务对应的审批主信息的主键id
                List<ProjectTemplateApproveRelationDto> appRelation = projectTemplateApproveRelationService.getAppRelation(byId.getTemplateId(), byId.getTemplateGroupId());
                Long approveTemplateId = appRelation.stream().map(ProjectTemplateApproveRelationDto::getApproveMatrixId).findFirst().orElse(null);
                //2.根据审批主键id找到关联的审批角色信息
                if (null == approveTemplateId) {
                    return list;
                }
                ProjectApproveDto approveDto = new ProjectApproveDto();
                List<ProjectApproveDetailDto> projectApproveDetailDtos = new ArrayList<>();
                List<ApproveTemplateDetailDto> templateDetail = approveTemplateDetailService.getApproveTemplateDetail(approveTemplateId);
                for (ApproveTemplateDetailDto detailDto : templateDetail) {
                    ProjectApproveDetailDto projectApproveDetailDto = new ProjectApproveDetailDto();
                    BeanUtils.copyProperties(detailDto, projectApproveDetailDto);
                    projectApproveDetailDtos.add(projectApproveDetailDto);
                }
                approveDto.setProjectApproveDetailDtos(projectApproveDetailDtos);
                list.add(approveDto);
            }
            return list;
        }
        return null;
    }
//    {
//        LambdaQueryWrapper queryApprove = Wrappers.lambdaQuery(ProjectApprove.class)
//                .eq(ProjectApprove::getNodeId, criteria.getNodeId())
//                .eq(ProjectApprove::getIsDelete, false)
//                .orderByDesc(ProjectApprove::getCreateTime);
//        List<ProjectApproveDetailDto> approveDtoList = new ArrayList<>();
//
//        List<ProjectApproveDto> list = Optional.ofNullable(projectApproveMapper.toDto(projectApproveRepository.selectList(queryApprove))).orElseGet(LinkedList::new);
//        for (ProjectApproveDto dto : list) {
//            List<ProjectNodeInfoApprovalRejection> versions = projectInfoService.getApproveRejectedVersions(dto.getProjectId(), dto.getApproveId().toString());
//            if (ObjectUtil.isNotEmpty(versions)) {
//                dto.setABooleanVersions(true);
//                dto.setVersions(versions);
//            }
//
//            if (null != dto.getSubmitUser()) {
//                UserDto UserDto = userService.findById(dto.getSubmitUser());
//                if (null != UserDto) {
//                    dto.setSubmitUserName(UserDto.getUsername());
//                    if (StringUtils.isNotEmpty(UserDto.getUsername()) && !UserDto.getUsername().equals(UserDto.getNickName())) {
//                        dto.setSubmitUserName(UserDto.getUsername() + "-" + UserDto.getNickName());
//                    }
//                }
//                List<Role> rolesByUserId = Optional.ofNullable(roleRepository.findRolesByUserId(dto.getSubmitUser())).orElseGet(LinkedList::new);
//                if (rolesByUserId.size() != 0) {
//                    //拼接角色
//
//                    // dto.setSubmitRoleName(rolesByUserId.get(0).getName());
//                    dto.setSubmitRoleName(rolesByUserId.stream().map(Role::getName).collect(Collectors.joining(",")));
//                }
//            }
//            LambdaQueryWrapper queryDetail = Wrappers.lambdaQuery(ProjectApproveDetail.class)
//                    .eq(ProjectApproveDetail::getApproveId, dto.getApproveId()).orderByAsc(ProjectApproveDetail::getApproveIndex);
//            List<ProjectApproveDetailDto> detailList = projectApproveDetailMapper.toDto(projectApproveDetailRepository.selectList(queryDetail));
//            for (ProjectApproveDetailDto detailDto : detailList) {
//                if (null != detailDto.getApproveUser()) {
//                    UserDto UserDto = userService.findById(detailDto.getApproveUser());
//                    if (null != UserDto) {
//                        detailDto.setApproveUserName(UserDto.getUsername());
//                        if (StringUtils.isNotEmpty(UserDto.getUsername()) && !UserDto.getUsername().equals(UserDto.getNickName())) {
//                            detailDto.setApproveUserName(UserDto.getUsername() + "-" + UserDto.getNickName());
//                        }
//                    }
//                    if (ObjectUtil.isNotEmpty(detailDto.getApproveRole())) {
//                        Role roleByRoleId = roleRepository.findRoleByRoleId(Long.parseLong(detailDto.getApproveRole()));
//                        if (roleByRoleId != null) {
//                            // detailDto.setApproveRoleName(rolesByUserId.get(0).getName());
//                            detailDto.setApproveRoleName(roleByRoleId.getName());
//                        }
//                    }
//                } else {
//                    detailDto.setApproveUserName(detailDto.getEmpCode());
//                }
//            }
//
//            //若审核拒绝后,未审核的人不展示
//            for(ProjectApproveDetailDto detailDto :detailList){
//                approveDtoList.add(detailDto);
//                if(approveStatusEnum.APPROVE_COMPLETE.getKey().equals(detailDto.getApproveStatus())&&approveResultEnum.APPROVE_REFUSE.getKey().equals(detailDto.getApproveResult())){
//                    break;
//                }
//            }
//            dto.setProjectApproveDetailDtos(approveDtoList);
//
//        }
//        return list;
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDateList(ProjectApproveDto projectApproveDto) {
        List<ApproveNodeInfoVo> approveNodeInfoVo = projectApproveDto.getApproveNodeInfoVo();
        if (approveNodeInfoVo == null || approveNodeInfoVo.size() == 0) {
            return Boolean.FALSE;
        }
        Long projectId = projectApproveDto.getProjectId();
        Long orderId = projectApproveDto.getOrderId();
        if (orderId != null) {
            List<OrderNodeInfoDto> orderNodeInfoDtos = new ArrayList<>();
            List<ApproveNodeInfoVo> approveNodeInfoVoList = projectApproveDto.getApproveNodeInfoVo();

            for (ApproveNodeInfoVo vo : approveNodeInfoVoList) {
                OrderNodeInfoDto orderNodeInfoDto = new OrderNodeInfoDto();
                BeanUtils.copyProperties(vo, orderNodeInfoDto);
                orderNodeInfoDto.setOrderId(orderId.toString());
                orderNodeInfoDto.setNodeId(vo.getNodeId());
                orderNodeInfoDtos.add(orderNodeInfoDto);
            }
            orderNodeInfoService.updateData(orderNodeInfoDtos, Boolean.FALSE);
        } else if (projectId != null) {
            List<ProjectNodeInfo> projectNodeInfos = new ArrayList<>();
            List<ApproveNodeInfoVo> approveNodeInfoVoList = projectApproveDto.getApproveNodeInfoVo();

            for (ApproveNodeInfoVo vo : approveNodeInfoVoList) {
                ProjectNodeInfo projectNodeInfo = new ProjectNodeInfo();
                BeanUtils.copyProperties(vo, projectNodeInfo);
                projectNodeInfo.setProjectId(projectId);
                projectNodeInfo.setNodeId(Long.parseLong(vo.getNodeId()));
                projectNodeInfos.add(projectNodeInfo);
            }
            //保存任务数据
            projectNodeInfoService.updateData(projectNodeInfos, Boolean.TRUE);
        }

        return Boolean.TRUE;
    }

    /**
     * 发送下一个节点的待办
     *
     * @param projectApproveDetail
     */
    public void sendGroupNextTask(ProjectApproveDto projectApproveDto, ProjectApproveDetail projectApproveDetail) {
        //查询是否有子集
        LambdaQueryWrapper<ProjectApproveGroup> groupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveGroup.class);
        groupLambdaQueryWrapper.eq(ProjectApproveGroup::getNodeId, projectApproveDto.getNodeId())
                .eq(ProjectApproveGroup::getApproveGroupId, projectApproveDetail.getApproveGroupId());
        ProjectApproveGroup projectApproveGroup = Optional.ofNullable(projectApproveGroupRepository.selectOne(groupLambdaQueryWrapper)).orElseGet(ProjectApproveGroup::new);
        //判断是否是顺序执行
        if (JhSystemEnum.approveBeginEnum.IN_ORDER.getKey().equals(projectApproveGroup.getApproveBegin())) {
            //发送下一节点的待办
            LambdaQueryWrapper<ProjectApproveDetail> detailLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class);
            detailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveGroupId, projectApproveGroup.getApproveGroupId())
                    .and(wrapper -> wrapper.eq(ProjectApproveDetail::getApproveResult, approveResultEnum.PENDING_APPROVAL.getKey())
                            .or().eq(ProjectApproveDetail::getApproveResult, approveResultEnum.UNDER_APPROVE.getKey()))
//                    .eq(ProjectApproveDetail::getApproveResult, approveResultEnum.UNDER_APPROVE.getKey())
                    .orderBy(true, true, ProjectApproveDetail::getApproveIndex);
            List<ProjectApproveDetail> projectApproveDetails = Optional.ofNullable(projectApproveDetailRepository.selectList(detailLambdaQueryWrapper)).orElseGet(LinkedList::new);
            if (projectApproveDetails.size() > 0) {
                //多人处理
                Map<Integer, List<ProjectApproveDetail>> collect = projectApproveDetails.stream().collect(Collectors.groupingBy(d -> ObjectUtil.isEmpty(d.getApproveIndex()) ? 0 : d.getApproveIndex()));
                if (MapUtils.isEmpty(collect)) {
                    collect = new HashMap<>();
                }
                Iterator<Map.Entry<Integer, List<ProjectApproveDetail>>> iterator = collect.entrySet().iterator();
                Map.Entry<Integer, List<ProjectApproveDetail>> integerListEntry = null;
                while (iterator.hasNext()) {
                    integerListEntry = iterator.next();
                    break;
                }
                if (ObjectUtils.isNotEmpty(integerListEntry)) {
                    for (ProjectApproveDetail detail : integerListEntry.getValue()) {
                        Long projectGroupId = projectApproveGroup.getNodeId();
                        Long projectId = projectApproveGroup.getProjectId();
                        //查询待办信息
                        ProjectGroupDto projectGroupDto = projectGroupService.findById(projectGroupId);

                        //修改状态为审批中
                        detail.setApproveStatus(approveStatusEnum.IN_APPROVE.getKey());
                        detail.setApproveResult(approveResultEnum.UNDER_APPROVE.getKey());

                        //创建待办
                        ProjectApproveDetailDto projectApproveDetailDto = projectApproveDetailMapper.toDto(detail);
                        projectApproveDetailDto.setApproveRole(detail.getApproveRole());
                        projectApproveDetailDto.setApproveRoleName(detail.getApproveRoleName());
                        projectTaskService.createAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK, projectApproveDetailDto);
//                    projectTaskService.createNoticeAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK, projectApproveDetailDto);

                        projectApproveDetailService.updateById(detail);
                    }
//                    projectApproveDetailService.updateBatchById(integerListEntry.getValue());
                }
            }
        }
    }

    public void changeStatus(ProjectApproveGroup projectApproveGroup) {
        //将任务状态转为审核中

    }

    /**
     * 发送子节点的待办
     *
     * @param projectApproveGroup
     */
    public void sendChildGroupTask(ProjectApproveGroup projectApproveGroup) {

        //判断是否是顺序执行
        if (JhSystemEnum.approveBeginEnum.IN_ORDER.getKey().equals(projectApproveGroup.getApproveBegin())) {
            //发送下一节点的待办
            LambdaQueryWrapper<ProjectApproveDetail> detailLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class);
            detailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveGroupId, projectApproveGroup.getApproveGroupId())
                    .orderBy(true, true, ProjectApproveDetail::getApproveIndex);
            List<ProjectApproveDetail> projectApproveDetails = Optional.ofNullable(projectApproveDetailRepository.selectList(detailLambdaQueryWrapper)).orElseGet(LinkedList::new);
            if (projectApproveDetails.size() > 0) {
                //多人处理
                Map<Integer, List<ProjectApproveDetail>> collect = projectApproveDetails.stream().collect(Collectors.groupingBy(d -> ObjectUtil.isEmpty(d.getApproveIndex()) ? 0 : d.getApproveIndex()));
                if (MapUtils.isEmpty(collect)) {
                    collect = new HashMap<>();
                }
                Iterator<Map.Entry<Integer, List<ProjectApproveDetail>>> iterator = collect.entrySet().iterator();
                Map.Entry<Integer, List<ProjectApproveDetail>> integerListEntry = null;
                while (iterator.hasNext()) {
                    integerListEntry = iterator.next();
                    break;
                }

                if (ObjectUtil.isNotEmpty(integerListEntry)) {
//                    List<ProjectApproveDetail> projectApproveDetails = new ArrayList<>();
                    for (ProjectApproveDetail detail : integerListEntry.getValue()) {
                        Long projectGroupId = projectApproveGroup.getNodeId();
                        Long projectId = projectApproveGroup.getProjectId();


                        //修改状态为审批中
                        detail.setApproveStatus(approveStatusEnum.IN_APPROVE.getKey());
                        detail.setApproveResult(approveResultEnum.UNDER_APPROVE.getKey());
                        //查询待办信息
                        ProjectGroupDto projectGroupDto = Optional.ofNullable(projectGroupService.findById(projectGroupId)).orElseGet(ProjectGroupDto::new);
                        //创建待办
                        ProjectApproveDetailDto projectApproveDetailDto = Optional.ofNullable(projectApproveDetailMapper.toDto(detail)).orElseGet(ProjectApproveDetailDto::new);
                        projectApproveDetailDto.setApproveRole(detail.getApproveRole());
                        projectApproveDetailDto.setApproveRoleName(detail.getApproveRoleName());
                        projectTaskService.createAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK, projectApproveDetailDto);
                        projectTaskService.createNoticeAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK, projectApproveDetailDto);

                        projectApproveDetailService.updateById(detail);
//                        projectApproveDetails.add(detail);
                    }
//                    projectApproveDetailService.updateBatchById(projectApproveDetails);
                }
            }
        } else if (JhSystemEnum.approveBeginEnum.ALL_EXCUTE.getKey().equals(projectApproveGroup.getApproveBegin())) {
            LambdaQueryWrapper<ProjectApproveDetail> detailLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class);
            detailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveGroupId, projectApproveGroup.getApproveGroupId())
                    .orderBy(true, true, ProjectApproveDetail::getApproveIndex);
            List<ProjectApproveDetail> projectApproveDetails = Optional.ofNullable(projectApproveDetailRepository.selectList(detailLambdaQueryWrapper)).orElseGet(LinkedList::new);
            if (projectApproveDetails.size() > 0) {
                for (ProjectApproveDetail detail : projectApproveDetails) {
                    Long projectGroupId = projectApproveGroup.getNodeId();
                    Long projectId = projectApproveGroup.getProjectId();

                    //修改状态为审批中
                    detail.setApproveStatus(approveStatusEnum.IN_APPROVE.getKey());
                    detail.setApproveResult(approveResultEnum.UNDER_APPROVE.getKey());

                    //查询待办信息
                    ProjectGroupDto projectGroupDto = Optional.ofNullable(projectGroupService.findById(projectGroupId)).orElseGet(ProjectGroupDto::new);
                    //创建待办
                    ProjectApproveDetailDto projectApproveDetailDto = Optional.ofNullable(projectApproveDetailMapper.toDto(detail)).orElseGet(ProjectApproveDetailDto::new);
                    projectApproveDetailDto.setApproveRole(detail.getApproveRole());
                    projectApproveDetailDto.setApproveRoleName(detail.getApproveRoleName());
                    projectTaskService.createAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK, projectApproveDetailDto);
                    projectTaskService.createNoticeAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK, projectApproveDetailDto);

                    projectApproveDetailService.updateById(detail);
                }
//                projectApproveDetailService.updateBatchById(projectApproveDetails);
            }
        }
    }


    /**
     * 更新当前group的状态
     *
     * @param projectApproveGroup
     * @return
     */
    public int updateCurrentGroup(ProjectApproveGroup projectApproveGroup) {
        int flag = 0;
        flag = projectApproveGroupRepository.updateById(projectApproveGroup);
        return flag;
    }


    /**
     * 更新当前父group状态
     *
     * @param projectParentGroup
     * @return
     */
    public int updateCurrentParentGroup(ProjectParentGroup projectParentGroup) {
        int flag = 0;
        flag = projectParentGroupRepository.updateById(projectParentGroup);
        return flag;
    }

    /**
     * 当前节点是否是当前group审批是否已完成，当前其他审批已完成的话，就说名当前审批是最后一个审批
     *
     * @param projectApproveDetail
     * @return
     */
    public Boolean isLastNodeInGroupExamine(ProjectApproveDetail projectApproveDetail) {
        Boolean flag = Boolean.FALSE;
        //查询当前节点所对应的所有detail
        LambdaQueryWrapper<ProjectApproveDetail> approveDetailLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class);
        approveDetailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveGroupId, projectApproveDetail.getApproveGroupId())
//                .eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey())
                .and(wrapper -> wrapper.eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.PENDING_APPROVAL.getKey())
                        .or().eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey()))
                .ne(ProjectApproveDetail::getApproveDetailId, projectApproveDetail.getApproveDetailId());
        Long selectCount = projectApproveDetailRepository.selectCount(approveDetailLambdaQueryWrapper);
        //判断是否是最后一个节点
        if (selectCount == 0) {
            flag = Boolean.TRUE;
        }
        return flag;
    }

    /**
     * 当前节点是否是当前group的最后一个节点
     *
     * @param projectApproveDetail
     * @return
     */
    public Boolean isLastNodeInGroup(ProjectApproveDetail projectApproveDetail) {
        Boolean flag = Boolean.FALSE;
        //查询当前节点所对应的所有detail
        LambdaQueryWrapper<ProjectApproveDetail> approveDetailLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class);
        approveDetailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveGroupId, projectApproveDetail.getApproveGroupId())
                .orderBy(true, true, ProjectApproveDetail::getApproveIndex);
        List<ProjectApproveDetail> projectApproveDetails = projectApproveDetailRepository.selectList(approveDetailLambdaQueryWrapper);
        ProjectApproveDetail detail = projectApproveDetails.get(projectApproveDetails.size() - 1);
        //判断是否是最后一个节点
        if (projectApproveDetail.getApproveDetailId().equals(detail.getApproveDetailId())) {
            flag = Boolean.TRUE;
        }
        return flag;
    }

    /**
     * 判断当前节点是否有子节点
     *
     * @param projectApproveDetail
     * @return
     */
    public ProjectApproveGroup hasChildNode(ProjectApproveDetail projectApproveDetail) {
        ProjectApproveGroup projectApproveGroup = new ProjectApproveGroup();
        //若存在子集，返回子集的group
        if (projectApproveDetail.getHasChild() != null && projectApproveDetail.getHasChild()) {
            Long approveTemplateDetailId = projectApproveDetail.getApproveTemplateDetailId();
            //查找子集所属group
            LambdaQueryWrapper<ProjectApproveDetail> approveDetailLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class);
            approveDetailLambdaQueryWrapper.eq(ProjectApproveDetail::getParentId, approveTemplateDetailId)
                    .eq(ProjectApproveDetail::getApproveId, projectApproveDetail.getApproveId());
            List<ProjectApproveDetail> projectApproveDetails = projectApproveDetailRepository.selectList(approveDetailLambdaQueryWrapper);
            if (projectApproveDetails.size() > 0) {
                Long approveGroupId = projectApproveDetails.get(0).getApproveGroupId();
                LambdaQueryWrapper<ProjectApproveGroup> approveGroupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveGroup.class);
                approveGroupLambdaQueryWrapper.eq(ProjectApproveGroup::getApproveGroupId, approveGroupId);
                projectApproveGroup = projectApproveGroupRepository.selectOne(approveGroupLambdaQueryWrapper);
            }

        }
        return projectApproveGroup;
    }

    /**
     * 获取当前审批所在节点的group逻辑
     *
     * @param projectApproveDetail
     * @return
     */
    public ProjectApproveGroup getCurrentGroup(ProjectApproveDetail projectApproveDetail) {
        Long approveGroupId = projectApproveDetail.getApproveGroupId();
        LambdaQueryWrapper<ProjectApproveGroup> approveGroupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveGroup.class);
        approveGroupLambdaQueryWrapper.eq(ProjectApproveGroup::getApproveGroupId, approveGroupId);
        ProjectApproveGroup projectApproveGroup = projectApproveGroupRepository.selectOne(approveGroupLambdaQueryWrapper);

        return projectApproveGroup;
    }

    /**
     * 判断当前父group是否结束
     *
     * @param projectApproveDetail
     * @return
     */
    public ProjectParentGroup getCurrentParentGroup(ProjectApproveDetail projectApproveDetail) {
        Boolean flag = Boolean.FALSE;
        Long approveGroupId = projectApproveDetail.getApproveGroupId();
        LambdaQueryWrapper<ProjectApproveGroup> approveGroupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveGroup.class);
        approveGroupLambdaQueryWrapper.eq(ProjectApproveGroup::getApproveGroupId, approveGroupId);
        ProjectApproveGroup projectApproveGroup = Optional.ofNullable(projectApproveGroupRepository.selectOne(approveGroupLambdaQueryWrapper)).orElseGet(ProjectApproveGroup::new);
        Long parentGroupId = projectApproveGroup.getParentGroupId();
        ProjectParentGroup projectParentGroup = projectParentGroupRepository.selectById(parentGroupId);
        //查询父group下所有group的状态
        LambdaQueryWrapper<ProjectApproveGroup> groupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveGroup.class);
        groupLambdaQueryWrapper.eq(ProjectApproveGroup::getParentGroupId, parentGroupId);
        List<ProjectApproveGroup> projectApproveGroups = projectApproveGroupRepository.selectList(groupLambdaQueryWrapper);
        for (ProjectApproveGroup group : projectApproveGroups) {
            if (approveStatusEnum.IN_APPROVE.getKey().equals(group.getApproveStatus())) {
                projectParentGroup.setApproveResult(approveResultEnum.UNDER_APPROVE.getKey());
                projectParentGroup.setApproveStatus(approveStatusEnum.IN_APPROVE.getKey());
                flag = Boolean.TRUE;
                break;
            }
            if (approveResultEnum.APPROVE_REFUSE.getKey().equals(group.getApproveResult())) {
                projectParentGroup.setApproveResult(approveResultEnum.APPROVE_REFUSE.getKey());
                projectParentGroup.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
            }
        }
        if ((!approveResultEnum.APPROVE_REFUSE.getKey().equals(projectParentGroup.getApproveResult())) && (!flag)) {
            projectParentGroup.setApproveResult(approveResultEnum.APPROVE_PASS.getKey());
            projectParentGroup.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
        }
        return projectParentGroup;
    }

    /**
     * 更新所有的子节点的状态
     *
     * @param projectApproveDetail
     * @return
     */
    public void updateAllChildGroup(ProjectApproveDetail projectApproveDetail) {
        Long approveGroupId = projectApproveDetail.getApproveGroupId();
        LambdaQueryWrapper<ProjectApproveGroup> approveGroupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveGroup.class);
        approveGroupLambdaQueryWrapper.eq(ProjectApproveGroup::getApproveGroupId, approveGroupId);
        ProjectApproveGroup projectApproveGroup = Optional.ofNullable(projectApproveGroupRepository.selectOne(approveGroupLambdaQueryWrapper)).orElseGet(ProjectApproveGroup::new);
        Long parentGroupId = projectApproveGroup.getParentGroupId();

        //查询父group下所有group的状态
        LambdaQueryWrapper<ProjectApproveGroup> groupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveGroup.class);
        groupLambdaQueryWrapper.eq(ProjectApproveGroup::getParentGroupId, parentGroupId);
        List<ProjectApproveGroup> projectApproveGroups = Optional.ofNullable(projectApproveGroupRepository.selectList(groupLambdaQueryWrapper)).orElseGet(LinkedList::new);
        for (ProjectApproveGroup group : projectApproveGroups) {
            if (!approveStatusEnum.APPROVE_COMPLETE.getKey().equals(group.getApproveResult())) {
                group.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
                updateCurrentGroup(group);
                //更新所有子节点的状态
                updateGroupDetail(group);
            }
        }
    }

    /**
     * 更新所有子节点
     *
     * @param projectApproveGroup
     */
    public void updateGroupDetail(ProjectApproveGroup projectApproveGroup) {
        LambdaQueryWrapper<ProjectApproveDetail> approveDetailLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class);
        approveDetailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveGroupId, projectApproveGroup.getApproveGroupId())
                .and(wrapper -> wrapper.eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.PENDING_APPROVAL.getKey())
                        .or().eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey()))
//                .eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey())
                .orderBy(true, true, ProjectApproveDetail::getApproveIndex);
        List<ProjectApproveDetail> projectApproveDetails = Optional.ofNullable(projectApproveDetailRepository.selectList(approveDetailLambdaQueryWrapper)).orElseGet(LinkedList::new);
        for (ProjectApproveDetail detail : projectApproveDetails) {
            detail.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
            //【系统结束】不在三级看板中的审批履历展示
            detail.setApproveOption("系统结束");
            projectApproveDetailRepository.updateById(detail);
            //结束所有的待办

        }
        //查找已出任务，更新为已完成
        LambdaQueryWrapper<ProjectTaskInfo> projectTaskInfoLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTaskInfo.class);
        projectTaskInfoLambdaQueryWrapper.eq(ProjectTaskInfo::getNodeId, projectApproveGroup.getNodeId());
        List<ProjectTaskInfo> projectTaskInfos = Optional.ofNullable(projectTaskInfoRepository.selectList(projectTaskInfoLambdaQueryWrapper)).orElseGet(LinkedList::new);
        if (projectTaskInfos.size() > 0) {
            for (ProjectTaskInfo projectTaskInfo : projectTaskInfos) {
                if ((projectTaskInfo.getNodeName().contains("审批"))) {

                    projectTaskInfo.setTaskStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
                    projectTaskInfoRepository.updateById(projectTaskInfo);
                }
            }
        }

    }

    /**
     * 获取当前审批节点数据
     *
     * @param projectParentGroup
     * @return
     */
    public ProjectApprove getProjectApprove(ProjectParentGroup projectParentGroup) {
        //查询当前所有父group是否都完成
        Boolean flag = Boolean.FALSE;
        Long approveId = projectParentGroup.getApproveId();
        LambdaQueryWrapper<ProjectApprove> approveLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApprove.class);
        approveLambdaQueryWrapper.eq(ProjectApprove::getNodeId, projectParentGroup.getNodeId())
                .eq(ProjectApprove::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey());
        ProjectApprove projectApprove = projectApproveRepository.selectOne(approveLambdaQueryWrapper);
        LambdaQueryWrapper<ProjectParentGroup> parentGroupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectParentGroup.class);
        parentGroupLambdaQueryWrapper.eq(ProjectParentGroup::getApproveId, approveId);
        List<ProjectParentGroup> projectParentGroups = projectParentGroupRepository.selectList(parentGroupLambdaQueryWrapper);
        for (ProjectParentGroup group : projectParentGroups) {
            if (approveStatusEnum.IN_APPROVE.getKey().equals(group.getApproveStatus())) {
                projectApprove.setApproveResult(approveResultEnum.UNDER_APPROVE.getKey());
                projectApprove.setApproveStatus(approveStatusEnum.IN_APPROVE.getKey());
                flag = Boolean.TRUE;
                break;
            }
            if (approveResultEnum.APPROVE_REFUSE.getKey().equals(group.getApproveResult())) {
                projectApprove.setApproveResult(approveResultEnum.APPROVE_REFUSE.getKey());
                projectApprove.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
            }
        }
        if ((!approveResultEnum.APPROVE_REFUSE.getKey().equals(projectApprove.getApproveResult())) && (!flag)) {
            projectApprove.setApproveResult(approveResultEnum.APPROVE_PASS.getKey());
            projectApprove.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
        }
        return projectApprove;
    }

    /**
     * 整体流程更新
     *
     * @param projectApprove
     */
    public void updateProjectApprove(ProjectApprove projectApprove) {
        if (approveStatusEnum.APPROVE_COMPLETE.getKey().equals(projectApprove.getApproveStatus())) {
            Timestamp nowTime = new Timestamp(System.currentTimeMillis());
            projectApprove.setApproveEnd(nowTime);
            projectApproveRepository.updateById(projectApprove);
            projectNodeInfoService.updateNodeStatus(projectApprove);
           /* if(JhSystemEnum.approveResultEnum.APPROVE_REFUSE.getKey().equals(projectApprove.getApproveResult())){
                //回退所有当前节点状态
                ProjectNodeInfoDto projectNodeInfoDto=new ProjectNodeInfoDto();
                projectNodeInfoDto.setNodeId(projectApprove.getNodeId().toString());
                projectNodeInfoDto.setProjectId(projectApprove.getProjectId().toString());
                projectNodeInfoService.fallbackStatus(projectNodeInfoDto);
            }*/
        }
    }

    /**
     * 订单作废审批全流程更新
     *
     * @param orderId
     */
    @Override
    public void updateOrderCancelApprove(Long orderId) {
        LambdaQueryWrapper<ProjectGroup> projectGroupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getOrderId, orderId)
                .eq(ProjectGroup::getNodeCode, "con-00901");
        List<ProjectGroup> projectGroupList = Optional.ofNullable(projectGroupService.list(projectGroupLambdaQueryWrapper)).orElseGet(LinkedList::new);
        if (!ObjectUtils.isNull(projectGroupList) && projectGroupList.size() > 0) {
            for (ProjectGroup projectGroup : projectGroupList) {
                Long projectGroupId = projectGroup.getProjectGroupId();
                LambdaQueryWrapper<ProjectApprove> projectApproveLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApprove.class)
                        .eq(ProjectApprove::getNodeId, projectGroupId)
                        .orderByDesc(ProjectApprove::getApproveStart);
                List<ProjectApprove> projectApproves = Optional.ofNullable(projectApproveService.list(projectApproveLambdaQueryWrapper)).orElseGet(LinkedList::new);
                ProjectApprove projectApprove = projectApproves.get(0);
                if (!ObjectUtils.isNull(projectApprove)) {
                    LambdaQueryWrapper<OrderInfo> orderInfoLambdaQueryWrapper = Wrappers.lambdaQuery(OrderInfo.class)
                            .eq(OrderInfo::getOrderId, orderId);
                    List<OrderInfo> orderInfoList = orderInfoRepository.selectList(orderInfoLambdaQueryWrapper);
                    OrderInfoDto orderInfoDto = new OrderInfoDto();
                    for (OrderInfo orderInfo : orderInfoList) {
                        BeanUtils.copyProperties(orderInfo, orderInfoDto);
                        orderNodeInfoService.updateOrderCancelStatus(orderInfoDto);
                    }
                }
            }
            LambdaQueryWrapper<ProjectGroup> queryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                    .eq(ProjectGroup::getOrderId, orderId)
                    .eq(ProjectGroup::getNodeCode, "con-00903");
            ProjectGroup projectGroup = projectGroupRepository.selectOne(queryWrapper);
            Long projectGroupId = projectGroup.getProjectGroupId();
            LambdaQueryWrapper<OrderNodeInfo> orderNodeInfoLambdaQueryWrapper = Wrappers.lambdaQuery(OrderNodeInfo.class)
                    .eq(OrderNodeInfo::getOrderId, orderId)
                    .eq(OrderNodeInfo::getNodeCode, "con-00903");
            OrderNodeInfo orderNodeInfo = orderNodeInfoRepository.selectOne(orderNodeInfoLambdaQueryWrapper);
            OrderNodeInfoDto orderNodeInfoDto = orderNodeInfoMapper.toDto(orderNodeInfo);
            orderNodeInfoDto.setProjectGroupId(String.valueOf(projectGroupId));
            orderNodeInfoService.submit(orderNodeInfoDto);
        }
    }

    /**
     * 更新所有节点状态
     *
     * @param projectApproveDto
     * @param projectApprove
     */
    public void updateAllApproveDetail(ProjectApproveDto projectApproveDto, ProjectApprove projectApprove) {
        LambdaQueryWrapper<ProjectApproveDetail> detailLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class);
        detailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveId, projectApprove.getApproveId())
                .and(wrapper -> wrapper.eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.PENDING_APPROVAL.getKey())
                        .or().eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey()));
//                .eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE);
        //查找还在审批中的节点（更新所有节点）
        List<ProjectApproveDetail> projectApproveDetails = projectApproveDetailRepository.selectList(detailLambdaQueryWrapper);
        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
        for (ProjectApproveDetail detail : projectApproveDetails) {
            detail.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
            detail.setApproveResult(projectApproveDto.getApproveResult());
            detail.setApproveOption(projectApproveDto.getApproveOption());
            detail.setApproveUser(projectApproveDto.getApproveUser());
            detail.setApproveEnd(nowTime);
            projectApproveDetailRepository.updateById(detail);

        }
        //查找已出任务，更新为已完成
        LambdaQueryWrapper<ProjectTaskInfo> projectTaskInfoLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTaskInfo.class);
        projectTaskInfoLambdaQueryWrapper.eq(ProjectTaskInfo::getNodeId, projectApprove.getNodeId());
        List<ProjectTaskInfo> projectTaskInfos = projectTaskInfoRepository.selectList(projectTaskInfoLambdaQueryWrapper);
        if (projectTaskInfos.size() > 0) {
            for (ProjectTaskInfo projectTaskInfo : projectTaskInfos) {
                if ((projectTaskInfo.getNodeName().contains("审批"))) {
                    projectTaskInfo.setTaskStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
                    projectTaskInfoRepository.updateById(projectTaskInfo);
                }
            }
        }
    }

    public void updateAllApproveGroup(ProjectApproveDto projectApproveDto, ProjectApprove projectApprove) {
        //获取当前node所属所有未完成的group
        Long nodeId = projectApproveDto.getNodeId();
        LambdaQueryWrapper<ProjectApproveGroup> approveGroupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveGroup.class);
        approveGroupLambdaQueryWrapper.eq(ProjectApproveGroup::getApproveId, projectApprove.getApproveId())
                .eq(ProjectApproveGroup::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey());
        List<ProjectApproveGroup> projectApproveGroups = projectApproveGroupRepository.selectList(approveGroupLambdaQueryWrapper);
        for (ProjectApproveGroup projectApproveGroup : projectApproveGroups) {
            //获取所有的审批Detail
            LambdaQueryWrapper<ProjectApproveDetail> approveDetailLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class);
            approveDetailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveGroupId, projectApproveGroup.getApproveGroupId())
                    .orderBy(true, true, ProjectApproveDetail::getApproveIndex);
            List<ProjectApproveDetail> projectApproveDetails = projectApproveDetailRepository.selectList(approveDetailLambdaQueryWrapper);
            for (ProjectApproveDetail detail : projectApproveDetails) {
                if (JhSystemEnum.approveModeEnum.ALL_PASS.getKey().equals(projectApproveGroup.getApproveMode())) {
                    //全部通过
                    if (approveResultEnum.APPROVE_REFUSE.getKey().equals(detail.getApproveResult())) {
                        projectApproveGroup.setApproveResult(approveResultEnum.APPROVE_REFUSE.getKey());
                        projectApproveGroup.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
                        break;
                    }

                } else if (JhSystemEnum.approveModeEnum.ONE_PASS_PASS.getKey().equals(projectApproveGroup.getApproveMode())) {
                    //一个通过则通过
                    if (approveResultEnum.APPROVE_PASS.getKey().equals(detail.getApproveResult())) {
                        projectApproveGroup.setApproveResult(approveResultEnum.APPROVE_PASS.getKey());
                        projectApproveGroup.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
                        break;
                    }

                }
            }
            if ((!approveResultEnum.APPROVE_REFUSE.getKey().equals(projectApproveGroup.getApproveResult()))) {
                projectApproveGroup.setApproveResult(approveResultEnum.APPROVE_PASS.getKey());
                projectApproveGroup.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
            }
            updateCurrentGroup(projectApproveGroup);


        }

    }

    public void updateAllParentGroup(ProjectApproveDto projectApproveDto, ProjectApprove projectApprove) {
        //获取当前node所属所有未完成的父
        Long nodeId = projectApproveDto.getNodeId();
        LambdaQueryWrapper<ProjectParentGroup> parentGroupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectParentGroup.class);
        parentGroupLambdaQueryWrapper.eq(ProjectParentGroup::getApproveId, projectApprove.getApproveId())
                .eq(ProjectParentGroup::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey());
        List<ProjectParentGroup> projectParentGroups = projectParentGroupRepository.selectList(parentGroupLambdaQueryWrapper);
        for (ProjectParentGroup projectParentGroup : projectParentGroups) {
            //获取所有的审批group
            LambdaQueryWrapper<ProjectApproveGroup> approveGroupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveGroup.class);
            approveGroupLambdaQueryWrapper.eq(ProjectApproveGroup::getParentGroupId, projectParentGroup.getParentGroupId());
            List<ProjectApproveGroup> projectApproveGroups = projectApproveGroupRepository.selectList(approveGroupLambdaQueryWrapper);
            for (ProjectApproveGroup group : projectApproveGroups) {
                if (approveResultEnum.APPROVE_REFUSE.getKey().equals(group.getApproveResult())) {
                    projectParentGroup.setApproveResult(approveResultEnum.APPROVE_REFUSE.getKey());
                    projectParentGroup.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());

                }
            }
            if ((!approveResultEnum.APPROVE_REFUSE.getKey().equals(projectParentGroup.getApproveResult()))) {
                projectParentGroup.setApproveResult(approveResultEnum.APPROVE_PASS.getKey());
                projectParentGroup.setApproveStatus(approveStatusEnum.APPROVE_COMPLETE.getKey());
            }
            updateCurrentParentGroup(projectParentGroup);
            ProjectApprove projectAllApprove = getProjectApprove(projectParentGroup);
            if (approveStatusEnum.APPROVE_COMPLETE.getKey().equals(projectAllApprove.getApproveStatus())) {
                updateProjectApprove(projectAllApprove);
            }

        }

    }

    private void startNextParentGroup(ProjectApprove projectApproveAll, ProjectParentGroup currentParentGroup) {
        //如果审批已完成直接返回
        if (approveStatusEnum.APPROVE_COMPLETE.getKey().equals(projectApproveAll.getApproveStatus())) {
            return;
        }

        ProjectApproveGroup nextGroup = new ProjectApproveGroup();
        String approveBegin = "";
        ProjectParentGroup nextParentGroup = new ProjectParentGroup();
        List<ProjectApproveDetail> approveDetails = new ArrayList<>();
        //如果当前parent组还在审批中则开启下个group
        if (approveStatusEnum.IN_APPROVE.getKey().equals(currentParentGroup.getApproveStatus())) {
            //查询parent下所有审批组
            LambdaQueryWrapper<ProjectApproveGroup> groupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveGroup.class)
                    .eq(ProjectApproveGroup::getParentGroupId, currentParentGroup.getParentGroupId())
                    .eq(ProjectApproveGroup::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey())
                    .orderBy(true, true, ProjectApproveGroup::getApproveLevel);
            List<ProjectApproveGroup> projectApproveGroups = projectApproveGroupRepository.selectList(groupLambdaQueryWrapper);
            if (CollectionUtils.isEmpty(projectApproveGroups)) {
                return;
            }
            updateAppGroup(projectApproveAll, projectApproveGroups);

            //如果parent组已完成则开启下一个parent组
        } else if (approveStatusEnum.APPROVE_COMPLETE.getKey().equals(currentParentGroup.getApproveStatus())
                && approveResultEnum.APPROVE_PASS.getKey().equals(currentParentGroup.getApproveResult())) {
            //下一个parent大组别
            String nextParentGroupS = "";
            try {
                Integer temp = Integer.valueOf(currentParentGroup.getParentGroup());
                nextParentGroupS = (++temp).toString();
            } catch (Exception e) {
                e.printStackTrace();
            }
            //查询下个parent组
            LambdaQueryWrapper<ProjectParentGroup> parentGroupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectParentGroup.class);
            parentGroupLambdaQueryWrapper.eq(ProjectParentGroup::getApproveId, projectApproveAll.getApproveId())
                    .eq(ProjectParentGroup::getParentGroup, nextParentGroupS);
            nextParentGroup = projectParentGroupRepository.selectOne(parentGroupLambdaQueryWrapper);
            if (ObjectUtil.isNull(nextParentGroup)) {
                return;
            }
            //根据parentd查询group
            LambdaQueryWrapper<ProjectApproveGroup> groupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveGroup.class);
            groupLambdaQueryWrapper.eq(ProjectApproveGroup::getParentGroupId, nextParentGroup.getParentGroupId())
                    .orderBy(true, true, ProjectApproveGroup::getApproveLevel);
            List<ProjectApproveGroup> projectApproveGroups = projectApproveGroupRepository.selectList(groupLambdaQueryWrapper);
            if (ObjectUtil.isEmpty(projectApproveGroups)) {
                return;
            }
            updateAppGroup(projectApproveAll, projectApproveGroups);
        }

    }

    private void updateAppGroup(ProjectApprove projectApproveAll, List<ProjectApproveGroup> projectApproveGroups) {
        ProjectApproveGroup nextGroup;
        List<ProjectApproveDetail> approveDetails;
        String approveBegin;
        //开启下个group的审批状态
        if (CollectionUtils.isEmpty(projectApproveGroups) || ObjectUtil.isNull(projectApproveAll)) {
            return;
        }
        nextGroup = projectApproveGroups.get(0);
        nextGroup.setApproveStatus(approveStatusEnum.IN_APPROVE.getKey());
        nextGroup.setApproveResult(approveResultEnum.UNDER_APPROVE.getKey());
        projectApproveGroupRepository.updateById(nextGroup);

        //执行策略
        approveBegin = nextGroup.getApproveBegin();
        //查询审批详细
        LambdaQueryWrapper<ProjectApproveDetail> approveDetailLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class);
        approveDetailLambdaQueryWrapper.eq(ProjectApproveDetail::getApproveGroupId, nextGroup.getApproveGroupId())
                .eq(ProjectApproveDetail::getApproveId, projectApproveAll.getApproveId())
                .and(wrapper -> wrapper.eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.PENDING_APPROVAL.getKey())
                        .or().eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey()))
//                .eq(ProjectApproveDetail::getApproveStatus, approveStatusEnum.IN_APPROVE.getKey())
                .orderBy(true, true, ProjectApproveDetail::getApproveIndex);
        approveDetails = projectApproveDetailService.list(approveDetailLambdaQueryWrapper);

        if (CollectionUtils.isEmpty(approveDetails)) {
            return;
        }

        //顺序执行
        if (JhSystemEnum.approveBeginEnum.IN_ORDER.getKey().equals(approveBegin)) {
            //多人处理
            Map<Integer, List<ProjectApproveDetail>> collect = approveDetails.stream().collect(Collectors.groupingBy(d -> ObjectUtil.isEmpty(d.getApproveIndex()) ? 0 : d.getApproveIndex()));
            if (MapUtils.isEmpty(collect)) {
                collect = new HashMap<>();
            }
            Iterator<Map.Entry<Integer, List<ProjectApproveDetail>>> iterator = collect.entrySet().iterator();
            Map.Entry<Integer, List<ProjectApproveDetail>> integerListEntry = null;
            while (iterator.hasNext()) {
                integerListEntry = iterator.next();
                break;
            }
            for (ProjectApproveDetail approveDetail : integerListEntry.getValue()) {
                approveDetail.setApproveStatus(approveStatusEnum.IN_APPROVE.getKey());
                approveDetail.setApproveResult(approveResultEnum.UNDER_APPROVE.getKey());

                //修改审批记录的状态为审批中
                projectApproveDetailService.updateById(approveDetail);
            }

            //创建代办
            createTodo(nextGroup, integerListEntry.getValue());
        } else if (JhSystemEnum.approveBeginEnum.ALL_EXCUTE.getKey().equals(approveBegin)) {
            for (ProjectApproveDetail approveDetail : approveDetails) {
                approveDetail.setApproveStatus(approveStatusEnum.IN_APPROVE.getKey());
                approveDetail.setApproveResult(approveResultEnum.UNDER_APPROVE.getKey());

                //修改审批记录的状态为审批中
                projectApproveDetailService.updateById(approveDetail);
            }

            //创建代办
            createTodo(nextGroup, approveDetails);
        }
    }

    /**
     * 创建代办
     *
     * @param nextGroup
     * @param approveDetails
     */
    private void createTodo(ProjectApproveGroup nextGroup, List<ProjectApproveDetail> approveDetails) {
        if (ObjectUtils.isNotEmpty(approveDetails)) {
            for (ProjectApproveDetail detail : approveDetails) {
                Long projectGroupId = nextGroup.getNodeId();
                Long projectId = nextGroup.getProjectId();
                //查询待办信息
                ProjectGroupDto projectGroupDto = projectGroupService.findById(projectGroupId);
                //创建待办
                ProjectApproveDetailDto projectApproveDetailDto = projectApproveDetailMapper.toDto(detail);
                projectApproveDetailDto.setApproveRole(detail.getApproveRole());
                projectApproveDetailDto.setApproveRoleName(detail.getApproveRoleName());
                projectTaskService.createAppTask(projectGroupDto, JhSystemEnum.TaskNameEnum.TODO_TASK, projectApproveDetailDto);
            }
        }
    }

    @Override
    public List<ProjectApprove> queryInfoByNodeId(Long nodeId) {
        return projectApproveRepository.queryInfoByNodeId(nodeId);
    }

    @Override
    public List<ProjectApprove> queryEmpByNodeId(Long nodeId) {
        return projectApproveRepository.queryEmpByNodeId(nodeId);
    }

    private List<ProjectNotice> generateNotice(Long approveUser, Long nodeId, ProjectGroup projectGroup, JhSystemEnum.MessageTemplate template) {
        List<ProjectNotice> result = new ArrayList<>();

        return result;
    }

}
