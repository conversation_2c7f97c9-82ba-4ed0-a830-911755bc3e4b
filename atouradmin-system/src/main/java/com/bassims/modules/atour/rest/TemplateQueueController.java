/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.TemplateQueue;
import com.bassims.modules.atour.service.TemplateQueueService;
import com.bassims.modules.atour.service.dto.TemplateQueueDto;
import com.bassims.modules.atour.service.dto.TemplateQueueQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-08-25
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_template_queue管理")
@RequestMapping("/api/templateQueue")
public class TemplateQueueController {

    private static final Logger logger = LoggerFactory.getLogger(TemplateQueueController.class);

    private final TemplateQueueService templateQueueService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, TemplateQueueQueryCriteria criteria) throws IOException {
        templateQueueService.download(templateQueueService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<TemplateQueueDto>>}
    */
    @GetMapping("/list")
    @Log("查询t_template_queue")
    @ApiOperation("查询t_template_queue")
    public ResponseEntity<Object> query(TemplateQueueQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(templateQueueService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<TemplateQueueDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询t_template_queue")
    @ApiOperation("查询t_template_queue")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(templateQueueService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增t_template_queue")
    @ApiOperation("新增t_template_queue")
    public ResponseEntity<Object> create(@Validated @RequestBody TemplateQueue resources){
        return new ResponseEntity<>(templateQueueService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改t_template_queue")
    @ApiOperation("修改t_template_queue")
    public ResponseEntity<Object> update(@Validated @RequestBody TemplateQueue resources){
        templateQueueService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_template_queue")
    @ApiOperation("删除t_template_queue")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        templateQueueService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    /**
     * 批量插入
     *
     * @param template 模板
     * @return {@link ResponseEntity}<{@link Object}>
     */
    @PostMapping("/batchInsert")
    @Log("批量新增模板")
    @ApiOperation("批量新增模板")
    public ResponseEntity<Object> batchInsert(@RequestBody List<TemplateQueue> template){
        return new ResponseEntity<>(templateQueueService.batchInsert(template) ,HttpStatus.OK);
    }
    /**
     * 批量插入模板子节点
     * @param templateQueueDto
     * @return
     */
    @PostMapping("/batchInsertTemplate")
    @Log("批量新增模板")
    @ApiOperation("批量新增模板")
    public ResponseEntity<Object> batchInsertTemplate(@RequestBody TemplateQueueDto templateQueueDto){
        return new ResponseEntity<>(templateQueueService.batchInsertTemplate(templateQueueDto) ,HttpStatus.OK);
    }
}