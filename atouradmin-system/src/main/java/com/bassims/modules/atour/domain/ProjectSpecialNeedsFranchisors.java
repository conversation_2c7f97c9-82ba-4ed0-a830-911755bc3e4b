/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description / 项目里的特许商特殊需求表
* <AUTHOR>
* @date 2023-12-16
**/
@Data
@TableName(value="t_project_special_needs_franchisors")
public class ProjectSpecialNeedsFranchisors implements Serializable {

    @TableId(value = "franchisors_id",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long franchisorsId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @TableField(value = "problem_description")
    @ApiModelProperty(value = "问题描述")
    private String problemDescription;

    @TableField(value = "whether_approval_required")
    @ApiModelProperty(value = "是否需审批(0不需要、1需要)")
    private String whetherApprovalRequired;

    @TableField(value = "treatment_proposal")
    @ApiModelProperty(value = "处理方案建议")
    private String treatmentProposal;

    @TableField(value = "agreed_completion_time")
    @ApiModelProperty(value = "约定完成时间")
    private Timestamp agreedCompletionTime;

    @TableField(value = "picture")
    @ApiModelProperty(value = "图片")
    private String picture;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

    public void copy(ProjectSpecialNeedsFranchisors source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}