package com.bassims.modules.atour.service;

import com.bassims.modules.atour.requestParam.IeamOrderSyncRequest;
import com.bassims.modules.atour.requestParam.IeamUserRequest;
import com.bassims.modules.atour.responseBody.IeamUserResponse;
import com.bassims.modules.atour.responseBody.IeamWareHouseResponse;
import com.bassims.modules.atour.responseBody.MaintenanceResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IeamHttpService {

    /**
     * 查询ieam用户信息
     *
     * @param request 用户
     * @return {@link IeamUserResponse}
     */
    Map<String, Object> queryIeamUserInfo(IeamUserRequest request);

    /**
     * 查询ieam仓库
     *
     * @param hrDepartmentCode 人力资源部门代码
     * @return {@link IeamWareHouseResponse}
     */
    List<IeamWareHouseResponse> queryIeamWareHouse(String hrDepartmentCode);

    /**
     * 同步以ieam
     *
     * @param ieamOrderSyncRequest ieam订单同步请求
     * @return
     */
    Object syncOrderToIeam(IeamOrderSyncRequest ieamOrderSyncRequest);

    /**
     * 同步维修记录
     * @return
     */
    Boolean syncMaintenances();

    /**
     * 获取维修记录
     * @param page
     * @return
     */
    List<MaintenanceResponse> getMaintenances(Integer page);

}
