package com.bassims.modules.atour.service.mapstruct;

import com.bassims.base.BaseMapper;
import com.bassims.modules.atour.domain.ProjectPersonReport;
import com.bassims.modules.atour.service.dto.ProjectPersonReportDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 工程运维人员报表
 *
 * <AUTHOR>
 * @date 2023/03/27
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProjectPersonReportMapper extends BaseMapper<ProjectPersonReportDto, ProjectPersonReport> {

}
