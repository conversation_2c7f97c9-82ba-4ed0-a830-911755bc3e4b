/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.bsEnum.AtourSystemEnum;
import com.bassims.constant.bsEnum.TemplateEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.domain.LocalStorage;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.repository.*;
import com.bassims.modules.atour.service.*;
import com.bassims.modules.atour.service.dto.*;
import com.bassims.modules.atour.service.mapstruct.ProjectCompletionReceiptMapper;
import com.bassims.modules.atour.util.NoteInfoMappingUtil;
import com.bassims.modules.atour.util.PPTFileUtil;
import com.bassims.modules.system.domain.User;
import com.bassims.modules.system.repository.UserRepository;
import com.bassims.repository.LocalStorageRepository;
import com.bassims.utils.*;
import com.github.pagehelper.PageInfo;
import org.apache.commons.io.IOUtils;
import org.apache.poi.xslf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2024-01-26
 **/
@Service
public class ProjectCompletionReceiptServiceImpl extends BaseServiceImpl<ProjectCompletionReceiptRepository, ProjectCompletionReceipt> implements ProjectCompletionReceiptService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectCompletionReceiptServiceImpl.class);

    @Autowired
    private ProjectCompletionPhotoRepository projectCompletionPhotoRepository;
    @Autowired
    private ProjectCompletionPhotoService projectCompletionPhotoService;
    @Autowired
    private ProjectCompletionReceiptRepository projectCompletionReceiptRepository;
    @Autowired
    private ProjectCompletionReceiptMapper projectCompletionReceiptMapper;
    @Autowired
    private ProjectCompletionReceiptDescriptionService projectCompletionReceiptDescriptionService;
    @Autowired
    private ProjectCompletionReceiptDescriptionRepository projectCompletionReceiptDescriptionRepository;
    @Autowired
    private ProjectNodeInfoService projectNodeInfoService;
    @Autowired
    private LocalStorageRepository localStorageRepository;
    @Autowired
    private ProjectCompletionReceiptSummaryService projectCompletionReceiptSummaryService;
    @Autowired
    private TemplateGroupCompletionReceiptService templateGroupCompletionReceiptService;
    @Autowired
    private PPTFileUtil pptFileUtil;
    @Autowired
    private ProjectInfoService projectInfoService;

    @Autowired
    private NoteInfoMappingUtil util;

    @Autowired
    private ProjectNodeInfoRepository projectNodeInfoRepository;
    @Autowired
    private ProjectGroupRepository projectGroupRepository;
    @Autowired
    private ProjectStakeholdersService projectStakeholdersService;
    @Autowired
    private SupplierInfoRepository supplierInfoRepository;
    @Autowired
    private UserRepository userRepository;


    @Override
    public void downloadTemplate(ProjectCompletionReceiptQueryCriteria criteria, HttpServletResponse response) {
//
//        try {
//
//            Presentation ppt = new Presentation();
//            try {
//                ppt.loadFromFile("亚朵验收报告 V20241021.pptx");
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//
//            //获取第4张幻灯片（被复制的幻灯片）
//            ISlide slide = ppt.getSlides().get(4);
//
//            //获取第6张幻灯片（被复制的幻灯片）
//            ISlide slide6 = ppt.getSlides().get(6);
//
//            //获取验收单不合格数据
//            LambdaQueryWrapper<ProjectCompletionReceipt> queryWrapper = Wrappers.lambdaQuery(ProjectCompletionReceipt.class)
//                    .eq(ProjectCompletionReceipt::getProjectId, criteria.getProjectId())
//                    .eq(ProjectCompletionReceipt::getAcceptance, JhSystemEnum.acceptance.UNQUALIFIED.getValue());
//            List<ProjectCompletionReceipt> receiptList = projectCompletionReceiptRepository.selectList(queryWrapper);
//
//            //获取验收单必拍现场照片
//            LambdaQueryWrapper<ProjectCompletionReceipt> queryWrapper1 = Wrappers.lambdaQuery(ProjectCompletionReceipt.class)
//                    .eq(ProjectCompletionReceipt::getProjectId, criteria.getProjectId())
//                    .eq(ProjectCompletionReceipt::getIsRealPhotosTaken, 1);
//            List<ProjectCompletionReceipt> receiptList1 = projectCompletionReceiptRepository.selectList(queryWrapper1);
//
//            //获取其他现场照片
//            LambdaQueryWrapper<ProjectCompletionPhoto> queryWrapper2 = Wrappers.lambdaQuery(ProjectCompletionPhoto.class)
//                    .eq(ProjectCompletionPhoto::getProjectId, criteria.getProjectId())
//                    .isNotNull(ProjectCompletionPhoto::getStandardPhotos);
//            List<ProjectCompletionPhoto> completionPhotos = projectCompletionPhotoRepository.selectList(queryWrapper2);
//
//            if (ObjectUtil.isEmpty(receiptList) && ObjectUtil.isEmpty(receiptList1) && ObjectUtil.isNotEmpty(completionPhotos)) {
//                throw new BadRequestException("当前项目不存在不合格以及实拍照片数据，无需导出！");
//            }
//            //获取所有的不合格照片
//            String collect = "";
//            if (ObjectUtil.isNotEmpty(receiptList)) {
//                collect = receiptList.stream().map(ProjectCompletionReceipt::getCheckAttachments).collect(Collectors.joining(","));
//                if (ObjectUtil.isEmpty(collect)) {
//                    throw new BadRequestException("当前项目不合格数据不存在照片，请重新填写验收单！");
//                }
//            }
//            //获取不合格照片的 Map<key=文件ID,val=文件名地址>
//            List<LocalStorage> storageList = localStorageRepository.findByIds(collect.split(","));
//            Map<Long, String> stringMap = storageList.stream().filter(e -> StringUtils.isNotEmpty(e.getNodeId()))
//                    .collect(Collectors.toMap(LocalStorage::getId, LocalStorage::getRealName));
//
//            List<ProjectCompletionReceipt> completionReceipts = new ArrayList<>();
//            Integer num = receiptList.size();
//            for (ProjectCompletionReceipt receipt : receiptList) {
//                if (ObjectUtil.isNotEmpty(receipt.getCheckAttachments())) {
//                    String[] split = receipt.getCheckAttachments().replaceAll("^,|,$", "").split(",");
//                    num = num + split.length - 1;
//                    for (String imgNodeId : split) {
//                        ProjectCompletionReceipt completionReceipt = new ProjectCompletionReceipt();
//                        completionReceipt.setCheckAttachments(stringMap.get(Long.valueOf(imgNodeId)));
//                        completionReceipt.setRemarks(receipt.getRemarks());
//                        completionReceipts.add(completionReceipt);
//                    }
//                }
//            }
//
//            //循环复制：循环插入复制后的幻灯片，作为第4张和第5张幻灯片
//            for (int i = 5; i < num + 4; i++) {
//                copyPptPage(slide, ppt, i);
//            }
//
//            //获取项目数据
//            Map<String, String> getProjectData = this.getGetProjectData(criteria.getProjectId() + "");
//            Map<String, Object> textDataMap = getTextDataMap(getProjectData, null);
////            pptFileUtil.makePPT(1, ppt, textDataMap);
////            pptFileUtil.makePPT(2, ppt, textDataMap);
////            //生成幻灯片数据
////            for (int i = 5; i < num + 5; i++) {
////                pptFileUtil.makePPT(i, ppt, getTextDataMap(getProjectData, completionReceipts.get(i - 5)));
////            }
//
//            //获取所有的实拍照片
//            collect = "";
//            Integer num1 = 0;
//            if (ObjectUtil.isNotEmpty(receiptList1)) {
//                //必拍照片的现场照片
//                collect = receiptList1.stream().map(ProjectCompletionReceipt::getCheckAttachments).collect(Collectors.joining(","));
//                //必拍照片的 现场视频
//                collect = collect + "," + receiptList1.stream().map(ProjectCompletionReceipt::getStandardVideos).collect(Collectors.joining(","));
//
//                if (ObjectUtil.isNotEmpty(completionPhotos)) {
//                    //其他照片的 现场照片
//                    collect = collect + "," + completionPhotos.stream().map(ProjectCompletionPhoto::getStandardPhotos).collect(Collectors.joining(","));
//                    //其他照片的 现场视频
//                    collect = collect + "," + completionPhotos.stream().map(ProjectCompletionPhoto::getStandardVideos).collect(Collectors.joining(","));
//                }
//
//                if (ObjectUtil.isEmpty(collect)) {
//                    throw new BadRequestException("当前项目实拍照片不存在现场照片，请重新填写验收单！");
//                }
//                //必拍照片的实例图片
//                collect = collect + receiptList1.stream().map(ProjectCompletionReceipt::getExampleDiagram).collect(Collectors.joining(","));
//            }
//
//            //获取不合格照片的 Map<key=文件ID,val=文件名地址>
//            storageList = localStorageRepository.findByIds(collect.split(","));
//            stringMap = storageList.stream().filter(e -> StringUtils.isNotEmpty(e.getNodeId()))
//                    .collect(Collectors.toMap(LocalStorage::getId, LocalStorage::getRealName));
//
//
//            for (ProjectCompletionReceipt receipt : receiptList1) {
//                if (ObjectUtil.isNotEmpty(receipt.getCheckAttachments())) {
//                    //必拍照片-示例图
//                    String[] exampleDiagram = new String[1];
//                    if (ObjectUtil.isNotEmpty(receipt.getExampleDiagram())) {
//                        exampleDiagram = receipt.getExampleDiagram().replaceAll("^,|,$", "").split(",");
//                    }
//
//                    //必拍照片-现场照片
//                    String[] checkAttachments = receipt.getCheckAttachments().replaceAll("^,|,$", "").split(",");
//
//                    num1 = num1 + checkAttachments.length;
//
//                    for (int i = 0; i < checkAttachments.length; i++) {
//                        String exampleDiagram1 = "";
//                        if (exampleDiagram.length > i && ObjectUtil.isNotEmpty(exampleDiagram[i])) {
//                            exampleDiagram1 = stringMap.get(Long.valueOf(exampleDiagram[i]));
//                        }
//                        String checkAttachment = stringMap.get(Long.valueOf(checkAttachments[i]));
//                        ProjectCompletionReceipt completionReceipt = new ProjectCompletionReceipt();
//                        completionReceipt.setCheckAttachments(exampleDiagram1 + "-" + checkAttachment);
//                        completionReceipt.setRemarks(receipt.getRemarks());
//                        completionReceipt.setContentLabel("酒店概况照片-【" + receipt.getSubItem() + "：" + receipt.getSubItemContent() + "】");
//                        completionReceipts.add(completionReceipt);
//                    }
//                }
//                if (ObjectUtil.isNotEmpty(receipt.getStandardVideos())) {
//                    //必拍照片-现场视频
//                    String[] standardVideos = receipt.getStandardVideos().replaceAll("^,|,$", "").split(",");
//
//                    num1 = num1 + standardVideos.length;
//                    for (String standardVideo : standardVideos) {
//                        ProjectCompletionReceipt completionReceipt = new ProjectCompletionReceipt();
//                        completionReceipt.setCheckAttachments(stringMap.get(Long.valueOf(standardVideo)));
//                        completionReceipt.setRemarks(receipt.getRemarks());
//                        completionReceipt.setContentLabel("酒店概况照片-【" + receipt.getSubItem() + "：" + receipt.getSubItemContent() + "】");
//                        completionReceipts.add(completionReceipt);
//                    }
//                }
//            }
//
//            for (ProjectCompletionPhoto completionPhoto : completionPhotos) {
//                //其他照片的现场照片
//                if (ObjectUtil.isNotEmpty(completionPhoto.getStandardPhotos())) {
//                    String[] split = completionPhoto.getStandardPhotos().replaceAll("^,|,$", "").split(",");
//                    for (int i = 0; i < split.length; i++) {
//                        String phono = "";
//                        if (i % 2 == 0) {
//                            //当前i是2的倍数
//                            num1 += 1;
//                            phono += "-" + stringMap.get(Long.valueOf(split[i]));
//                            ProjectCompletionReceipt completionReceipt = new ProjectCompletionReceipt();
//                            completionReceipt.setCheckAttachments(phono);
//                            completionReceipt.setRemarks(completionPhoto.getRemarks());
//                            completionReceipt.setContentLabel("酒店概况照片-【" + completionPhoto.getSubItem() + "：其他现场照片】");
//                            completionReceipts.add(completionReceipt);
//                        } else {
//                            phono = stringMap.get(Long.valueOf(split[i]));
//                        }
//
//                    }
//                }
//
//                //其他照片的视频
//                if (ObjectUtil.isNotEmpty(completionPhoto.getStandardVideos())) {
//                    String[] standardVideos = completionPhoto.getStandardVideos().replaceAll("^,|,$", "").split(",");
//                    num1 += standardVideos.length;
//                    for (String standardVideo : standardVideos) {
//                        ProjectCompletionReceipt completionReceipt = new ProjectCompletionReceipt();
//                        completionReceipt.setCheckAttachments(stringMap.get(Long.valueOf(standardVideo)));
//                        completionReceipt.setRemarks(completionPhoto.getRemarks());
//                        completionReceipt.setContentLabel("酒店概况照片-【" + completionPhoto.getSubItem() + "：其他现场照片】");
//                        completionReceipts.add(completionReceipt);
//                    }
//                }
//            }
//
//            //循环复制：循环插入复制后的幻灯片，作为第4张和第5张幻灯片
//            int size = ppt.getSlides().size();
//            for (int i = size; i < num1 + size; i++) {
//                copyPptPage(slide6, ppt, i);
//            }
////            //生成幻灯片数据
////            Integer num2 = 0;
////            for (int i = size; i < num1 + size; i++) {
////                pptFileUtil.makePPT(i, ppt, getTextDataMap(getProjectData, completionReceipts.get(num2 + num)));
////                num2++;
////            }
////
//            ppt.saveToFile("亚朵验收报告 V20241119.pptx", FileFormat.PPTX_2013);
//            ppt.dispose();
//
//            // 这里能读到这个流，但是是找不到这个文件的
//            ClassPathResource classPathResource = new ClassPathResource("/亚朵验收报告 V20241119.pptx");
//            // 打开PPTX文件
//            InputStream fis = classPathResource.getInputStream();
//            // 将新的ppt写入到指定的文件中
//            OutputStream outputStream = response.getOutputStream();
//            // 根据枚举类型，设置下拉选项
//            response.setContentType("application/x-download");
//            response.addHeader("Content-Disposition", "attachment;filename=" + new String("亚朵验收报告 V20241119001.pptx".getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
//            // 如果 workbook 有值，则代表设置了下拉选项，需要写回 workbook
//            if (ppt == null) {
//                IOUtils.copy(fis, outputStream);
//                outputStream.flush();
//                fis.close();
//            } else {
//                outputStream.flush();
//                fis.close();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
    }

    static class wrokThread extends Thread {
        private final CountDownLatch countDownLatch;
        private Integer page;
        private XMLSlideShow ppt;
        private Map<String, Object> textDataMap;
        private PPTFileUtil pptFileUtil;

        public wrokThread(CountDownLatch countDownLatch, Integer page, XMLSlideShow ppt, Map<String, Object> textDataMap, PPTFileUtil pptFileUtil) {
            this.countDownLatch = countDownLatch;
            this.page = page;
            this.ppt = ppt;
            this.textDataMap = textDataMap;
            this.pptFileUtil = pptFileUtil;
        }

        @Override
        public void run() {
            try {
                pptFileUtil.makePPT(page, ppt, textDataMap);
            } catch (Exception e) {
                e.printStackTrace();
            }
            countDownLatch.countDown();
        }
    }
    //下载文件锁
    private static  Object fileLock = new Object();
    private static LinkedHashMap<Long,Boolean> onList= new LinkedHashMap();
    @Override
    public void downloadTemplateOld(ProjectCompletionReceiptQueryCriteria criteria, HttpServletResponse response) {
        synchronized (fileLock){
            try{
                onList.put(criteria.getProjectId(),true);
                downfile(criteria,response);
            }catch (Exception e){
                e.printStackTrace();
            }finally {
                onList.remove(criteria.getProjectId());
            }
        }
    }
    private void downfile(ProjectCompletionReceiptQueryCriteria criteria, HttpServletResponse response){
        // 这里能读到这个流，但是是找不到这个文件的
        ClassPathResource classPathResource = new ClassPathResource("template/亚朵验收报告 V20241021.pptx");
        // 打开PPTX文件
        InputStream fis = null;
        try {
            //先从本地的文件中查找有没有该项目近期生成的pptx，如果有则直接下载
            File dir = new File(new File("").getAbsolutePath());
            if (dir == null) {
                return;
            }
            boolean isfind = false;
            for (File file : dir.listFiles()) {
                if (file.getName().contains(criteria.getProjectId() + "_") && file.getName().contains(".pptx")) {
                    System.out.println("发现文件" + file.getName()); // 打印文件路径
//                    Long filetime = Long.parseLong(file.getName().replace(".pptx", "").split("_")[1]);
//                    System.out.println(new Date().getTime() - filetime);
//                    if (new Date().getTime() - filetime < 5 * 60 * 1000l) {
                        response.setContentType("application/x-download");
                        response.addHeader("Content-Disposition", "attachment;filename=" + new String("亚朵验收报告 V20241106001.pptx".getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
                        OutputStream outputStream = response.getOutputStream();
                        try (InputStream in = new BufferedInputStream(new FileInputStream(file))) {
                            byte[] buffer = new byte[4096];
                            int bytesRead;
                            while ((bytesRead = in.read(buffer)) != -1) {
                                outputStream.write(buffer, 0, bytesRead);
                            }
                        } finally {
                            outputStream.flush();
                            outputStream.close();
                        }
                        isfind = true;
//                    }else{
//                        try{file.delete();}catch (Exception e){}
//                    }
                }
            }
            //没有从本地的文件中查找有没有该项目近期生成的pptx，则开始生成新的PPTX
            if (!isfind) {
                fis = classPathResource.getInputStream();

                XMLSlideShow ppt = new XMLSlideShow(fis);

                //获取第4张幻灯片（被复制的幻灯片）
                XSLFSlide slide = ppt.getSlides().get(4);

                //获取第6张幻灯片（被复制的幻灯片）
                XSLFSlide slide6 = ppt.getSlides().get(6);


                //获取验收单不合格数据
                LambdaQueryWrapper<ProjectCompletionReceipt> queryWrapper = Wrappers.lambdaQuery(ProjectCompletionReceipt.class)
                        .eq(ProjectCompletionReceipt::getProjectId, criteria.getProjectId())
                        .eq(ProjectCompletionReceipt::getAcceptance, JhSystemEnum.acceptance.UNQUALIFIED.getValue())
                        .isNotNull(ProjectCompletionReceipt::getCheckAttachments);
                List<ProjectCompletionReceipt> receiptList = projectCompletionReceiptRepository.selectList(queryWrapper);

                //获取验收单必拍现场照片
                LambdaQueryWrapper<ProjectCompletionReceipt> queryWrapper1 = Wrappers.lambdaQuery(ProjectCompletionReceipt.class)
                        .eq(ProjectCompletionReceipt::getProjectId, criteria.getProjectId())
                        .eq(ProjectCompletionReceipt::getIsRealPhotosTaken, 1)
                        .isNotNull(ProjectCompletionReceipt::getCheckAttachments);
                List<ProjectCompletionReceipt> receiptList1 = projectCompletionReceiptRepository.selectList(queryWrapper1);

                //获取其他现场照片
                LambdaQueryWrapper<ProjectCompletionPhoto> queryWrapper2 = Wrappers.lambdaQuery(ProjectCompletionPhoto.class)
                        .eq(ProjectCompletionPhoto::getProjectId, criteria.getProjectId())
                        .isNotNull(ProjectCompletionPhoto::getStandardPhotos);
                List<ProjectCompletionPhoto> completionPhotos = projectCompletionPhotoRepository.selectList(queryWrapper2);

                if (ObjectUtil.isEmpty(receiptList) && ObjectUtil.isEmpty(receiptList1) && ObjectUtil.isNotEmpty(completionPhotos)) {
                    throw new BadRequestException("当前项目不存在不合格以及实拍照片数据，无需导出！");
                }
                //获取所有的不合格照片
                String collect = "";
                if (ObjectUtil.isNotEmpty(receiptList)) {
                    collect = receiptList.stream().map(ProjectCompletionReceipt::getCheckAttachments).collect(Collectors.joining(","));
                    String collectnew = receiptList.stream().map(ProjectCompletionReceipt::getRectificationAttachments).collect(Collectors.joining(","));
                    if(StringUtils.isNotEmpty(collectnew)){
                        collect = collect+","+collectnew;
                    }
                    if (ObjectUtil.isEmpty(collect)) {
                        throw new BadRequestException("当前项目不合格数据不存在照片，请重新填写验收单！");
                    }
                }
                //获取不合格照片的 Map<key=文件ID,val=文件名地址>
                List<LocalStorage> storageList = localStorageRepository.findByIds(collect.split(","));
                Map<Long, String> stringMap = storageList.stream().filter(e -> StringUtils.isNotEmpty(e.getNodeId()))
                        .collect(Collectors.toMap(LocalStorage::getId, LocalStorage::getRealName));

                List<ProjectCompletionReceipt> completionReceipts = new LinkedList<>();
                Integer num = receiptList.size();
                for (ProjectCompletionReceipt receipt : receiptList) {
                    if (ObjectUtil.isNotEmpty(receipt.getCheckAttachments())) {
                        String[] split = receipt.getCheckAttachments().replaceAll("^,|,$", "").split(",");
                        String[] split2 = StringUtils.isEmpty(receipt.getRectificationAttachments())?new String[0]:receipt.getRectificationAttachments().replaceAll("^,|,$", "").split(",");
                        int length = split.length>split2.length?split.length:split2.length;
                        num = num + length - 1;
                        for(int i=0;i<length;i++){
                            ProjectCompletionReceipt completionReceipt = new ProjectCompletionReceipt();
                            completionReceipt.setCheckAttachments((i<=(split.length-1))?stringMap.get(Long.valueOf(split[i])):(split.length>0?stringMap.get(Long.valueOf(split[split.length-1])):null));
                            completionReceipt.setRectificationAttachments((i<=(split2.length-1))?stringMap.get(Long.valueOf(split2[i])):(split2.length>0?stringMap.get(Long.valueOf(split[split2.length-1])):null));
                            completionReceipt.setRemarks(receipt.getRemarks());
                            completionReceipts.add(completionReceipt);
                        }
//                        for (String imgNodeId : split) {
//                            ProjectCompletionReceipt completionReceipt = new ProjectCompletionReceipt();
//                            completionReceipt.setCheckAttachments(stringMap.get(Long.valueOf(imgNodeId)));
//                            completionReceipt.setRemarks(receipt.getRemarks());
//                            completionReceipts.add(completionReceipt);
//                        }
                    }
                }

                //循环复制：循环插入复制后的幻灯片，作为第4张和第5张幻灯片
                for (int i = 5; i < num + 4; i++) {
                    copyPptPageOld(slide, ppt, i);
                }

                //获取项目数据
                Map<String, String> getProjectData = this.getGetProjectData(criteria.getProjectId() + "");
                Map<String, Object> textDataMap = getTextDataMap(getProjectData, null);
                pptFileUtil.makePPT(1, ppt, textDataMap);
                pptFileUtil.makePPT(2, ppt, textDataMap);
                CountDownLatch latch1 = null;
                //生成幻灯片数据
                for (int i = 5; i < num + 5; i++) {
                    if (i % 5 == 0) {
                        latch1 = new CountDownLatch(num + 5 - i >= 5 ? 5 : (num + 5 - i));
                    }
                    Map<String, Object> currmap = getTextDataMap(getProjectData, completionReceipts.get(i - 5));
                    wrokThread thread = new wrokThread(latch1, i, ppt, currmap, pptFileUtil);
                    thread.start();
                    if (i % 5 == 4 || i == (num + 4)) {
                        latch1.await();
                    }
//                pptFileUtil.makePPT(i, ppt, );
                }
                //获取所有的实拍照片
                collect = "";
                Integer num1 = 0;
                if (ObjectUtil.isNotEmpty(receiptList1)) {
                    //必拍照片的现场照片
                    collect = receiptList1.stream().map(ProjectCompletionReceipt::getCheckAttachments).collect(Collectors.joining(","));
                    //必拍照片的 现场视频
                    collect = collect + "," + receiptList1.stream().map(ProjectCompletionReceipt::getStandardVideos).collect(Collectors.joining(","));

                    if (ObjectUtil.isNotEmpty(completionPhotos)) {
                        //其他照片的 现场照片
                        collect = collect + "," + completionPhotos.stream().map(ProjectCompletionPhoto::getStandardPhotos).collect(Collectors.joining(","));
                        //其他照片的 现场视频
                        collect = collect + "," + completionPhotos.stream().map(ProjectCompletionPhoto::getStandardVideos).collect(Collectors.joining(","));
                    }

                    if (ObjectUtil.isEmpty(collect)) {
                        throw new BadRequestException("当前项目实拍照片不存在现场照片，请重新填写验收单！");
                    }
                    //必拍照片的实例图片
                    collect = collect + receiptList1.stream().map(ProjectCompletionReceipt::getExampleDiagram).collect(Collectors.joining(","));
                }

                //获取不合格照片的 Map<key=文件ID,val=文件名地址>
                storageList = localStorageRepository.findByIds(collect.split(","));
                stringMap = storageList.stream().filter(e -> StringUtils.isNotEmpty(e.getNodeId()))
                        .collect(Collectors.toMap(LocalStorage::getId, LocalStorage::getRealName));


                for (ProjectCompletionReceipt receipt : receiptList1) {
                    if (ObjectUtil.isNotEmpty(receipt.getCheckAttachments())) {
                        //必拍照片-示例图
                        String[] exampleDiagram = new String[1];
                        if (ObjectUtil.isNotEmpty(receipt.getExampleDiagram())) {
                            exampleDiagram = receipt.getExampleDiagram().replaceAll("^,|,$", "").split(",");
                        }

                        //必拍照片-现场照片
                        String[] checkAttachments = receipt.getCheckAttachments().replaceAll("^,|,$", "").split(",");

                        num1 = num1 + checkAttachments.length;

                        for (int i = 0; i < checkAttachments.length; i++) {
                            String exampleDiagram1 = "";
                            if (exampleDiagram.length > i && ObjectUtil.isNotEmpty(exampleDiagram[i])) {
                                exampleDiagram1 = stringMap.get(Long.valueOf(exampleDiagram[i]));
                            }
                            String checkAttachment = stringMap.get(Long.valueOf(checkAttachments[i]));
                            ProjectCompletionReceipt completionReceipt = new ProjectCompletionReceipt();
                            completionReceipt.setCheckAttachments(exampleDiagram1 + "-" + checkAttachment);
                            completionReceipt.setRemarks(receipt.getRemarks());
                            completionReceipt.setContentLabel("酒店概况照片-【" + receipt.getSubItem() + "：" + receipt.getSubItemContent() + "】");
                            completionReceipts.add(completionReceipt);
                        }
                    }
//                if (ObjectUtil.isNotEmpty(receipt.getStandardVideos())) {
//                    //必拍照片-现场视频
//                    String[] standardVideos = receipt.getStandardVideos().replaceAll("^,|,$", "").split(",");
//
//                    num1 = num1 + standardVideos.length;
//                    for (String standardVideo : standardVideos) {
//                        ProjectCompletionReceipt completionReceipt = new ProjectCompletionReceipt();
//                        completionReceipt.setCheckAttachments(stringMap.get(Long.valueOf(standardVideo)));
//                        completionReceipt.setRemarks(receipt.getRemarks());
//                        completionReceipt.setContentLabel("酒店概况照片-【" + receipt.getSubItem() + "：" + receipt.getSubItemContent() + "】");
//                        completionReceipts.add(completionReceipt);
//                    }
//                }
                }

                for (ProjectCompletionPhoto completionPhoto : completionPhotos) {
                    //其他照片的现场照片
                    if (ObjectUtil.isNotEmpty(completionPhoto.getStandardPhotos())) {
                        String[] split = completionPhoto.getStandardPhotos().replaceAll("^,|,$", "").split(",");
                        String phono = "";
                        for (int i = 0; i < split.length; i++) {
                            if ((i % 2 == 0) && i != 0) {
                                //当前i是2的倍数
                                num1 += 1;
                                phono += "-" + stringMap.get(Long.valueOf(split[i]));
                                ProjectCompletionReceipt completionReceipt = new ProjectCompletionReceipt();
                                completionReceipt.setCheckAttachments(phono);
                                completionReceipt.setRemarks(completionPhoto.getRemarks());
                                completionReceipt.setContentLabel("酒店概况照片-【" + completionPhoto.getSubItem() + "：其他现场照片】");
                                completionReceipts.add(completionReceipt);
                            } else if (i == split.length - 1) {
                                //当前i不是2的倍数，且是最后一个
                                num1 += 1;
                                phono = stringMap.get(Long.valueOf(split[i]));
                                ProjectCompletionReceipt completionReceipt = new ProjectCompletionReceipt();
                                completionReceipt.setCheckAttachments(phono);
                                completionReceipt.setRemarks(completionPhoto.getRemarks());
                                completionReceipt.setContentLabel("酒店概况照片-【" + completionPhoto.getSubItem() + "：其他现场照片】");
                                completionReceipts.add(completionReceipt);
                            } else {
                                phono = stringMap.get(Long.valueOf(split[i]));
                            }
                        }
                    }

//                //其他照片的视频
//                if (ObjectUtil.isNotEmpty(completionPhoto.getStandardVideos())) {
//                    String[] standardVideos = completionPhoto.getStandardVideos().replaceAll("^,|,$", "").split(",");
//                    num1 += standardVideos.length;
//                    for (String standardVideo : standardVideos) {
//                        ProjectCompletionReceipt completionReceipt = new ProjectCompletionReceipt();
//                        completionReceipt.setCheckAttachments(stringMap.get(Long.valueOf(standardVideo)));
//                        completionReceipt.setRemarks(completionPhoto.getRemarks());
//                        completionReceipt.setContentLabel("酒店概况照片-【" + completionPhoto.getSubItem() + "：其他现场照片】");
//                        completionReceipts.add(completionReceipt);
//                    }
//                }
                }

//            copyPptPage(slide5, ppt, ppt.getSlides().size());

                //循环复制：循环插入复制后的幻灯片，作为第4张和第5张幻灯片
                int size = ppt.getSlides().size();
                for (int i = size; i < num1 + size - 1; i++) {
                    copyPptPageOld(slide6, ppt, i);
                }

                //生成幻灯片数据
                Integer num2 = 0;
                CountDownLatch latch2 = null;
                for (int i = size; i < num1 + size; i++) {
                    if ((i - size) % 5 == 0) {
                        latch2 = new CountDownLatch(num1 + size - i >= 5 ? 5 : (num1 + size - i));
                    }
                    wrokThread thread = new wrokThread(latch2, i, ppt, getTextDataMap(getProjectData, completionReceipts.get(num2 + num)), pptFileUtil);
                    thread.start();
                    if ((i - size) % 5 == 4 || i == (num1 + size - 1)) {
                        latch2.await();
                    }
                    //pptFileUtil.makePPT(i, ppt, getTextDataMap(getProjectData, completionReceipts.get(num2 + num)));
                    num2++;
                }
                //latch2.await();
                // 本地在下载前保留一份，方便下次短期时间内请求，直接下载开始
                File bendifile = new File(criteria.getProjectId() + "_" + new Date().getTime() + ".pptx");
                if (bendifile.exists()) {
                    bendifile.createNewFile();
                }
                ppt.write(new FileOutputStream(bendifile));
                // 本地在下载前保留一份，方便下次短期时间内请求，直接下载结束
                OutputStream outputStream = response.getOutputStream();
//            ppt.write(outputStream);

                // 根据枚举类型，设置下拉选项
                response.setContentType("application/x-download");
                response.addHeader("Content-Disposition", "attachment;filename=" + new String("亚朵验收报告 V20241106001.pptx".getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
                // 如果 workbook 有值，则代表设置了下拉选项，需要写回 workbook
                if (ppt == null) {
                    IOUtils.copy(fis, outputStream);
                    outputStream.flush();
                    fis.close();
                } else {
                    ppt.write(outputStream);
                    outputStream.flush();
                    ppt.close();
                    fis.close();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
    @Override
    public String queryUnqualifiedDownloadState(Long projectId) {
        //先从本地的文件中查找有没有该项目近期生成的pptx，如果有则直接下载
        if(onList.get(projectId)!=null&&onList.get(projectId)){ return "-1";}
        File dir = new File(new File("").getAbsolutePath());
        if (dir == null) {
            return "0";
        }
        boolean isfind = false;
        for (File file : dir.listFiles()) {
            if (file.getName().contains(projectId + "_") && file.getName().contains(".pptx")) {
                System.out.println("发现文件" + file.getName()); // 打印文件路径
                Long filetime = Long.parseLong(file.getName().replace(".pptx", "").split("_")[1]);
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String returnstr = format.format(new Date(filetime));
                return returnstr;
            }
        }
        return "0";
    }
    /**
     * 删除已经生成的文件
     * @param projectId
     */
    @Override
    public String removeUnqualified(Long projectId){
        //先从本地的文件中查找有没有该项目近期生成的pptx，如果有则直接下载
        if(onList.get(projectId)!=null&&onList.get(projectId)){ return "-1";}
        File dir = new File(new File("").getAbsolutePath());
        if (dir == null) {
            return "0";
        }
        boolean isfind = false;
        for (File file : dir.listFiles()) {
            if (file.getName().contains(projectId + "_") && file.getName().contains(".pptx")) {
                System.out.println("发现文件" + file.getName()); // 打印文件路径
                try{file.delete();}catch (Exception e){}
                return "1";
            }
        }
        return "1";
    }

    private Map<String, String> getGetProjectData(String projectId) {
        Map<String, String> projectData = new LinkedHashMap<>();
        ProjectInfo byId = projectInfoService.getById(projectId);
        projectData.put("projectName", byId.getProjectName());

        //从二级任务中获取负责人  竣工验收申请、竣工验收
        LambdaQueryWrapper<ProjectGroup> query = Wrappers.lambdaQuery(ProjectGroup.class);
        query.eq(ProjectGroup::getProjectId, projectId)
                .eq(ProjectGroup::getNodeCode, "eng-00129");
        ProjectGroup projectGroup = projectGroupRepository.selectOne(query);
        ProjectStakeholdersQueryCriteria criteria1 = new ProjectStakeholdersQueryCriteria();
        criteria1.setProjectId(Long.valueOf(projectId));
        criteria1.setRoleCodes(projectGroup.getRoleCode().split(","));
        List<ProjectStakeholdersDto> stakeholdersDtos = projectStakeholdersService.queryAll(criteria1);
        if (ObjectUtil.isNotEmpty(stakeholdersDtos)) {
            String acceptor = stakeholdersDtos.stream().map(ProjectStakeholdersDto::getUserName).collect(Collectors.joining(","));
            //申请验收人员
            projectData.put("acceptor", acceptor);
        }


        query = Wrappers.lambdaQuery(ProjectGroup.class);
        query.eq(ProjectGroup::getProjectId, projectId)
                .eq(ProjectGroup::getNodeCode, "eng-00133");
        projectGroup = projectGroupRepository.selectOne(query);
        criteria1.setProjectId(Long.valueOf(projectId));
        criteria1.setRoleCodes(projectGroup.getRoleCode().split(","));
        stakeholdersDtos = projectStakeholdersService.queryAll(criteria1);
        if (ObjectUtil.isNotEmpty(stakeholdersDtos)) {
            String acceptancePersonnel = stakeholdersDtos.stream().map(ProjectStakeholdersDto::getUserName).collect(Collectors.joining(","));
            //竣工验收人员
            projectData.put("acceptancePersonnel", acceptancePersonnel);
        }


        //从三级字段上获取          验收日期        验收时间         设计房间数        施工单位         项目经理         现长          客房设计单位      公区设计单位
        String[] nodeCode = {"eng-00133012", "eng-00133047", "eng-00129023", "eng-00133005", "eng-00129029", "eng-00121002", "des-00101077", "des-00101083"};
        ProjectInfo projectInfo = projectInfoService.getById(projectId);
        util.initialize(projectInfo.getProjectId());
        LambdaQueryWrapper<ProjectNodeInfo> wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectInfo.getProjectId())
                .in(ProjectNodeInfo::getNodeCode, nodeCode);
        List<ProjectNodeInfo> list = projectNodeInfoRepository.selectList(wrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            Map<String, String> stringStringMap = list.stream().filter(e -> StringUtils.isNotEmpty(e.getRemark())).collect(Collectors.toMap(ProjectNodeInfo::getNodeCode, ProjectNodeInfo::getRemark));
            String s3 = stringStringMap.get("eng-00133012");
            String s4 = stringStringMap.get("eng-00133047");
            if (ObjectUtil.isNotEmpty(s3)) {
                //验收日期
                projectData.put("acceptanceDate", s3 + AtourSystemEnum.CheckTime.getCheckTime(s4));
            }

            //设计房间数
            projectData.put("totalRoomCapacity", stringStringMap.get("eng-00129023"));
            //施工单位
            String s2 = stringStringMap.get("eng-00133005");
            if (ObjectUtil.isNotEmpty(s2)) {
                SupplierInfo supplierInfo = supplierInfoRepository.selectById(Long.valueOf(s2));
                projectData.put("constructionUnit", ObjectUtil.isNotEmpty(supplierInfo) ? supplierInfo.getSupNameCn() : "");
            }
            //项目经理
            String s5 = stringStringMap.get("eng-00129029");
            if (ObjectUtil.isNotEmpty(s5)) {
                String[] split = s5.split("-");
                projectData.put("projectManager", split[split.length - 1]);
            }


            //现长
            User byUserId = userRepository.getByUserId(stringStringMap.get("eng-00121002"));
            projectData.put("storeManager", ObjectUtil.isNotEmpty(byUserId) ? byUserId.getNickName() : "");
            //设计单位
            String s = stringStringMap.get("des-00101077");
            String s1 = stringStringMap.get("des-00101083");
            LambdaQueryWrapper<SupplierInfo> queryWrapper = Wrappers.lambdaQuery(SupplierInfo.class);
            queryWrapper.and(queryWrapper1 -> queryWrapper1.eq(SupplierInfo::getId, s).or().eq(SupplierInfo::getId, s1));
            List<SupplierInfo> supplierInfos = supplierInfoRepository.selectList(queryWrapper);
            if (ObjectUtil.isNotEmpty(supplierInfos)) {
                String supNameCn = supplierInfos.stream().map(SupplierInfo::getSupNameCn).collect(Collectors.joining(","));
                projectData.put("designUnit", supNameCn);
            }

        }
        return projectData;
    }

//
//    /**
//     * 复制ppt单页
//     *
//     * @param template 模板页
//     * @param ppt      ppt
//     * @param index    复制页放置位置
//     * @return 复制页
//     */
//    public static ISlide copyPptPage(ISlide template, Presentation ppt, int index) {
//
//        // 创建一个新的幻灯片，并设置其基于默认的模板
////        XSLFSlide slide = ppt.createSlide();
//
//        System.out.println("当前幻灯片：" + index + " ： " + ppt.getSlides().size());
//        // 创建新的一页PPT，按模板的布局母页
//        try {
//            int append = ppt.getSlides().append(template);
//            System.out.println(append);
//            ISlide iSlide = ppt.getSlides().get(ppt.getFirstSlideNumber());
//            // 排序（在PPT中的第几页）
//            iSlide.setSlideNumber(index);
//            System.out.println("创建后幻灯片：" + index + " ： " + ppt.getSlides().size());
//            return iSlide;
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return null;
//    }

    /**
     * 复制ppt单页
     *
     * @param template 模板页
     * @param ppt      ppt
     * @param index    复制页放置位置
     * @return 复制页
     */
    public static XSLFSlide copyPptPageOld(XSLFSlide template, XMLSlideShow ppt, int index) {

        // 创建一个新的幻灯片，并设置其基于默认的模板
//        XSLFSlide slide = ppt.createSlide();

        // 创建新的一页PPT，按模板的布局母页
        XSLFSlide newSlide = ppt.createSlide(template.getMasterSheet());

        // 复制模板页中的shapes
        List<XSLFShape> shapes = template.getShapes();
        if (shapes.size() > 0) {
            for (XSLFShape shape : shapes) {
                newSlide.importContent(shape.getSheet());
            }
        }
        // 排序（在PPT中的第几页）
        ppt.setSlideOrder(newSlide, index);
        return newSlide;
    }


    private static Map<String, Object> getTextDataMap(Map<String, String> getProjectData, ProjectCompletionReceipt receipt) {
        // 文本数据映射表
        Map<String, Object> textDataMap = new HashMap<>();

        Date currentDate = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = dateFormat.format(currentDate);
        textDataMap.put("${currentDate}", "Ver. " + formattedDate);
        textDataMap.put("${projectName}", ObjectUtil.isNotEmpty(getProjectData.get("projectName")) ? getProjectData.get("projectName") : "");


        textDataMap.put("${totalRoomCapacity}", ObjectUtil.isNotEmpty(getProjectData.get("totalRoomCapacity")) ? getProjectData.get("totalRoomCapacity") : "");
        textDataMap.put("${acceptor}", ObjectUtil.isNotEmpty(getProjectData.get("acceptor")) ? getProjectData.get("acceptor") : "");

        textDataMap.put("${constructionUnit}", ObjectUtil.isNotEmpty(getProjectData.get("constructionUnit")) ? getProjectData.get("constructionUnit") : "");
        textDataMap.put("${designUnit}", ObjectUtil.isNotEmpty(getProjectData.get("designUnit")) ? getProjectData.get("designUnit") : "");


        textDataMap.put("${projectManager}", getProjectData.get("projectManager"));
        textDataMap.put("${storeManager}", ObjectUtil.isNotEmpty(getProjectData.get("storeManager")) ? getProjectData.get("storeManager") : "");


        textDataMap.put("${acceptancePersonnel}", ObjectUtil.isNotEmpty(getProjectData.get("acceptancePersonnel")) ? getProjectData.get("acceptancePersonnel") : "");
        textDataMap.put("${acceptanceDate}", ObjectUtil.isNotEmpty(getProjectData.get("acceptanceDate")) ? getProjectData.get("acceptanceDate") : "");

        if (ObjectUtil.isNotEmpty(receipt)) {

            //酒店概况   实拍照片备注
            textDataMap.put("${problemStatement}", ObjectUtil.isNotEmpty(receipt.getRemarks()) ? receipt.getRemarks() : "暂无");

            //酒店概况   实拍照片标准
            textDataMap.put("${realPhotoStandards}", ObjectUtil.isNotEmpty(receipt.getContentLabel()) ? receipt.getContentLabel() : "");

            //验收单上传图片
            textDataMap.put("textPic", ObjectUtil.isNotEmpty(receipt.getCheckAttachments()) ? receipt.getCheckAttachments() : "");

            //实拍照片
            if (ObjectUtil.isNotEmpty(receipt.getCheckAttachments())) {
                if (receipt.getCheckAttachments().contains("-")) {
                    String[] checkAttachments = receipt.getCheckAttachments().split("-");
                    if (ObjectUtil.isNotEmpty(checkAttachments[0])) {
                        //酒店概况   实拍照片左侧（照片）示例图
                        textDataMap.put("textPicLeft", checkAttachments[0]);
                    }
                    if (ObjectUtil.isNotEmpty(checkAttachments[1])) {
                        //酒店概况   实拍照片右侧（照片）
                        textDataMap.put("textPicRight", checkAttachments[1]);
                    }
                }else{
                    textDataMap.put("textPicLeft", receipt.getCheckAttachments());
                    textDataMap.put("textPicRight",receipt.getRectificationAttachments()==null?"":receipt.getRectificationAttachments());
                }
            }else if(ObjectUtil.isNotEmpty(receipt.getRectificationAttachments())){
                textDataMap.put("textPicLeft", "");
                textDataMap.put("textPicRight",receipt.getRectificationAttachments()==null?"":receipt.getRectificationAttachments());
            }
        }


//        String[] ary = new String[3];
//        ary[0] = "文本框1";
//        ary[1] = "文本框2";
//        ary[2] = "文本框3";
//        textDataMap.put("testTextBox", "ary");
        return textDataMap;
    }

    @Override
    public List<ProjectCompletionReceiptSubItemDto> getTotalItemList(ProjectCompletionReceiptQueryCriteria criteria) {
        List<ProjectCompletionReceiptSubItemDto> subItemList = projectCompletionReceiptRepository.getTotalItemList(criteria);
        return subItemList;
    }

    @Override
    public List<ProjectCompletionReceiptSubItemDto> getSubItemList(ProjectCompletionReceiptQueryCriteria criteria) {
        List<ProjectCompletionReceiptSubItemDto> subItemList = projectCompletionReceiptRepository.getSubItemList(criteria);
        return subItemList;
    }

    @Override
    public ProjectCompletionReceiptListDto getListBySubItem(ProjectCompletionReceiptQueryCriteria criteria) {
        ProjectCompletionReceiptListDto dto = new ProjectCompletionReceiptListDto();

        List<ProjectCompletionReceipt> receiptDtos = projectCompletionReceiptRepository.getListBySubItem(criteria);
        String collect = receiptDtos.stream().map(ProjectCompletionReceipt::getExampleDiagram).collect(Collectors.joining(","));
        List<LocalStorage> storageIds = localStorageRepository.getByStorageIds(collect.split(","));
        Map<Long, String> collect1 = storageIds.stream().collect(Collectors.toMap(LocalStorage::getId, LocalStorage::getPath));
        for (ProjectCompletionReceipt receiptDto : receiptDtos) {
            if (ObjectUtil.isNotEmpty(receiptDto.getExampleDiagram())) {
                String[] split = receiptDto.getExampleDiagram().split(",");
                String[] exampleDiagramPaths = new String[split.length];
                for (int i = 0; i < split.length; i++) {
                    exampleDiagramPaths[i] = collect1.get(Long.valueOf(split[i]));
                }
                receiptDto.setExampleDiagramPaths(exampleDiagramPaths);
            }
        }
        dto.setReceiptDtos(receiptDtos);

        List<ProjectCompletionReceipt> mustPhotoDtos = projectCompletionReceiptRepository.getMustPhotoDtos(criteria);
        collect = mustPhotoDtos.stream().map(ProjectCompletionReceipt::getExampleDiagram).collect(Collectors.joining(","));
        storageIds = localStorageRepository.getByStorageIds(collect.split(","));
        collect1 = storageIds.stream().collect(Collectors.toMap(LocalStorage::getId, LocalStorage::getPath));
        for (ProjectCompletionReceipt receiptDto : mustPhotoDtos) {
            if (ObjectUtil.isNotEmpty(receiptDto.getExampleDiagram())) {
                String[] split = receiptDto.getExampleDiagram().split(",");
                String[] exampleDiagramPaths = new String[split.length];
                for (int i = 0; i < split.length; i++) {
                    exampleDiagramPaths[i] = collect1.get(Long.valueOf(split[i]));
                }
                receiptDto.setExampleDiagramPaths(exampleDiagramPaths);
            }
        }
        dto.setMustPhotoDtos(mustPhotoDtos);


        List<ProjectCompletionPhoto> photoDtos = projectCompletionPhotoRepository.getPhotoBySubItem(criteria);
        dto.setPhotoDtos(photoDtos);
        return dto;
    }

    @Override
    public void updateCompletionReceipt(ProjectCompletionReceiptListDto listDto) {
        //保存验收单列表数据 、必拍现场照片 、 其他现场照片  、项目验收单说明表
        if (ObjectUtil.isNotEmpty(listDto.getReceiptDtos())) {
            this.updateBatchById(listDto.getReceiptDtos());
        }
        if (ObjectUtil.isNotEmpty(listDto.getMustPhotoDtos())) {
            this.updateBatchById(listDto.getMustPhotoDtos());
        }
        if (ObjectUtil.isNotEmpty(listDto.getPhotoDtos())) {
            projectCompletionPhotoService.saveOrUpdateBatch(listDto.getPhotoDtos());
        }
        if (ObjectUtil.isNotEmpty(listDto.getCompletionReceiptDescription())) {
            if (ObjectUtil.isNotEmpty(listDto.getCompletionReceiptDescription().getProjectId())) {
                LambdaQueryWrapper<ProjectCompletionReceiptDescription> queryWrapper = Wrappers.lambdaQuery(ProjectCompletionReceiptDescription.class);
                queryWrapper.and(wrapper -> wrapper.in(ProjectCompletionReceiptDescription::getProjectId, listDto.getCompletionReceiptDescription().getProjectId())
                        .or().eq(ProjectCompletionReceiptDescription::getId, listDto.getCompletionReceiptDescription().getId()));
                projectCompletionReceiptDescriptionRepository.delete(queryWrapper);
                listDto.getCompletionReceiptDescription().setId(null);
                projectCompletionReceiptDescriptionService.saveOrUpdate(listDto.getCompletionReceiptDescription());
            }
        }
        if (ObjectUtil.isNotEmpty(listDto.getProjectCompletionReceiptSummary()) && ObjectUtil.isNotEmpty(listDto.getProjectCompletionReceiptSummary().getProjectId())) {
            projectCompletionReceiptSummaryService.insertOrUpdate(listDto.getProjectCompletionReceiptSummary());
        }
    }

    @Override
    public ProjectCompletionReceiptSummaryDto getAcceptanceSummary(ProjectCompletionReceiptQueryCriteria criteria) {
        //TODO 获取竣工验收汇总数据
        ProjectCompletionReceiptSummaryDto summaryDto = new ProjectCompletionReceiptSummaryDto();
        String key = JhSystemEnum.completionContentEnum.ALLCOMPLETION.getKey();
        criteria.setCompletionContent(key);
        //单项内容的验收状态
//        List<ProjectCompletionReceiptSingleQualificationDto> qualificationDtos = projectCompletionReceiptRepository.getQualificationDtos(criteria);
//        summaryDto.setQualificationDtos(qualificationDtos);
        ProjectCompletionReceiptSingleQualificationDto dto = new ProjectCompletionReceiptSingleQualificationDto();
        ProjectCompletionReceiptSummary receiptSummary = projectCompletionReceiptSummaryService.selectByProjectId(criteria.getProjectId());
        if (ObjectUtil.isNotEmpty(receiptSummary)) {
            dto.setLinkageTesting(ObjectUtil.isNotEmpty(receiptSummary.getLinkageTest()) ? receiptSummary.getLinkageTest() : "");
            dto.setFireBroadcasting(ObjectUtil.isNotEmpty(receiptSummary.getFireBroadcast()) ? receiptSummary.getFireBroadcast() : "");
            dto.setFireWaterSystem(ObjectUtil.isNotEmpty(receiptSummary.getFireWater()) ? receiptSummary.getFireWater() : "");
            dto.setAccessControl(ObjectUtil.isNotEmpty(receiptSummary.getAccessControl()) ? receiptSummary.getAccessControl() : "");
            dto.setInstallationStandards(ObjectUtil.isNotEmpty(receiptSummary.getInstallationStandards()) ? receiptSummary.getInstallationStandards() : "");
            dto.setEvacuate(ObjectUtil.isNotEmpty(receiptSummary.getFireEvacuation()) ? receiptSummary.getFireEvacuation() : "");
        }
        summaryDto.setSingleQualificationDto(dto);

        //验收汇总列表
        List<ProjectCompletionReceiptSummaryListDto> summaryListDtos = projectCompletionReceiptRepository.getSummaryListDtos(criteria);
        ProjectCompletionReceiptSummaryListDto listDto = new ProjectCompletionReceiptSummaryListDto();
        listDto.setTotalItemLabel("汇总总分");
        if (ObjectUtil.isNotEmpty(summaryListDtos)) {
            //分项总分
            listDto.setTotalScoreOfSubItems(summaryListDtos.stream().mapToDouble(ProjectCompletionReceiptSummaryListDto::getTotalScoreOfSubItems).sum());
            //分项得分
            listDto.setSubItemScore(summaryListDtos.stream().mapToDouble(ProjectCompletionReceiptSummaryListDto::getSubItemScore).sum());
            if (ObjectUtil.isNotEmpty(listDto.getTotalScoreOfSubItems()) && listDto.getTotalScoreOfSubItems() != 0) {
                listDto.setQualificationRate(String.format("%.2f", (listDto.getSubItemScore() / listDto.getTotalScoreOfSubItems() * 100.0)));
            }

//            for (ProjectCompletionReceiptSummaryListDto summaryListDto : summaryListDtos) {
//                //分项得分-最终得分（百分制）
//                if (ObjectUtil.isNotEmpty(listDto.getTotalScoreOfSubItems()) && 0 != listDto.getTotalScoreOfSubItems() && ObjectUtil.isNotEmpty(summaryListDto.getSubItemScore())) {
//                    double percentageSystem = 100d / listDto.getTotalScoreOfSubItems() * summaryListDto.getSubItemScore();
//                    summaryListDto.setPercentageSystem(Math.round(percentageSystem*100)/100d);
//                }else if(ObjectUtil.isEmpty(summaryListDto.getSubItemScore())||summaryListDto.getSubItemScore()==0d){
//                    summaryListDto.setPercentageSystem(0.0d);
//                }
//            }
//
//            //分项得分-最终得分（百分制）
//            listDto.setPercentageSystem(Math.round(summaryListDtos.stream().mapToDouble(ProjectCompletionReceiptSummaryListDto::getPercentageSystem).sum()*100)/100d);


            for (ProjectCompletionReceiptSummaryListDto summaryListDto : summaryListDtos) {
                //分项得分-最终得分（百分制）
                if (ObjectUtil.isNotEmpty(listDto.getTotalScoreOfSubItems()) && 0 != listDto.getTotalScoreOfSubItems() && ObjectUtil.isNotEmpty(summaryListDto.getSubItemScore())) {
                    //    分项得分 /  分项总分 *  100
                    double percentageSystem = summaryListDto.getSubItemScore() / listDto.getTotalScoreOfSubItems() * 100d;
                    summaryListDto.setPercentageSystem(Math.round(percentageSystem * 100) / 100d);
                } else if (ObjectUtil.isEmpty(summaryListDto.getSubItemScore()) || summaryListDto.getSubItemScore() == 0d) {
                    summaryListDto.setPercentageSystem(0.0d);
                }
            }

            //分项得分-最终得分（百分制）
//            double sum = summaryListDtos.stream().mapToDouble(ProjectCompletionReceiptSummaryListDto::getPercentageSystem).sum();
//            listDto.setPercentageSystem(Math.round(sum)*100/100d);

            listDto.setPercentageSystem(Math.round(summaryListDtos.stream().mapToDouble(ProjectCompletionReceiptSummaryListDto::getPercentageSystem).sum() * 100) / 100d);

        }

        summaryListDtos.add(listDto);
        summaryDto.setSummaryListDtos(summaryListDtos);

        return summaryDto;
    }

    @Override
    public ProjectCompletionReceipt getScoreCalculation(ProjectCompletionReceipt sumUpDTOS) {
        ProjectCompletionReceipt completionReceipt = new ProjectCompletionReceipt();
        BeanUtil.copyProperties(sumUpDTOS, completionReceipt, CopyOptions.create().setIgnoreNullValue(true));
        //计算标准分值
        completionReceipt.setStandardScore(this.standardScore(completionReceipt.getStandardScore(), completionReceipt.getAcceptance(), completionReceipt.getScore()));
        //计算得分
        //查询模板表的一票否决不及格分数和必备项不及格分数
        String oneVoteVetoUnqualifiedScore = null;
        String mandatoryItemsUnqualifiedScore = null;
        //重要性扣分数   一般项扣分数
        String importantItemUnqualifiedScore = null;
        String genericItemUnqualifiedScore = null;

        if (sumUpDTOS.getReceiptGroupId() != null) {
            TemplateGroupCompletionReceipt groupCompletionReceipt = templateGroupCompletionReceiptService.getById(sumUpDTOS.getReceiptGroupId());
            if (groupCompletionReceipt != null) {
                oneVoteVetoUnqualifiedScore = groupCompletionReceipt.getOneVoteVetoUnqualifiedScore();
                mandatoryItemsUnqualifiedScore = groupCompletionReceipt.getMandatoryItemsUnqualifiedScore();

                importantItemUnqualifiedScore = groupCompletionReceipt.getImportantItemUnqualifiedScore();
                genericItemUnqualifiedScore = groupCompletionReceipt.getGenericItemUnqualifiedScore();
            }
        }
        //计算得分
        completionReceipt.setGetScore(this.getScore(completionReceipt.getGetScore(), completionReceipt.getClassification(), completionReceipt.getAcceptance(),
                completionReceipt.getStandardScore(), oneVoteVetoUnqualifiedScore, mandatoryItemsUnqualifiedScore, importantItemUnqualifiedScore, genericItemUnqualifiedScore));
        //计算合格率
        completionReceipt.setQualificationRate(this.getQualificationRate(completionReceipt.getQualificationRate(), completionReceipt.getAcceptance(), completionReceipt.getGetScore(), completionReceipt.getStandardScore()));
        //计算总包标准分值
        completionReceipt.setGeneralContractingStandardScore(this.getGeneralContractingStandardScore(completionReceipt.getGeneralContractingStandardScore(), completionReceipt.getGeneralContractingOrNot(), completionReceipt.getStandardScore()));
        //计算总包得分
        completionReceipt.setOverallContractingScore(this.getOverallContractingScore(completionReceipt.getOverallContractingScore(), completionReceipt.getGeneralContractingOrNot(), completionReceipt.getGetScore()));
        //计算总包得分率
        completionReceipt.setOverallContractingScoreRate(this.getOverallContractingScoreRate(completionReceipt.getOverallContractingScoreRate(), completionReceipt.getGeneralContractingStandardScore(), completionReceipt.getOverallContractingScore()));
        return completionReceipt;
    }

    private String getOverallContractingScoreRate(String overallContractingScoreRate, String generalContractingStandardScoreVal, String overallContractingScoreVal) {
        /*if ( 总包标准分值 == '缺省' ) {  总包得分率= '缺省'} else {  总包得分率 = 总包得分 / 总包标准分值}*/
        if (ObjectUtil.isNotEmpty(generalContractingStandardScoreVal) && generalContractingStandardScoreVal.equals(JhSystemEnum.acceptance.DEFAULT.getLabel())) {
            overallContractingScoreRate = "缺省";
        } else {
            overallContractingScoreRate = "0%";
            if (ObjectUtil.isNotEmpty(generalContractingStandardScoreVal) && ObjectUtil.isNotEmpty(overallContractingScoreVal) && !generalContractingStandardScoreVal.equals("缺省") && !generalContractingStandardScoreVal.equals("0")) {
                overallContractingScoreRate = (Double.parseDouble(overallContractingScoreVal) / Double.parseDouble(generalContractingStandardScoreVal) * 100) + "%";
            } else {
                overallContractingScoreRate = "0%";
            }
        }
        return overallContractingScoreRate;
    }

    private String getOverallContractingScore(String overallContractingScore, String generalContractingOrNotVal, String scoreVal) {
        /*if ( 是否总比 == '是' ) {  总包得分 = 得分} else {  总包得分 = '缺省'}*/
        if (ObjectUtil.isNotEmpty(generalContractingOrNotVal)) {
            if (generalContractingOrNotVal.equals(JhSystemEnum.generalContractingOrNot.YES.getValue())) {
                overallContractingScore = scoreVal;
            } else {
                overallContractingScore = "缺省";
            }
        }
        return overallContractingScore;
    }


    private String getGeneralContractingStandardScore(String generalContractingStandardScore, String generalContractingOrNotVal, String standardScoreVal) {
        /*if ( 是否总包 == '是' ) {  总包标准分值 = 标准分值} else {  总包标准分值 = '缺省'}*/
        if (ObjectUtil.isNotEmpty(generalContractingOrNotVal)) {
            if (generalContractingOrNotVal.equals(JhSystemEnum.generalContractingOrNot.YES.getValue())) {
                generalContractingStandardScore = standardScoreVal;
            } else {
                generalContractingStandardScore = "缺省";
            }
        }
        return generalContractingStandardScore;
    }

    private String getQualificationRate(String qualificationRate, String acceptanceVal, String scoreVal, String standardScoreVal) {
        /*if ( 验收 == '缺省' ) {  合格率 = '缺省'} else {  合格率 = 得分 / 标准分值}*/
        if (ObjectUtil.isNotEmpty(acceptanceVal)) {
            if (acceptanceVal.equals(JhSystemEnum.acceptance.DEFAULT.getValue())) {
                qualificationRate = JhSystemEnum.acceptance.DEFAULT.getLabel();
            } else {
                if (ObjectUtil.isNotEmpty(scoreVal) && ObjectUtil.isNotEmpty(standardScoreVal)) {
                    if (!standardScoreVal.equals("0")) {
                        qualificationRate = (Double.parseDouble(scoreVal) / Double.parseDouble(standardScoreVal) * 100) + "%";
                    } else {
                        qualificationRate = "0%";
                    }
                }
            }
        }
        return qualificationRate;
    }

    private String getScore(String getScore, String classificationVal, String acceptanceVal, String standardScoreVal, String oneVoteVetoUnqualifiedScore, String mandatoryItemsUnqualifiedScore, String importantItemUnqualifiedScore, String genericItemUnqualifiedScore) {
        //根据 classificationVal 分类、acceptanceVal 验收、score 得分、standardScore 标准分值
        if (ObjectUtil.isNotEmpty(classificationVal) && ObjectUtil.isNotEmpty(acceptanceVal)) {
            /*if ( 分类 == '一票否决' && 验收 == '不合格' ) {  得分 = -40} else {  得分 = 0}*/
            if (classificationVal.equals(JhSystemEnum.classification.ONE_VOTE_VETO.getValue()) && acceptanceVal.equals(JhSystemEnum.acceptance.UNQUALIFIED.getValue())) {
                if (oneVoteVetoUnqualifiedScore == null) {
                    getScore = "-40";
                } else {
                    getScore = oneVoteVetoUnqualifiedScore;
                }
            } else if (classificationVal.equals(JhSystemEnum.classification.MANDATORY_ITEMS.getValue()) && acceptanceVal.equals(JhSystemEnum.acceptance.UNQUALIFIED.getValue())) {
                /*if ( 分类 == '必备项' && 验收 =='不合格' ) {  得分 = -5} else {  得分 = 0} */
                if (mandatoryItemsUnqualifiedScore == null) {
                    getScore = "-5";
                } else {
                    getScore = mandatoryItemsUnqualifiedScore;
                }
            } else if (classificationVal.equals(JhSystemEnum.classification.IMPORTANT_ITEMS.getValue())) {
                /*if ( 分类 == '重要项' ) {  if ( 验收 == '合格' ) {    得分 = 标准分值  } else if ( 验收 == '不合格' ) {    得分 = -标准分值  } else if ( 验收 == '缺省' ) {    得分 = 0  }}*/
                if (acceptanceVal.equals(JhSystemEnum.acceptance.QUALIFIED.getValue())) {
                    getScore = standardScoreVal;
                } else if (acceptanceVal.equals(JhSystemEnum.acceptance.UNQUALIFIED.getValue())) {
                    if (ObjectUtil.isNotEmpty(importantItemUnqualifiedScore)) {
                        getScore = importantItemUnqualifiedScore;
                    } else {
                        getScore = "-" + standardScoreVal;
                    }
                } else if (acceptanceVal.equals(JhSystemEnum.acceptance.DEFAULT.getValue())) {
                    getScore = "0";
                }
            } else if (classificationVal.equals(JhSystemEnum.classification.GENERAL_ITEMS.getValue())) {
                /*if ( 分类 == '一般项' ) {  if ( 验收 == '合格' ) {    得分 = 标准分值  } else if ( 验收 == '不合格' ) {    得分 = 0  } else if ( 验收 == '缺省' ) {    得分 = 0  }}*/
                if (acceptanceVal.equals(JhSystemEnum.acceptance.QUALIFIED.getValue())) {
                    getScore = standardScoreVal;
                } else if (acceptanceVal.equals(JhSystemEnum.acceptance.UNQUALIFIED.getValue())) {
                    if (ObjectUtil.isNotEmpty(genericItemUnqualifiedScore)) {
                        getScore = genericItemUnqualifiedScore;
                    } else {
                        getScore = "0";
                    }
                } else if (acceptanceVal.equals(JhSystemEnum.acceptance.DEFAULT.getValue())) {
                    getScore = "0";
                }
            } else {
                getScore = "0";
            }
        }
        return getScore;
    }

    private String standardScore(String standardScore, String acceptanceVal, String scoreVal) {
        //if ( acceptanceVal验收 == '缺省' ) {    标准分值 = 0  } else {  标准分值 = scoreVal期初分值  }
        if (ObjectUtil.isNotEmpty(acceptanceVal)) {
            if (acceptanceVal.equals(JhSystemEnum.acceptance.DEFAULT.getValue())) {
                standardScore = "0";
            } else {
                standardScore = scoreVal;
            }
        }
        return standardScore;
    }


    @Override
    public Map<String, Object> queryAll(ProjectCompletionReceiptQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<ProjectCompletionReceipt> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectCompletionReceipt.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectCompletionReceiptMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectCompletionReceiptDto> queryAll(ProjectCompletionReceiptQueryCriteria criteria) {
        return projectCompletionReceiptMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectCompletionReceipt.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectCompletionReceiptDto findById(Long projectReceiptId) {
        ProjectCompletionReceipt projectCompletionReceipt = Optional.ofNullable(getById(projectReceiptId)).orElseGet(ProjectCompletionReceipt::new);
        ValidationUtil.isNull(projectCompletionReceipt.getProjectReceiptId(), getEntityClass().getSimpleName(), "projectReceiptId", projectReceiptId);
        return projectCompletionReceiptMapper.toDto(projectCompletionReceipt);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectCompletionReceiptDto create(ProjectCompletionReceipt resources) {
        save(resources);
        return findById(resources.getProjectReceiptId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectCompletionReceipt resources) {
        ProjectCompletionReceipt projectCompletionReceipt = Optional.ofNullable(getById(resources.getProjectReceiptId())).orElseGet(ProjectCompletionReceipt::new);
        ValidationUtil.isNull(projectCompletionReceipt.getProjectReceiptId(), "ProjectCompletionReceipt", "id", resources.getProjectReceiptId());
        projectCompletionReceipt.copy(resources);
        updateById(projectCompletionReceipt);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long projectReceiptId : ids) {
            projectCompletionReceiptRepository.deleteById(projectReceiptId);
        }
    }

    @Override
    public void download(List<ProjectCompletionReceiptDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectCompletionReceiptDto projectCompletionReceipt : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("项目ID", projectCompletionReceipt.getProjectId());
            map.put("是否操作（0否1是）", projectCompletionReceipt.getIsOperation());
            map.put("总项(码值completion_total_items)", projectCompletionReceipt.getTotalItem());
            map.put("序号", projectCompletionReceipt.getSerialNumber());
            map.put("分项", projectCompletionReceipt.getSubItem());
            map.put("项目", projectCompletionReceipt.getProject());
            map.put("内容（码值completion_content）", projectCompletionReceipt.getContent());
            map.put("分项", projectCompletionReceipt.getSubItemContent());
            map.put("分值", projectCompletionReceipt.getScore());
            map.put("标准分值", projectCompletionReceipt.getStandardScore());
            map.put("验收（码值acceptance）", projectCompletionReceipt.getAcceptance());
            map.put("得分", projectCompletionReceipt.getGetScore());
            map.put("分类（码值classification）", projectCompletionReceipt.getClassification());
            map.put("合格率", projectCompletionReceipt.getQualificationRate());
            map.put("是否总包（码值general_contracting_or_not）", projectCompletionReceipt.getGeneralContractingOrNot());
            map.put("总包标准分值", projectCompletionReceipt.getGeneralContractingStandardScore());
            map.put("总包得分", projectCompletionReceipt.getOverallContractingScore());
            map.put("总包得分率", projectCompletionReceipt.getOverallContractingScoreRate());
            map.put("现场照片", projectCompletionReceipt.getCheckAttachments());
            map.put("标准照片", projectCompletionReceipt.getStandardPhotos());
            map.put("备注", projectCompletionReceipt.getRemarks());
            map.put("整改说明", projectCompletionReceipt.getRectificationInstructions());
            map.put("整改照片", projectCompletionReceipt.getRectificationAttachments());
            map.put("整改日期", projectCompletionReceipt.getRectificationDate());
            map.put("创建时间", projectCompletionReceipt.getCreateTime());
            map.put("创建人", projectCompletionReceipt.getCreateBy());
            map.put("更新时间", projectCompletionReceipt.getUpdateTime());
            map.put("更新人", projectCompletionReceipt.getUpdateBy());
            map.put("是否可用", projectCompletionReceipt.getIsEnabled());
            map.put("是否删除", projectCompletionReceipt.getIsDelete());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<ProjectCompletionReceipt> listByUnqualified(ProjectCompletionReceiptQueryCriteria criteria) {
        final LambdaQueryWrapper<ProjectCompletionReceipt> queryWrapper = Wrappers.lambdaQuery(ProjectCompletionReceipt.class)
                .eq(ProjectCompletionReceipt::getProjectId, criteria.getProjectId());
        if(StringUtils.isNotEmpty(criteria.getSubItemContent())){
            queryWrapper.like(ProjectCompletionReceipt::getSubItemContent,criteria.getSubItemContent());
        }

        List<ProjectCompletionReceipt> receipts = projectCompletionReceiptRepository.selectList(queryWrapper);
        receipts = receipts.stream()
                .filter(r -> "unqualified".equals(r.getAcceptance()))
                .collect(Collectors.toList());
        receipts.forEach(receipt -> {
            String commitmentLetter = receipt.getCommitmentLetter();
            if (StrUtil.isNotBlank(commitmentLetter)) {
                List<LocalStorage> localStorages = localStorageRepository.findByIds(commitmentLetter.split(","));
                receipt.setCommitmentLetterStorage(localStorages);
            }
        });
        return receipts;
    }

    @Override
    public Map groupApprovalStatus(Long projectId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("project_id", projectId);
        queryWrapper.eq("acceptance", "unqualified");
        queryWrapper.isNotNull("approval_status");
        queryWrapper.groupBy("approval_status");
        queryWrapper.select("approval_status,  count(1) as count");
        List<Map> list = listMaps(queryWrapper);
        Map map = new HashMap();
        for (Map m : list) {
            map.put(m.get("approval_status"), m.get("count"));
        }
        return map;
    }

    @Override
    @Transactional
    public Boolean updateByUnqualified(List<ProjectCompletionReceipt> resources) {
        final List<ProjectCompletionReceipt> collect = resources.stream().filter(r -> "unqualified".equals(r.getAcceptance()))
                .collect(Collectors.toList());
        StringBuilder builder = new StringBuilder();
        for (ProjectCompletionReceipt projectCompletionReceipt : collect) {
            if (StrUtil.isNotBlank(projectCompletionReceipt.getApprovalRemarks())) {
                builder.append(projectCompletionReceipt.getApprovalRemarks());
                builder.append("\n");
            }
        }
        if (builder.length() > 0 && collect.size() > 0) {
            ProjectNodeInfo projectNodeInfo = new ProjectNodeInfo();
            projectNodeInfo.setProjectId(collect.get(0).getProjectId());
            projectNodeInfo.setNodeCode("eng-00135012");
            projectNodeInfo.setRemark(builder.toString());
            projectNodeInfoService.updateRemark(projectNodeInfo);
        }
        return this.saveOrUpdateBatch(collect);
    }

    @Override
    public void updateRemarks(ProjectCompletionReceipt projectCompletionReceipt) {
        ProjectCompletionReceipt receipt = getById(projectCompletionReceipt.getProjectReceiptId());
        receipt.setRemarks(projectCompletionReceipt.getRemarks());
        updateById(receipt);
    }

    @Override
    public void keepRecordsAuditComments(ProjectApprove projectApprove) {
        //审批拒绝后，保存当前审核意见历史 1、获取当前项目所有存在审核意见的整改项
        LambdaQueryWrapper<ProjectCompletionReceipt> wrapper = Wrappers.lambdaQuery(ProjectCompletionReceipt.class)
                .eq(ProjectCompletionReceipt::getProjectId, projectApprove.getProjectId())
                .isNotNull(ProjectCompletionReceipt::getApprovalRemarks);
        List<ProjectCompletionReceipt> completionReceiptList = projectCompletionReceiptRepository.selectList(wrapper);
        for (ProjectCompletionReceipt completionReceipt : completionReceiptList) {
            String approvalCommentRecord = null;
            if (ObjectUtil.isNotEmpty(completionReceipt.getApproverUser())) {
                approvalCommentRecord = completionReceipt.getApproverUser();
            }
            if (ObjectUtil.isNotEmpty(completionReceipt.getApprovalStatus())) {
                //0 不合格、1 合格、2 承诺项
                String approvalStatus="";
                if (completionReceipt.getApprovalStatus().equals("0")) {
                    approvalStatus = "不合格";
                }else  if (completionReceipt.getApprovalStatus().equals("1")){
                    approvalStatus = "合格";
                }else  if (completionReceipt.getApprovalStatus().equals("2")){
                    approvalStatus = "承诺项";
                }
                approvalCommentRecord = ObjectUtil.isNotEmpty(approvalCommentRecord) ?approvalCommentRecord+ "：" + approvalStatus : approvalStatus;
            }
            if (ObjectUtil.isNotEmpty(completionReceipt.getApprovalRemarks())) {
                approvalCommentRecord = ObjectUtil.isNotEmpty(approvalCommentRecord)?approvalCommentRecord+"\\n审核意见："+completionReceipt.getApprovalRemarks():completionReceipt.getApprovalRemarks();
            }
            if (ObjectUtil.isNotEmpty(completionReceipt.getApprovalCommentRecord())) {
                completionReceipt.setApprovalCommentRecord(completionReceipt.getApprovalCommentRecord()+"\\n"+approvalCommentRecord);
            }else{
                completionReceipt.setApprovalCommentRecord(approvalCommentRecord);
            }
        }
        this.saveOrUpdateBatch(completionReceiptList);
    }
}