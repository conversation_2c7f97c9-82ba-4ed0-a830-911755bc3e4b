/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-03-24
**/
@Data
@TableName(value="t_project_info")
public class ProjectInfo implements Serializable {

    @TableId(value = "project_id")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @TableField(value = "project_hlm_id")
    @ApiModelProperty(value = "hlm系统过来的项目ID")
    private Integer projectHlmId;

    @TableField(value = "brand_id")
    @ApiModelProperty(value = "品牌id")
    private Long brandId;

    @TableField(value = "template_id")
    @ApiModelProperty(value = "模板id")
    private Long templateId;

    @TableField(value = "store_id")
    @ApiModelProperty(value = "门店id")
    private Long storeId;


    @TableField(value = "project_no")
    @ApiModelProperty(value = "项目编码")
    private String projectNo;

    @TableField(value = "project_version")
    @ApiModelProperty(value = "项目版本号 ")
    private String projectVersion;

    @TableField(value = "project_name")
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @TableField(value = "business_nature")
    @ApiModelProperty(value = "经营性质")
    private String businessNature;


    @TableField(value = "store_name")
    @ApiModelProperty(value = "门店名称  任务名称")
    private String storeName;

    @TableField(value = "store_no")
    @ApiModelProperty(value = "门店编码")
    private String storeNo;


    @TableField(value = "store_type")
    @ApiModelProperty(value = "门店类型")
    private String storeType;

    @TableField(value = "design_image")
    @ApiModelProperty(value = "设计形象")
    private String designImage;


    @TableField(value = "region")
    @ApiModelProperty(value = "大区value")
    private String region;

    @TableField(value = "city_company")
    @ApiModelProperty(value = "城市公司value")
    private String cityCompany;

    @TableField(value = "province")
    @ApiModelProperty(value = "省份id")
    private Long province;

    @TableField(value = "province_name")
    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @TableField(value = "city")
    @ApiModelProperty(value = "城市id")
    private Long city;

    @TableField(value = "city_name")
    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @TableField(value = "county")
    @ApiModelProperty(value = "区县id")
    private Long county;

    @TableField(value = "county_name")
    @ApiModelProperty(value = "区县名称")
    private String countyName;

    @TableField(value = "project_address")
    @ApiModelProperty(value = "详细地址（不包括省市县）")
    private String projectAddress;

    @TableField(value = "trade_id")
    @ApiModelProperty(value = "商圈id")
    private Long tradeId;

    @TableField(value = "project_plan_start")
    @ApiModelProperty(value = "项目计划开始时间")
    private Date projectPlanStart;

    @TableField(value = "project_plan_end")
    @ApiModelProperty(value = "项目计划结束时间")
    private Date projectPlanEnd;

    @TableField(value = "project_actual_end")
    @ApiModelProperty(value = "项目实际结束时间")
    private Date projectActualEnd;

    @TableField(value = "establish_time")
    @ApiModelProperty(value = "立项时间")
    private Date establishTime;

    @TableField(value = "region_net_manager")
    @ApiModelProperty(value = "区域经理")
    private Long regionNetManager;

    @TableField(value = "construction_manager")
    @ApiModelProperty(value = "工程经理")
    private Long constructionManager;

    @TableField(value = "engineer_director")
    @ApiModelProperty(value = "总部经理")
    private Long engineerDirector;

    @TableField(value = "general_network")
    @ApiModelProperty(value = "总部网发")
    private Long generalNetwork;

    @TableField(value = "regiona_finance_manager")
    @ApiModelProperty(value = "城市公司财务")
    private Long regionaFinanceManager;

    @TableField(value = "plan_open_date")
    @ApiModelProperty(value = "计划开业日期")
    private Date planOpenDate;

    @TableField(value = "plan_approach_date")
    @ApiModelProperty(value = "计划进场日期（预计完工时间）")
    private Date planApproachDate;

    @TableField(value = "project_create_date")
    @ApiModelProperty(value = "项目创建日期")
    private Date projectCreateDate;

    @TableField(value = "actual_open_date")
    @ApiModelProperty(value = "实际开业日期")
    private String actualOpenDate;

    @TableField(value = "is_open")
    @ApiModelProperty(value = "是否开业")
    private Boolean isOpen;

    @TableField(value = "is_create")
    @ApiModelProperty(value = "是否立项")
    private Boolean isCreate;

    @TableField(value = "total_area")
    @ApiModelProperty(value = "总计面积")
    private BigDecimal totalArea;

    @TableField(value = "used_area")
    @ApiModelProperty(value = "租赁合同面积")
    private BigDecimal usedArea;

    @TableField(value = "decorate_area")
    @ApiModelProperty(value = "装修面积")
    private BigDecimal decorateArea;

    @TableField(value = "project_type")
    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @TableField(value = "project_status")
    @ApiModelProperty(value = "项目状态")
    private String projectStatus;


    @TableField(value = "task_phase")
    @ApiModelProperty(value = "当前任务阶段")
    private String taskPhase;


    @TableField(value = "account_phase")
    @ApiModelProperty(value = "结算任务阶段")
    private String accountPhase;

    @TableField(value = "is_overdue")
    @ApiModelProperty(value = "是否逾期")
    private Boolean isOverdue;

    @TableField(value = "account_overdue")
    @ApiModelProperty(value = "结算是否逾期")
    private Boolean accountOverdue;

    @TableField(value = "remark")
    @ApiModelProperty(value = "项目备注")
    private String remark;

    @TableField(value = "is_active")
    @ApiModelProperty(value = "是否生效")
    private Boolean isActive;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "修改时间")
    private Timestamp updateTime;

    @TableField(value = "create_by",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_by",fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_sheet")
    @ApiModelProperty(value = "是否申请指令单")
    private Boolean isSheet;

    @TableField(value = "sheet_id")
    @ApiModelProperty(value = "指令单id")
    private Long sheetId;

    @TableField(value = "decorate_grade")
    @ApiModelProperty(value = "装修等级")
    private String decorateGrade;

    @TableField(exist = false)
    private String nodeCode;

    @TableField(exist = false)
    private String nodeName;

    @TableField(value = "floor")
    @ApiModelProperty(value = "楼层")
    private String floor;

    @TableField(value = "design_position")
    @ApiModelProperty(value = "设计定位")
    private String designPosition;

    @TableField(value = "final_settlement_expenses")
    @ApiModelProperty(value = "决算费用")
    private BigDecimal finalSettlementExpenses;

    @TableField(value = "flag")
    @ApiModelProperty(value = "标识")
    private String flag;

    @TableField(value = "note1")
    @ApiModelProperty(value = "备注1")
    private String note1;

    @TableField(value = "note2")
    @ApiModelProperty(value = "备注2")
    private String note2;

    @TableField(value = "note3")
    @ApiModelProperty(value = "备注3")
    private String note3;

    @TableField(value = "note4")
    @ApiModelProperty(value = "备注4")
    private String note4;

    @TableField(value = "note5")
    @ApiModelProperty(value = "备注5")
    private String note5;

    @TableField(exist = false)
    @ApiModelProperty(value = "中心负责人")
    private List<Long> centerManager;

    @TableField(exist = false)
    @ApiModelProperty(value = "风险管控")
    private List<Long> riskController;

    @TableField(exist = false)
    @ApiModelProperty(value = "设计经理")
    private List<Long> designManager;

    @TableField(exist = false)
    @ApiModelProperty(value = "设计总监")
    private List<Long> designDirector;

    @TableField(exist = false)
    @ApiModelProperty(value = "工程财务")
    private List<Long> constructionFinance;

    @TableField(exist = false)
    @ApiModelProperty(value = "施工厂商")
    private List<Long> firmOwner;

    @TableField(value = "node_table_name")
    @ApiModelProperty(value = "节点表实际名称")
    private String nodeTableName;

    @TableField(value = "operations_theater_leader")
    @ApiModelProperty(value = "运营战区负责人")
    private Long operationsTheaterLeader;

    @TableField(value = "opening_manager")
    @ApiModelProperty(value = "开业经理")
    private Long openingManager;

    @TableField(value = "operations_manager")
    @ApiModelProperty(value = "运营经理")
    private Long operationsManager;

    @TableField(value = "franchise_manager")
    @ApiModelProperty(value = "特许经理")
    private Long franchiseManager;

    @TableField(value = "development_theater_leader")
    @ApiModelProperty(value = "开发战区负责人")
    private Long developmentTheaterLeader;

    @TableField(value = "development_zone_leader")
    @ApiModelProperty(value = "开发分区负责人")
    private Long developmentZoneLeader;

    @TableField(value = "development_manager")
    @ApiModelProperty(value = "开发经理  12/6 改成多个逗号拼接")
    private String developmentManager;

    @TableField(value = "construction_area_leader")
    @ApiModelProperty(value = "营建区域负责人")
    private Long constructionAreaLeader;

    @TableField(value = "project_manager")
    @ApiModelProperty(value = "项目经理")
    private Long projectManager;

    @TableField(value = "designer")
    @ApiModelProperty(value = "设计师")
    private Long designer;

    @TableField(value = "mechanical_and_electrical_engineer")
    @ApiModelProperty(value = "机电工程师")
    private Long mechanicalAndElectricalEngineer;

    @TableField(value = "weak_current_engineer")
    @ApiModelProperty(value = "弱电工程师")
    private Long weakCurrentEngineer;

    @TableField(value = "design_co_management_personnel")
    @ApiModelProperty(value = "设计共管人员")
    private Long designCoManagementPersonnel;

    @TableField(value = "weak_current_acceptance_personnel")
    @ApiModelProperty(value = "弱电验收人员")
    private Long weakCurrentAcceptancePersonnel;

    @TableField(value = "mechanical_and_electrical_acceptance_personnel")
    @ApiModelProperty(value = "机电验收人员")
    private Long mechanicalAndElectricalAcceptancePersonnel;

    @TableField(value = "flight_quality_inspection_personnel")
    @ApiModelProperty(value = "飞行质检人员")
    private Long flightQualityInspectionPersonnel;

    @TableField(value = "completion_acceptance_personnel")
    @ApiModelProperty(value = "竣工验收人员")
    private Long completionAcceptancePersonnel;

    @TableField(value = "delivery_center_shared_support")
    @ApiModelProperty(value = "交付中心-共享支持")
    private Long deliveryCenterSharedSupport;

    @TableField(value = "owner")
    @ApiModelProperty(value = "业主")
    private Long owner;

    @TableField(value = "owner_project_manager")
    @ApiModelProperty(value = "业主项目经理/业主对接人")
    private Long ownerProjectManager;

    @TableField(value = "design_unit")
    @ApiModelProperty(value = "设计单位")
    private Long designUnit;

    @TableField(value = "procurement_marketing")
    @ApiModelProperty(value = "采购营销")
    private Long procurementMarketing;

    @TableField(value = "delivery_center_engineering_leader")
    @ApiModelProperty(value = "交付中心-工程负责人")
    private Long deliveryCenterEngineeringLeader;

    @TableField(value = "delivery_center_technical_leader")
    @ApiModelProperty(value = "交付中心-技术负责人")
    private Long deliveryCenterTechnicalLeader;

    @TableField(value = "delivery_center_department_head")
    @ApiModelProperty(value = "交付中心-部门负责人")
    private Long deliveryCenterDepartmentHead;

    @TableField(value = "decoration_designer")
    @ApiModelProperty(value = "装修设计师")
    private Long decorationDesigner;

    @TableField(value = "mechanical_and_electrical_designer")
    @ApiModelProperty(value = "机电设计师")
    private Long mechanicalAndElectricalDesigner;

    @TableField(value = "soft_decoration_design")
    @ApiModelProperty(value = "软装设计")
    private Long softDecorationDesign;

    @TableField(value = "allocation_status")
    @ApiModelProperty(value = "分配状态")
    private String allocationStatus;

    @TableField(value = "effective_date")
    @ApiModelProperty(value = "生效日期")
    private Date effectiveDate;

    @TableField(value = "planned_start_date")
    @ApiModelProperty(value = "计划开工日期")
    private Date plannedStartDate;

    @TableField(value = "adjusting_the_start_date")
    @ApiModelProperty(value = "调整开工日期")
    private Date adjustingTheStartDate;

    @TableField(value = "actual_commencement_date")
    @ApiModelProperty(value = "实际开工日期")
    private Date actualCommencementDate;

    @TableField(value = "planned_completion_date")
    @ApiModelProperty(value = "计划完工日期")
    private Date plannedCompletionDate;

    @TableField(value = "adjust_completion_date")
    @ApiModelProperty(value = "调整完工日期")
    private Date adjustCompletionDate;

    @TableField(value = "actual_completion_date")
    @ApiModelProperty(value = "实际完工日期")
    private Date actualCompletionDate;

    @TableField(value = "adjusting_the_opening_date")
    @ApiModelProperty(value = "调整开业日期")
    private Date adjustingTheOpeningDate;

    @TableField(value = "start_cycle")
    @ApiModelProperty(value = "启动周期")
    private String startCycle;

    @TableField(value = "construction_period")
    @ApiModelProperty(value = "施工周期")
    private String constructionPeriod;

    @TableField(value = "certification_cycle")
    @ApiModelProperty(value = "办证周期")
    private String certificationCycle;

    @TableField(value = "signing_status")
    @ApiModelProperty(value = "签约状态")
    private String signingStatus;

    @TableField(value = "construction_status")
    @ApiModelProperty(value = "营建状态")
    private String constructionStatus;

    @TableField(value = "opening_status")
    @ApiModelProperty(value = "开业状态")
    private String openingStatus;

    @TableField(value = "product_code")
    @ApiModelProperty(value = "产品code")
    private String productCode;

    @TableField(value = "brand_code")
    @ApiModelProperty(value = "品牌code")
    private String brandCode;

    @TableField(exist = false)
    @ApiModelProperty(value = "产品名称")
    private String productName;

    @TableField(exist = false)
    @ApiModelProperty(value = "品牌")
    private String brandName;


    @TableField(value = "project_task_phase")
    @ApiModelProperty(value = "项目任务阶段code")
    private String projectTaskPhase;

    @TableField(value = "stair_plan_end_date")
    @ApiModelProperty(value = "当前未完成任务的计划完成时间")
    private Timestamp stairPlanEndDate;

    @TableField(value = "supply_chain_business_unit")
    @ApiModelProperty(value = "HBG-供应链事业部VP")
    private String supplyChainBusinessUnit;

    @TableField(value = "legal_manager")
    @ApiModelProperty(value = "法务经理")
    private String legalManager;

    @TableField(value = "shared_support_design_lead")
    @ApiModelProperty(value = "共享支持设计负责人")
    private String sharedSupportDesignLead;

    @TableField(value = "head_accommodation_operations")
    @ApiModelProperty(value = "住宿运营负责人")
    private String headAccommodationOperations;

    @TableField(value = "head_operation_maintenance")
    @ApiModelProperty(value = "运维负责人")
    private String headOperationMaintenance;




    public void copy(ProjectInfo source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}