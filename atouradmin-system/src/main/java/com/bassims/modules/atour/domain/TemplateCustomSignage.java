/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-04-22
**/
@Data
@TableName(value="t_template_custom_signage")
public class TemplateCustomSignage implements Serializable {

    @TableId(value = "custom_signage_id",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "九宫格id")
    private Long customSignageId;

    @TableField(value = "primary_name")
    @ApiModelProperty(value = "一级名称")
    private String primaryName;

    @TableField(value = "second_name")
    @ApiModelProperty(value = "二级名称")
    private String secondName;

    @TableField(value = "custom_signage_sort")
    @ApiModelProperty(value = "九宫格排序")
    private Integer customSignageSort;

    @TableField(value = "jump_address")
    @ApiModelProperty(value = "跳转地址")
    private String jumpAddress;

    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @TableField(value = "project_version")
    @ApiModelProperty(value = "项目版本号")
    private String projectVersion;

    @TableField(value = "project_type")
    @ApiModelProperty(value = "项目类型（码值project_type）")
    private String projectType;

    @TableField(value = "brand")
    @ApiModelProperty(value = "所属品牌")
    private String brand;

    @TableField(value = "store_type")
    @ApiModelProperty(value = "门店类型")
    private String storeType;

    @TableField(value = "product_code")
    @ApiModelProperty(value = "产品code")
    private String productCode;

    @TableField(value = "brand_code")
    @ApiModelProperty(value = "品牌code")
    private String brandCode;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "修改时间")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_edit")
    @ApiModelProperty(value = "是否可以编辑")
    private String isEdit;

    @TableField(value = "pid")
    @ApiModelProperty(value = "上级ID")
    private Long pid;

    @TableField(value = "node_code")
    @ApiModelProperty(value = "关联nodeCode")
    private Long nodeCode;


    @TableField(value = "no_right_role_code")
    @ApiModelProperty(value = "无权查看的角色")
    private String noRightRoleCode;


    public void copy(TemplateCustomSignage source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}