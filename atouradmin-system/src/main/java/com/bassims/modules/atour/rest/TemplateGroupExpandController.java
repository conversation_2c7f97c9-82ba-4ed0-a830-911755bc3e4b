/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.TemplateGroupExpand;
import com.bassims.modules.atour.service.TemplateGroupExpandService;
import com.bassims.modules.atour.service.dto.TemplateGroupExpandDto;
import com.bassims.modules.atour.service.dto.TemplateGroupExpandQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-12-27
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "审图配置表（关于深化、设计审图列表的配置）管理")
@RequestMapping("/api/templateGroupExpand")
public class TemplateGroupExpandController {

    private static final Logger logger = LoggerFactory.getLogger(TemplateGroupExpandController.class);

    private final TemplateGroupExpandService templateGroupExpandService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, TemplateGroupExpandQueryCriteria criteria) throws IOException {
        templateGroupExpandService.download(templateGroupExpandService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<TemplateGroupExpandDto>>}
    */
    @GetMapping("/list")
    @Log("查询审图配置表（关于深化、设计审图列表的配置）")
    @ApiOperation("查询审图配置表（关于深化、设计审图列表的配置）")
    public ResponseEntity<Object> query(TemplateGroupExpandQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(templateGroupExpandService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<TemplateGroupExpandDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询审图配置表（关于深化、设计审图列表的配置）")
    @ApiOperation("查询审图配置表（关于深化、设计审图列表的配置）")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(templateGroupExpandService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增审图配置表（关于深化、设计审图列表的配置）")
    @ApiOperation("新增审图配置表（关于深化、设计审图列表的配置）")
    public ResponseEntity<Object> create(@Validated @RequestBody TemplateGroupExpand resources){
        return new ResponseEntity<>(templateGroupExpandService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改审图配置表（关于深化、设计审图列表的配置）")
    @ApiOperation("修改审图配置表（关于深化、设计审图列表的配置）")
    public ResponseEntity<Object> update(@Validated @RequestBody TemplateGroupExpand resources){
        templateGroupExpandService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除审图配置表（关于深化、设计审图列表的配置）")
    @ApiOperation("删除审图配置表（关于深化、设计审图列表的配置）")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        templateGroupExpandService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}