/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.modules.atour.domain.ProductTemplate;
import com.bassims.modules.atour.service.ProductTemplateService;
import com.bassims.modules.atour.service.dto.ProductTemplateDto;
import com.bassims.modules.atour.service.dto.ProductTemplateQueryCriteria;
import com.bassims.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-11-08
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "产品模板表管理")
@RequestMapping("/api/productTemplate")
public class ProductTemplateController {

    private static final Logger logger = LoggerFactory.getLogger(ProductTemplateController.class);

    private final ProductTemplateService productTemplateService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
//    @MyDataPermission(title = "配置管理")
    public void download(HttpServletResponse response, ProductTemplateQueryCriteria criteria) throws IOException {
        productTemplateService.download(productTemplateService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<ProductTemplateDto>>}
    */
    @GetMapping("/list")
    @Log("查询产品模板表")
    @ApiOperation("查询产品模板表")
//    @MyDataPermission(title = "项目推进,配置管理")
    public ResponseEntity<Object> query(ProductTemplateQueryCriteria criteria, Pageable pageable){
        UserDetails user = SecurityUtils.getCurrentUser();
        return new ResponseEntity<>(productTemplateService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<ProductTemplateDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询产品模板表")
    @ApiOperation("查询产品模板表")
//    @MyDataPermission(title = "配置管理")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(productTemplateService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增产品模板表")
    @ApiOperation("新增产品模板表")
//    @MyDataPermission(title = "配置管理")
    public ResponseEntity<Object> create(@Validated @RequestBody ProductTemplate resources){
        return new ResponseEntity<>(productTemplateService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改产品模板表")
    @ApiOperation("修改产品模板表")
//    @MyDataPermission(title = "配置管理")
    public ResponseEntity<Object> update(@Validated @RequestBody ProductTemplate resources){
        productTemplateService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除产品模板表")
    @ApiOperation("删除产品模板表")
//    @MyDataPermission(title = "配置管理")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        productTemplateService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

//    @GetMapping("/getProductByBrand")
//    @Log("通过品牌获取对应的产品")
//    @ApiOperation("通过品牌获取对应的产品")
//    public ResponseEntity<Object> getProductByBrand(String brandName) {
//        return new ResponseEntity<>(productTemplateService.getProductByBrand(brandName),HttpStatus.OK);
//    }
}