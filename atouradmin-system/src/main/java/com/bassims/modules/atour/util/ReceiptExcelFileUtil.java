package com.bassims.modules.atour.util;


import cn.hutool.core.util.ObjectUtil;
import com.bassims.modules.atour.domain.vo.ReceiptInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import java.io.*;
import java.util.*;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2024/12/23 13:39
 */
@Slf4j
@Component
public class ReceiptExcelFileUtil {
    public static File ReceiptExcelFile(ReceiptInfo receiptInfo,OutputStream outputStream){
        ClassPathResource classPathResource = new ClassPathResource("template/回执单页面.xlsx");

        File result = null;
        Workbook wb = null;
        File newFile = null;
        try {
            result = classPathResource.getFile();
            newFile = new File(new Date().getTime()+"");
            if(newFile.exists()){
                newFile.delete();
            }
            newFile.createNewFile();
            if(result.exists()&&newFile.exists()){
                try(FileInputStream in = new FileInputStream(result);
                    FileOutputStream out = new FileOutputStream(newFile);)
                {
                    byte[] b = new byte[1024];
                    int len;
                    while ((len=in.read(b))>0){
                        out.write(b,0,len);
                    }
                }
            }
            wb= WorkbookFactory.create(newFile);
            Map<String,Map<Integer,Map<Integer,Object>>> valueMap = new HashMap<>();
            if(valueMap.get("回执单")==null){
                Map<Integer,Map<Integer,Object>> rowMap = new HashMap<>();
                valueMap.put("回执单",rowMap);
                setMapValue(rowMap,2,3,receiptInfo.getProjectId());
                setMapValue(rowMap,3,3,receiptInfo.getProjectName());
                setMapValue(rowMap,4,3,receiptInfo.getProjectAddress());
                setMapValue(rowMap,5,3,receiptInfo.getConstUnit());
                setMapValue(rowMap,7,3,receiptInfo.getStoreManager());
                setMapValue(rowMap,7,6,receiptInfo.getAllHouseNum());
                setMapValue(rowMap,7,7,receiptInfo.getNowCheck());
                setMapValue(rowMap,7,8,receiptInfo.getCheckUser());
                setMapValue(rowMap,7,11,receiptInfo.getCheckDate());
                setMapValue(rowMap,9,3,receiptInfo.getDeveloper());
                setMapValue(rowMap,9,6,receiptInfo.getDesigner());
                setMapValue(rowMap,9,8,receiptInfo.getManager());
                setMapValue(rowMap,9,11,receiptInfo.getProjectStartTime());
                setMapValue(rowMap,11,4,receiptInfo.getType1());
                setMapValue(rowMap,11,5,receiptInfo.getType1Remark());
                setMapValue(rowMap,12,4,receiptInfo.getType2());
                setMapValue(rowMap,12,5,receiptInfo.getType2Remark());
                setMapValue(rowMap,13,4,receiptInfo.getType3());
                setMapValue(rowMap,13,5,receiptInfo.getType3Remark());
                setMapValue(rowMap,14,4,receiptInfo.getType4());
                setMapValue(rowMap,14,5,receiptInfo.getType4Remark());
                setMapValue(rowMap,15,4,receiptInfo.getType5());
                setMapValue(rowMap,15,5,receiptInfo.getType5Remark());
                setMapValue(rowMap,11,11,receiptInfo.getType6());
                setMapValue(rowMap,11,12,receiptInfo.getType6Remark());
                setMapValue(rowMap,12,11,receiptInfo.getType7());
                setMapValue(rowMap,12,12,receiptInfo.getType7Remark());
                setMapValue(rowMap,13,11,receiptInfo.getType8());
                setMapValue(rowMap,13,12,receiptInfo.getType8Remark());
                setMapValue(rowMap,14,11,receiptInfo.getType9());
                setMapValue(rowMap,14,12,receiptInfo.getType9Remark());
                setMapValue(rowMap,22,4,receiptInfo.getPropertyManage());
                setMapValue(rowMap,24,4,receiptInfo.getStoreManagerSign());
                setMapValue(rowMap,25,4,receiptInfo.getRepairerSign());
                setMapValue(rowMap,26,4,receiptInfo.getProjectManagerSign());
                setMapValue(rowMap,27,4,receiptInfo.getConstructionSign());
                setMapValue(rowMap,28,4,receiptInfo.getFranchisorSign());
                setMapValue(rowMap,29,4,receiptInfo.getAcceptSign());
                setMapValue(rowMap,31,4,receiptInfo.getNetworkExperience());
                setMapValue(rowMap,32,4,receiptInfo.getSoundExperience());
                setMapValue(rowMap,33,4,receiptInfo.getTasteExperience());
                setMapValue(rowMap,34,4,receiptInfo.getAirconditioningExperience());
                setMapValue(rowMap,35,4,receiptInfo.getHotwaterExperience());
            }
            writeValue(valueMap,wb,receiptInfo.getChcekAcceptInfoList());
            if(outputStream!=null){
                wb.write(outputStream);
                outputStream.flush();
                outputStream.close();
            }

        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            if(wb!=null){try {
                wb.close();
            }catch (Exception e){}}
            try{
                if(newFile.exists()){
                    newFile.delete();
                }
            }catch(Exception e){}
        }
        return result;
    }

    /**
     *
     * @param rowMap
     * @param row
     * @param col
     * @param value
     */
    private static void setMapValue(Map<Integer,Map<Integer,Object>> rowMap,Integer row,Integer col,Object value){
        if(value!=null){
            Map<Integer,Object> colMap= null;
            if(rowMap.get(row)==null){
                colMap = new HashMap<>();
                rowMap.put(row,colMap);
            }else{
                colMap = rowMap.get(row);
            }
            colMap.put(col,value);
        }
    }
    /**
     *  往excel中写值
     * @param valueMap
     */
    public static void writeValue(Map<String,Map<Integer,Map<Integer,Object>>> valueMap, Workbook wb, List<ReceiptInfo.ChcekAcceptInfo> chcekAcceptInfoList){
        for(String sheet:valueMap.keySet()){
            for(Integer row:valueMap.get(sheet).keySet()){
                for(Integer col:valueMap.get(sheet).get(row).keySet()){
                    Cell cell = wb.getSheet(sheet).getRow(row).getCell(col);
                    Map<Integer,Map<Integer,Object>> v1 = valueMap.get(sheet);
                    Map<Integer,Object> v2 = v1.get(row);
                    Object v3 = v2.get(col);
                    try{
//                        Double value = Double.parseDouble(v3.toString());
//                        cell.setCellValue(value);
                        cell.setCellValue(v3.toString());
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
            }
        }
        if(chcekAcceptInfoList!=null&&chcekAcceptInfoList.size()>0){
            Sheet sheet = wb.getSheet("回执单");
            Integer startrow = 37;
            CellStyle rowStyle = sheet.getRow(startrow).getRowStyle();
            CellStyle cellStyle= wb.createCellStyle();
            Font font = wb.createFont();
            font.setFontName("宋体");
            font.setFontHeight((short) 10);
            font.setBold(false);
            font.setFontHeightInPoints((short) 12);
            cellStyle.setFont(font);
            for(int i =0;i<chcekAcceptInfoList.size();i++){
                Row thisRow = sheet.createRow(startrow+1+i);
                thisRow.setRowStyle(rowStyle);
                for(int j=0;j<14;j++){
                    Cell col = thisRow.createCell(j);
                    col.setCellStyle(cellStyle);
                    if(j==1){
                        col.setCellValue(i+1);
                    }
                    if(j==2){
                        col.setCellValue(chcekAcceptInfoList.get(i).getProblemcode());
                    }
                    if(j==3){
                        col.setCellValue(chcekAcceptInfoList.get(i).getImportant());
                    }
                    if(j==4){
                        col.setCellValue(chcekAcceptInfoList.get(i).getProblemText());
                    }
                }
                CellRangeAddress cellAddresses = new CellRangeAddress(startrow+1+i,startrow+1+i,4,13);
                sheet.addMergedRegion(cellAddresses);
            }
        }
        wb.setForceFormulaRecalculation(true);
    }

}
