package com.bassims.modules.atour.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.bassims.constant.bsEnum.KidsSystemEnum;
import com.bassims.modules.atour.domain.ProjectAreaDetail;
import com.bassims.modules.atour.domain.ProjectInfo;
import com.bassims.modules.atour.repository.ProjectAreaDetailRepository;
import com.bassims.modules.atour.service.CodeValueConversionService;
import com.bassims.modules.atour.service.ProjectAreaDetailService;
import com.bassims.modules.atour.service.dto.ProjectAreaDetailDto;
import com.bassims.modules.atour.service.dto.ProjectAreaDetailQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.ProjectAreaDetailMapper;
import com.bassims.modules.system.domain.DictDetail;
import com.bassims.modules.system.repository.DictDetailRepository;
import com.bassims.modules.system.repository.UserRepository;
import com.bassims.utils.PageUtil;
import com.bassims.utils.QueryHelpPlus;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import com.bassims.base.BaseServiceImpl;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;

@Service
public class ProjectAreaDetailServiceImpl extends BaseServiceImpl<ProjectAreaDetailRepository, ProjectAreaDetail> implements ProjectAreaDetailService {

    @Autowired
    private ProjectAreaDetailMapper projectAreaDetailMapper;

    @Autowired
    private DictDetailRepository dictDetailRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CodeValueConversionService codeValueConversionService;

    @Override
    public void download(HttpServletResponse response, ProjectAreaDetailQueryCriteria criteria) throws IOException {

        String fileName = "面积明细表_" + System.currentTimeMillis() + ".xlsx";

        OutputStream outputStream = response.getOutputStream();
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment; filename=" + fileName);
        response.setContentType("application/msexcel;charset=UTF-8");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);

        String sheetName = "面积明细";
        List<ProjectAreaDetailDto> projectAreaDetailDtoList = projectAreaDetailMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectAreaDetail.class,criteria)));
        for (ProjectAreaDetailDto projectAreaDetailDto : projectAreaDetailDtoList){
            DictDetail label = Optional.ofNullable(dictDetailRepository.findDictDetailByValue(projectAreaDetailDto.getDesignPosition())).orElseGet(DictDetail::new);
            projectAreaDetailDto.setDesignPosition(label.getLabel());
            String projectType = projectAreaDetailDto.getProjectType();
            if (KidsSystemEnum.ProcessType.NEW.getValue().equals(projectType)){
                projectAreaDetailDto.setProjectType(KidsSystemEnum.ProcessType.NEW.getLabel());
            }
            if (KidsSystemEnum.ProcessType.MAJOR.getValue().equals(projectType)){
                projectAreaDetailDto.setProjectType(KidsSystemEnum.ProcessType.MAJOR.getLabel());
            }
            if (KidsSystemEnum.ProcessType.MINOR.getValue().equals(projectType)){
                projectAreaDetailDto.setProjectType(KidsSystemEnum.ProcessType.MINOR.getLabel());
            }
            if (KidsSystemEnum.ProcessType.CLOSE.getValue().equals(projectType)){
                projectAreaDetailDto.setProjectType(KidsSystemEnum.ProcessType.CLOSE.getLabel());
            }
            if (KidsSystemEnum.ProcessType.REFORM.getValue().equals(projectType)){
                projectAreaDetailDto.setProjectType(KidsSystemEnum.ProcessType.REFORM.getLabel());
            }
            if (KidsSystemEnum.ProcessType.Adjust.getValue().equals(projectType)){
                projectAreaDetailDto.setProjectType(KidsSystemEnum.ProcessType.Adjust.getLabel());
            }
            String cityCompany = codeValueConversionService.getCityCompany(projectAreaDetailDto.getCityCompany());
            projectAreaDetailDto.setCityCompany(cityCompany);
        }
        int index = 1;
        EasyExcel.write(outputStream,ProjectAreaDetailDto.class).sheet(index,sheetName).doWrite(projectAreaDetailDtoList);
    }

    @Override
    public Map<String,Object> queryAll(ProjectAreaDetailQueryCriteria criteria, Pageable pageable) {
        List<ProjectAreaDetail> list = list(QueryHelpPlus.getPredicate(ProjectAreaDetail.class, criteria));
        PageInfo<ProjectInfo> page = new PageInfo<>();
        if (list.size() > 0){
            List list1 = PageUtil.toPage(pageable.getPageNumber() == 0 ? 1 : (pageable.getPageNumber() - 1), pageable.getPageSize(), list);
            page = new PageInfo<ProjectInfo>(list1);
        }
        for (ProjectAreaDetail projectAreaDetail : list){
            DictDetail label = Optional.ofNullable(dictDetailRepository.findDictDetailByValue(projectAreaDetail.getDesignPosition())).orElseGet(DictDetail::new);
            projectAreaDetail.setDesignPosition(label.getLabel());
            String projectType = projectAreaDetail.getProjectType();
            if (ObjectUtil.isNotNull(projectType)) {
                projectAreaDetail.setProjectType(projectType);
            }
        }
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content",page.getList());
        map.put("totalElements",list.size());
        return map;
    }
}
