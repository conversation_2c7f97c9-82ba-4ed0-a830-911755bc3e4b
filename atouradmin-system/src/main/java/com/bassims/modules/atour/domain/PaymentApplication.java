/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-12-21
**/
@Data
@TableName(value="s_payment_application")
public class PaymentApplication implements Serializable {

    @TableId(value = "payment_application_id")
    @ApiModelProperty(value = "主键id")
    private Long paymentApplicationId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "订单id")
    private Long projectId;

    @TableField(value = "contract_type")
    @ApiModelProperty(value = "订单类型")
    private String contractType;

    @TableField(value = "apply_sn")
    @ApiModelProperty(value = "申请单号")
    private String applySn;

    @TableField(value = "create_dept")
    @ApiModelProperty(value = "创建人部门")
    private String createDept;

    @TableField(value = "expense_dept")
    @ApiModelProperty(value = "费用核算部门")
    private String expenseDept;

    @TableField(value = "expense_dept_code")
    @ApiModelProperty(value = "费用核算部门code")
    private String expenseDeptCode;

    @TableField(value = "invoice_header")
    @ApiModelProperty(value = "发票抬头")
    private String invoiceHeader;

    @TableField(value = "step")
    @ApiModelProperty(value = "流程状态")
    private String step;

    @TableField(value = "expense_period")
    @ApiModelProperty(value = "费用归属期间")
    private String expensePeriod;

    @TableField(value = "settle_method")
    @ApiModelProperty(value = "结算付款方式")
    private String settleMethod;

    @TableField(value = "supplier_name")
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @TableField(value = "supplier_code")
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @TableField(value = "bank_name_cn")
    @ApiModelProperty(value = "供应商开户行名称")
    private String bankNameCn;

    @TableField(value = "bank_account_number")
    @ApiModelProperty(value = "供应商开户行账号")
    private String bankAccountNumber;

    @TableField(value = "contract_fee")
    @ApiModelProperty(value = "合同金额")
    private BigDecimal contractFee;

    @TableField(value = "contract_to_tax_fee")
    @ApiModelProperty(value = "合同金额（去税）")
    private BigDecimal contractToTaxFee;

    @TableField(value = "reimburse_total")
    @ApiModelProperty(value = "报销总金额")
    private BigDecimal reimburseTotal;

    @TableField(value = "rest_type")
    @ApiModelProperty(value = "尾款类型")
    private String restType;

    @TableField(value = "rest")
    @ApiModelProperty(value = "尾款金额")
    private BigDecimal rest;

    @TableField(value = "actual_amount")
    @ApiModelProperty(value = "实际支付金额")
    private BigDecimal actualAmount;

    @TableField(value = "reason")
    @ApiModelProperty(value = "报销事由")
    private String reason;

    @TableField(value = "koa_apply_sn")
    @ApiModelProperty(value = "KOA申请单号")
    private String koaApplySn;

    @TableField(value = "pay_time")
    @ApiModelProperty(value = "付款时间")
    private String payTime;

    @TableField(value = "file_path")
    @ApiModelProperty(value = "附件")
    private String filePath;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "create_by",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_by",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableLogic
    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    public void copy(PaymentApplication source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}