/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.repository.ProjectTemplateRepository;
import com.bassims.modules.atour.repository.TemplateTableGroupRepository;
import com.bassims.modules.atour.repository.TemplateTableRelevanceRepository;
import com.bassims.modules.atour.repository.TemplateTableRepository;
import com.bassims.modules.atour.service.TableConfiguringTertiaryNodesService;
import com.bassims.modules.atour.service.TemplateTableGroupService;
import com.bassims.modules.atour.service.TemplateTableRelevanceExpansionService;
import com.bassims.modules.atour.service.TemplateTableRelevanceService;
import com.bassims.modules.atour.service.dto.TableConfiguringTertiaryNodesVO;
import com.bassims.modules.atour.service.dto.TemplateTableDto;
import com.bassims.modules.atour.service.dto.TemplateTableGroupDto;
import com.bassims.modules.atour.service.dto.TemplateTableGroupQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.TemplateTableGroupMapper;
import com.bassims.modules.atour.util.CodeUtil;
import com.bassims.modules.atour.util.JSONSerializer;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2023-10-15
 **/
@Service
public class TemplateTableGroupServiceImpl extends BaseServiceImpl<TemplateTableGroupRepository, TemplateTableGroup> implements TemplateTableGroupService {

    private static final Logger logger = LoggerFactory.getLogger(TemplateTableGroupServiceImpl.class);

    @Autowired
    private TemplateTableGroupRepository templateTableGroupRepository;
    @Autowired
    private TemplateTableGroupMapper templateTableGroupMapper;

    @Autowired
    private TemplateTableRepository templateTableRepository;

    @Autowired
    private TableConfiguringTertiaryNodesService tableConfiguringTertiaryNodesService;

    @Autowired
    private TemplateTableRelevanceService templateTableRelevanceService;
    @Autowired
    private TemplateTableRelevanceRepository templateTableRelevanceRepository;
    @Autowired
    private TemplateTableRelevanceExpansionService templateTableRelevanceExpansionService;
    @Autowired
    private ProjectTemplateRepository projectTemplateRepository;

    @Override
    public List<TemplateTableDto> getNodeList(TemplateTableGroupQueryCriteria criteria) {
        List<TemplateTableDto> dtos = new ArrayList<>();
        //根据条件查询到对应的模版信息
        LambdaQueryWrapper<TemplateTableGroup> queryWrapper = Wrappers.lambdaQuery(TemplateTableGroup.class)
                .eq(TemplateTableGroup::getProjectType, criteria.getProjectType())
                .eq(TemplateTableGroup::getBrand, criteria.getBrand())
                .eq(TemplateTableGroup::getStoreType, criteria.getStoreType())
                .eq(TemplateTableGroup::getRelevancyNodeCode, criteria.getRelevancyNodeCode()).last("limit 1");

        TemplateTableGroup projectInfos = templateTableGroupRepository.selectOne(queryWrapper);
        //查询表头数据
        String startSignList = tableConfiguringTertiaryNodesService.getNodeByStartSign(projectInfos.getDynamicTableType());
        if (ObjectUtils.isNotEmpty(startSignList)) {
            String serialize = JSONSerializer.serialize(startSignList);
            dtos.add(new TemplateTableDto(serialize));
        }
        //查到对应的模版后，去查询模版的列表数据
        List<TemplateTableDto> listByGroup = templateTableRepository.getListByGroup(projectInfos.getTemplateTableGroupId());
        dtos.addAll(listByGroup);
        return dtos;
    }

    @Override
    public Map<String, List<TemplateTableDto>> getNodeLists(TemplateTableGroupQueryCriteria criteria) {
        Map<String, List<TemplateTableDto>> listMap = new HashMap<>();
        //根据条件查询到对应的模版信息
        LambdaQueryWrapper<TemplateTableGroup> queryWrapper = Wrappers.lambdaQuery(TemplateTableGroup.class)
                .eq(TemplateTableGroup::getProjectType, criteria.getProjectType())
                .eq(TemplateTableGroup::getBrand, criteria.getBrand())
                .eq(TemplateTableGroup::getStoreType, criteria.getStoreType())
                .eq(TemplateTableGroup::getRelevancyNodeCode, criteria.getRelevancyNodeCode()).last("limit 1");

        TemplateTableGroup projectInfos = templateTableGroupRepository.selectOne(queryWrapper);
        //查询表头数据
        List<TableConfiguringTertiaryNodesVO> startSignList = tableConfiguringTertiaryNodesService.getNodeByStartSignList(projectInfos.getDynamicTableType());

//        Map<String, List<TableConfiguringTertiaryNodesVO>> collect = startSignList.stream().collect(Collectors.groupingBy(a -> a.getTableType()));
//        for (String tableType : collect.keySet()) {
//            List<TemplateTableDto> dtos = new ArrayList<>();
//            String serialize = JSONSerializer.serialize(collect.get(tableType));
//            dtos.add(new TemplateTableDto(serialize));
//            //查到对应的模版后，去查询模版的列表数据
//            List<TemplateTableDto> listByGroup = templateTableRepository.getListByGroup(projectInfos.getTemplateTableGroupId());
//            dtos.addAll(listByGroup);
//            listMap.put(tableType, dtos);
//        }
        return listMap;
    }

    @Override
    public Map<String, Object> queryAll(TemplateTableGroupQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<TemplateTableGroup> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(TemplateTableGroup.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", templateTableGroupMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<TemplateTableGroupDto> queryAll(TemplateTableGroupQueryCriteria criteria) {
        return templateTableGroupMapper.toDto(list(QueryHelpPlus.getPredicate(TemplateTableGroup.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateTableGroupDto findById(Long templateTableGroupId) {
        TemplateTableGroupDto byId = templateTableGroupRepository.getByIdNodeList(templateTableGroupId);
        return byId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateTableGroupDto saveOrup(TemplateTableGroup resources) {
        LambdaQueryWrapper<ProjectTemplate> queryWrapper = Wrappers.lambdaQuery(ProjectTemplate.class)
                .eq(ProjectTemplate::getNodeCode, resources.getRelevancyNodeCode())
                .eq(ProjectTemplate::getNodeType, JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST);
        ProjectTemplate projectTemplate = projectTemplateRepository.selectOne(queryWrapper);
        if (ObjectUtils.isEmpty(projectTemplate)) {
            throw new BadRequestException("请确认当前三级节点是否是动态表格类型");
        }

        if (ObjectUtil.isEmpty(resources.getTemplateTableGroupId())) {
            Snowflake snowflake = IdUtil.getSnowflake(1, 1);
            resources.setTemplateTableGroupId(snowflake.nextId());
            LambdaQueryWrapper<TemplateTableGroup> lambdaQuery = Wrappers.lambdaQuery(TemplateTableGroup.class);
            lambdaQuery.like(TemplateTableGroup::getCreateTime, new DateTime().toString("yyyy-MM-dd"));
            long count = this.count(lambdaQuery);
            resources.setGroupCode(CodeUtil.idGenerate1("template_", count));
            save(resources);
        } else {
            update(resources);
        }

        LambdaQueryWrapper<TemplateTableRelevance> wrapper = Wrappers.lambdaQuery(TemplateTableRelevance.class);
        wrapper.eq(TemplateTableRelevance::getTemplateTableGroupId, resources.getTemplateTableGroupId());
        templateTableRelevanceRepository.delete(wrapper);
        List<TemplateTableRelevance> tableRelevances = new ArrayList<>();
        //把列表仓库数据存到关联表中
        for (TemplateTable templateTable : resources.getTemplateTableList()) {
            TemplateTableRelevance templateTableRelevance = new TemplateTableRelevance();
            templateTableRelevance.setTemplateTableGroupId(resources.getTemplateTableGroupId());
            templateTableRelevance.setTableId(templateTable.getTemplateTableId());
            tableRelevances.add(templateTableRelevance);

            templateTableRelevanceService.save(templateTableRelevance);
            if (ObjectUtil.isNotEmpty(templateTable.getIsMustMined())) {
                TemplateTableRelevanceExpansion expansion = new TemplateTableRelevanceExpansion();
                expansion.setRelevanceId(templateTableRelevance.getRelevanceId());
                expansion.setIsMustMined(templateTable.getIsMustMined());
                expansion.setConstructionType(templateTable.getConstructionType());
                templateTableRelevanceExpansionService.save(expansion);
            }
        }
        templateTableRelevanceService.saveOrUpdateBatch(tableRelevances);
        return findById(resources.getTemplateTableGroupId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TemplateTableGroup resources) {
        TemplateTableGroup templateTableGroup = Optional.ofNullable(getById(resources.getTemplateTableGroupId())).orElseGet(TemplateTableGroup::new);
        ValidationUtil.isNull(templateTableGroup.getTemplateTableGroupId(), "TemplateTableGroup", "id", resources.getTemplateTableGroupId());
        templateTableGroup.copy(resources);
        updateById(templateTableGroup);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long templateTableGroupId : ids) {
            templateTableGroupRepository.deleteById(templateTableGroupId);
        }
    }

    @Override
    public void download(List<TemplateTableGroupDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TemplateTableGroupDto templateTableGroup : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("分组编码", templateTableGroup.getGroupCode());
            map.put("系统表格id", templateTableGroup.getTemplateTableId());
            map.put("系统表格code", templateTableGroup.getNodeCode());
            map.put("关联三级节点的编码", templateTableGroup.getRelevancyNodeCode());
            map.put("关联三级节点的名称", templateTableGroup.getRelevancyNodeName());
            map.put(" isDelete", templateTableGroup.getIsDelete());
            map.put("项目版本号", templateTableGroup.getProjectVersion());
            map.put("创建时间", templateTableGroup.getCreateTime());
            map.put(" createBy", templateTableGroup.getCreateBy());
            map.put("修改时间", templateTableGroup.getUpdateTime());
            map.put("修改人", templateTableGroup.getUpdateBy());
            map.put("是否可用", templateTableGroup.getIsEnabled());
            map.put("是否可以编辑", templateTableGroup.getIsEdit());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<TemplateTableGroup> getVersion(TemplateTableGroupQueryCriteria criteria) {
        final LambdaQueryWrapper<TemplateTableGroup> wrapper = Wrappers.lambdaQuery(TemplateTableGroup.class)
                .eq(TemplateTableGroup::getDynamicTableType, "weak_current_acceptance_score");
        final List<TemplateTableGroup> templateTableGroups = templateTableGroupRepository.selectList(wrapper);
        final List<TemplateTableGroup> list = new ArrayList<>();
        templateTableGroups.stream().forEach(e->{
            final TemplateTableGroup templateTableGroup = new TemplateTableGroup();
            templateTableGroup.setTemplateTableGroupId(e.getTemplateTableGroupId());
            templateTableGroup.setGroupName(e.getGroupName());
            list.add(templateTableGroup);
        });
        return list;
    }

    @Override
    public List<TemplateTableGroup> getCompleteVersion(TemplateTableGroupQueryCriteria criteria) {
        final LambdaQueryWrapper<TemplateTableGroup> wrapper = Wrappers.lambdaQuery(TemplateTableGroup.class)
                .eq(TemplateTableGroup::getDynamicTableType, "completion_acceptance_score");
        final List<TemplateTableGroup> templateTableGroups = templateTableGroupRepository.selectList(wrapper);
        final List<TemplateTableGroup> list = new ArrayList<>();
        templateTableGroups.stream().forEach(e->{
            final TemplateTableGroup templateTableGroup = new TemplateTableGroup();
            templateTableGroup.setTemplateTableGroupId(e.getTemplateTableGroupId());
            templateTableGroup.setGroupName(e.getGroupName());
            list.add(templateTableGroup);
        });
        return list;
    }
}