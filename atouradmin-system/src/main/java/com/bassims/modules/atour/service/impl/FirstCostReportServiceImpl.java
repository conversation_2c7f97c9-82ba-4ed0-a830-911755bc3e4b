package com.bassims.modules.atour.service.impl;

import com.alibaba.excel.EasyExcel;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.domain.FirstCostReport;
import com.bassims.modules.atour.repository.FirstCostReportRepository;
import com.bassims.modules.atour.service.CodeValueConversionService;
import com.bassims.modules.atour.service.FirstCostReportService;
import com.bassims.modules.atour.service.dto.FirstCostReportDto;
import com.bassims.modules.atour.service.dto.FirstCostReportQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.FirstCostReportMapper;
import com.bassims.utils.PageUtil;
import com.bassims.utils.QueryHelpPlus;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service
public class FirstCostReportServiceImpl extends BaseServiceImpl<FirstCostReportRepository, FirstCostReport> implements FirstCostReportService {

    @Autowired
    private FirstCostReportMapper firstCostReportMapper;

    @Autowired
    private CodeValueConversionService codeValueConversionService;

    @Override
    public void download(HttpServletResponse response, FirstCostReportQueryCriteria criteria) throws IOException {
        String fileName = "甲供材订单报表_" + System.currentTimeMillis() + ".xlsx";

        OutputStream outputStream = response.getOutputStream();
        String fileNameURL = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileNameURL + ";" + "filename*=utf-8''" + fileNameURL);
        response.setContentType("application/msexcel;charset=UTF-8");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);

        String sheetName = "甲供材订单报表";
        List<FirstCostReportDto> data = firstCostReportMapper.toDto(list(QueryHelpPlus.getPredicate(FirstCostReport.class,criteria)));
        int index = 1;
        for (FirstCostReportDto datum : data) {
            String cityCompany = codeValueConversionService.getCityCompany(datum.getCityCompany());
            datum.setCityCompany(cityCompany);
            String projectType = codeValueConversionService.getProjectType(datum.getProjectType());
            datum.setProjectType(projectType);
            String costType = codeValueConversionService.getCostType(datum.getCostType());
            datum.setCostType(costType);
            String designPosition = codeValueConversionService.getDesignPosition(datum.getDesignPosition());
            datum.setDesignPosition(designPosition);

            datum.setIndexNo(index++);
        }
        EasyExcel.write(outputStream, FirstCostReportDto.class).sheet(sheetName).doWrite(data);
    }

    @Override
    public Object queryTime(FirstCostReportQueryCriteria criteria, Pageable pageable) {
        List<FirstCostReport> finalList=list(QueryHelpPlus.getPredicate(FirstCostReport.class,criteria));
        PageInfo<FirstCostReport> page = new PageInfo<>();
        if(finalList.size()>0){
            List list1 = PageUtil.toPage(pageable.getPageNumber()==0?1:(pageable.getPageNumber()-1), pageable.getPageSize(), finalList);
            page = new PageInfo<FirstCostReport>(list1);
        }
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", page.getList());
        map.put("totalElements", finalList.size());
        return map;
    }
}
