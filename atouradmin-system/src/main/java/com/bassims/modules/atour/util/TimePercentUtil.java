package com.bassims.modules.atour.util;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/10/31 16:10
 */
public class TimePercentUtil {

    public TimePercentUtil() {
    }

    /**
     * 计算当前时间占一个时间区间内的比例
     *
     * @param nowTime
     * @param startTime
     * @param endTime
     * @return
     */
    public static   long getTimePercent(java.util.Date nowTime, java.util.Date startTime, java.util.Date endTime) {
        long percent;
        if (nowTime.getTime() <= startTime.getTime()) {
            percent = 0;
        } else if (nowTime.getTime() >= endTime.getTime()) {
            percent = 100;
        } else {
            //结束时间和开始时间中间的天数
            Double a = (endTime.getTime() - startTime.getTime()) / (double) (1000 * 3600 * 24);
            //当前时间和开始时间中间的天数
            Double b = (nowTime.getTime() - startTime.getTime()) / (double) (1000 * 3600 * 24);
            //计算百分比存入
            percent = Math.round(b / a * 100);
        }
        return percent;
    }
//
//    public static void main(String[] args) throws Exception {
//        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd hh:mm");
//        System.out.println("今天的时间已经过去了" +  getTimePercent(sf.parse("2023-10-16 00:00"), sf.parse("2023-10-1 00:00"), sf.parse("2023-10-31 23:59")) + "%");
//    }
}


