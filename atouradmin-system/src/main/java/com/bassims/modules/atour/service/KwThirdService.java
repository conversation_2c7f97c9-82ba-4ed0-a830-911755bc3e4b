package com.bassims.modules.atour.service;

import com.bassims.modules.atour.domain.ProjectGroup;
import com.bassims.modules.atour.requestParam.KwWorkFlowContractRequest;

/**
 * shi
 */
public interface KwThirdService {
    Boolean sendKoaRequest(KwWorkFlowContractRequest kwWorkFlowContractRequest,Boolean isSendAgain);

    KwWorkFlowContractRequest getKoaContract(ProjectGroup projectGroup);

    Boolean submitKoaContractProcess(ProjectGroup projectGroup,Boolean isSendAgain);
}
