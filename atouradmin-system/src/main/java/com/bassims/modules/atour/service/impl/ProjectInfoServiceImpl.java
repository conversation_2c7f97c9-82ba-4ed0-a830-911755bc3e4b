/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.bsEnum.AtourSystemEnum;
import com.bassims.constant.bsEnum.KidsSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum.cateKey;
import com.bassims.constant.jhEnum.JhSystemEnum.tertiaryKey;
import com.bassims.domain.LocalStorage;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.domain.vo.*;
import com.bassims.modules.atour.repository.*;
import com.bassims.modules.atour.service.*;
import com.bassims.modules.atour.service.dto.*;
import com.bassims.modules.atour.service.mapstruct.*;
import com.bassims.modules.atour.util.MyMathUtil;
import com.bassims.modules.atour.util.NoteInfoMappingUtil;
import com.bassims.modules.system.domain.*;
import com.bassims.modules.system.repository.AreaRepository;
import com.bassims.modules.system.repository.DictDetailRepository;
import com.bassims.modules.system.repository.RoleRepository;
import com.bassims.modules.system.repository.UserRepository;
import com.bassims.modules.system.service.AreaService;
import com.bassims.modules.system.service.TemplateConfigService;
import com.bassims.repository.LocalStorageRepository;
import com.bassims.service.LocalStorageService;
import com.bassims.service.dto.LocalStorageDto;
import com.bassims.service.mapstruct.LocalStorageMapper;
import com.bassims.utils.*;
import com.bassims.utils.excelutil.ExcelUtils;
import com.bassims.utils.excelutil.ValuePosition;
import com.bassims.utils.wordutil.WordUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-03-24
 **/
@Slf4j
@Service
public class ProjectInfoServiceImpl extends BaseServiceImpl<ProjectInfoRepository, ProjectInfo> implements ProjectInfoService, Runnable {

    private static final Logger logger = LoggerFactory.getLogger(ProjectInfoServiceImpl.class);

    @Autowired
    private ProjectInfoRepository projectInfoRepository;
    @Autowired
    private ProjectInfoMapper projectInfoMapper;
    @Autowired
    private StoreTemplateRelationRepository storeTemplateRelationRepository;
    @Autowired
    private ProjectTemplateRepository projectTemplateRepository;
    @Autowired
    private ProjectNodeInfoRepository projectNodeInfoRepository;
    @Autowired
    private ProjectStakeholdersRepository projectStakeholdersRepository;
    @Autowired
    private TProjectPlaneventRelationRepository projectPlaneventRelationRepository;
    @Autowired
    private TPlanEventInfoRepository planEventInfoRepository;

    @Autowired
    private ProjectStakeholdersService projectStakeholdersService;
    @Autowired
    private AreaRepository areaRepository;
    @Autowired
    private ProjectNodeInfoService projectNodeInfoService;
    @Autowired
    private LocalStorageService localStorageService;
    @Autowired
    private LocalStorageMapper localStorageMapper;
    @Autowired
    private LocalStorageRepository localStorageRepository;
    @Autowired
    private DictDetailRepository dictDetailRepository;
    @Autowired
    private ProjectTaskService projectTaskService;
    @Autowired
    private RoleUserService roleUserService;
    @Autowired
    private CheckStakeholdersService checkStakeholdersService;
    @Autowired
    private ProjectTemplateApproveRelationService projectTemplateApproveRelationService;
    @Autowired
    private ProjectAppTemplateService projectAppTemplateService;
    @Autowired
    private ProjectTaskInfoService projectTaskInfoService;
    @Autowired
    private ProjectApproveDetailService projectApproveDetailService;

    @Autowired
    private TemplateGroupRepository templateGroupRepository;
    @Autowired
    private TemplateQueueRepository templateQueueRepository;
    @Autowired
    private ProjectGroupService projectGroupService;
    @Autowired
    private ProjectApproveRepository projectApproveRepository;
    @Autowired
    private WordUtils wordUtils;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private MasterDrawingInfoRepository masterDrawingInfoRepository;
    @Autowired
    private MasterDrawingInfoMapper masterDrawingInfoMapper;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StoreMasterInfoService storeMasterInfoService;
    @Autowired
    private MasterPersonInfoRepository masterPersonInfoRepository;
    @Autowired
    private MasterContractInfoRepository masterContractInfoRepository;
    @Autowired
    private MasterDeviceInfoRepository masterDeviceInfoRepository;
    @Autowired
    private MasterEnergyConsumeRepository masterEnergyConsumeRepository;
    @Autowired
    private MasterAreaInfoRepository masterAreaInfoRepository;
    @Autowired
    private ProjectToMasterService projectToMasterService;
    @Autowired
    private StoreMasterInfoRepository storeMasterInfoRepository;
    @Autowired
    private TemplateConfigService templateConfigService;
    @Autowired
    private ExcelUtils excelUtils;
    @Autowired
    private ProjectGroupRepository projectGroupRepository;
    @Autowired
    private NoteInfoMappingUtil util;
    @Autowired
    private ProjectInfoService projectInfoService;
    @Autowired
    private RoleUserRepository roleUserRepository;
    @Autowired
    private TemplateConditionRepository templateConditionRepository;
    @Autowired
    private TemplateConditionRelationRepository templateCollectionRelationRepository;
    @Autowired
    private ConditionsRelationRepository conditionsRelationRepository;
    @Autowired
    private TemplateTableGroupRepository templateTableGroupRepository;
    @Autowired
    private TemplateTableRepository templateTableRepository;
    @Autowired
    private ProjectJointTaskOnfigurationService projectJointTaskOnfigurationService;
    @Autowired
    private TemplateGroupMaterielRepository templateGroupMaterielRepository;
    @Autowired
    private ProjectMtoBaseMaterielRepository projectMtoBaseMaterielRepository;
    @Autowired
    private MtoBaseMaterielRepository mtoBaseMaterielRepository;
    @Autowired
    private ProjectMtoBaseMaterielService projectMtoBaseMaterielService;
    @Autowired
    private TableConfiguringTertiaryNodesServiceImpl tableConfiguringTertiaryNodesService;
    @Autowired
    private ProjectInfoExpansionRepository projectInfoExpansionRepository;
    @Autowired
    private ProjectInfoExpansionService projectInfoExpansionService;
    @Autowired
    private OwnerUserService ownerUserService;
    @Autowired
    private OwnerService ownerService;
    @Autowired
    private BrandTemplateRepository brandTemplateRepository;
    @Autowired
    private ProductTemplateRepository productTemplateRepository;

    @Autowired
    private TemplateTableRelationService templateTableRelationService;
    @Autowired
    private ProjectTableNodeInfoService projectTableNodeInfoService;
    @Autowired
    private ProjectTableNodeInfoRepository projectTableNodeInfoRepository;
    @Autowired
    private HlmProjectInfoOwnerRepository hlmProjectInfoOwnerRepository;
    @Autowired
    private HlmProjectInfoStartWillResultRepository hlmProjectInfoStartWillResultRepository;
    @Autowired
    private HlmProjectInfoOssFileRepository hlmProjectInfoOssFileRepository;
    @Autowired
    private HlmProjectInfoDevelopeManagerRepository hlmProjectInfoDevelopeManagerRepository;
    @Autowired
    private HlmProjectInfoConstructAreaManagerRepository hlmProjectInfoConstructAreaManagerRepository;
    @Autowired
    private ProjectInfoAbarbeitungService projectInfoAbarbeitungService;
    @Autowired
    private ProjectInfoAbarbeitungRepository projectInfoAbarbeitungRepository;
    @Autowired
    private ProjectNodeInfoApprovalRejectionRepository projectNodeInfoApprovalRejectionRepository;
    @Autowired
    private ProjectApproveService projectApproveService;
    @Autowired
    private ProjectInfoAbarbeitungApprovalRejectionRepository approvalRejectionRepository;

    @Autowired
    private MaterialManagementGroupRepository materialManagementGroupRepository;
    @Autowired
    private ProjectRoomService projectRoomService;
    @Autowired
    private ProjectMaterialManagementRepository projectMaterialManagementRepository;
    @Autowired
    private ProjectMaterialManagementService projectMaterialManagementService;
    @Autowired
    private TemplateSpecialCaseDescriptionService templateSpecialCaseDescriptionService;

    @Autowired
    private ProjectSpecialCaseDescriptionService projectSpecialCaseDescriptionService;

    @Autowired
    private QualityControlRepository qualityControlRepository;

    @Autowired
    private RoleControlElementRepository roleControlElementRepository;
    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private TemplateGroupExpandService templateGroupExpandService;
    @Autowired
    private ProjectGroupExpandService projectGroupExpandService;
    @Autowired
    private TemplateCateService templateCateService;
    @Autowired
    private ProjectCateInfoService projectCateInfoService;
    @Autowired
    private TemplateSystemSelfInspectionRepository templateSystemSelfInspectionRepository;
    @Autowired
    private ProjectSystemSelfInspectionService projectSystemSelfInspectionService;
    @Autowired
    private ProjectConstructionLogRepository projectConstructionLogRepository;
    @Autowired
    private ProjectConstructionLogService projectConstructionLogService;
    @Autowired
    private TemplateTableRelationRepository templateTableRelationRepository;
    @Autowired
    private TemplateTableRelationMapper templateTableRelationMapper;
    @Autowired
    private ProjectSystemSelfInspectionRepository projectSystemSelfInspectionRepository;
    @Autowired
    private ProjectCompletionReceiptRepository projectCompletionReceiptRepository;

    @Autowired
    private ProjectNoticeService projectNoticeService;
    @Autowired
    private ProjectVisaFilingRepository projectVisaFilingRepository;
    @Autowired
    private QualityControlService qualityControlService;
    @Autowired
    private UnauthorizedConstructionService unauthorizedConstructionService;
    @Autowired
    private ProjectWeakCurrentService projectWeakCurrentService;
    @Autowired
    private ProjectCompletionReceiptDescriptionService projectCompletionReceiptDescriptionService;

    @Autowired
    private ProjectCompletionReceiptSummaryService projectCompletionReceiptSummaryService;
    @Autowired
    private ProjectCompletionReceiptService projectCompletionReceiptService;

    @Autowired
    private ProjectCompletionPhotoService projectCompletionPhotoService;
    @Autowired
    private ProjectSpecialNeedsFranchisorsService projectSpecialNeedsFranchisorsService;
    @Autowired
    private ConstructionPhotographService constructionPhotographService;
    @Autowired
    private ProjectTemplateApproveRelationRepository projectTemplateApproveRelationRepository;
    @Autowired
    private ProjectTemplateApproveRelationMapper projectTemplateApproveRelationMapper;
    @Autowired
    private ApproveTemplateDetailRepository approveTemplateDetailRepository;
    @Autowired
    private SupplierInfoService supplierInfoService;
    @Autowired
    private SupplierPmService supplierPmService;
    @Autowired
    private SupplierInfoRepository supplierInfoRepository;
    @Autowired
    private ProjectGroupMapper projectGroupMapper;
    @Autowired
    private AreaService areaService;

    private static final String PROJECT_NO_PREFIX = "project_no::";

    public ProjectInfoServiceImpl(TemplateSpecialCaseDescriptionService templateSpecialCaseDescriptionService) {
        this.templateSpecialCaseDescriptionService = templateSpecialCaseDescriptionService;
    }


    //立项时调用 这个方法,证照执行8次
    public Boolean cate(Long projectId) {
        if (ObjectUtil.isNotEmpty(projectId)) {
            this.templateToInfo(projectId, cateKey.CATE_TWO_1.getKey(), cateKey.CATE_THREE_1.getKey());
            this.templateToInfo(projectId, cateKey.CATE_TWO_2.getKey(), cateKey.CATE_THREE_2.getKey());
            this.templateToInfo(projectId, cateKey.CATE_TWO_3.getKey(), cateKey.CATE_THREE_3.getKey());
            this.templateToInfo(projectId, cateKey.CATE_TWO_4.getKey(), cateKey.CATE_THREE_4.getKey());
            this.templateToInfo(projectId, cateKey.CATE_TWO_5.getKey(), cateKey.CATE_THREE_5.getKey());
            this.templateToInfo(projectId, cateKey.CATE_TWO_6.getKey(), cateKey.CATE_THREE_6.getKey());
            this.templateToInfo(projectId, cateKey.CATE_TWO_7.getKey(), cateKey.CATE_THREE_7.getKey());
            this.templateToInfo(projectId, cateKey.CATE_TWO_8.getKey(), cateKey.CATE_THREE_8.getKey());
            return true;
        }
        return false;
    }

    /**
     * 把证照模版复制到 证照项目表中
     *
     * @param
     * @return ProjectInfoDto
     */
    public Boolean templateToInfo(Long projectId, String twoNodeCode, String threeNodeCode) {

        LambdaQueryWrapper infoWrapper = Wrappers.lambdaQuery(ProjectInfo.class)
                .eq(ProjectInfo::getProjectId, projectId)
                .eq(ProjectInfo::getIsDelete, 0);
        ProjectInfo projectInfo = projectInfoRepository.selectOne(infoWrapper);
        //获取项目类型 -- 新开店 new  改造major  闭店close
        String type = projectInfo.getProjectType();

        //过滤证照类型要和 项目类型一致
        LambdaQueryWrapper<TemplateCate> wrapper = Wrappers.lambdaQuery();
        wrapper.in(TemplateCate::getCateType, type)
                .eq(TemplateCate::getCateStauts, cateKey.CATE_ONLINE.getKey())
                .eq(TemplateCate::getIsDelete, 0);
        List<TemplateCate> templateCateList = templateCateService.list(wrapper);
        List<ProjectCateInfo> projectCateInfoList = new ArrayList<>();
        for (TemplateCate t : templateCateList) {
            ProjectCateInfo projectCateInfo = new ProjectCateInfo();
            BeanUtils.copyProperties(t, projectCateInfo);
            projectCateInfo.setProjectId(projectId);
            projectCateInfo.setTwoNodeCode(twoNodeCode);
            projectCateInfo.setThreeNodeCode(threeNodeCode);
            projectCateInfo.setCateFiles(t.getCateShili());
            projectCateInfo.setIsDelete(0);
            projectCateInfo.setNotDelete("1");//是否可以删除 0可删除；1不可删除
            projectCateInfoList.add(projectCateInfo);
        }
        //保存进证照项目表
        return projectCateInfoService.saveBatch(projectCateInfoList);
    }

    //查询证照项目表
    public List<ProjectCateInfo> selectCateInfoList(ProjectCateInfo cateInfo) {

        LambdaQueryWrapper infoWrapper = Wrappers.lambdaQuery(ProjectInfo.class)
                .eq(ProjectInfo::getProjectId, cateInfo.getProjectId())
                .eq(ProjectInfo::getIsDelete, 0);
        ProjectInfo projectInfo = projectInfoRepository.selectOne(infoWrapper);
        //获取项目类型 -- 新开店 new  改造major  闭店close
        String type = projectInfo.getProjectType();
        //过滤证照类型要和 项目类型一致
        LambdaQueryWrapper<ProjectCateInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProjectCateInfo::getProjectId, cateInfo.getProjectId())
                .in(ProjectCateInfo::getCateType, type)
                .eq(ProjectCateInfo::getTwoNodeCode, cateInfo.getTwoNodeCode())
                .eq(ProjectCateInfo::getIsDelete, 0);
        return projectCateInfoService.list(wrapper);
    }

    /**
     * 变更自检表信息
     *
     * @param
     * @return ProjectInfoDto
     */
    public Boolean updateSelfTest(Long projectId, String nodeCode, String same, String nosame, String status) {

        List<ProjectTableNodeInfo> projectTableNodeInfoList = this.getDynamicMaskClick(projectId, nodeCode);
        //不符合的集合
        List<ProjectTableNodeInfo> inconformityList = new ArrayList<>();
        //符合的集合
        List<ProjectTableNodeInfo> conformList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(projectTableNodeInfoList)) {
            for (ProjectTableNodeInfo p : projectTableNodeInfoList) {
                //json转实体
                String json = p.getRemark();
                if (StringUtils.isNotEmpty(json)) {

                    List<ProjectInfoTableCheckReceiptSumUpDateDTO> SumUpDateDTOList = JSONObject.parseArray(json, ProjectInfoTableCheckReceiptSumUpDateDTO.class);
                    ProjectInfoTableCheckReceiptSumUpDateDTO SumUpDateDTO = SumUpDateDTOList.stream().filter(n -> n.getTertiaryKey().equals(tertiaryKey.INSPECTION.getKey())).findFirst().orElse(null);
                    if (ObjectUtils.isNotEmpty(SumUpDateDTO)) {
                        if (tertiaryKey.ACCORD_WITH.getKey().equals(SumUpDateDTO.getTertiaryValue())) {
                            //符合的
                            conformList.add(p);
                        } else if (tertiaryKey.INCONFORMITY.getKey().equals(SumUpDateDTO.getTertiaryValue())) {
                            //不符合的
                            inconformityList.add(p);
                        }
                    }
                }
            }
        }
        LambdaQueryWrapper<ProjectNodeInfo> queryNodeWrapper = Wrappers.lambdaQuery();
        //查询出自检里的 3个项
        queryNodeWrapper.eq(ProjectNodeInfo::getProjectId, projectId)
                .and(wrapper -> wrapper.eq(ProjectNodeInfo::getNodeCode, status)
                        .or().eq(ProjectNodeInfo::getNodeCode, nosame)
                        .or().eq(ProjectNodeInfo::getNodeCode, same));
        util.setProjectTableName(projectId);

        List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoService.list(queryNodeWrapper);
        for (ProjectNodeInfo info : projectNodeInfos) {
            if (info.getNodeCode().equals(status)) {
                info.setRemark("已完成");
                projectNodeInfoService.updateById(info);
            } else if (nosame.contains(info.getNodeCode())) {
                info.setRemark(String.valueOf(inconformityList.size()));
                projectNodeInfoService.updateById(info);
            } else if (same.contains(info.getNodeCode())) {
                info.setRemark(String.valueOf(conformList.size()));
                projectNodeInfoService.updateById(info);
            }
        }
        return true;
    }

    @Override
    public Map<String, Object> queryAll(ProjectInfoQueryCriteria criteria, Pageable pageable) {
        LocalDateTime currentDateTime = LocalDateTime.now();
        //项目类型
        List<String> projectTypes = criteria.getProjectType();
        if (ObjectUtils.isNotEmpty(projectTypes) && KidsSystemEnum.ProjectTypeOEnum.REFORM.getValue().equals(projectTypes.get(0))) {
            projectTypes.add(KidsSystemEnum.ProjectTypeEnum.MAJOR.getValue());
            projectTypes.add(KidsSystemEnum.ProjectTypeEnum.MINOR.getValue());
        }
        Long currentUserId = SecurityUtils.getCurrentUserId();
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        User user = userRepository.getOne(currentUserId);
        List<Long> projectIds = new LinkedList<>();
        //根据权限查询project数据
        if (ObjectUtils.isNotEmpty(role)) {
            if (role.getIsCity() != null && (JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) || (ObjectUtils.isNotEmpty(criteria.getPreparationDocking()) && 1 == criteria.getPreparationDocking())) {
                projectIds = projectInfoRepository.getProjectIdsByCity(currentUserId, Boolean.FALSE, criteria.getPreparationDocking(), criteria.getProjectTaskPhase(), criteria.getTaskPhase());
            } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
                projectIds = projectInfoRepository.getProjectIdsByStakeholders(currentUserId, Boolean.FALSE, criteria.getPreparationDocking(), criteria.getProjectTaskPhase(), criteria.getTaskPhase());
            }
        }
        this.getPage(pageable);
        PageInfo<ProjectInfo> page = new PageInfo<>();
        List<ProjectInfoDto> list = new LinkedList<>();
        if (projectIds.size() > 0) {
            criteria.setIsDelete(false);
            criteria.setProjectIds(projectIds);
            page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectInfo.class, criteria)));
            list = projectInfoMapper.toDto(page.getList());

            System.out.println("获取项目毫秒数: " + ChronoUnit.MILLIS.between(currentDateTime, LocalDateTime.now()));
            if (ObjectUtils.isNotEmpty(list)) {
                projectIds = list.stream().map(ProjectInfoDto::getProjectId).distinct().collect(Collectors.toList());
                List<Integer> projectHlmIds = list.stream().map(ProjectInfoDto::getProjectHlmId).distinct().collect(Collectors.toList());
                //获取项目当前进行中的工程和设计节点
                List<ProjectGroupDto> groupDtos = projectGroupRepository.selectByProjectIds(projectIds, null, null);
                Map<String, List<ProjectGroupDto>> collect1 = groupDtos.stream().collect(Collectors.groupingBy(group -> group.getProjectId()));
                //获取项目开工申请任务信息
                LambdaQueryWrapper<ProjectGroup> eq = Wrappers.lambdaQuery(ProjectGroup.class)
                        .in(ProjectGroup::getProjectId, projectIds)
                        .eq(ProjectGroup::getNodeCode, AtourSystemEnum.EngineeringNodeTow.ENG00105.getKey());
                List<ProjectGroup> projectGroupKG = projectGroupRepository.selectList(eq);
                Map<Long, ProjectGroup> groupMap = projectGroupKG.stream().collect(Collectors.toMap(i -> i.getProjectId(), j -> j, (k1, k2) -> k1));
//                        .stream().collect(Collectors.toMap(ProjectGroup::getProjectId, r -> r));

                //获取项目竣工验收任务信息
                LambdaQueryWrapper<ProjectGroup> eqPlan = Wrappers.lambdaQuery(ProjectGroup.class)
                        .in(ProjectGroup::getProjectId, projectIds)
                        .eq(ProjectGroup::getNodeCode, AtourSystemEnum.EngineeringNodeTow.ENG00133.getKey());
                List<ProjectGroup> projectGroupPlanJG = projectGroupRepository.selectList(eqPlan);
                Map<Long, ProjectGroup> projectGroupPlanMap = projectGroupPlanJG.stream().collect(Collectors.toMap(i -> i.getProjectId(), j -> j, (k1, k2) -> k1));
//                        .stream().collect(Collectors.toMap(ProjectGroup::getProjectId, r -> r));

                //获取hlm系统推送的项目信息
                LambdaQueryWrapper<ProjectInfoExpansion> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(ProjectInfoExpansion::getProjectId, projectHlmIds);
                List<ProjectInfoExpansion> infoExpansionHlm = projectInfoExpansionRepository.selectList(queryWrapper);
                Map<Integer, ProjectInfoExpansion> expansionMap = infoExpansionHlm.stream().collect(Collectors.toMap(i -> i.getProjectId(), j -> j, (k1, k2) -> k1));
//                        .stream().collect(Collectors.toMap(ProjectInfoExpansion::getProjectId, r -> r));


                System.out.println("查询毫秒数: " + ChronoUnit.MILLIS.between(currentDateTime, LocalDateTime.now()));
                // 查询节点中的列表需要的节点数据
                for (ProjectInfoDto projectInfo : list) {
                    if (ObjectUtil.isNotEmpty(groupDtos)) {
                        //获取项目的进行中的节点
                        List<ProjectGroupDto> groupDtoList = collect1.get(projectInfo.getProjectId().toString());
                        if (ObjectUtil.isNotEmpty(groupDtoList)) {
                            for (ProjectGroupDto projectGroupDto : groupDtoList) {
                                /*阶段节点名称（工程、设计）*/
                                projectInfo.setStageNodeName(projectGroupDto.getStageNodeName());
                                if (projectGroupDto.getStageNodeName().equals(JhSystemEnum.kanbanTaskPhaseEnum.ENGINEERING.getKey())) {
                                    projectInfo.setEngineeringNode(projectGroupDto.getNodeName());
                                    projectInfo.setEngineeringNodeTask(projectGroupDto.getNodeNameTask());
                                }
                                if (projectGroupDto.getStageNodeName().equals(JhSystemEnum.kanbanTaskPhaseEnum.DESIGN.getKey())) {
                                    projectInfo.setDesignNode(projectGroupDto.getNodeName());
                                    projectInfo.setDesignNodeTask(projectGroupDto.getNodeNameTask());
                                }
                            }
                        }
                    }


                    // 预计竣工日期；开工申请的业主约定完成时间predictEndDate；没有则竣工验收计划完成时间
                    ProjectGroup projectGroup = groupMap.get(projectInfo.getProjectId());
                    ProjectGroup projectGroupPlan = projectGroupPlanMap.get(projectInfo.getProjectId());
                    final boolean present = Optional.ofNullable(projectGroup).map(ProjectGroup::getPredictEndDate).filter(ObjectUtil::isNotEmpty).isPresent();
                    final String time = String.valueOf(Optional.ofNullable(projectGroupPlan).map(ProjectGroup::getPlanEndDate).filter(ObjectUtil::isNotEmpty).orElse(null));
                    java.sql.Date date = null;
                    if (present) {
                        date = DateUtil.stringToDate(String.valueOf(projectGroup.getPredictEndDate()));
                    }
                    if ("null" != time) {
                        projectInfo.setPlanOverDate(present ? date : DateUtil.stringToDate(time));
                    }

                    //获取保密字段数据
//                    LambdaQueryWrapper<ProjectInfoExpansion> queryWrapper = new LambdaQueryWrapper<>();
//                    queryWrapper.eq(ProjectInfoExpansion::getProjectId, projectInfo.getProjectHlmId()).last("limit 1");
//                    ProjectInfoExpansion infoExpansion = projectInfoExpansionRepository.selectOne(queryWrapper);
                    ProjectInfoExpansion infoExpansion = expansionMap.get(projectInfo.getProjectHlmId());
                    if (ObjectUtil.isNotEmpty(infoExpansion)) {
                        projectInfo.setSecurityProject(infoExpansion.getSecurityProject());
                        projectInfo.setProjectStartTime(infoExpansion.getProjectStartTime());
                    }

//                    if (ObjectUtils.isNotEmpty(projectInfo.getConstructionManager())) {
//                        User one = userRepository.getOne(projectInfo.getConstructionManager());
//                        if (ObjectUtils.isNotEmpty(one)) {
//                            projectInfo.setConstructionManagerName(one.getNickName());
//                        }
//                    }

                    /*项目经理*/
                    if (ObjectUtils.isNotEmpty(projectInfo.getProjectManager())) {
                        User one = userRepository.getOne(projectInfo.getProjectManager());
                        if (ObjectUtils.isNotEmpty(one)) {
                            projectInfo.setProjectManagerName(one.getNickName());
                        }
                    }
//                    if (ObjectUtils.isNotEmpty(projectInfo.getRegionNetManager())) {
////                        User one = userRepository.getOne(projectInfo.getRegionNetManager());
////                        if (ObjectUtils.isNotEmpty(one)) {
////                            projectInfo.setRegionNetManagerName(one.getNickName());
////                        }
////                    }
                    if (ObjectUtils.isNotEmpty(projectInfo.getCityCompany())) {
                        DictDetail dictDetailByValue = dictDetailRepository.findDictDetailByValue(projectInfo.getCityCompany());
                        if (ObjectUtils.isNotEmpty(dictDetailByValue)) {
                            projectInfo.setCityCompanyName(dictDetailByValue.getLabel());
                        }
                    }

                    if (ObjectUtils.isNotEmpty(projectInfo.getBrandCode())) {
                        projectInfo.setBrandName(dictDetailRepository.getIdByNodeCodeRemark(projectInfo.getBrandCode()));
                    }
                    if (ObjectUtils.isNotEmpty(projectInfo.getProductCode())) {
                        projectInfo.setProductName(dictDetailRepository.getIdByNodeCodeRemark(projectInfo.getProductCode()));
                    }
                }
            }
            System.out.println("项目初始化毫秒数: " + ChronoUnit.MILLIS.between(currentDateTime, LocalDateTime.now()));
        } else {
            PageHelper.clearPage();
        }

        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", list);
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectInfoDto> queryChangeStakeholders(ProjectInfoQueryCriteria criteria) {
        //查询项目批量更换干系人列表
        List<String> projectTypes = criteria.getProjectType();
        if (ObjectUtils.isNotEmpty(projectTypes) && KidsSystemEnum.ProjectTypeOEnum.REFORM.getValue().equals(projectTypes.get(0))) {
            projectTypes.add(KidsSystemEnum.ProjectTypeEnum.MAJOR.getValue());
            projectTypes.add(KidsSystemEnum.ProjectTypeEnum.MINOR.getValue());
        }
        Long currentUserId = SecurityUtils.getCurrentUserId();
        List<ProjectInfoDto> byStakeholders = projectInfoRepository.getByStakeholders(currentUserId, criteria.getLeavingUser());
        return byStakeholders;
    }

    @Override
    public Map<String, Object> queryAdjustAll(ProjectInfoQueryCriteria criteria, Pageable pageable) {
        List<String> projectTypes = criteria.getProjectType();
        if (ObjectUtils.isNotEmpty(projectTypes) && KidsSystemEnum.ProjectTypeOEnum.REFORM.getValue().equals(projectTypes.get(0))) {
            projectTypes.add(KidsSystemEnum.ProjectTypeEnum.MAJOR.getValue());
            projectTypes.add(KidsSystemEnum.ProjectTypeEnum.MINOR.getValue());
        }
        Long currentUserId = SecurityUtils.getCurrentUserId();
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        User user = userRepository.getOne(currentUserId);
        List<Long> projectIds = new LinkedList<>();
        //根据权限查询project数据
        if ("1".equals(user.getUserStatus())) {
            projectIds = projectInfoRepository.getOutPersonAccessIds(currentUserId);
        } else {
            if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
                projectIds = projectInfoRepository.getAdjustProjectIdsByCity(currentUserId, Boolean.FALSE);
            } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
                projectIds = projectInfoRepository.getAdjustProjectIdsByStakeholders(currentUserId, Boolean.FALSE);
            }
        }
        getPage(pageable);
        PageInfo<ProjectInfo> page = new PageInfo<>();
        List<ProjectInfoDto> list = new LinkedList<>();
        if (projectIds.size() > 0) {
            criteria.setIsDelete(false);
            criteria.setProjectIds(projectIds);
            page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectInfo.class, criteria)));
            list = projectInfoMapper.toDto(page.getList());

            if (ObjectUtils.isNotEmpty(list)) {
                // 查询节点中的列表需要的节点数据
                List<Long> projectIdList = list.stream().map(ProjectInfoDto::getProjectId).collect(Collectors.toList());
                LambdaQueryWrapper<ProjectNodeInfo> projectNodeInfoLambdaQueryWrapper = Wrappers.lambdaQuery();
                // 门店版本、装修面积(m3)、预计开业日期、预计开工日期、预计竣工日期
                projectNodeInfoLambdaQueryWrapper.in(ProjectNodeInfo::getNodeCode, "con-0070105", "con-0013407", "con-0013411", "con-0010307", "con-0010305", "con-0010306");
                projectNodeInfoLambdaQueryWrapper.in(ProjectNodeInfo::getProjectId, projectIdList);
                List<ProjectNodeInfo> projectNodeInfoList = projectNodeInfoService.list(projectNodeInfoLambdaQueryWrapper);
                Map<Long, List<ProjectNodeInfo>> projectNodeInfoListMap = projectNodeInfoList.stream().collect(Collectors.groupingBy(ProjectNodeInfo::getProjectId));
                for (ProjectInfoDto projectInfo : list) {
                    List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoListMap.get(projectInfo.getProjectId());
                    if (CollUtil.isNotEmpty(projectNodeInfos)) {
                        Map<String, String> nodeCodeRemarkMap = projectNodeInfos.stream().filter(e -> StringUtils.isNotEmpty(e.getRemark())).collect(Collectors.toMap(ProjectNodeInfo::getNodeCode, ProjectNodeInfo::getRemark));
                        // 门店版本
                        String storeVersion = nodeCodeRemarkMap.get("con-0070105");
                        if (ObjectUtils.isNotEmpty(storeVersion)) {
                            projectInfo.setStoreVersion(storeVersion);
                        }
                        // 门店面积
                        BigDecimal totalArea = StringUtils.isEmpty(nodeCodeRemarkMap.get("con-0013407")) ? BigDecimal.ZERO : new BigDecimal(nodeCodeRemarkMap.get("con-0013407"));
                        // 招商面积
                        BigDecimal shipArea = StringUtils.isEmpty(nodeCodeRemarkMap.get("con-0013411")) ? BigDecimal.ZERO : new BigDecimal(nodeCodeRemarkMap.get("con-0013411"));
                        BigDecimal area = totalArea.subtract(shipArea);
                        if (ObjectUtils.isNotEmpty(area)) {
                            projectInfo.setDecorateArea(area);
                        }
                        // 预计开业日期
                        String planOpenDate = nodeCodeRemarkMap.get("con-0010307");
                        if (ObjectUtils.isNotEmpty(planOpenDate)) {
                            projectInfo.setPlanOpenDate(StringUtils.isEmpty(planOpenDate) ? null : DateUtil.stringToDate(planOpenDate));
                        }
                        // 预计开工日期
                        String planApproachDate = nodeCodeRemarkMap.get("con-0010305");
                        if (ObjectUtils.isNotEmpty(planApproachDate)) {
                            projectInfo.setPlanApproachDate(StringUtils.isEmpty(planApproachDate) ? null : DateUtil.stringToDate(planApproachDate));
                        }
                        // 预计竣工日期
                        String overTime = nodeCodeRemarkMap.get("con-0010306");
                        if (ObjectUtils.isNotEmpty(overTime)) {
                            projectInfo.setPlanOverDate(StringUtils.isEmpty(overTime) ? null : DateUtil.stringToDate(overTime));
                        }
                    }

                    if (ObjectUtils.isNotEmpty(projectInfo.getConstructionManager())) {
                        User one = userRepository.getOne(projectInfo.getConstructionManager());
                        if (ObjectUtils.isNotEmpty(one)) {
                            projectInfo.setConstructionManagerName(one.getNickName());
                        }
                    }
                    if (ObjectUtils.isNotEmpty(projectInfo.getCityCompany())) {
                        DictDetail dictDetailByValue = dictDetailRepository.findDictDetailByValue(projectInfo.getCityCompany());
                        if (ObjectUtils.isNotEmpty(dictDetailByValue)) {
                            projectInfo.setCityCompanyName(dictDetailByValue.getLabel());
                        }
                    }
                }
            }
        }


        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", list);
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public Map<String, Object> queryAllForDelete(ProjectInfoQueryCriteria criteria, Pageable pageable) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        User user = userRepository.getOne(currentUserId);
        List<Long> projectIds = new LinkedList<>();
        //根据权限查询project数据
        if ("1".equals(user.getUserStatus())) {
            projectIds = projectInfoRepository.getOutPersonAccessIds(currentUserId);
        } else {
            if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
                projectIds = projectInfoRepository.getProjectIdsByCity(currentUserId, null, null, null, null);
            } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
                projectIds = projectInfoRepository.getProjectIdsByStakeholders(currentUserId, null, null, null, null);
            }
        }
        getPage(pageable);
        PageInfo<ProjectInfo> page = new PageInfo<>();
        if (projectIds.size() > 0) {
            //criteria.setIsDelete(false);
            criteria.setProjectIds(projectIds);
            page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectInfo.class, criteria)));
        }


        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public Map<String, Object> queryTask(ProjectInfoQueryCriteria criteria, Pageable pageable) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        List<ProjectInfo> projectInfoList = new LinkedList<>();
        List<ProjectInfo> list = list(QueryHelpPlus.getPredicate(ProjectInfo.class, criteria));
        List<Long> projectIds = new LinkedList<>();
        list.forEach(p -> projectIds.add(p.getProjectId()));
        //根据权限查询project数据
        if (role.getIsStakeholder() != null && role.getIsStakeholder()) {
            if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
                projectInfoList = projectInfoRepository.getProjectTaskCityByNodeCode(currentUserId, projectIds, "todo_task", criteria.getNodeCode());
            } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
                projectInfoList = projectInfoRepository.getProjectTaskStaByNodeCode(currentUserId, projectIds, "todo_task", criteria.getNodeCode());
            }
        } else {
            projectInfoList = projectInfoRepository.getProjectTaskUserByNodeCode(currentUserId, projectIds, "todo_task", criteria.getNodeCode());
        }
        PageInfo<ProjectInfo> page = new PageInfo<>();
        page = new PageInfo<ProjectInfo>(projectInfoList);


        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public ProjectInfoDto taskQueryByProjectId(Long projectId) {
        return projectInfoMapper.toDto(projectInfoRepository.taskQueryByProjectId(projectId));
    }


    /**
     * mobile端查询
     *
     * @param criteria
     * @param pageable
     * @return
     */
    @Override
    public Map<String, Object> queryAllForMobile(ProjectInfoQueryCriteria criteria, Pageable pageable) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        User user = userRepository.getOne(currentUserId);
        List<Long> projectIds = new LinkedList<>();
        //根据权限查询project数据
        if (ObjectUtils.isNotEmpty(role)) {
            if (role.getIsCity() != null && (JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) || (ObjectUtils.isNotEmpty(criteria.getPreparationDocking()) && 1 == criteria.getPreparationDocking())) {
                projectIds = projectInfoRepository.getProjectIdsByCity(currentUserId, Boolean.FALSE, criteria.getPreparationDocking(), criteria.getProjectTaskPhase(), criteria.getTaskPhase());
            } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
                projectIds = projectInfoRepository.getProjectIdsByStakeholders(currentUserId, Boolean.FALSE, criteria.getPreparationDocking(), criteria.getProjectTaskPhase(), criteria.getTaskPhase());
            }
        }
        getPage(pageable);
        PageInfo<ProjectInfo> page = new PageInfo<>();
        if (projectIds.size() > 0) {
            criteria.setIsDelete(false);
            criteria.setProjectIds(projectIds);
        }
        page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectInfo.class, criteria)));
        List<ProjectInfoDto> projectInfoDtoList = projectInfoMapper.toDto(page.getList());

        List<ProjectInfo> projectInfos = new LinkedList<>();

        criteria.setBrandCode(null);
        projectInfos = list(QueryHelpPlus.getPredicate(ProjectInfo.class, criteria));
        Map<String, Long> collect = projectInfos.stream().collect(Collectors.groupingBy(d -> d.getBrandId().toString(), Collectors.counting()));
        Map<String, Object> map = new HashMap<>();
        map.put("storeTotal", collect);
        map.put("content", projectInfoDtoList);
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public Map<String, Object> queryAllForComplateData(ProjectInfoQueryCriteria criteria, Pageable pageable) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        User user = userRepository.getOne(currentUserId);
        List<Long> projectIds = new LinkedList<>();
        //根据权限查询project数据
        if ("1".equals(user.getUserStatus())) {
            projectIds = projectInfoRepository.getOutPersonAccessIds(currentUserId);
        } else {
            if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
                projectIds = projectInfoRepository.getProjectIdsByCity(currentUserId, Boolean.FALSE, null, criteria.getProjectTaskPhase(), criteria.getTaskPhase());
            } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
                projectIds = projectInfoRepository.getProjectIdsByStakeholders(currentUserId, Boolean.FALSE, null, criteria.getProjectTaskPhase(), criteria.getTaskPhase());
            }
        }
        getPage(pageable);
        PageInfo<ProjectInfo> page = new PageInfo<>();
        if (projectIds.size() > 0) {
            criteria.setIsDelete(false);
            criteria.setProjectIds(projectIds);
            page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectInfo.class, criteria)));
        }


        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectInfoDto> queryAll(ProjectInfoQueryCriteria criteria) {
        return projectInfoMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectInfo.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectInfoDto findById(Long projectId) {
        return projectInfoRepository.getFindById(projectNodeInfoService.getSubmeterProjectId(projectId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectInfoDto findById(Long projectId, String nodeCode) {
        ProjectInfo projectInfo = Optional.ofNullable(getById(projectId)).orElseGet(ProjectInfo::new);
        ValidationUtil.isNull(projectInfo.getProjectId(), getEntityClass().getSimpleName(), "projectId", projectId);
        return projectInfoMapper.toDto(projectInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectInfoDto create(ProjectInfo resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setProjectId(snowflake.nextId());
        save(resources);
        return findById(resources.getProjectId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectInfo resources) {
        String addressNew = resources.getProjectAddress();
        ProjectInfo projectInfo = Optional.ofNullable(getById(resources.getProjectId())).orElseGet(ProjectInfo::new);
        String addressOrign = projectInfo.getProjectAddress();
        ValidationUtil.isNull(projectInfo.getProjectId(), "ProjectInfo", "id", resources.getProjectId());
        projectInfo.copy(resources);
        updateById(projectInfo);

        if ((ObjectUtils.isNotEmpty(addressOrign) && !addressOrign.equals(addressNew)) || ObjectUtils.isNotEmpty(addressNew)) {
            //引用项目表的值需要update
            LambdaUpdateWrapper nodeInfoUpdate = Wrappers.lambdaUpdate(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectInfo.getProjectId())
                    .eq(ProjectNodeInfo::getNodeCode, JhSystemEnum.NodeCodeEnum.NODE_10108.getKey())
                    .set(ProjectNodeInfo::getRemark, addressNew);
            projectNodeInfoService.update(nodeInfoUpdate);
        }
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long projectId : ids) {
            projectInfoRepository.deleteById(projectId);
        }
    }

    @Override
    public void download(List<ProjectInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectInfoDto projectInfo : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("品牌id", projectInfo.getBrandId());
            map.put("模板id", projectInfo.getTemplateId());
            map.put("项目编码", projectInfo.getProjectNo());
            map.put("门店id", projectInfo.getStoreId());
            map.put("项目版本号 ", projectInfo.getProjectVersion());
            map.put("项目名称", projectInfo.getProjectName());
            map.put("门店名称", projectInfo.getStoreName());
            map.put("门店编码", projectInfo.getStoreNo());
            map.put("经营性质", projectInfo.getBusinessNature());
            map.put("门店类型", projectInfo.getStoreType());
            map.put("设计形象", projectInfo.getDesignImage());
            map.put("大区value", projectInfo.getRegion());
            map.put("城市公司value", projectInfo.getCityCompany());
            map.put("省份id", projectInfo.getProvince());
            map.put("省份名称", projectInfo.getProvinceName());
            map.put("城市id", projectInfo.getCity());
            map.put("城市名称", projectInfo.getCityName());
            map.put("区县id", projectInfo.getCounty());
            map.put("区县名称", projectInfo.getCountyName());
            map.put("详细地址（不包括省市县）", projectInfo.getProjectAddress());
            map.put("商圈id", projectInfo.getTradeId());
            map.put("立项时间", projectInfo.getEstablishTime());
            map.put("城市公司网发经理", projectInfo.getRegionNetManager());
            map.put("SE营建经理", projectInfo.getConstructionManager());
            map.put("工程总监", projectInfo.getEngineerDirector());
            map.put("设计师", projectInfo.getDesigner());
            map.put("总部网发", projectInfo.getGeneralNetwork());
            map.put("城市公司财务", projectInfo.getRegionaFinanceManager());
            map.put("项目计划开始时间", projectInfo.getProjectPlanStart());
            map.put("项目计划结束时间", projectInfo.getProjectPlanEnd());
            map.put("项目实际结束时间", projectInfo.getProjectActualEnd());
            map.put("计划开业日期", projectInfo.getPlanOpenDate());
            map.put("计划进场日期", projectInfo.getPlanApproachDate());
            map.put("项目创建日期", projectInfo.getProjectCreateDate());
            map.put("实际开业日期", projectInfo.getActualOpenDate());
            map.put("是否开业", projectInfo.getIsOpen());
            map.put("是否立项", projectInfo.getIsCreate());
            map.put("总计面积", projectInfo.getTotalArea());
            map.put("使用面积", projectInfo.getUsedArea());
            map.put("装修面积", projectInfo.getDecorateArea());
            map.put("项目类型", projectInfo.getProjectType());
            map.put("项目状态", projectInfo.getProjectStatus());
            map.put("当前任务阶段", projectInfo.getTaskPhase());
            map.put("是否逾期", projectInfo.getIsOverdue());
            map.put("结算任务阶段", projectInfo.getAccountPhase());
            map.put("结算是否逾期", projectInfo.getAccountOverdue());
            map.put("项目备注", projectInfo.getRemark());
            map.put("是否生效", projectInfo.getIsActive());
            map.put("是否删除", projectInfo.getIsDelete());
            map.put("创建时间", projectInfo.getCreateTime());
            map.put("修改时间", projectInfo.getUpdateTime());
            map.put("创建人", projectInfo.getCreateBy());
            map.put("更新人", projectInfo.getUpdateBy());
            map.put("是否可用", projectInfo.getIsEnabled());
            map.put("楼层", projectInfo.getFloor());
            map.put("设计定位", projectInfo.getDesignPosition());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    private String getByNodeCode(List<ProjectNodeInfo> nodeInfoDtos, String nodeCode) {
        for (ProjectNodeInfo nodeInfoDto : nodeInfoDtos) {
            if (nodeInfoDto.getNodeCode().equals(nodeCode)) {
                return nodeInfoDto.getRemark();
            }
        }
        return "";
    }

    @Override
    public void downloadNew(HttpServletResponse response) throws IOException {
        ExecutorService executor = Executors.newFixedThreadPool(1);
        executor.submit(() -> {
            try {
                List<ProjectInfoDto> all = this.baseMapper.getDownloadData();

                long t1 = System.currentTimeMillis();
                File dir = new File("excel");
                if (dir.listFiles() != null && Objects.requireNonNull(dir.listFiles()).length > 0) {
                    return;
                }
                List<Map<String, Object>> list = new ArrayList<>();
                for (ProjectInfoDto projectInfo : all) {
                    Map<String, Object> map = new LinkedHashMap<>();
                    map.put("酒店id", projectInfo.getProjectNo());
                    map.put("项目名称", projectInfo.getProjectName());
                    map.put("品牌", projectInfo.getBrandName());
                    map.put("产品名称", projectInfo.getProductName());
                    map.put("省份 ", projectInfo.getProvinceName());
                    map.put("城市", projectInfo.getCityName());
                    map.put("签约日期", projectInfo.getEffectiveDate());
                    map.put("营建状态", projectInfo.getProjectStatus());

                    map.put("施工单位项目项目经理", projectInfo.getConstructionManagerName());
                    map.put("施工单位", projectInfo.getConstructionManagerName());
                    map.put("客房装饰设计师", projectInfo.getDecorationDesignerName());
                    map.put("客房设计单位", projectInfo.getDesignUnitName());
                    map.put("公区装饰设计师", projectInfo.getAreaDesignUnit());
                    map.put("公区设计单位", projectInfo.getAreaDesigner());
                    map.put("客房机电设计师", projectInfo.getMechanicalAndElectricalDesignerName());
                    map.put("客房机电单位", projectInfo.getMechanicalAndElectricalDesignerUnit());
                    map.put("公区机电设计师", projectInfo.getElectricalDesignerName());
                    map.put("公区机电单位", projectInfo.getElectricalDesignerUnit());
                    map.put("开发战区负责人", projectInfo.getDevelopmentTheaterLeaderName());
                    map.put("营建区域负责人", projectInfo.getConstructionAreaLeaderName());
                    map.put("项目经理", projectInfo.getProjectManagerName());
                    map.put("设计师", projectInfo.getDesignerName());
                    map.put("弱电工程师", projectInfo.getWeakCurrentEngineerName());
                    map.put("机电工程师", projectInfo.getMechanicalAndElectricalEngineerName());
                    map.put("软装设计师", projectInfo.getSoftDesigner());
                    map.put("签约房量", projectInfo.getRoomsNumber());
                    map.put("设计节点", projectInfo.getDesignNodeTask());
                    map.put("工程节点", projectInfo.getEngineeringNodeTask());
                    map.put("预计开工时间", projectInfo.getProjectPlanStart());
                    map.put("实际开工时间", projectInfo.getProjectActualStart());
                    map.put("预计客房交底时间", projectInfo.getProjectPlanEnd());
                    map.put("实际客房交底时间", projectInfo.getProjectActualEnd());
                    map.put("预计公区交底时间", projectInfo.getProjectPlanEnd());
                    map.put("实际工区交底时间", projectInfo.getProjectActualEnd());
                    map.put("预计隐蔽验收时间", projectInfo.getProjectPlanEnd());
                    map.put("实际隐蔽验收时间", projectInfo.getProjectActualEnd());
                    map.put("预计开业时间", projectInfo.getProjectPlanEnd());
                    map.put("实际开业时间", projectInfo.getProjectActualEnd());
                    map.put("使用面积", projectInfo.getUsedArea());
                    map.put("装修面积", projectInfo.getDecorateArea());
                    map.put("项目类型", projectInfo.getProjectType());
                    map.put("项目状态", projectInfo.getProjectStatus());
                    map.put("当前任务阶段", projectInfo.getTaskPhase());
                    map.put("是否逾期", projectInfo.getIsOverdue());
                    map.put("结算任务阶段", projectInfo.getAccountPhase());
                    map.put("结算是否逾期", projectInfo.getAccountOverdue());
                    map.put("项目备注", projectInfo.getRemark());
                    map.put("是否生效", projectInfo.getIsActive());
                    map.put("是否可用", projectInfo.getIsEnabled());
                    map.put("楼层", projectInfo.getFloor());
                    map.put("设计定位", projectInfo.getDesignPosition());
                    list.add(map);
                }
                long t2 = System.currentTimeMillis();
                log.info("耗时：{}", t2 - t1);
                FileUtil.uploadExcel(list, "excel" + File.separator + "项目信息.xlsx");
            } catch (Exception ignored) {
            }
        });
        executor.shutdown();
    }

    private String getUserNameByNodeCode(List<ProjectStakeholders> projectStakeholders, JhSystemEnum.TitleNodeCode code) {
        StringBuilder sb = new StringBuilder();
        for (ProjectStakeholders s : projectStakeholders) {
            String user = this.baseMapper.getUserNameByUserId(s.getUserId());
            if (code.getRoleCode().equals(s.getRoleCode())) {
                sb.append(user).append(",");
            }
        }
        if (sb.length() == 0) return "";
        return sb.substring(0, sb.length() - 1);
    }

    private Long saveProjectNodeInfoByTemplate(List<TemplateGroup> templateGroups,
                                               int i, Long projectId, Long createProjectProjectGroupId,
                                               Map<String, List<ProjectTemplateApproveRelationDto>> relationDtoMap,
                                               Map<Long, List<ApproveTemplateDetail>> approveTemplateDetailMap,
                                               ArrayList<ProjectGroup> todoTaskList, List<ProjectGroup> groupInfoForCount,
                                               Map<Long, List<TemplateQueue>> templateQueueMap, Map<Long, List<ProjectTemplate>> templateMap,
                                               ArrayList<ProjectNodeInfo> projectNodeInfos, Map<String, List<TemplateQueue>> templateQueueMap2,
                                               String templateCode) {
        long s2 = System.currentTimeMillis();
        TemplateGroup group = templateGroups.get(i);
        ProjectGroup projectGroup = new ProjectGroup();
        BeanUtils.copyProperties(group, projectGroup);
        projectGroup.setProjectId(projectId);
        projectGroup.setIsDelete(false);
        projectGroup.setIsEnabled(true);
        projectGroup.setCreateBy(null);
        projectGroup.setCreateTime(null);
        Snowflake snowflake1 = IdUtil.getSnowflake(1, 1);
        Long projectGroupId = snowflake1.nextId();
        projectGroup.setProjectGroupId(projectGroupId);
        projectGroup.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS2.getKey());

        if (group.getNodeLevel() == 2) {
            // 审批模板入库
            //List<ProjectTemplateApproveRelationDto> appRelations = ProjectTemplateApproveRelationDto.getAppRelation(group.getTemplateId(), group.getTemplateGroupId());
            if (ObjectUtil.isNotEmpty(relationDtoMap)) {
                List<ProjectTemplateApproveRelationDto> appRelations = relationDtoMap.get(group.getTemplateId() + ":" + group.getTemplateGroupId());
                //若存在审批节点，则复制当前节点数据
                if (ObjectUtils.isNotNull(appRelations)) {
                    List<ApproveTemplateDetail> list = approveTemplateDetailMap.get(appRelations.get(0).getApproveMatrixId());
                    if (ObjectUtil.isNotEmpty(list) && list.size() > 0) {
                        List<ProjectAppTemplate> saveList = new ArrayList<>();
                        for (ApproveTemplateDetail detail : list) {
                            ProjectAppTemplate projectAppTemplate = new ProjectAppTemplate();
                            BeanUtils.copyProperties(detail, projectAppTemplate);
                            projectAppTemplate.setProjectId(projectGroup.getProjectId());
                            projectAppTemplate.setOrderId(projectGroup.getOrderId());
                            projectAppTemplate.setNodeId(projectGroup.getProjectGroupId());
                            projectAppTemplate.setNodeCode(projectGroup.getNodeCode());
                            projectAppTemplate.setNodeName(projectGroup.getNodeName());
                            saveList.add(projectAppTemplate);
                        }
                        projectAppTemplateService.saveBatch(saveList);
                    }
                }
            }
            String frontWbsConfig = group.getFrontWbsConfig();
            if (null == frontWbsConfig) {
                //出待办
                todoTaskList.add(projectGroup);
                projectGroup.setIsOpen(Boolean.TRUE);
            }
        }
        groupInfoForCount.add(projectGroup);
        if (group.getTemplateQueueId() != null) {
            //获取二级三级数据
            //LambdaQueryWrapper<TemplateQueue> parentLambdaQueryWrapper = Wrappers.lambdaQuery(TemplateQueue.class).eq(TemplateQueue::getTemplateQueueId, group.getTemplateQueueId());
            List<TemplateQueue> queueList = templateQueueMap.get(group.getTemplateQueueId());
            TemplateQueue templateQueue = null;
            if (queueList != null && queueList.size() > 0) {
                templateQueue = queueList.get(0);
            } else {
                throw new BadRequestException("二级节点为空，请重新选择数据！");
            }
            //查找二级对应的节点
            //LambdaQueryWrapper<ProjectTemplate> twoLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTemplate.class).eq(ProjectTemplate::getTemplateId, templateQueue.getTemplateId());
            List<ProjectTemplate> templateList1 = templateMap.get(templateQueue.getTemplateId());
            ProjectTemplate template = null;
            if (templateList1 != null && templateList1.size() > 0) {
                template = templateList1.get(0);
            } else {
                throw new BadRequestException("对应的模版ID为空，请重新选择数据！");
            }
            ProjectNodeInfo projectNodeInfo = new ProjectNodeInfo();
            BeanUtils.copyProperties(template, projectNodeInfo);
            projectNodeInfo.setTemplateQueueId(templateQueue.getTemplateQueueId());
            projectNodeInfo.setProjectId(projectId);
            projectNodeInfo.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS2.getKey());
            projectNodeInfo.setIsDelete(false);
            projectNodeInfo.setIsEnabled(true);
            projectNodeInfo.setCreateBy(null);
            projectNodeInfo.setCreateTime(null);

            projectNodeInfos.add(projectNodeInfo);
            //log.info("time1{}{}:{}", i, i, System.currentTimeMillis() - s2);
            if (group.getNodeLevel() == 2) {
                //排序位置，如果存在模版表格，把模板表格后面的 nodeIndex重新排序
                Integer count = 0;
                //根据templateQueue查找子集
                //LambdaQueryWrapper<TemplateQueue> queueLambdaQueryWrapper = Wrappers.lambdaQuery(TemplateQueue.class).eq(TemplateQueue::getParentId, template.getTemplateId()).eq(TemplateQueue::getTemplateCode, templateCode);
                List<TemplateQueue> templateQueues = null;
                if (ObjectUtil.isNotEmpty(templateQueueMap2.get(template.getTemplateId() + ":" + templateCode))) {
                    templateQueues = templateQueueMap2.get(template.getTemplateId() + ":" + templateCode).stream()
                            .sorted(Comparator.comparing(TemplateQueue::getNodeIndex)).collect(Collectors.toList());
                }
                if (templateQueues != null && !templateQueues.isEmpty()) {
                    for (TemplateQueue queue : templateQueues) {
                        ProjectTemplate finalTemplate = template;
                        Long finalProjectId = projectId;
                        ProjectTemplate thirdtemplate = templateMap.get(queue.getTemplateId()).get(0);
                        ProjectNodeInfo thirdprojectNodeInfo = new ProjectNodeInfo();
                        BeanUtils.copyProperties(thirdtemplate, thirdprojectNodeInfo);
                        thirdprojectNodeInfo.setParentId(finalTemplate.getTemplateId());
                        thirdprojectNodeInfo.setTemplateQueueId(queue.getTemplateQueueId());
                        thirdprojectNodeInfo.setProjectId(finalProjectId);
                        thirdprojectNodeInfo.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
                        thirdprojectNodeInfo.setNodeIndex(queue.getNodeIndex());
                        thirdprojectNodeInfo.setIsDelete(thirdtemplate.getIsDelete());
                        thirdprojectNodeInfo.setIsEnabled(true);
                        thirdprojectNodeInfo.setCreateBy(null);
                        thirdprojectNodeInfo.setCreateTime(null);
                        if (ObjectUtils.isNotEmpty(queue.getNodeIndex())) {
                            thirdprojectNodeInfo.setNodeIndex(queue.getNodeIndex() + count);
                        }
                        assignment(projectId, thirdprojectNodeInfo, templateCode);
                        projectNodeInfos.add(thirdprojectNodeInfo);
                    }
                }
            }

        }
        return createProjectProjectGroupId;
    }

    private void assignment(Long projectId, ProjectNodeInfo nodeInfo, String templateCode) {
        if (JhSystemEnum.SupplierTypeEnum.SUPPLIER.getKey().equals(templateCode)) {
            SupplierInfo supplierInfo = supplierInfoService.getById(projectId);
            if (supplierInfo == null) {
                return;
            }
            if ("sup-00101002".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getSupNameCn());
            }
            if ("sup-00101003".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getSupplierType());
            }
            if ("sup-00101004".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getCompRegAddr());
            }
            if ("sup-00101005".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getPlatformProviderNot());
            }
            if ("sup-00101006".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getContact());
            }
            if ("sup-00101007".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getPhone());
            }
            if ("sup-00101008".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getEmail());
            }
            if ("sup-00101009".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getSupPostCode());
            }
            if ("sup-00101010".equals(nodeInfo.getNodeCode())) {
                JSONArray array = (JSONArray) JSONArray.toJSON(supplierInfo.getReferrer().split(","));
                nodeInfo.setRemark(array.toJSONString());
            }
            if ("sup-00101011".equals(nodeInfo.getNodeCode())) {
                List<Long> areas = supplierInfoRepository.getSupplierCity(supplierInfo.getId());
                if (!areas.isEmpty()) {
                    JSONArray array = (JSONArray) JSONArray.toJSON(areas);
                    nodeInfo.setRemark(array.toJSONString());
                }
            }
            if ("sup-00101029".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getRemark());
            }
            if ("sup-00101030".equals(nodeInfo.getNodeCode())) {
                if (StringUtils.isNotEmpty(supplierInfo.getServiceScope())) {
                    JSONArray array = (JSONArray) JSONArray.toJSON(supplierInfo.getServiceScope().split(","));
                    nodeInfo.setRemark(array.toJSONString());
                }
            }
            if ("sup-00101012".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getSupShortName());
            }
            if ("sup-00101013".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getBusinessLicense());
            }
            if ("sup-00101014".equals(nodeInfo.getNodeCode())) {
                if ("long".equals(supplierInfo.getBusinessExpDate()))
                    nodeInfo.setRemark(supplierInfo.getBusinessExpDate());
                else nodeInfo.setRemark("short");
            }
            if ("sup-00101033".equals(nodeInfo.getNodeCode())) {
                if (!"long".equals(supplierInfo.getBusinessExpDate()))
                    nodeInfo.setRemark(supplierInfo.getBusinessExpDate());
            }
            if ("sup-00101015".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getDecorationQualification());
            }
            if ("sup-********".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getDecorationExpDate());
            }
            if ("sup-********".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getTaxNumber());
            }
            if ("sup-********".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getBankNameCn());
            }
            if ("sup-********".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getBankAccountNumber());
            }
            if ("sup-********".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getBankNameEn());
            }
            if ("sup-********".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getBankBranchCityCn());
            }
            if ("sup-********".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierInfo.getMarketType());
            }
            List<SupplierCase> caseList = supplierInfoRepository.getSupplierCaseList(supplierInfo.getId());

        } else if (JhSystemEnum.SupplierTypeEnum.SUPPLIER_PM.getKey().equals(templateCode)) {
            SupplierPm supplierPm = supplierPmService.getById(projectId);
            if (supplierPm == null) {
                return;
            }
            if ("spm-********".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierPm.getPhone());
            }
            if ("spm-********".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierPm.getPmName());
            }
            if ("spm-********".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierPm.getPmGender());
            }
            if ("spm-********".equals(nodeInfo.getNodeCode())) {
                if (StringUtils.isNotEmpty(supplierPm.getSupplierPersonnelRole())) {
                    JSONArray array = (JSONArray) JSONArray.toJSON(supplierPm.getSupplierPersonnelRole().split(","));
                    nodeInfo.setRemark(array.toJSONString());
                }
            }
            if ("spm-00201006".equals(nodeInfo.getNodeCode())) {
                if (supplierPm.getSupplierId() != null) {
                    SupplierInfo supplierInfo = supplierInfoService.getById(supplierPm.getSupplierId());
                    nodeInfo.setRemark(supplierInfo == null ? "" : supplierInfo.getSupNameCn());
                }
            }
            if ("spm-00201017".equals(nodeInfo.getNodeCode())) {
                if (StringUtils.isNotEmpty(supplierPm.getFisyear())) {
                    JSONArray array = (JSONArray) JSONArray.toJSON(supplierPm.getFisyear().split(","));
                    nodeInfo.setRemark(array.toJSONString());
                }
            }
            if ("spm-00201007".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierPm.getProjectScope());
            }
            if ("spm-00201018".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierPm.getRemark());
            }
            if ("spm-00201008".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierPm.getPlanAmount());
            }
            if ("spm-00201009".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierPm.getAssignAmount());
            }
            if ("spm-00201009".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierPm.getAssignAmount());
            }
            String[] availableAmounts;
            if (StringUtils.isNotEmpty(supplierPm.getAvailableAmount())) {
                availableAmounts = supplierPm.getAvailableAmount().split(",");
            } else {
                availableAmounts = new String[]{"", ""};
            }
            if ("spm-00201010".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(availableAmounts[0]);
            }
            if ("spm-00201011".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(availableAmounts[1]);
            }
            if ("spm-00201013".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierPm.getFinishAmount());
            }
            if ("spm-00201014".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierPm.getCurrentQualityScore());
            }
            if ("spm-00201015".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierPm.getAvgQualityScore());
            }
            if ("spm-00201016".equals(nodeInfo.getNodeCode())) {
                nodeInfo.setRemark(supplierPm.getEvaluationScore());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createSupplierProject(JhSystemEnum.SupplierTypeEnum typeEnum, Long id) {
        if (id == null) {
            return Boolean.TRUE;
        }
        util.initialize(id);
        Long createProjectProjectGroupId = null;
        //查找相应模板
        LambdaQueryWrapper<TemplateGroup> templateGroupLambdaQueryWrapper = Wrappers.lambdaQuery(TemplateGroup.class).eq(TemplateGroup::getTemplateCode, typeEnum.getKey());
        List<TemplateGroup> templateGroups = templateGroupRepository.selectList(templateGroupLambdaQueryWrapper);
        List<ProjectGroup> groupInfoForCount = new LinkedList<>();
        //模板插入相应node表
        ArrayList<ProjectNodeInfo> projectNodeInfos = new ArrayList<>();
        //获取二级三级数据
        LambdaQueryWrapper<TemplateQueue> parentLambdaQueryWrapper = Wrappers.lambdaQuery(TemplateQueue.class).eq(TemplateQueue::getIsDelete, 0);
        List<TemplateQueue> templateQueueList = templateQueueRepository.selectList(parentLambdaQueryWrapper);
        Map<Long, List<TemplateQueue>> templateQueueMap = templateQueueList.stream().collect(Collectors.groupingBy(TemplateQueue::getTemplateQueueId));
        Map<String, List<TemplateQueue>> templateQueueMap2 = templateQueueList.stream().collect(Collectors.groupingBy(a -> a.getParentId() + ":" + a.getTemplateCode()));
        //查找二级对应的节点
        LambdaQueryWrapper<ProjectTemplate> twoLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTemplate.class);
        List<ProjectTemplate> templateList = projectTemplateRepository.selectList(twoLambdaQueryWrapper);
        Map<Long, List<ProjectTemplate>> templateMap = templateList.stream().collect(Collectors.groupingBy(ProjectTemplate::getTemplateId));
        final int size = templateGroups.size();
        LambdaQueryWrapper<ProjectTemplateApproveRelation> ptarLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTemplateApproveRelation.class);
        ptarLambdaQueryWrapper.eq(ProjectTemplateApproveRelation::getIsDelete, 0);
        List<ProjectTemplateApproveRelation> projectTemplateApproveRelations = projectTemplateApproveRelationRepository.selectList(ptarLambdaQueryWrapper);
        List<ProjectTemplateApproveRelationDto> relationDtos = projectTemplateApproveRelationMapper.toDto(projectTemplateApproveRelations);
        Map<String, List<ProjectTemplateApproveRelationDto>> relationDtoMap = relationDtos.stream().collect(Collectors.groupingBy(a -> a.getTemplateId() + ":" + a.getTemplateGroupId()));
        LambdaQueryWrapper<ApproveTemplateDetail> detailLambdaQueryWrapper = Wrappers.lambdaQuery(ApproveTemplateDetail.class);
        List<ApproveTemplateDetail> approveTemplateDetails = approveTemplateDetailRepository.selectList(detailLambdaQueryWrapper);
        Map<Long, List<ApproveTemplateDetail>> approveTemplateDetailMap = approveTemplateDetails.stream().collect(Collectors.groupingBy(ApproveTemplateDetail::getApproveTemplateId));

        final ArrayList<ProjectGroup> todoTaskList = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            //模板生成项目信息
            createProjectProjectGroupId = saveProjectNodeInfoByTemplate(templateGroups, i, id, createProjectProjectGroupId,
                    relationDtoMap, approveTemplateDetailMap, todoTaskList, groupInfoForCount, templateQueueMap, templateMap,
                    projectNodeInfos, templateQueueMap2, typeEnum.getKey());
        }
        //干系人
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            //未登陆
            groupInfoForCount.forEach(g -> {
                if (g.getNodeLevel() == 2) {
                    g.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS2.getKey());
                    ProjectStakeholders projectStakeholders = new ProjectStakeholders();
                    Role role = roleRepository.findRoleIdByCode("zbgysgly");
                    SupplierInfo resources;
                    if (typeEnum == JhSystemEnum.SupplierTypeEnum.SUPPLIER) {
                        resources = supplierInfoService.getById(id);
                    } else {
                        SupplierPm pm = supplierPmService.getById(id);
                        resources = supplierInfoService.getById(pm.getSupplierId());
                    }
                    if (resources.getSupplierType().contains("zxdw-xmjl")) {
//                        role = roleRepository.findRoleIdByCode("zbgysgly");
                        projectStakeholders.setUserId(1839501155110096896L);
                    } else {
//                        role = roleRepository.findRoleIdByCode("zbgysgly");
                        projectStakeholders.setUserId(1564536093785270123L);
                    }
                    g.setRoleCode(role.getRoleCode());
                    projectStakeholders.setProjectId(id);
                    projectStakeholders.setRoleId(role.getId());
                    projectStakeholders.setRoleName(role.getName());
                    projectStakeholders.setJoinTime(new Timestamp(System.currentTimeMillis()));
                    projectStakeholders.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
                    projectStakeholders.setIsApprove(Boolean.FALSE);
                    projectStakeholders.setCreateTime(new Timestamp(System.currentTimeMillis()));
                    projectStakeholders.setCreateBy("系统:0");
                    projectStakeholders.setIsDelete(Boolean.FALSE);
                    projectStakeholders.setRoleCode(role.getRoleCode());
                    projectStakeholdersService.create(projectStakeholders);
                }
            });
        } else {
            User user = userRepository.getOne(currentUserId);
            //已登陆
            groupInfoForCount.forEach(g -> {
                if (g.getNodeLevel() == 2) {
                    List<Role> roles = roleRepository.findRolesByUserId(currentUserId);
                    Role role;
                    role = getByCode(roles);
                    if (role == null) {
                        role = roleRepository.findRoleIdByCode("zbgysgly");
                        ProjectStakeholders projectStakeholders = new ProjectStakeholders();
                        SupplierInfo resources;
                        if (typeEnum == JhSystemEnum.SupplierTypeEnum.SUPPLIER) {
                            resources = supplierInfoService.getById(id);
                        } else {
                            SupplierPm pm = supplierPmService.getById(id);
                            resources = supplierInfoService.getById(pm.getSupplierId());
                        }
                        if (resources.getSupplierType().contains("zxdw-xmjl")) {
                            projectStakeholders.setUserId(1839501155110096896L);
                        } else {
                            projectStakeholders.setUserId(1564536093785270123L);
                        }
                        projectStakeholders.setProjectId(id);
                        projectStakeholders.setRoleId(role.getId());
                        projectStakeholders.setRoleName(role.getName());
                        projectStakeholders.setJoinTime(new Timestamp(System.currentTimeMillis()));
                        projectStakeholders.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
                        projectStakeholders.setIsApprove(Boolean.FALSE);
                        projectStakeholders.setCreateTime(new Timestamp(System.currentTimeMillis()));
                        projectStakeholders.setCreateBy(user.getNickName() + ":" + user.getId());
                        projectStakeholders.setIsDelete(Boolean.FALSE);
                        projectStakeholders.setRoleCode(role.getRoleCode());
                        projectStakeholdersService.create(projectStakeholders);
                        ProjectStakeholders projectStakeholders1 = new ProjectStakeholders();
                        projectStakeholders1.setProjectId(id);
                        projectStakeholders1.setUserId(user.getId());
                        projectStakeholders1.setRoleId(roles.get(0).getId());
                        projectStakeholders1.setRoleName(roles.get(0).getName());
                        projectStakeholders1.setJoinTime(new Timestamp(System.currentTimeMillis()));
                        projectStakeholders1.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
                        projectStakeholders1.setIsApprove(Boolean.FALSE);
                        projectStakeholders1.setCreateTime(new Timestamp(System.currentTimeMillis()));
                        projectStakeholders1.setCreateBy(user.getNickName() + ":" + user.getId());
                        projectStakeholders1.setIsDelete(Boolean.FALSE);
                        projectStakeholders1.setRoleCode(roles.get(0).getRoleCode());
                        projectStakeholdersService.create(projectStakeholders1);
                        g.setRoleCode(roles.get(0).getRoleCode());
                    } else {
                        ProjectStakeholders projectStakeholders = new ProjectStakeholders();
                        projectStakeholders.setProjectId(id);
                        projectStakeholders.setUserId(user.getId());
                        projectStakeholders.setRoleId(role.getId());
                        projectStakeholders.setRoleName(role.getName());
                        projectStakeholders.setJoinTime(new Timestamp(System.currentTimeMillis()));
                        projectStakeholders.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
                        projectStakeholders.setIsApprove(Boolean.FALSE);
                        projectStakeholders.setCreateTime(new Timestamp(System.currentTimeMillis()));
                        projectStakeholders.setCreateBy(user.getNickName() + ":" + user.getId());
                        projectStakeholders.setIsDelete(Boolean.FALSE);
                        projectStakeholders.setRoleCode(role.getRoleCode());
                        projectStakeholdersService.create(projectStakeholders);
                        g.setRoleCode(role.getRoleCode());
                    }
                }
                //审批
                List<ProjectTemplateApproveRelationDto> appRelation = projectTemplateApproveRelationService.getAppRelation(g.getTemplateId(), g.getTemplateGroupId());
                if (!appRelation.isEmpty()) {
                    projectApproveService.createApprove(projectGroupMapper.toDto(g));
                }
            });
        }
        projectGroupService.saveBatch(groupInfoForCount);
        // 任务3：创建待办任务
        for (ProjectGroup projectGroup : todoTaskList) {
            projectTaskService.generateTodoTask(projectGroup);
        }
        projectNodeInfoService.saveBatch(projectNodeInfos);
        //文件
        if (typeEnum == JhSystemEnum.SupplierTypeEnum.SUPPLIER) {
            SupplierInfo supplierInfo = supplierInfoService.getById(id);
            if (StringUtils.isNotEmpty(supplierInfo.getBusinessLicense())) {
                ProjectNodeInfo i = projectNodeInfoService.getOne(Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, id).eq(ProjectNodeInfo::getNodeCode, "sup-00101013"));
                if (i != null) {
                    localStorageRepository.update(null, Wrappers.lambdaUpdate(LocalStorage.class)
                            .in(LocalStorage::getId, supplierInfo.getBusinessLicense().split(","))
                            .set(LocalStorage::getNodeId, i.getNodeId()));
                }
            }
            if (StringUtils.isNotEmpty(supplierInfo.getDecorationQualification())) {
                ProjectNodeInfo i = projectNodeInfoService.getOne(Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, id).eq(ProjectNodeInfo::getNodeCode, "sup-00101015"));
                if (i != null) {
                    localStorageRepository.update(null, Wrappers.lambdaUpdate(LocalStorage.class)
                            .in(LocalStorage::getId, supplierInfo.getDecorationQualification().split(","))
                            .set(LocalStorage::getNodeId, i.getNodeId()));
                }
            }
            if (StringUtils.isNotEmpty(supplierInfo.getCaseStudy())) {
                JSONArray array = JSONObject.parseArray(supplierInfo.getCaseStudy());
                if (!array.isEmpty()) {
                    ProjectNodeInfo i = projectNodeInfoService.getOne(Wrappers.lambdaQuery(ProjectNodeInfo.class)
                            .eq(ProjectNodeInfo::getProjectId, id).eq(ProjectNodeInfo::getNodeCode, "sup-00101027"));
                    if (i != null) {
                        localStorageRepository.update(null, Wrappers.lambdaUpdate(LocalStorage.class)
                                .in(LocalStorage::getId, array.getJSONObject(0).getString("img").split(","))
                                .set(LocalStorage::getNodeId, i.getNodeId()));
                    }
                    ProjectNodeInfo i1 = projectNodeInfoService.getOne(Wrappers.lambdaQuery(ProjectNodeInfo.class)
                            .eq(ProjectNodeInfo::getProjectId, id).eq(ProjectNodeInfo::getNodeCode, "sup-00101028"));
                    if (i1 != null) {
                        localStorageRepository.update(null, Wrappers.lambdaUpdate(LocalStorage.class)
                                .in(LocalStorage::getId, array.getJSONObject(0).getString("file").split(","))
                                .set(LocalStorage::getNodeId, i1.getNodeId()));
                    }
                }
            }
        } else if (typeEnum == JhSystemEnum.SupplierTypeEnum.SUPPLIER_PM) {
            SupplierPm pm = supplierPmService.getById(id);
            if (StringUtils.isNotEmpty(pm.getProjectScope())) {
                ProjectNodeInfo i = projectNodeInfoService.getOne(Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, id).eq(ProjectNodeInfo::getNodeCode, "spm-00201007"));
                if (i != null) {
                    localStorageRepository.update(null, Wrappers.lambdaUpdate(LocalStorage.class)
                            .in(LocalStorage::getId, pm.getProjectScope().split(","))
                            .set(LocalStorage::getNodeId, i.getNodeId()));
                }
            }

            if (StringUtils.isNotEmpty(pm.getPlanAmount())) {
                ProjectNodeInfo i = projectNodeInfoService.getOne(Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, id).eq(ProjectNodeInfo::getNodeCode, "spm-00201008"));
                if (i != null) {
                    localStorageRepository.update(null, Wrappers.lambdaUpdate(LocalStorage.class)
                            .in(LocalStorage::getId, pm.getPlanAmount().split(","))
                            .set(LocalStorage::getNodeId, i.getNodeId()));
                }
            }
        }
        return Boolean.TRUE;
    }

    public Role getByCode(List<Role> roles) {
        for (Role role : roles) {
            if ("zbgysgly".equals(role.getRoleCode())) return role;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User insertProjectUser() {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        User one = userRepository.getOne(currentUserId);
        return one;
    }

    @Override
    public List<ConditionsRelation> getConditionsRelation() {
        LambdaQueryWrapper<ConditionsRelation> conditionsRelationLambdaQueryWrapper = Wrappers
                .lambdaQuery(ConditionsRelation.class);
        return conditionsRelationRepository.selectList(conditionsRelationLambdaQueryWrapper);
    }

    @Override
    public Boolean saveDrawingReviewData(List<ProjectInfoAbarbeitung> projectTableNodeInfos) {
        //项目整改信息的保存
        boolean saveOrUpdateBatch = projectInfoAbarbeitungService.saveOrUpdateBatch(projectTableNodeInfos);

        ProjectInfoAbarbeitung projectInfoAbarbeitung = projectTableNodeInfos.get(0);
        Long nodeId = projectInfoAbarbeitung.getFileNodeId();
        //根据 NodeCode查询出二级的
        util.initialize(projectNodeInfoService.getSubmeterProjectId(projectInfoAbarbeitung.getProjectId()));

        LambdaQueryWrapper<ProjectNodeInfo> wrapper = Wrappers
                .lambdaQuery(ProjectNodeInfo.class);
        wrapper.eq(ProjectNodeInfo::getProjectId, projectInfoAbarbeitung.getProjectId())
                .eq(ProjectNodeInfo::getNodeCode, projectInfoAbarbeitung.getNodeCode());
        ProjectNodeInfo projectNodeInfo = projectNodeInfoRepository.selectOne(wrapper);
        String remark = projectNodeInfo.getRemark();


        ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectById(projectNodeInfo.getParentId());
        Long currentUserId = SecurityUtils.getCurrentUserId();
        //查询审批人信息
        ProjectApproveQueryCriteria criteria = new ProjectApproveQueryCriteria();
        criteria.setNodeId(projectNodeInfo.getParentId());
        List<ProjectApproveDto> approveDtos = projectApproveService.listSubmitAndDetail(criteria);


        return saveOrUpdateBatch;
    }

    @Override
    public List<ProjectInfoAbarbeitung> getDrawingReviewData(Long projectId, String nodeCode, String nodeId) {
        LambdaQueryWrapper<ProjectInfoAbarbeitung> queryWrapper = Wrappers.lambdaQuery(ProjectInfoAbarbeitung.class)
                .eq(ProjectInfoAbarbeitung::getProjectId, projectId)
                .eq(ProjectInfoAbarbeitung::getNodeCode, nodeCode)
                .eq(ProjectInfoAbarbeitung::getFileNodeId, nodeId);
        List<ProjectInfoAbarbeitung> abarbeitungs = projectInfoAbarbeitungRepository.selectList(queryWrapper);
        return abarbeitungs;
    }

    @Override
    public List<ProjectNodeInfoApprovalRejection> getApproveRejectedVersions(Long projectId, String approveId) {
        //项目审批拒绝的版本,目前只存审图
        LambdaQueryWrapper<ProjectNodeInfoApprovalRejection> queryWrapper = Wrappers.lambdaQuery(ProjectNodeInfoApprovalRejection.class)
                .eq(ProjectNodeInfoApprovalRejection::getProjectId, projectId)
                .eq(ProjectNodeInfoApprovalRejection::getApproveId, approveId);
        List<ProjectNodeInfoApprovalRejection> rejections = projectNodeInfoApprovalRejectionRepository.selectList(queryWrapper);
        return rejections;
    }

    @Override
    public List<ProjectInfoAbarbeitungApprovalRejection> getDrawingReviewDataVersions(Long projectId, String nodeCode, String nodeId) {
        LambdaQueryWrapper<ProjectInfoAbarbeitungApprovalRejection> queryWrapper = Wrappers.lambdaQuery(ProjectInfoAbarbeitungApprovalRejection.class)
                .eq(ProjectInfoAbarbeitungApprovalRejection::getProjectId, projectId)
                .eq(ProjectInfoAbarbeitungApprovalRejection::getNodeCode, nodeCode)
                .eq(ProjectInfoAbarbeitungApprovalRejection::getFileNodeId, nodeId);
        List<ProjectInfoAbarbeitungApprovalRejection> abarbeitungs = approvalRejectionRepository.selectList(queryWrapper);
        return abarbeitungs;
    }

    @Override
    public Map<String, List<ProjectMaterialManagement>> getMaskPopupClick(Long projectId, String nodeCode) {
        //根据项目信息，查询项目动态列表存在数据么
        LambdaQueryWrapper<ProjectInfo> queryWrapper = Wrappers.lambdaQuery(ProjectInfo.class);
        queryWrapper.eq(ProjectInfo::getProjectId, projectId)
                .eq(ProjectInfo::getIsDelete, Boolean.FALSE)
                .last("limit 1");
        ProjectInfo projectInfo = projectInfoRepository.selectOne(queryWrapper);
        if (ObjectUtils.isNotEmpty(projectInfo)) {
            //查询项目房间表的数据 获取样板间的房间号
            List<ProjectRoom> modelRoom = projectRoomService.getModelRoom(projectId);
            if (ObjectUtil.isNotEmpty(modelRoom)) {
                //根据必采和非必采分组，查询模版
                Map<String, List<ProjectRoom>> objectListMap = modelRoom.stream().collect(Collectors.groupingBy(a -> a.getIsMandatory()));
                for (String s : objectListMap.keySet()) {
                    //查询模版，并根据分组后的房间号插入不同的模版
                    MaterialManagementGroupDto managementGroup = materialManagementGroupRepository.getMaterialManagementGroup(projectInfo.getBrandCode(), projectInfo.getProductCode(), nodeCode, s);
                    if (ObjectUtils.isNotEmpty(managementGroup)) {
                        for (ProjectRoom projectRoom : objectListMap.get(s)) {
                            //查询当前房间号的当前模版是否生成（必采/非必采）
                            LambdaQueryWrapper<ProjectMaterialManagement> wrapper = Wrappers.lambdaQuery(ProjectMaterialManagement.class)
                                    .eq(ProjectMaterialManagement::getProjectId, projectId)
                                    .eq(ProjectMaterialManagement::getRoomNumber, projectRoom.getRoomNum())
                                    .eq(ProjectMaterialManagement::getCurrentTemplateType, s)
                                    .eq(ProjectMaterialManagement::getIsDelete, Boolean.FALSE);
                            List<ProjectMaterialManagement> projectTableNodeInfos = projectMaterialManagementRepository.selectList(wrapper);
                            if (ObjectUtils.isEmpty(projectTableNodeInfos)) {
                                if (!projectMaterialManagementService.copyProjectMaterialManagement(projectId, projectRoom.getRoomNum(), managementGroup)) {
                                    throw new BadRequestException("数据错误！");
                                }
                            }
                        }
                    }
                }
                //处理不需要的房间号数据
                Map<String, List<ProjectMaterialManagement>> materialManagementList = projectMaterialManagementService.getProcessingData(projectId, nodeCode, modelRoom);
                return materialManagementList;
            }
        }
        return null;
    }

    /**
     * 获取开发系统同步
     *
     * @param projectHlmId
     * @return
     */
    public ProjectInfoExpansion getDevelopmentSystem(Integer projectHlmId) {
        final LambdaQueryWrapper<ProjectInfoExpansion> eq = Wrappers.lambdaQuery(ProjectInfoExpansion.class)
                .eq(ProjectInfoExpansion::getProjectId, projectHlmId);
        ProjectInfoExpansion expansion = projectInfoExpansionRepository.selectOne(eq);
        return expansion;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createProject(ProjectInfoDto resources, List<TemplateCollection> templateCollections) throws IllegalAccessException {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        Long storeMasterParam = resources.getStoreMasterId();
        String projectType1 = resources.getProjectType();
        Long projectId = snowflake.nextId();
        if (resources.getProjectId() != null) {
            projectId = resources.getProjectId();
        } else {
            resources.setProjectId(projectId);
        }

        String current = cn.hutool.core.date.DateUtil.format(new Date(), "yyyyMMdd");
        String projectType = resources.getProjectType();
        String key = PROJECT_NO_PREFIX + projectType + current;
        Integer value = (Integer) redisUtils.get(key);

        String num = "";
        String prefix = "";
        StoreMasterInfoDto storeMasterInfoDto = new StoreMasterInfoDto();
        if (KidsSystemEnum.ProcessType.NEW.getValue().equals(projectType)) {
            prefix = "XD";

            //入主档信息
            StoreMasterInfo storeMasterInfo = new StoreMasterInfo();
            BeanUtils.copyProperties(resources, storeMasterInfo);
            Snowflake snowflakemaster = IdUtil.getSnowflake(1, 1);
            Long masterId = snowflakemaster.nextId();
            //todo
            resources.setStoreId(masterId);
            storeMasterInfo.setProvince(resources.getProvince());
            storeMasterInfo.setProvinceName(resources.getProvinceName());
            storeMasterInfo.setCity(resources.getCity());
            storeMasterInfo.setCityName(resources.getCityName());
            storeMasterInfo.setCounty(resources.getCounty());
            storeMasterInfo.setCountyName(resources.getCountyName());
            storeMasterInfo.setStoreMasterId(masterId);
            storeMasterInfo.setIsEnabled(Boolean.TRUE);
            storeMasterInfo.setIsDelete(Boolean.FALSE);
            //刚创建的门店的状态为筹建中
            storeMasterInfo.setStoreStatus(KidsSystemEnum.StoreStatusEnum.PREPARE.getValue());
            storeMasterInfoService.saveOrUpdate(storeMasterInfo);

        } else if (KidsSystemEnum.ProcessType.CLOSE.getValue().equals(projectType)) {
            prefix = "CL";
            if (resources.getStoreMasterId() != null) {
                resources.setStoreId(resources.getStoreMasterId());
                StoreMasterInfoDto checkStore = storeMasterInfoService.findById(resources.getStoreMasterId());
                if (ObjectUtil.isNotEmpty(checkStore) && KidsSystemEnum.StoreStatusEnum.CLOSE.getValue().equals(checkStore.getStoreStatus())) {
                    throw new BadRequestException("该门店已闭店，不可再创建项目！");
                }
            }
            if (resources.getStoreNo() != null) {
                resources.setStoreNo(resources.getStoreNo());
            }
        } else if (KidsSystemEnum.ProcessType.MAJOR.getValue().equals(projectType)
                || KidsSystemEnum.ProcessType.MINOR.getValue().equals(projectType)
                || KidsSystemEnum.ProcessType.REFORM.getValue().equals(projectType)) {
            prefix = "GZ";

            //处理改造项目数据
            if (resources.getStoreMasterId() != null) {
                Long storeMasterId = resources.getStoreMasterId();
                storeMasterInfoDto = storeMasterInfoService.findById(storeMasterId);
                if (ObjectUtil.isNotEmpty(storeMasterInfoDto) && KidsSystemEnum.StoreStatusEnum.CLOSE.getValue().equals(storeMasterInfoDto.getStoreStatus())) {
                    throw new BadRequestException("该门店已闭店，不可再创建项目！");
                }
            }
        }


        if (null == value) {
            redisUtils.set(key, 1, 86400, TimeUnit.SECONDS);
            num = "01";
        } else {
            Long increment = redisUtils.increment(key);
            StringBuilder s = new StringBuilder(increment.toString());
            for (int i = s.length(); i < 2; i++) {
                s.insert(0, "0");
            }
            num = s.toString();
        }


        resources.setIsDelete(false);
        resources.setIsOverdue(false);
        //项目当前任务阶段 工程和设计节点 ,项目任务阶段code  筹建启动会和设计勘测
        resources.setProjectStatus(JhSystemEnum.ProjectStatusEnum.PROJECT_PREPARE.getKey());
        //城市code
        Long city = resources.getCity();
        ProjectInfo projectInfo = projectInfoMapper.toEntity(resources);
        if (storeMasterInfoDto != null && storeMasterInfoDto.getStoreMasterId() != null) {
            BeanUtils.copyProperties(storeMasterInfoDto, projectInfo, "createBy", "createTime", "updateBy", "updateTime", "isDelete", "isActive", "isEnabled");
            projectInfo.setStoreId(storeMasterInfoDto.getStoreMasterId());
        }

        //1立项信息入库
        projectInfo.setBrandCode(resources.getBrandCode());
        projectInfo.setProductCode(resources.getProductCode());
        //分配状态直接分为【已分配】
//        projectInfo.setAllocationStatus(AtourSystemEnum.AllocationStatus.ALLOCATED.getKey());
        this.saveOrUpdate(projectInfo);

        //2入干系人

        //住宿运营负责人
        if (resources.getHeadAccommodationOperations() != null) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.ZSYYFZR.getKey(), Long.parseLong(resources.getHeadAccommodationOperations()), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.ZSYYFZR.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.ZSYYFZR.getKey(), byCode.getId(), null);
                }
            }
        }

        //运维负责人
        if (resources.getHeadOperationMaintenance() != null) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.YWFZR.getKey(), Long.parseLong(resources.getHeadOperationMaintenance()), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.YWFZR.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.YWFZR.getKey(), byCode.getId(), null);
                }
            }
        }

        //HBG-供应链事业部VP
        if (resources.getSupplyChainBusinessUnit() != null) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.HBGFZR.getKey(), Long.parseLong(resources.getSupplyChainBusinessUnit()), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.HBGFZR.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.HBGFZR.getKey(), byCode.getId(), null);
                }
            }
        }

        //法务
        if (resources.getLegalManager() != null) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.FWJL.getKey(), Long.parseLong(resources.getLegalManager()), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.FWJL.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.FWJL.getKey(), byCode.getId(), null);
                }
            }
        }

        //共享支持设计负责人
        if (resources.getSharedSupportDesignLead() != null) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.GXZCSJFZR.getKey(), Long.parseLong(resources.getSharedSupportDesignLead()), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.GXZCSJFZR.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.GXZCSJFZR.getKey(), byCode.getId(), null);
                }
            }
        }

        //运营战区负责人
        if (resources.getOperationsTheaterLeader() != null) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.YYZQFZR.getKey(), resources.getOperationsTheaterLeader(), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.YYZQFZR.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.YYZQFZR.getKey(), byCode.getId(), null);
                }
            }
        }

        //开发经理
        if (resources.getDevelopmentManager() != null) {
            for (String developmentManager : resources.getDevelopmentManager().split(",")) {
                createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.KFJL.getKey(), Long.parseLong(developmentManager), null);
            }
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.KFJL.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.KFJL.getKey(), byCode.getId(), null);
                }
            }
        }

        //运营经理
        if (resources.getOperationsManager() != null) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.YYJL.getKey(), resources.getOperationsManager(), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.YYJL.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.YYJL.getKey(), byCode.getId(), null);
                }
            }
        }

        //开业经理
        if (resources.getOpeningManager() != null) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.KYJL.getKey(), resources.getOpeningManager(), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.KYJL.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.KYJL.getKey(), byCode.getId(), null);
                }
            }
        }

        //采购销售
        if (resources.getProcurementMarketing() != null) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.CGXS.getKey(), resources.getProcurementMarketing(), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.CGXS.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.CGXS.getKey(), byCode.getId(), null);
                }
            }
        }

        //营建区域负责人（总部中心负责人）
        if (resources.getConstructionAreaLeader() != null) {
            createProjectStakeholders(projectId, JhSystemEnum.JobEnum.ZBZXFZR.getKey(), resources.getConstructionAreaLeader(), null);
        } else {
            //插入当前用户的信息
            //createProjectStakeholders(projectId, JhSystemEnum.JobEnum.ZBZXFZR.getKey(), SecurityUtils.getCurrentUserId(), null);
            final List<User> byCodes = userRepository.findByRoleCodeA(JhSystemEnum.JobEnum.ZBZXFZR.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, JhSystemEnum.JobEnum.ZBZXFZR.getKey(), byCode.getId(), null);
                }
            }
        }
        //交付中心-共享支持
        if (resources.getDeliveryCenterSharedSupport() != null) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.JFZXGXZC.getKey(), resources.getDeliveryCenterSharedSupport(), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.JFZXGXZC.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.JFZXGXZC.getKey(), byCode.getId(), null);
                }
            }
        }

        //业主项目经理
        if (resources.getOwnerProjectManager() != null) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.YZXMJL.getKey(), resources.getOwnerProjectManager(), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.YZXMJL.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.YZXMJL.getKey(), byCode.getId(), null);
                }
            }
        }
        //交付中心-工程负责人
        if (ObjectUtil.isNotEmpty(resources.getDeliveryCenterEngineeringLeader())) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.JFZXGCFZR.getKey(), resources.getDeliveryCenterEngineeringLeader(), null);
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.ZLSH.getKey(), resources.getDeliveryCenterEngineeringLeader(), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.JFZXGCFZR.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.JFZXGCFZR.getKey(), byCode.getId(), null);
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.ZLSH.getKey(), byCode.getId(), null);
                }
            }
        }

        //交付中心-技术负责人
        if (ObjectUtil.isNotEmpty(resources.getDeliveryCenterTechnicalLeader())) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.JFZXJSFZR.getKey(), resources.getDeliveryCenterTechnicalLeader(), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.JFZXJSFZR.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.JFZXJSFZR.getKey(), byCode.getId(), null);
                }
            }
        }

        //交付中心-部门负责人
        if (ObjectUtil.isNotEmpty(resources.getDeliveryCenterDepartmentHead())) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.JFZXBMFZR.getKey(), resources.getDeliveryCenterDepartmentHead(), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.JFZXBMFZR.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.JFZXBMFZR.getKey(), byCode.getId(), null);
                }
            }
        }

        //开发战区负责人
        if (ObjectUtil.isNotEmpty(resources.getDevelopmentTheaterLeader())) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.KFZQFZR.getKey(), resources.getDevelopmentTheaterLeader(), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.KFZQFZR.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.KFZQFZR.getKey(), byCode.getId(), null);
                }
            }
        }

        //开发分区负责人
        if (ObjectUtil.isNotEmpty(resources.getDevelopmentZoneLeader())) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.KFFQFZR.getKey(), resources.getDevelopmentZoneLeader(), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.KFFQFZR.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.KFFQFZR.getKey(), byCode.getId(), null);
                }
            }
        }


        //特许经理
        if (resources.getFranchiseManager() != null) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.TXJL.getKey(), resources.getFranchiseManager(), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.TXJL.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.TXJL.getKey(), byCode.getId(), null);
                }
            }
        }


        //设计共管人员
        if (resources.getDesignCoManagementPersonnel() != null) {
            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.SJGGRY.getKey(), resources.getDesignCoManagementPersonnel(), null);
        } else {
            final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.SJGGRY.getKey(), resources.getCity());
            if (ObjectUtil.isNotEmpty(byCodes)) {
                for (User byCode : byCodes) {
                    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.SJGGRY.getKey(), byCode.getId(), null);
                }
            }
        }

        //发送营建对接启动的代办
        List<ProjectStakeholders> listByRoleCodes = projectStakeholdersRepository.selectListByRoleCodes(projectInfo.getProjectId(), AtourSystemEnum.constructionDockingStartedRole.CONSTRUCTION_DOCKING_STARTED_ROLE.getKey());
        Map<Long, Long> collect = new HashMap<>();
        for (ProjectStakeholders listByRoleCode : listByRoleCodes) {
            collect.put(listByRoleCode.getUserId(), listByRoleCode.getRoleId());
        }
        projectTaskInfoService.projectApproval(projectId, null, collect, KidsSystemEnum.TaskTypeEnum.TODO);

//        if (resources.getConstructionManager() != null) {
//            //工程经理
//            createProjectStakeholders(projectId, JhSystemEnum.JobEnum.GCJL.getKey(), resources.getConstructionManager(), null);
//        } else {
//            createProjectStakeholders(projectId, JhSystemEnum.JobEnum.GCJL.getKey(), 0L, null);
//        }
//        if (resources.getRegionNetManager() != null) {
//            //区域经理
//            createProjectStakeholders(projectId, JhSystemEnum.JobEnum.QYJL.getKey(), resources.getRegionNetManager(), null);
//        } else {
//            createProjectStakeholders(projectId, JhSystemEnum.JobEnum.QYJL.getKey(), 0L, null);
//        }
//        if (resources.getEngineerDirector() != null) {
//            //总部经理 即工程负责人
//            createProjectStakeholders(projectId, JhSystemEnum.JobEnum.ZBJL.getKey(), resources.getEngineerDirector(), null);
//        } else {
//            createProjectStakeholders(projectId, JhSystemEnum.JobEnum.ZBJL.getKey(), 0L, null);
//        }
        //设计师（设计负责人）
        //if (resources.getDesigner() != null) {
        //    createProjectStakeholders(projectId, JhSystemEnum.JobEnum.SJFZR.getKey(), resources.getDesigner(), null);
        //} else {
        //    final List<User> byCodes = userRepository.findByRoleCodeA(JhSystemEnum.JobEnum.SJFZR.getKey(),resources.getCity());
        //    if (ObjectUtil.isNotEmpty(byCodes)){
        //        for (User byCode: byCodes) {
        //            createProjectStakeholders(projectId, JhSystemEnum.JobEnum.SJFZR.getKey(), byCode.getId(), null);
        //        }
        //    }
        //}
        //弱电验收人员
        //if (resources.getWeakCurrentAcceptancePersonnel() != null) {
        //    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.RDYSRY.getKey(), resources.getWeakCurrentAcceptancePersonnel(), null);
        //} else {
        //    final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.RDYSRY.getKey(),resources.getCity());
        //    if (ObjectUtil.isNotEmpty(byCodes)){
        //        for (User byCode: byCodes) {
        //            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.RDYSRY.getKey(), byCode.getId(), null);
        //        }
        //    }
        //}
        //机电验收人员
        //if (resources.getMechanicalAndElectricalAcceptancePersonnel() != null) {
        //    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.JDYSRY.getKey(), resources.getMechanicalAndElectricalAcceptancePersonnel(), null);
        //} else {
        //    final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.JDYSRY.getKey(),resources.getCity());
        //    if (ObjectUtil.isNotEmpty(byCodes)){
        //        for (User byCode: byCodes) {
        //            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.JDYSRY.getKey(), byCode.getId(), null);
        //        }
        //    }
        //}
        //竣工验收人员
        //if (resources.getCompletionAcceptancePersonnel() != null) {
        //    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.JGYSRY.getKey(), resources.getCompletionAcceptancePersonnel(), null);
        //} else {
        //    final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.JGYSRY.getKey(),resources.getCity());
        //    if (ObjectUtil.isNotEmpty(byCodes)){
        //        for (User byCode: byCodes) {
        //            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.JGYSRY.getKey(), byCode.getId(), null);
        //        }
        //    }
        //} //竣工验收人员
        //        //if (resources.getCompletionAcceptancePersonnel() != null) {
        //        //    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.JGYSRY.getKey(), resources.getCompletionAcceptancePersonnel(), null);
        //        //} else {
        //        //    final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.JGYSRY.getKey(),resources.getCity());
        //        //    if (ObjectUtil.isNotEmpty(byCodes)){
        //        //        for (User byCode: byCodes) {
        //        //            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.JGYSRY.getKey(), byCode.getId(), null);
        //        //        }
        //        //    }
        //        //}
        //装修设计师
        //if (resources.getDeliveryCenterEngineeringLeader() != null) {
        //    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.ZXSJS.getKey(), resources.getDesignUnit(), null);
        //} else {
        //    final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.ZXSJS.getKey(),resources.getCity());
        //    if (ObjectUtil.isNotEmpty(byCodes)){
        //        for (User byCode: byCodes) {
        //            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.ZXSJS.getKey(), byCode.getId(), null);
        //        }
        //    }
        //}
        //机电设计师
        //if (resources.getDeliveryCenterEngineeringLeader() != null) {
        //    createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.JDSJS.getKey(), resources.getDesignUnit(), null);
        //} else {
        //    final List<User> byCodes = userRepository.findByRoleCodeA(AtourSystemEnum.engineeringRoleCodeEnum.JDSJS.getKey(),resources.getCity());
        //    if (ObjectUtil.isNotEmpty(byCodes)){
        //        for (User byCode: byCodes) {
        //            createProjectStakeholders(projectId, AtourSystemEnum.engineeringRoleCodeEnum.JDSJS.getKey(), byCode.getId(), null);
        //        }
        //    }
        //}
        return Boolean.TRUE;
    }

    private void generatingTemplate(ProjectInfo projectInfo, ProjectInfoDto resources, List<TemplateCollection> templateCollections) {
        if (ObjectUtil.isNotEmpty(resources.getDesigner()) && ObjectUtils.isNotEmpty(resources.getProjectManager())) {
            ProjectInfoExpansion developmentSystem = getDevelopmentSystem(resources.getProjectHlmId());
            //3根据门店信息查找模板
            //门店类型
            String storeType = resources.getStoreType();
            // 获取创建项目的projectGroupId
            Long createProjectProjectGroupId = null;

            ExecutorService executor = Executors.newFixedThreadPool(templateCollections.size());
            CountDownLatch count = new CountDownLatch(templateCollections.size());
            long start = System.currentTimeMillis();

            for (TemplateCollection collection : templateCollections) {
                //单个模板入projectNodeInfo立项
//              saveProject(collection,projectId,projectType,resources,createProjectProjectGroupId,projectInfo,projectType1,storeMasterParam,getNodeList);
                Long finalProjectId = projectInfo.getProjectId();
                executor.execute(new Runnable() {
                    @Override
                    public void run() {
                        saveProject(collection, finalProjectId, resources.getProjectType(), resources, createProjectProjectGroupId, projectInfo, resources.getProjectType(), resources.getStoreMasterId(), developmentSystem);
                        count.countDown();
                        upStairPlanEndDate(projectInfo);
                    }
                });
            }
            executor.shutdown();
            //查询特殊情况说明模版 入项目的特殊情况说明模版
            this.getSpecialCaseDescription(projectInfo.getProjectId());
            //【审图图纸】模板数据插到项目的审图表
            this.saveTemplateDrawing(projectInfo);
            //8个二级的证照管理
            this.cate(projectInfo.getProjectId());
            //查询【竣工系统自检】模板，并插入项目的竣工系统自检
            getProjectSystemSelfInspection(projectInfo);
            //新增竣工验收数据
            projectNodeInfoService.saveCompletionReceiptByNewVersion(resources.getProjectId());
            // 所有任务完成后的代码
            System.out.println("所有任务已完成");
            long end = System.currentTimeMillis(); //获取结束时间
            System.out.println("程序运行时间： " + (end - start) + "ms");
        }
    }

    private void getProjectSystemSelfInspection(ProjectInfo projectInfo) {
        final LambdaQueryWrapper<TemplateSystemSelfInspection> wrapper = Wrappers.lambdaQuery(TemplateSystemSelfInspection.class)
                .eq(TemplateSystemSelfInspection::getIsDelete, 0);
        final List<TemplateSystemSelfInspection> templateSystemSelfInspections = templateSystemSelfInspectionRepository.selectList(wrapper);
        final ArrayList<ProjectSystemSelfInspection> list = new ArrayList<>();
        final AtourSystemEnum.CompletionSelfInspectionSys[] values = AtourSystemEnum.CompletionSelfInspectionSys.values();
        for (int i = 0; i < values.length; i++) {
            int finalI = i;
            templateSystemSelfInspections.stream().forEach(templateSystemSelfInspection -> {
                final ProjectSystemSelfInspection inspection = new ProjectSystemSelfInspection();
                BeanUtil.copyProperties(templateSystemSelfInspection, inspection, CopyOptions.create().setIgnoreNullValue(true));
                inspection.setProjectId(projectInfo.getProjectId());
                inspection.setNodeCode(values[finalI].getKey());
                inspection.setSystemSelfInspectionId(IdUtil.getSnowflake(1, 1).nextId());
                list.add(inspection);
            });
        }
        projectSystemSelfInspectionService.saveBatch(list, 1000);
    }

    //【安全文明施工】的部分带出为不可编辑，需要修改动态表表头；
    private void updateSafeCivilizedConstructionHead(ProjectInfo projectInfo) {
        util.initialize(projectInfo.getProjectId());
        final AtourSystemEnum.SafeAndCivilizedConstruction[] head = AtourSystemEnum.SafeAndCivilizedConstruction.head();
        final ArrayList<ProjectNodeInfo> list = new ArrayList<>();
        for (int i = 0; i < head.length; i++) {
            final LambdaQueryWrapper<ProjectNodeInfo> queryWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectInfo.getProjectId())
                    .eq(ProjectNodeInfo::getNodeCode, head[i].getIntoKey());
            final ProjectNodeInfo projectNodeInfo = projectNodeInfoRepository.selectOne(queryWrapper);
            if (ObjectUtil.isNotEmpty(projectNodeInfo) && ObjectUtils.isNotEmpty(projectNodeInfo.getRemark())) {
                final String isCompile = updateJsonByRemark(projectNodeInfo);
                projectNodeInfo.setRemark(isCompile);
                list.add(projectNodeInfo);
            }
        }
        projectNodeInfoService.saveOrUpdateBatch(list);
    }

    private String updateJsonByRemark(ProjectNodeInfo nodeInfo) {
        cn.hutool.json.JSONArray jsonArray = new cn.hutool.json.JSONArray(nodeInfo.getRemark());
        for (int i = 0; i < jsonArray.size(); i++) {
            final cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(i);
            jsonObject.set("isCompile", 0);
        }
        return jsonArray.toString();
    }

    private void saveTemplateDrawing(ProjectInfo projectInfo) {
        final List<TemplateGroupExpand> templateGroupExpands = templateGroupExpandService.list();
        final ArrayList<ProjectGroupExpand> list = new ArrayList<>();
        templateGroupExpands.stream().forEach(templateGroupExpand -> {
            final ProjectGroupExpand projectGroupExpand = new ProjectGroupExpand();
            projectGroupExpand.setProjectId(String.valueOf(projectInfo.getProjectId()));
            BeanUtil.copyProperties(templateGroupExpand, projectGroupExpand);
            Snowflake snowflake = IdUtil.getSnowflake(1, 1);
            projectGroupExpand.setExpandId(snowflake.nextId());
            projectGroupExpand.setDrawingStatus(AtourSystemEnum.ReviewDrawingStatus.NULL.getKey());
            list.add(projectGroupExpand);
        });
        projectGroupExpandService.saveBatch(list);
    }

    @SneakyThrows
    @Override
//    @Transactional
    public Boolean createProjectSta(ProjectInfoInsertStaDto resources) {
        // 用户保存干系人
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }

        ProjectInfo projectInfo = projectInfoRepository.selectById(resources.getProjectId());
        try {
            projectInfo.setProjectPlanStart(java.sql.Date.valueOf(resources.getProjectPlanStart()));
            //,ljp 更新项目事件关系表中的时间
            TProjectPlaneventRelation relation = projectPlaneventRelationRepository.selectOne(new QueryWrapper<TProjectPlaneventRelation>()
                    .eq("project_id", resources.getProjectId()).eq("node_code", "eng-001"));
            TPlanEventInfo eventInfo = planEventInfoRepository.selectById(relation.getPlanEventId());
            if (relation != null && eventInfo != null) {
                relation.setPlanStartDate(new java.sql.Date(projectInfo.getProjectPlanStart().getTime() + (1000l * 60 * 60 * 24 * (eventInfo.getAllDuration() - eventInfo.getPlanEventDuration()))));
                relation.setPlanEndDate(new java.sql.Date(projectInfo.getProjectPlanStart().getTime() + (1000l * 60 * 60 * 24 * eventInfo.getAllDuration())));
                projectPlaneventRelationRepository.updateById(relation);
            }

        } catch (Exception e) {
        }

        BeanUtil.copyProperties(resources, projectInfo, CopyOptions.create().setIgnoreNullValue(true));
        this.createProjectStakeholder(projectInfo, resources);
        if (ObjectUtil.isNotEmpty(resources.getProjectManager()) && ObjectUtil.isNotEmpty(resources.getMechanicalAndElectricalEngineer()) && ObjectUtil.isNotEmpty(resources.getWeakCurrentEngineer())
                && ObjectUtil.isNotEmpty(resources.getSoftDecorationDesign()) && ObjectUtil.isNotEmpty(resources.getDesigner()) && ObjectUtil.isNotEmpty(resources.getFlightQualityInspectionPersonnel())) {
            //分配状态直接分为【已分配】
            projectInfo.setAllocationStatus(AtourSystemEnum.AllocationStatus.ALLOCATED.getKey());
        }
        projectInfo.setProjectPlanStart(StringUtils.isEmpty(resources.getProjectPlanStart()) ? null : java.sql.Date.valueOf(resources.getProjectPlanStart()));
        projectInfoRepository.updateById(projectInfo);

        //查询当前角色的筹建启动菜单有没有提交按钮
        if (!this.setPrepareSubmitted(currentUserId, projectInfo, resources.getProjectId())) {
            //当前登录人不能进行提交，说明已经完成了自己需要操作的数据，查询出对应的代办消息，并且把代办消息改为已完成
            List<ProjectTaskInfo> taskInfos = projectTaskInfoService.getProjectTaskInfoNodeName(resources.getProjectId(), currentUserId, "营建对接启动");
            taskInfos.forEach(s -> {
                projectTaskInfoService.projectApprovalOperation(s.getProjectTaskId());
            });
        }

        if (ObjectUtil.isNotEmpty(resources.getDesigner()) && ObjectUtils.isNotEmpty(resources.getProjectManager()) && ObjectUtil.isEmpty(projectInfo.getNodeTableName())) {
            //项目经理和设计师不为空的话，分配分表
            String noteInfoName = util.getNoteInfoName(Long.valueOf(RandomUtil.randomNumbers(6)));
            projectInfo.setNodeTableName(noteInfoName);
            projectInfo.setEstablishTime(DateUtil.stringToDate(DateUtil.getNowDate()));
            projectInfo.setTaskPhase(JhSystemEnum.TaskPhaseEnum.ENGINEERING.getKey() + "," + JhSystemEnum.TaskPhaseEnum.DESIGN.getKey());
            projectInfo.setProjectTaskPhase(JhSystemEnum.oneNodeCodeEnum.NODE_ENG001.getKey() + "," + JhSystemEnum.oneNodeCodeEnum.NODE_DES001.getKey());
            ProjectInfoDto infoDto = projectInfoMapper.toDto(projectInfo);
            List<TemplateCollection> templateCollectionList = projectInfoService.findTemplateByConditionCode(infoDto, null);
            final List<TemplateCollection> templateCollections = templateCollectionList.stream()
                    .filter(p -> !AtourSystemEnum.AdditionalTemplatesEnum.ENGINEERING_RECTIFCATION_ISSUES.getKey().equals(p.getTemplateCode())
                            && !AtourSystemEnum.AdditionalTemplatesEnum.SYSTEM_SELF_INSPECTION.getKey().equals(p.getTemplateCode())
                            && !AtourSystemEnum.AdditionalTemplatesEnum.DESIGN_SAMPLE_ROOM.getKey().equals(p.getTemplateCode())
                            && !AtourSystemEnum.AdditionalTemplatesEnum.ROUTINE_SELF_INSPECTION.getKey().equals(p.getTemplateCode())
                            && !p.getTemplateCode().contains(AtourSystemEnum.AdditionalTemplatesEnum.VISA_FILING.getKey())
                    )
                    .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(templateCollections)) {
                //模版不为空，起模版数据
                this.generatingTemplate(projectInfo, infoDto, templateCollections);
            } else {
                throw new BadRequestException("当前暂无模版数据，请联系管理员！");
            }
        }

        return true;
    }

    @Override
    public Boolean saveRefreshShopRotation(ProjectTemplateCodeDto dto) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            //currentUserId =1l;
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        TemplateCollection templateCollection = new TemplateCollection();
        templateCollection.setTemplateCode(dto.getTemplateCode());
        templateCollection.setTemplateId(dto.getTemplateId());

        LambdaQueryWrapper<ProjectInfo> wrapper = Wrappers.lambdaQuery(ProjectInfo.class)
                .eq(ProjectInfo::getProjectId, dto.getProjectId());
        ProjectInfo projectInfo = projectInfoService.getOne(wrapper);

        if (ObjectUtil.isNotEmpty(templateCollection) && ObjectUtil.isNotEmpty(projectInfo)) {
            ProjectInfoDto infoDto = new ProjectInfoDto();
            BeanUtil.copyProperties(projectInfo, infoDto, CopyOptions.create().setIgnoreNullValue(true));
            //当前焕新店轮次模版流程
            infoDto.setProjectRefreshShopRotationDto(dto);
            try {
                projectInfoService.saveProject(templateCollection, dto.getProjectId(),
                        KidsSystemEnum.ProjectTypeEnum.MAJOR.getValue(), infoDto, null
                        , projectInfo, projectInfo.getProjectType(), null, null);

            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    /**
     * 重新设定项目各个节点的计划开始时间和计划结束时间
     *
     * @return
     */
//    @Override
//    public boolean newProjectTime(Long projectId, String startTime) {
//        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//        Date start = null;
//        if (StringUtils.isEmpty(startTime)) {
//            try {
//                start = format.parse(startTime);
//            } catch (Exception e) {
//            }
//        } else {
//            start = new Date();
//        }
//        ProjectGroupQueryCriteria query = new ProjectGroupQueryCriteria();
//        query.setProjectId(projectId + "");
//        List<ProjectGroup> projectGroupDtoList = projectGroupService.list(QueryHelpPlus.getPredicate(ProjectGroup.class, query));
//        List<ProjectGroup> saveList = new ArrayList<>();
//        if (projectGroupDtoList != null && projectGroupDtoList.size() > 0) {
//            for (ProjectGroup projectGroup : projectGroupDtoList) {
//                if (ObjectUtils.isNotEmpty(projectGroup.getTotalDay()) && ObjectUtils.isNotEmpty(projectGroup.getPlanDay())) {
//                    //根据父级的工期和总工期，计算出当前二级的计划开始日期和计划结束日期
//                    this.upInitializeScheduleDate(projectGroup, start);
//                    saveList.add(projectGroup);
//                }
//            }
//        }
//        return projectGroupService.saveOrUpdateBatch(saveList);
//    }
    private void createProjectStakeholder(ProjectInfo resources, ProjectInfoInsertStaDto resources2) {
        //        项目经理（工程经理）
        if (ObjectUtil.isNotEmpty(resources.getProjectManager())) {
            createProjectStakeholders(resources.getProjectId(), JhSystemEnum.JobEnum.GCJL.getKey(), resources.getProjectManager(), null);
        }
        //机电工程师
        if (ObjectUtil.isNotEmpty(resources.getMechanicalAndElectricalEngineer())) {
            createProjectStakeholders(resources.getProjectId(), AtourSystemEnum.engineeringRoleCodeEnum.JDGCS.getKey(), resources.getMechanicalAndElectricalEngineer(), null);
        }
        // 弱电工程师
        if (ObjectUtil.isNotEmpty(resources.getWeakCurrentEngineer())) {
            createProjectStakeholders(resources.getProjectId(), AtourSystemEnum.engineeringRoleCodeEnum.RDGCS.getKey(), resources.getWeakCurrentEngineer(), null);
        }
        //软装设计
        if (ObjectUtil.isNotEmpty(resources.getSoftDecorationDesign())) {
            createProjectStakeholders(resources.getProjectId(), AtourSystemEnum.engineeringRoleCodeEnum.RZSJ.getKey(), resources.getSoftDecorationDesign(), null);
        }
        //设计单位（设计师）
        if (ObjectUtil.isNotEmpty(resources.getDesigner())) {
            createProjectStakeholders(resources.getProjectId(), JhSystemEnum.JobEnum.SJFZR.getKey(), resources.getDesigner(), null);
        }
        //飞行质检人员
        if (ObjectUtil.isNotEmpty(resources.getFlightQualityInspectionPersonnel())) {
            createProjectStakeholders(resources.getProjectId(), AtourSystemEnum.engineeringRoleCodeEnum.FXZJRY.getKey(), resources.getFlightQualityInspectionPersonnel(), null);
        }
        //设计共管人员
        if (ObjectUtil.isNotEmpty(resources2.getDesignCoManagementPersonnel())) {
            createProjectStakeholders(resources.getProjectId(), AtourSystemEnum.engineeringRoleCodeEnum.SJGGRY.getKey(), resources2.getDesignCoManagementPersonnel(), null);
        }
    }

    private void upStairPlanEndDate(ProjectInfo projectInfo) {
        //查询当前项目里的还没有完成的二级任务最近的计划结束日期
        LambdaQueryWrapper<ProjectGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjectGroup::getProjectId, projectInfo.getProjectId())
                .eq(ProjectGroup::getNodeLevel, 2)
                .ne(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1)
                .isNotNull(ProjectGroup::getPlanEndDate)
                .orderByAsc(ProjectGroup::getPlanEndDate).last("limit 1");
        ProjectGroup groupServiceOne = projectGroupService.getOne(wrapper);
        if (ObjectUtil.isNotEmpty(groupServiceOne)) {
            projectInfo.setStairPlanEndDate(groupServiceOne.getPlanEndDate());
        }
        projectInfoRepository.updateById(projectInfo);
    }

    private void getSpecialCaseDescription(Long projectId) {
        LambdaQueryWrapper<TemplateSpecialCaseDescription> wrapper = Wrappers.lambdaQuery();
        wrapper.in(TemplateSpecialCaseDescription::getIsDelete, 0);
        List<TemplateSpecialCaseDescription> list = templateSpecialCaseDescriptionService.list(wrapper);
        List<ProjectSpecialCaseDescription> descriptions = new ArrayList<>();
        for (TemplateSpecialCaseDescription caseDescription : list) {
            ProjectSpecialCaseDescription description = new ProjectSpecialCaseDescription();
            BeanUtil.copyProperties(caseDescription, description, CopyOptions.create().setIgnoreNullValue(true));
            description.setId(null);
            description.setProjectId(projectId);
            //默认类型不可删除
            description.setNotDelete("1");
            descriptions.add(description);
        }
        projectSpecialCaseDescriptionService.saveBatch(descriptions);
    }


    @Override
    public void run() {
        System.out.println("已立项");
    }

    /**
     * 某个模板的nodeIfo立项
     *
     * @return
     */
    @Override
    public Boolean saveProject(TemplateCollection collection, Long projectId, String projectType, ProjectInfoDto resources
            , Long createProjectProjectGroupId, ProjectInfo projectInfo, String projectType1, Long storeMasterParam, ProjectInfoExpansion developmentSystem) {
//        LambdaQueryWrapper<StoreTemplateRelation> storeTemplateRelationLambdaQueryWrapper = Wrappers
//                .lambdaQuery(StoreTemplateRelation.class);
//        storeTemplateRelationLambdaQueryWrapper
//                .eq(ObjectUtil.isNotEmpty(resources.getBrandCode()), StoreTemplateRelation::getStoreType, resources.getBrandCode())
//                .eq(ObjectUtil.isNotEmpty(projectType), StoreTemplateRelation::getProjectType, projectType)
//                .eq(ObjectUtil.isNotEmpty(collection.getTemplateCode()), StoreTemplateRelation::getTemplateCode, collection.getTemplateCode())
//                .last("limit 1");
//        StoreTemplateRelation storeTemplateRelation = storeTemplateRelationRepository.selectOne(storeTemplateRelationLambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(collection)
                || AtourSystemEnum.AdditionalTemplatesEnum.ENGINEERING_RECTIFCATION_ISSUES.getKey().equals(collection.getTemplateCode())
                || AtourSystemEnum.AdditionalTemplatesEnum.ROUTINE_SELF_INSPECTION.getKey().equals(collection.getTemplateCode())
                || AtourSystemEnum.AdditionalTemplatesEnum.SYSTEM_SELF_INSPECTION.getKey().equals(collection.getTemplateCode())
                || AtourSystemEnum.AdditionalTemplatesEnum.DESIGN_SAMPLE_ROOM.getKey().equals(collection.getTemplateCode())) {
            String templateCode = null;
            if (ObjectUtil.isNotEmpty(collection) && ObjectUtil.isNotEmpty(collection.getTemplateCode())) {
                templateCode = collection.getTemplateCode();
            }

            if (templateCode != null) {
                String lTwoDate = DateUtil.getNowDate();
                NodeInfo nodeInfo = new NodeInfo();

                if (resources.getProjectCreateDate() != null) {
                    lTwoDate = DateUtil.changeDate(resources.getProjectCreateDate());
                    nodeInfo.setBegin(lTwoDate);
                }

                //3查找相应模板
                LambdaQueryWrapper<TemplateGroup> queryWrapper = Wrappers.lambdaQuery(TemplateGroup.class);
                queryWrapper.eq(TemplateGroup::getTemplateCode, collection.getTemplateCode());
                if (ObjectUtil.isNotEmpty(collection.getNodeCode()) && ObjectUtil.isNotEmpty(collection.getParentId())) {
                    Integer[] level = {0};
                    queryWrapper.and(wrapper -> wrapper.in(TemplateGroup::getNodeLevel, level)
                            .or().eq(TemplateGroup::getNodeCode, collection.getNodeCode())
                            .or().eq(TemplateGroup::getTemplateId, collection.getParentId()));
                }
                List<TemplateGroup> templateGroups = templateGroupRepository.selectList(queryWrapper);

                List<ProjectGroup> groupInfoForCount = new LinkedList<>();

                //质量管理模板的项目ID
                if (ObjectUtil.isNotEmpty(resources.getQualityControlDto())) {
                    projectId = resources.getQualityControlDto().getQualityControlId();
                }
                //施工日志模板的项目ID
                if (ObjectUtil.isNotEmpty(resources.getProjectConstructionLog())) {
                    projectId = resources.getProjectConstructionLog().getId();
                }
                //竣工系统自检项目ID
                if (ObjectUtil.isNotEmpty(resources.getProjectSystemSelfInspection())) {
                    projectId = resources.getProjectSystemSelfInspection().getSystemSelfInspectionId();
                }
                //竣工常规项目自检项目ID
                if (ObjectUtil.isNotEmpty(resources.getRoutineSelfInspection())) {
                    projectId = resources.getRoutineSelfInspection().getQualityControlId();
                }
                //设计样板间验收项目ID
                if (ObjectUtil.isNotEmpty(resources.getProjectTableNodeInfo())) {
                    projectId = resources.getProjectTableNodeInfo().getNodeId();
                }
                //签证报备项目ID
                if (ObjectUtil.isNotEmpty(resources.getProjectVisaFiling())) {
                    projectId = resources.getProjectVisaFiling().getId();
                }
                //工程抽查项目ID
                if (ObjectUtil.isNotEmpty(resources.getProjectSpotCheck())) {
                    projectId = resources.getProjectSpotCheck().getId();
                }

                //查询当前模版在该项目中存在了几轮次
                String roundMarking = this.getRoundMarking(projectId, templateCode);
                //4模板插入相应node表
                ArrayList<ProjectNodeInfo> projectNodeInfos = new ArrayList<>();
                for (TemplateGroup group : templateGroups) {
                    ProjectGroup projectGroup = new ProjectGroup();
                    BeanUtils.copyProperties(group, projectGroup);
                    projectGroup.setProjectId(projectId);
                    projectGroup.setRoundMarking(roundMarking);

                    //工程抽查不为空，清空工程抽查的前置任务
                    if (ObjectUtil.isNotEmpty(resources.getProjectSpotCheck())) {
                        projectGroup.setFrontWbsConfig(null);
                        projectGroup.setIsShow(null);
                        projectGroup.setNodeName(projectGroup.getNodeName() + "[抽查第" + resources.getProjectSpotCheck().getGroupIndex() + "轮]");
                    }

                    String nodeStatus = null;
                    if (projectGroup.getTemplateCode().equals(JhSystemEnum.TaskPhaseEnum.DEEPENING_PLAN.getKey()) && !projectGroup.getNodeCode().equals("dep-00231")) {
                        //深化方案模板的状态为 --
                        nodeStatus = JhSystemEnum.NodeStatusEnum.NODE_STATUS5.getKey();
                    } else if (projectGroup.getTemplateCode().contains(JhSystemEnum.TaskPhaseEnum.VISA_FILING.getKey())) {
//                        //签证报备模版的状态为 审批中
//                        nodeStatus = JhSystemEnum.NodeStatusEnum.NODE_STATUS6.getKey();
//                        projectGroup.setIsOpen(Boolean.TRUE);
                        nodeStatus = JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey();
                        if (ObjectUtil.isEmpty(projectGroup.getRoleCode()) && ObjectUtil.isNotEmpty(resources.getProjectVisaFiling())) {
                            projectGroup.setRoleCode(resources.getProjectVisaFiling().getRoleCode());
                        }
                    } else if (projectGroup.getTemplateCode().contains(JhSystemEnum.TaskPhaseEnum.DECISION_MAKING.getKey())) {
                        //决策信息模板的状态为 已完成
                        nodeStatus = JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey();
                        projectGroup.setIsOpen(Boolean.TRUE);
                        //【决策信息】不需要任务信息
                        projectGroup.setIsNotTask(true);
                        projectGroup.setNodeIsfin(true);
                    } else {
                        nodeStatus = JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey();
                    }
                    projectGroup.setNodeStatus(nodeStatus);
                    projectGroup.setIsDelete(false);
                    projectGroup.setIsEnabled(true);
                    projectGroup.setCreateBy(null);
                    projectGroup.setCreateTime(null);
                    Snowflake snowflake1 = IdUtil.getSnowflake(1, 1);
                    Long projectGroupId = snowflake1.nextId();
                    projectGroup.setProjectGroupId(projectGroupId);
//                    if (projectGroup.getNodeCode().equals("con-00101") || projectGroup.getNodeCode().equals("con-00146")) {
//                        createProjectProjectGroupId = projectGroupId;
//                    }
                    if (ObjectUtils.isNotEmpty(group.getTotalDay()) && ObjectUtils.isNotEmpty(group.getPlanDay())) {
                        //，ljp 根据父级的工期和总工期，计算出当前二级的计划开始日期和计划结束日期
                        this.upInitializeScheduleDate(projectGroup, group);
                    }


                    //解析紧前json
                    if (group.getNodeLevel() == 2) {
                        // 审批模板入库
                        List<ProjectTemplateApproveRelationDto> appRelations = projectTemplateApproveRelationService.getAppRelation(group.getTemplateId(), group.getTemplateGroupId());
                        //若存在审批节点，则复制当前节点数据
                        if (ObjectUtils.isNotNull(appRelations)) {
                            //projectAppTemplateService.copyApproveTemplate(projectGroup, appRelations.get(0));

                            //当前二级的状态为已提交的话，修改为审批中
                            if (projectGroup.getNodeStatus().equals(JhSystemEnum.NodeStatusEnum.NODE_STATUS2.getKey())) {
                                ProjectGroupDto projectGroupDto = new ProjectGroupDto();
                                BeanUtil.copyProperties(projectGroup, projectGroupDto);
                                //创建审批
                                projectApproveService.createApprove(projectGroupDto);
                                //发送消息通知-提交-需要审批
                                //projectNoticeService.generateNotice(Long.valueOf(projectGroup.getProjectGroupId()), projectGroup, JhSystemEnum.MessageTemplate.MB1000004, initiatUser);
                            }
                        } else if (projectGroup.getNodeStatus().equals(JhSystemEnum.NodeStatusEnum.NODE_STATUS2.getKey())) {
                            projectGroup.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
                        }

                        String frontWbsConfig = projectGroup.getFrontWbsConfig();
                        if (ObjectUtil.isEmpty(frontWbsConfig) && projectGroup.getNodeStatus().equals(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey())) {
                            //出待办
                            projectTaskService.generateTodoTask(projectGroup);
                            projectGroup.setIsOpen(Boolean.TRUE);
                            projectGroup.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS6.getKey());
                        }
                    }
                    groupInfoForCount.add(projectGroup);

//                    //查询当前节点是否存在联合二级，存在则保存进项目中
//                    projectJointTaskOnfigurationService.createJointTaskOnfiguration(projectGroup.getNodeCode(), projectGroup.getProjectId());

                    if (group.getTemplateQueueId() != null) {
                        //获取二级三级数据
                        LambdaQueryWrapper<TemplateQueue> parentLambdaQueryWrapper = Wrappers.lambdaQuery(TemplateQueue.class)
                                .eq(TemplateQueue::getTemplateQueueId, group.getTemplateQueueId());
                        TemplateQueue templateQueue = templateQueueRepository.selectOne(parentLambdaQueryWrapper);
                        if (templateQueue == null) {
                            throw new BadRequestException("二级节点为空，请重新选择数据！");
                        }
                        //查找二级对应的节点
                        LambdaQueryWrapper<ProjectTemplate> twoLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTemplate.class)
                                .eq(ProjectTemplate::getTemplateId, templateQueue.getTemplateId());
                        ProjectTemplate template = projectTemplateRepository.selectOne(twoLambdaQueryWrapper);
                        ProjectNodeInfo projectNodeInfo = new ProjectNodeInfo();
                        BeanUtils.copyProperties(template, projectNodeInfo);
                        projectNodeInfo.setTemplateQueueId(templateQueue.getTemplateQueueId());
                        projectNodeInfo.setProjectId(projectId);
                        projectNodeInfo.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
                        projectNodeInfo.setIsDelete(false);
                        projectNodeInfo.setIsEnabled(true);
                        projectNodeInfo.setCreateBy(null);
                        projectNodeInfo.setCreateTime(null);
                        projectNodeInfo.setRoundMarking(roundMarking);

                        projectNodeInfos.add(projectNodeInfo);
                        if (group.getNodeLevel() == 2) {
                            //排序位置，如果存在模版表格，把模板表格后面的 nodeIndex重新排序
                            Integer count = 0;
                            //根据templateQueue查找子集
                            LambdaQueryWrapper<TemplateQueue> queueLambdaQueryWrapper = Wrappers
                                    .lambdaQuery(TemplateQueue.class)
                                    .eq(TemplateQueue::getParentId, template.getTemplateId())
                                    .eq(TemplateQueue::getTemplateCode, templateCode)
                                    .orderByAsc(TemplateQueue::getNodeIndex);
                            List<TemplateQueue> templateQueues = templateQueueRepository.selectList(queueLambdaQueryWrapper);
                            for (TemplateQueue queue : templateQueues) {
                                LambdaQueryWrapper<ProjectTemplate> thirdLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTemplate.class).eq(ProjectTemplate::getTemplateId, queue.getTemplateId());
                                ProjectTemplate thirdtemplate = projectTemplateRepository.selectOne(thirdLambdaQueryWrapper);
                                ProjectNodeInfo thirdprojectNodeInfo = new ProjectNodeInfo();
                                BeanUtils.copyProperties(thirdtemplate, thirdprojectNodeInfo);
                                thirdprojectNodeInfo.setParentId(template.getTemplateId());
                                thirdprojectNodeInfo.setTemplateQueueId(queue.getTemplateQueueId());
                                thirdprojectNodeInfo.setProjectId(projectId);
                                thirdprojectNodeInfo.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
                                thirdprojectNodeInfo.setNodeIndex(queue.getNodeIndex());
                                thirdprojectNodeInfo.setIsDelete(thirdtemplate.getIsDelete());
                                thirdprojectNodeInfo.setIsEnabled(true);
                                thirdprojectNodeInfo.setCreateBy(null);
                                thirdprojectNodeInfo.setCreateTime(null);
                                thirdprojectNodeInfo.setRoundMarking(roundMarking);
                                if (ObjectUtils.isNotEmpty(queue.getNodeIndex())) {
                                    thirdprojectNodeInfo.setNodeIndex(queue.getNodeIndex() + count);
                                }
                                //模版表格
                                if (ObjectUtils.isNotEmpty(thirdtemplate.getNodeType()) && thirdtemplate.getNodeType().equals(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST
                                        .getValue())) {
                                    count += saveDynamicConditionList(projectId, resources, projectNodeInfos, template, count, queue, thirdtemplate);
                                }

                                if (thirdprojectNodeInfo.getNodeCode().contains(AtourSystemEnum.engineeringRectificationIssuesNodeTwo.ERI00103.getKey()) && ObjectUtils.isNotEmpty(resources.getQualityControlDto())) {
                                    //同步工程整改问题
                                    this.breakOutQualityControl(thirdprojectNodeInfo, resources);
                                }
                                if (thirdprojectNodeInfo.getNodeCode().contains(AtourSystemEnum.engineeringRectificationIssuesNodeTwo.RSI00107.getKey())
                                        && ObjectUtils.isNotEmpty(resources.getProjectSystemSelfInspection())) {
                                    //同步竣工系统自检
                                    this.breakOutSystemSelfInspection(thirdprojectNodeInfo, resources.getProjectSystemSelfInspection());
                                }
                                if (thirdprojectNodeInfo.getNodeCode().contains(AtourSystemEnum.engineeringRectificationIssuesNodeTwo.SSI00105.getKey())
                                        && ObjectUtils.isNotEmpty(resources.getRoutineSelfInspection())) {
                                    //同步竣工常规项目自检
                                    this.breakOutRoutineSelfInspection(thirdprojectNodeInfo, resources.getRoutineSelfInspection());
                                }
                                if (thirdprojectNodeInfo.getNodeCode().contains(AtourSystemEnum.engineeringRectificationIssuesNodeTwo.DSR00109.getKey())
                                        && ObjectUtils.isNotEmpty(resources.getProjectTableNodeInfo())) {
                                    //同步设计样板间验收
                                    this.breakOutDesignSampleRoom(thirdprojectNodeInfo, resources.getProjectTableNodeInfo());
                                }
                                if (thirdprojectNodeInfo.getNodeCode().contains(AtourSystemEnum.EngineeringNodeTow.ENG00103.getKey()) && ObjectUtil.isNotEmpty(developmentSystem)) {
                                    //将开发系统同步到三级节点
                                    try {
                                        insertDevEng(developmentSystem, thirdprojectNodeInfo, resources);
                                    } catch (IOException e) {
                                        e.printStackTrace();
                                    }
                                    insertDevEngB(developmentSystem, thirdprojectNodeInfo, resources);
                                }

                                if (thirdprojectNodeInfo.getNodeCode().contains(AtourSystemEnum.EngineeringNodeTow.ENG00129.getKey())) {
                                    //同步竣工验收申请
                                    this.breakOutApplicationCompletionAcceptance(thirdprojectNodeInfo, resources);
                                }

                                if (ObjectUtil.isNotEmpty(resources.getProjectVisaFiling())) {
                                    //将签证报备数据 同步到三级节点
                                    synchronizeVisaFiling(thirdprojectNodeInfo, resources.getProjectVisaFiling(), resources);
                                }

                                if (ObjectUtil.isNotEmpty(resources.getProjectSpotCheck())) {
                                    //将被抽查的任务，同步到工程抽查的三级节点中
                                    synchronizeSpotCheck(thirdprojectNodeInfo, resources.getProjectSpotCheck(), resources);
                                }

//                                if (ObjectUtil.isNotEmpty(resources.getProjectConstructionLog())) {
//                                    //施工日志的带出
//                                    this.broughtOutCol(thirdprojectNodeInfo,resources.getProjectConstructionLog());
//                                }


                                if (thirdprojectNodeInfo.getNodeCode().contains(AtourSystemEnum.DesignNodeTow.DES00101.getKey()) && ObjectUtil.isNotEmpty(developmentSystem)) {
                                    insertDevDes(developmentSystem, thirdprojectNodeInfo, resources);
                                }

//                                if (thirdprojectNodeInfo.getNodeCode().contains(AtourSystemEnum.DecisionMakingTwo.DEC00501.getKey())) {
//                                    //insertDevDec(developmentSystem, thirdprojectNodeInfo);
//                                }
//
//                                if (thirdprojectNodeInfo.getNodeCode().contains(AtourSystemEnum.DecisionMakingTwo.DEC00701.getKey())) {
//                                    //insertDevDec(developmentSystem, thirdprojectNodeInfo);
//                                }

                                if (!ObjectUtil.isNotEmpty(resources.getQualityControlDto())) {
                                    //立项数据复制
                                    thirdprojectNodeInfo = copyProjectNodeInfo(thirdprojectNodeInfo, resources);
                                }
//                                //固定值录入
//                                if (JhSystemEnum.NodeCodeSEEnum.isCreateApplyNo(group.getNodeCode())) {
//                                    thirdprojectNodeInfo = saveContractApplyInfo(thirdprojectNodeInfo);
//                                }

                                //项目勘察中的项目编号从项目表中拿值
                                if (JhSystemEnum.NodeCodeSEEnum.NODE_102.getKey().equals(projectNodeInfo.getNodeCode())) {
                                    if (JhSystemEnum.NodeCodeEnum.NODE_10202.getKey().equals(thirdprojectNodeInfo.getNodeCode())) {
                                        thirdprojectNodeInfo.setRemark(projectInfo.getProjectNo());
                                    }
                                }
                                //门店主档涉及相关信息入pro_info
                                if (projectType1.equals(KidsSystemEnum.ProcessType.MAJOR.getValue()) || projectType1.equals(KidsSystemEnum.ProcessType.MINOR.getValue()) || projectType1.equals(KidsSystemEnum.ProcessType.REFORM.getValue())) {
                                    projectToMasterService.byStoreMasterUpdateProjectInfo(storeMasterParam, queue.getNodeCode(), thirdprojectNodeInfo, queue);
                                }


                                projectNodeInfos.add(thirdprojectNodeInfo);
                                if (projectNodeInfos.size() >= 1000) {
                                    //分表查询
                                    util.setProjectTableName(projectInfo.getProjectId());
                                    projectNodeInfoService.saveBatch(projectNodeInfos);
                                    projectNodeInfos.clear();
                                }

                            }


                        }


                    }

                }
//                if (ObjectUtil.isNotEmpty(projectTaskPhases)) {
//                    List<String> collect = projectTaskPhases.stream().distinct().collect(Collectors.toList());
//                    projectInfo.setProjectTaskPhase(String.join(",", collect));
//                }
//                if (ObjectUtil.isNotEmpty(taskPhase)) {
//                    List<String> collect = taskPhase.stream().distinct().collect(Collectors.toList());
//                    projectInfo.setTaskPhase(String.join(",", collect));
//                }

                projectInfoService.update(projectInfo); //修改项目
                projectGroupService.saveBatch(groupInfoForCount);

                //分表查询
                util.setProjectTableName(projectInfo.getProjectId());
                projectNodeInfoService.saveBatch(projectNodeInfos);
                //【安全文明施工】的部分带出为不可编辑，需要修改动态表表头；
                if (JhSystemEnum.TaskPhaseEnum.ENGINEERING.getKey().equals(templateCode)
                        || templateCode.contains(JhSystemEnum.TaskPhaseEnum.ENGINEERING.getKey() + "(")) {
                    this.updateSafeCivilizedConstructionHead(projectInfo);
                    //查询【竣工自检】的计划完成时间，并将前60天的时间带出到【封板确认】的【现长预计到岗时间】
                    getSealingConfirmationDate(projectId);
                    //将项目经理和设计师带出到工程节点
                    saveSjsXmjl(projectId);
                    //将弱电工程师带出到【弱电验收】的验收人eng-00139010
                    final ProjectInfoDto dto = projectInfoService.findById(projectId);
                    if (ObjectUtil.isNotEmpty(dto)) {
                        saveSystemPersonelByInfo(projectId, AtourSystemEnum.WeakCurrentAcceptance.ENG00139010.getKey()
                                , dto, dto.getWeakCurrentEngineer());
                    }
                    //入业主项目经理到ENG00141007
                    savePersonelBySta(projectId, AtourSystemEnum.WeakCurrentAcceptance.ENG00141007.getKey()
                            , AtourSystemEnum.engineeringRoleCodeEnum.YZXMJL.getKey());
                }


                if (AtourSystemEnum.AdditionalTemplatesEnum.SYSTEM_SELF_INSPECTION.getKey().equals(templateCode)
                        || AtourSystemEnum.AdditionalTemplatesEnum.ROUTINE_SELF_INSPECTION.getKey().equals(templateCode)
                        || AtourSystemEnum.AdditionalTemplatesEnum.DESIGN_SAMPLE_ROOM.getKey().equals(templateCode)
                        || AtourSystemEnum.AdditionalTemplatesEnum.ENGINEERING_RECTIFCATION_ISSUES.getKey().equals(templateCode)) {
                    //对新模板校验去重
                    newTemplateDis(projectId);
                    //拷贝一份主项目的干系人
                    //copyProjectSta(projectId);
                    //附件带出eri-00103008；ssi-00105008
                    broughtOutQuaFile(projectId, templateCode, AtourSystemEnum.AdditionalTemplatesEnum.ENGINEERING_RECTIFCATION_ISSUES, "eri-00103008");
                    broughtOutQuaFile(projectId, templateCode, AtourSystemEnum.AdditionalTemplatesEnum.SYSTEM_SELF_INSPECTION, "ssi-00105008");
                }
                if (ObjectUtil.isNotEmpty(resources.getProjectVisaFiling())) {
                    //将签证报备数据 同步到三级节点
                    synchronizeVisaFilingFile(projectNodeInfos, resources.getProjectVisaFiling());
                }
                if (ObjectUtil.isNotEmpty(resources.getProjectSpotCheck())) {
                    //将被抽查的任务，同步到工程抽查的三级节点中
                    synchronizeSpotCheckFile(projectNodeInfos, resources.getProjectSpotCheck());
                }

//                    projectNodeInfoRepository.insert(projectNodeInfo);
                //todo 暂时去除 2023.09.06
                // 提交项目节点
             /*   ProjectNodeInfoDto projectNodeInfoD = new ProjectNodeInfoDto();
                projectNodeInfoD.setProjectId(projectId.toString());
                projectNodeInfoD.setProjectGroupId(createProjectProjectGroupId.toString());
                projectNodeInfoService.submit(projectNodeInfoD);*/
            } else {
                return Boolean.FALSE;
            }
        }
        System.out.println("任务已完成");
        return Boolean.TRUE;
    }

    private String getRoundMarking(Long projectId, String templateCode) {
        if (ObjectUtil.isNotEmpty(templateCode) && templateCode.equals(JhSystemEnum.TaskPhaseEnum.RENEWAL_REFURBISHMENT.getKey())) {
            LambdaQueryWrapper<ProjectGroup> groupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class);
            groupLambdaQueryWrapper.eq(ProjectGroup::getProjectId, projectId);
            groupLambdaQueryWrapper.eq(ProjectGroup::getTemplateCode, templateCode);
            groupLambdaQueryWrapper.eq(ProjectGroup::getNodeLevel, 0);
            Long aLong = projectGroupRepository.selectCount(groupLambdaQueryWrapper);
            if (ObjectUtil.isEmpty(aLong)) {
                return "1";
            } else {
                return String.valueOf(aLong + 1);
            }
        }
        return null;
    }

    private void broughtOutCol(ProjectNodeInfo nodeInfo, ProjectConstructionLogDto resources) {
        util.initialize(resources.getProjectId());

        final LambdaQueryWrapper<ProjectInfo> queryWrapper = Wrappers.lambdaQuery(ProjectInfo.class)
                .eq(ProjectInfo::getProjectId, resources.getProjectId());
        final ProjectInfo infoDto = projectInfoService.getOne(queryWrapper);


        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.BroughtOutCol.COL00101002.getKey())) {
            //项目名称
            nodeInfo.setRemark(Optional.ofNullable(infoDto).map(ProjectInfo::getProjectName).orElse(null));
        } else if (nodeInfo.getNodeCode().equals(AtourSystemEnum.BroughtOutCol.COL00101003.getKey())) {
            //项目地址
            nodeInfo.setRemark(Optional.ofNullable(infoDto).map(ProjectInfo::getProjectAddress).orElse(null));
        } else {
            //带出正式开工的开工日期；计划完工日期；计划开业日期 col-00101007；col-00101008；col-00101009
            this.broughtOutTimeToCol(nodeInfo, resources);
            //将施工单位项目经理eng-00105020入到干系人里的zxdw-xmjl和【施工日志】的col-00101005
            projectNodeInfoService.broughtOutXmjlToCol(nodeInfo, resources.getProjectId());
        }
    }

    //施工日志带出正式开工的开工日期；计划完工日期；计划开业日期col-00101007；col-00101008；col-00101009
    private void broughtOutTimeToCol(ProjectNodeInfo nodeInfo, ProjectConstructionLogDto resources) {
        final LambdaQueryWrapper<ProjectGroup> eq = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getProjectId, resources.getProjectId())
                .eq(ProjectGroup::getNodeCode, AtourSystemEnum.EngineeringNodeTow.ENG00107.getKey())
                .last("limit 1");
        final ProjectGroup group = projectGroupRepository.selectOne(eq);

        if ("col-00101007".equals(nodeInfo.getNodeCode())) {
            Optional.ofNullable(group).map(ProjectGroup::getActualEndDate).filter(p -> ObjectUtil.isNotEmpty(p)).ifPresent(aed -> {
                final String format = new SimpleDateFormat("yyyy-MM-dd").format(aed);
                nodeInfo.setRemark(format);
            });
        }
        if ("col-00101008".equals(nodeInfo.getNodeCode())) {
            Optional.ofNullable(group).map(ProjectGroup::getPlanEndDate).filter(ObjectUtil::isNotEmpty).ifPresent(ped -> {
                final String format = new SimpleDateFormat("yyyy-MM-dd").format(ped);
                nodeInfo.setRemark(format);
            });
        }
        if ("col-00101009".equals(nodeInfo.getNodeCode())) {
            Optional.ofNullable(group).map(ProjectGroup::getPlanStartDate).filter(p -> ObjectUtil.isNotEmpty(p)).ifPresent(psd -> {
                final String format = new SimpleDateFormat("yyyy-MM-dd").format(psd);
                nodeInfo.setRemark(format);
            });
        }
    }


    private void broughtOutQuaFile(Long projectId, String templateCode, AtourSystemEnum.AdditionalTemplatesEnum engineeringRectifcationIssues, String s) {
        if (engineeringRectifcationIssues.getKey().equals(templateCode)) {
            util.initialize(projectNodeInfoService.getSubmeterProjectId(projectId));
            final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(ProjectNodeInfo::getNodeCode, s);
            final ProjectNodeInfo info = projectNodeInfoRepository.selectOne(eq);
            Optional.ofNullable(info).flatMap(i -> {
                Optional.ofNullable(i.getRemark()).ifPresent(
                        localId -> {
                            if (ObjectUtil.isNotEmpty(localId)) {
                                final LocalStorageDto localStorageDto = localStorageService.findById(Long.valueOf(localId));
                                LocalStorage localStorage = new LocalStorage();
                                BeanUtil.copyProperties(localStorageDto, localStorage, CopyOptions.create().setIgnoreNullValue(true));
                                localStorage.setId(null);
                                localStorage.setNodeId(String.valueOf(info.getNodeId()));
                                localStorageRepository.insert(localStorage);
                            }
                        }
                );
                return Optional.empty();
            });
        }
    }

    private void copyProjectSta(Long projectId) {
        final List<ProjectStakeholders> list = new ArrayList<>();
        final LambdaQueryWrapper<ProjectStakeholders> eq = Wrappers.lambdaQuery(ProjectStakeholders.class)
                .eq(ProjectStakeholders::getProjectId, projectNodeInfoService.getSubmeterProjectId(projectId));
        final List<ProjectStakeholders> projectStakeholders = projectStakeholdersRepository.selectList(eq);
        Long finalProjectId = projectId;
        projectStakeholders.stream().forEach(sta -> {
            final ProjectStakeholders stakeholders = new ProjectStakeholders();
            BeanUtil.copyProperties(sta, stakeholders, CopyOptions.create().setIgnoreNullValue(true));
            stakeholders.setStakeholderId(null);
            stakeholders.setProjectId(finalProjectId);
            list.add(stakeholders);
        });
        projectStakeholdersService.saveBatch(list);
    }

    private void newTemplateDis(Long projectId) {
        final LambdaQueryWrapper<ProjectGroup> eq = Wrappers.lambdaQuery(ProjectGroup.class).eq(ProjectGroup::getProjectId, projectId);
        final List<ProjectGroup> projectGroups = projectGroupRepository.selectList(eq);
        final LambdaQueryWrapper<ProjectNodeInfo> wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class).eq(ProjectNodeInfo::getProjectId, projectId);
        final List<ProjectNodeInfo> nodeInfos = projectNodeInfoRepository.selectList(wrapper);
        List<ProjectGroup> list = new ArrayList<>(projectGroups);
        Set<ProjectGroup> set = new LinkedHashSet<>(list);
        list.clear();
        list.addAll(set);
        List<ProjectNodeInfo> listInfo = new ArrayList<>(nodeInfos);
        Set<ProjectNodeInfo> setInfo = new LinkedHashSet<>(listInfo);
        listInfo.clear();
        listInfo.addAll(setInfo);
    }

    private void savePersonelBySta(Long projectId, String nodeCode, String roleCode) {
        final LambdaQueryWrapper<ProjectStakeholders> queryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class)
                .eq(ProjectStakeholders::getProjectId, projectId)
                .eq(ProjectStakeholders::getRoleCode, roleCode);
        final List<ProjectStakeholders> projectStakeholders = projectStakeholdersRepository.selectList(queryWrapper);
        final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getNodeCode, nodeCode);
        final ProjectNodeInfo info = projectNodeInfoRepository.selectOne(eq);
        final ArrayList<ProjectNodeInfo> list = new ArrayList<>();
        projectStakeholders.stream().filter(sta -> JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey().equals(sta.getShakeholderStatus())).limit(1)
                .forEach(s -> {
                    Optional.ofNullable(s.getUserId()).ifPresent(user -> {
                        final User one = userRepository.getOne(user);
                        Optional.ofNullable(one).ifPresent(o -> Optional.ofNullable(info).ifPresent(i -> {
                            i.setRemark(o.getUsername() + "-" + o.getNickName());
                            list.add(i);
                        }));
                    });
                });
        projectNodeInfoService.updateBatchById(list);
    }

    private void saveSystemPersonelByInfo(Long projectId, String nodeCode, ProjectInfoDto dto, Long id) {
        final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getNodeCode, nodeCode);
        final ProjectNodeInfo one = projectNodeInfoRepository.selectOne(eq);
        Optional.ofNullable(one).ifPresent(o -> Optional.ofNullable(dto).ifPresent(d -> Optional.ofNullable(id)
                .ifPresent(w -> {
                    final User user = userRepository.getOne(w);
                    Optional.ofNullable(user).ifPresent(u -> {
                        o.setRemark(u.getUsername() + "-" + u.getNickName());
                        projectNodeInfoService.update(o);
                    });
                })));
    }

    private void saveSjsXmjl(Long projectId) {
        final AtourSystemEnum.BreakOutSjsXmjl[] sjs = AtourSystemEnum.BreakOutSjsXmjl.sjs();
        final AtourSystemEnum.BreakOutSjsXmjl[] xmjl = AtourSystemEnum.BreakOutSjsXmjl.xmjl();
        final ProjectInfoDto id = projectInfoService.findById(projectId);
        final ArrayList<ProjectNodeInfo> list = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(id) && ObjectUtil.isNotEmpty(id.getProjectManager()) && ObjectUtil.isNotEmpty(id.getDesigner())) {
            final User designer = userRepository.getOne(id.getDesigner());
            final User projectManager = userRepository.getOne(id.getProjectManager());
            for (int i = 0; i < sjs.length; i++) {
                final LambdaQueryWrapper<ProjectNodeInfo> wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, projectId)
                        .eq(ProjectNodeInfo::getNodeCode, sjs[i].getKey());
                final ProjectNodeInfo one = projectNodeInfoRepository.selectOne(wrapper);
                Optional.ofNullable(one).flatMap(o -> {
                    Optional.ofNullable(designer).ifPresent(
                            d -> {
                                o.setRemark(d.getUsername() + "-" + d.getNickName());
                                list.add(o);
                            });
                    return Optional.empty();
                });
            }
            for (int i = 0; i < xmjl.length; i++) {
                final LambdaQueryWrapper<ProjectNodeInfo> wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, projectId)
                        .eq(ProjectNodeInfo::getNodeCode, xmjl[i].getKey());
                final ProjectNodeInfo one = projectNodeInfoRepository.selectOne(wrapper);
                Optional.ofNullable(one).flatMap(o -> {
                    Optional.ofNullable(projectManager).ifPresent(
                            p -> {
                                o.setRemark(p.getUsername() + "-" + p.getNickName());
                                list.add(o);
                            });
                    return Optional.empty();
                });
            }
            projectNodeInfoService.updateBatchById(list);
        }
    }

    private void getSealingConfirmationDate(Long projectId) {
        final LambdaQueryWrapper<ProjectGroup> wrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getProjectId, projectId)
                .eq(ProjectGroup::getNodeCode, AtourSystemEnum.EngineeringNodeTow.ENG00127.getKey());
        final ProjectGroup projectGroup = projectGroupRepository.selectOne(wrapper);
        if (ObjectUtil.isNotEmpty(projectGroup) && ObjectUtil.isNotEmpty(projectGroup.getPlanEndDate()) && !projectGroup.getNodeName().contains("抽查")) {
            //查询【封板节点】的【现长预计到岗时间】；eng-00119324
            final LambdaQueryWrapper<ProjectNodeInfo> queryWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(ProjectNodeInfo::getNodeCode, AtourSystemEnum.SealingConfirmation.ENG00119324.getKey());
            final ProjectNodeInfo projectNodeInfo = projectNodeInfoRepository.selectOne(queryWrapper);
            if (ObjectUtil.isNotEmpty(projectNodeInfo)) {
                projectNodeInfo.setRemark(String.valueOf(projectGroup.getPlanEndDate().toLocalDateTime().minusDays(60)));
            }
            projectNodeInfoService.update(projectNodeInfo);
        }
    }

//    private void upInitializeScheduleDate(ProjectGroup projectGroup, Date stratDate) {
//        //根据系统的二级工期和总工期，计算出当前项目的计划开始日期计划结束日期和工期
//        //总工期
//        Integer totalDay = projectGroup.getTotalDay();
//        //工期
//        Integer planDay = projectGroup.getPlanDay();
//        projectGroup.setPlanDay(planDay);
//
//        // 创建一个Calendar对象
//        Calendar calendarEndDate = Calendar.getInstance();
//        Timestamp currentTimestamp = null;
//        if (stratDate != null) {
//            calendarEndDate.setTime(stratDate);
//        } else {
//            // 获取当前时间
//            currentTimestamp = new Timestamp(System.currentTimeMillis());
//            calendarEndDate.setTime(currentTimestamp);
//        }
//        // 获取计划结束时间 =======    当前日期根据总工期往后推迟
//        calendarEndDate.add(Calendar.DAY_OF_MONTH, totalDay);
//        Timestamp futureTimestampEnd = new Timestamp(calendarEndDate.getTimeInMillis());
//        projectGroup.setPlanEndDate(futureTimestampEnd);
//
//        Calendar calendarStartDate = Calendar.getInstance();
//        calendarStartDate.setTime(futureTimestampEnd);
//        // 获取计划开始时间 =======    当前日期根据计划结束日期和工期往前计算
//        calendarStartDate.add(Calendar.DAY_OF_MONTH, -(int) planDay);
//        Timestamp futureTimestampStart = new Timestamp(calendarStartDate.getTimeInMillis());
//        projectGroup.setPlanStartDate(futureTimestampStart);
//    }

    private void upInitializeScheduleDate(ProjectGroup projectGroup, TemplateGroup templateGroups) {
        //根据系统的二级工期和总工期，计算出当前项目的计划开始日期计划结束日期和工期
        //总工期
        Integer totalDay = templateGroups.getTotalDay();
        //工期
        Integer planDay = templateGroups.getPlanDay();
        projectGroup.setPlanDay(planDay);

        // 创建一个Calendar对象
        Calendar calendarEndDate = Calendar.getInstance();
        // 获取当前时间
        Timestamp currentTimestamp = new Timestamp(System.currentTimeMillis());
        calendarEndDate.setTime(currentTimestamp);
        // 获取计划结束时间 =======    当前日期根据总工期往后推迟
        calendarEndDate.add(Calendar.DAY_OF_MONTH, totalDay);
        Timestamp futureTimestampEnd = new Timestamp(calendarEndDate.getTimeInMillis());
        projectGroup.setPlanEndDate(futureTimestampEnd);

        Calendar calendarStartDate = Calendar.getInstance();
        calendarStartDate.setTime(futureTimestampEnd);
        // 获取计划开始时间 =======    当前日期根据计划结束日期和工期往前计算
        calendarStartDate.add(Calendar.DAY_OF_MONTH, -(int) planDay);
        Timestamp futureTimestampStart = new Timestamp(calendarStartDate.getTimeInMillis());
        projectGroup.setPlanStartDate(futureTimestampStart);
    }

    private void addTowNodes(Long projectId, ArrayList<ProjectNodeInfo> projectNodeInfos, TemplateQueue
            queue, ProjectTemplate template) {
        if (queue.getTemplateQueueId() == null) {
            throw new BadRequestException("二级节点为空，请重新选择数据！");
        }
        //这里的template匹配的是2级
        if (queue.getTemplateId().equals(template.getTemplateId())) {
            ProjectNodeInfo projectNodeInfo = new ProjectNodeInfo();
            BeanUtils.copyProperties(template, projectNodeInfo);
            projectNodeInfo.setTemplateQueueId(queue.getTemplateQueueId());
            projectNodeInfo.setProjectId(projectId);
            projectNodeInfo.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
            projectNodeInfo.setIsDelete(false);
            projectNodeInfo.setIsEnabled(true);
            projectNodeInfo.setCreateBy(null);
            projectNodeInfo.setCreateTime(null);
            projectNodeInfos.add(projectNodeInfo);
            if (projectNodeInfos.size() >= 1000) {
                projectNodeInfoService.saveBatch(projectNodeInfos);
                projectNodeInfos.clear();
            }
        }
    }

    private void addThreeNodes(Long projectId, ProjectInfoDto resources, String projectType1, Long
            storeMasterParam, List<ProjectStakeholders> stakeholders, ArrayList<ProjectNodeInfo> projectNodeInfos, TemplateGroup
                                       group, TemplateQueue queue, ProjectTemplate template) {
        if (group.getTemplateId().equals(queue.getParentId())) {
            Integer count = 0;
            if (queue.getTemplateId().equals(template.getTemplateId())) {
                if (ObjectUtils.isEmpty(template)) {
                    System.out.println(queue.getTemplateId());
                }
                ProjectNodeInfo thirdprojectNodeInfo = new ProjectNodeInfo();
                BeanUtils.copyProperties(template, thirdprojectNodeInfo);
                thirdprojectNodeInfo.setParentId(template.getTemplateId());
                thirdprojectNodeInfo.setTemplateQueueId(queue.getTemplateQueueId());
                thirdprojectNodeInfo.setProjectId(projectId);
                thirdprojectNodeInfo.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
                thirdprojectNodeInfo.setNodeIndex(queue.getNodeIndex());
                thirdprojectNodeInfo.setIsDelete(false);
                thirdprojectNodeInfo.setIsEnabled(true);
                thirdprojectNodeInfo.setCreateBy(null);
                thirdprojectNodeInfo.setCreateTime(null);
                thirdprojectNodeInfo.setNodeIndex(queue.getNodeIndex() + count);
                //模版表格
                if (template.getNodeType().equals(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST
                        .getValue())) {
                    saveDynamicConditionList(projectId, resources, projectNodeInfos, template, count
                            , queue, template);
                }


                //立项数据复制
                thirdprojectNodeInfo = copyProjectNodeInfo(thirdprojectNodeInfo, resources);
//                                //固定值录入
//                                if (JhSystemEnum.NodeCodeSEEnum.isCreateApplyNo(group.getNodeCode())) {
//                                    thirdprojectNodeInfo = saveContractApplyInfo(thirdprojectNodeInfo);
//                                }


                //门店主档涉及相关信息入pro_info
                if (projectType1.equals(KidsSystemEnum.ProcessType.MAJOR.getValue()) || projectType1.equals(KidsSystemEnum.ProcessType.MINOR.getValue()) || projectType1.equals(KidsSystemEnum.ProcessType.REFORM.getValue())) {
                    projectToMasterService.byStoreMasterUpdateProjectInfo(storeMasterParam, queue.getNodeCode(), thirdprojectNodeInfo, queue);
                }


                projectNodeInfos.add(thirdprojectNodeInfo);
                if (projectNodeInfos.size() >= 1000) {
                    projectNodeInfoService.saveBatch(projectNodeInfos);
                    projectNodeInfos.clear();
                }
            }
        }
    }

    /**
     * 插入动态表
     */
    private Integer saveDynamicConditionList(Long projectId, ProjectInfoDto
            resources, List<ProjectNodeInfo> projectNodeInfos
            , ProjectTemplate template, Integer countNode, TemplateQueue queue, ProjectTemplate thirdtemplate) {
        Integer count = countNode;
        TemplateTableRelationDto tableRelations = templateTableRelationService.getTemplateTable(resources.getBrandCode(), resources.getProductCode(), queue.getNodeCode());
        if (ObjectUtils.isEmpty(tableRelations)) {
            System.out.println(resources.getNodeCode());
        } else {
            //根据条件查询到对应的模版信息
            LambdaQueryWrapper<TemplateTableGroup> queryWrapper = Wrappers.lambdaQuery(TemplateTableGroup.class)
                    .eq(TemplateTableGroup::getTemplateTableGroupId, tableRelations.getTemplateTableGroupId())
                    .last("limit 1");
            TemplateTableGroup projectInfos = templateTableGroupRepository.selectOne(queryWrapper);
            if (ObjectUtil.isNotEmpty(projectInfos)) {
                //查询表头数据
                String byStartSign = tableConfiguringTertiaryNodesService.getNodeByStartSign(projectInfos.getDynamicTableType());
                if (ObjectUtils.isNotEmpty(byStartSign)) {
                    //查到对应的模版后，去查询模版的列表数据
                    List<TemplateTableDto> getNodeList = templateTableRepository.getListByGroup(projectInfos.getTemplateTableGroupId());
                    Map<String, List<TemplateTableDto>> collect = new HashMap<>();
//                    if (projectInfos.getDynamicTableType().equals(JhSystemEnum.dynamicTableType.MATERIAL_MANAGEMENT.getKey())) {
//                        //是否必采（0必采、1非必采）
//                        collect = getNodeList.stream().collect(Collectors.groupingBy(a -> ObjectUtil.isEmpty(a.getIsMustMined()) ? "-1" : a.getIsMustMined()));
//                    } else if (projectInfos.getDynamicTableType().equals(JhSystemEnum.dynamicTableType.SAFE_CIVILIZED_CONSTRUCTION.getKey())) {
//                        // 安全文明施工类型（0办公室管理、1临时水电、3工人住宿、4安全管理）
//                        collect = getNodeList.stream().collect(Collectors.groupingBy(a -> ObjectUtil.isEmpty(a.getConstructionType()) ? "-1" : a.getConstructionType()));
//                    } else {
                    collect = getNodeList.stream().collect(Collectors.groupingBy(a -> ObjectUtil.isEmpty(a.getNodeLevel()) ? "-1" : a.getNodeLevel().toString()));
//                    }

                    for (String type : collect.keySet()) {
                        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
                        TemplateTableDto tableDto = new TemplateTableDto();
                        TemplateTableDto tableDtoByStartSign = new TemplateTableDto();
                        if (ObjectUtils.isNotEmpty(type) && !type.equals("-1")) {
//                            if (projectInfos.getDynamicTableType().equals(JhSystemEnum.dynamicTableType.MATERIAL_MANAGEMENT.getKey())) {
//                                //是否必采（0必采、1非必采）
//                                tableDto.setRemark("大货材料");
//                                tableDto.setNodeCode(thirdtemplate.getNodeCode() + "D-" + "table-tab");
//
//                                tableDtoByStartSign.setNodeType(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_HEADER.getValue());
//                                tableDtoByStartSign.setNodeCode(thirdtemplate.getNodeCode() + "D-" + "table-head");
//                                if (type.equals(JhSystemEnum.dynamicTableTypeValue.MUST_PAY.getKey())) {
//                                    tableDto.setRemark("样板间材料");
//                                    tableDto.setNodeCode(thirdtemplate.getNodeCode() + "Y-" + "table-tab");
//
//                                    tableDtoByStartSign.setNodeType(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_HEADER.getValue());
//                                    tableDtoByStartSign.setNodeCode(thirdtemplate.getNodeCode() + "Y-" + "table-head");
//                                }
//                            } else
//                            if (projectInfos.getDynamicTableType().equals(JhSystemEnum.dynamicTableType.SAFE_CIVILIZED_CONSTRUCTION.getKey())) {
//                                // 安全文明施工类型（0办公室管理、1临时水电、2工人住宿、3安全管理）
//                                tableDto.setRemark(JhSystemEnum.dynamicTableTypeValue.OFFICE_ADMINISTRATOR.getSpec());
//                                tableDto.setNodeCode(thirdtemplate.getNodeCode() + "B-" + "table-tab");
//
//                                tableDtoByStartSign.setNodeType(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_HEADER.getValue());
//                                tableDtoByStartSign.setNodeCode(thirdtemplate.getNodeCode() + "B-" + "table-head");
//                                if (type.equals(JhSystemEnum.dynamicTableTypeValue.TEMPORARY_HYDROPOWER.getKey())) {
//                                    tableDto.setRemark(JhSystemEnum.dynamicTableTypeValue.TEMPORARY_HYDROPOWER.getSpec());
//                                    tableDto.setNodeCode(thirdtemplate.getNodeCode() + "L-" + "table-tab");
//
//                                    tableDtoByStartSign.setNodeType(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_HEADER.getValue());
//                                    tableDtoByStartSign.setNodeCode(thirdtemplate.getNodeCode() + "L-" + "table-head");
//                                } else if (type.equals(JhSystemEnum.dynamicTableTypeValue.ACCOMMODATION_WORKERS.getKey())) {
//                                    tableDto.setRemark(JhSystemEnum.dynamicTableTypeValue.ACCOMMODATION_WORKERS.getSpec());
//                                    tableDto.setNodeCode(thirdtemplate.getNodeCode() + "G-" + "table-tab");
//
//                                    tableDtoByStartSign.setNodeType(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_HEADER.getValue());
//                                    tableDtoByStartSign.setNodeCode(thirdtemplate.getNodeCode() + "G-" + "table-head");
//                                } else if (type.equals(JhSystemEnum.dynamicTableTypeValue.SAFETY_MANAGEMENT.getKey())) {
//                                    tableDto.setRemark(JhSystemEnum.dynamicTableTypeValue.SAFETY_MANAGEMENT.getSpec());
//                                    tableDto.setNodeCode(thirdtemplate.getNodeCode() + "A-" + "table-tab");
//
//                                    tableDtoByStartSign.setNodeType(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_HEADER.getValue());
//                                    tableDtoByStartSign.setNodeCode(thirdtemplate.getNodeCode() + "A-" + "table-head");
//                                }
//                            }
                            tableDto.setRelevanceId(snowflake.nextId());//如果不足两位，前面补0
                            tableDto.setNodeType(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_TAB.getValue());
                            count = this.insertProjectNodeInfoTable(projectId, resources, projectNodeInfos, template, count, queue, thirdtemplate, tableDto);

                            tableDtoByStartSign.setRemark(byStartSign);
                            tableDtoByStartSign.setRelevanceId(snowflake.nextId());//如果不足两位，前面补0
                            count = this.insertProjectNodeInfoTable(projectId, resources, projectNodeInfos, template, count, queue, thirdtemplate, tableDtoByStartSign);
                        } else {
                            tableDtoByStartSign.setRemark(byStartSign);
                            tableDtoByStartSign.setRelevanceId(snowflake.nextId());//如果不足两位，前面补0
                            tableDtoByStartSign.setNodeType(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_HEADER.getValue());
                            tableDtoByStartSign.setNodeCode(thirdtemplate.getNodeCode() + "-" + "table-head");
                            count = this.insertProjectNodeInfoTable(projectId, resources, projectNodeInfos, template, count, queue, thirdtemplate, tableDtoByStartSign);
                        }
                        for (TemplateTableDto templateTable : collect.get(type)) {
                            templateTable.setNodeType(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_VALUE.getValue());
                            templateTable.setNodeCode(thirdtemplate.getNodeCode() + "-" + templateTable.getNodeCode());
                            count = this.insertProjectNodeInfoTable(projectId, resources, projectNodeInfos, template, count, queue, thirdtemplate, templateTable);
                        }
                    }
                }
            }
        }
        return count;
    }

    private Integer insertProjectNodeInfoTable(Long projectId, ProjectInfoDto
            resources, List<ProjectNodeInfo> projectNodeInfos
            , ProjectTemplate template, Integer count, TemplateQueue queue, ProjectTemplate
                                                       thirdtemplate, TemplateTableDto templateTable) {
        ProjectNodeInfo info = new ProjectNodeInfo();
        info = copyProjectNodeInfo(info, resources);
        info.setProjectId(projectId);
        info.setTemplateQueueId(queue.getTemplateQueueId());
        info.setParentId(template.getTemplateId());
        info.setNodeCode(templateTable.getNodeCode());
        info.setIsDelete(true);
        count++;
        info.setNodeIndex(queue.getNodeIndex() + count);
        info.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
        info.setUseCase(thirdtemplate.getUseCase());
        info.setIsDelete(queue.getNodeCode().equals(AtourSystemEnum.DesignNodeTow.DES00103.getKey()));
        if (templateTable.getNodeType().equals(JhSystemEnum.NodeType.SHOW_TAB.getValue())) {
            info.setNodeName(thirdtemplate.getRemark());
        }
        info.setIsEnabled(true);
        info.setIsMobile(true);
        info.setIsEdit(thirdtemplate.getIsEdit());
        info.setSeat("3");
        info.setRemark(templateTable.getRemark());
        info.setNodeLevel(3);
        info.setTemplateId(templateTable.getRelevanceId());
        info.setNodeWbs(2);
        info.setNodeType(templateTable.getNodeType());
        info.setRoleCode(templateTable.getRoleCode());
        projectNodeInfos.add(info);
        return count;
    }

    /**
     * 将【基础物料表】选择的数据插入到【项目样板间材料表】
     */
    private void saveProjectMtoBaseMateriel(ProjectInfoDto resources) {
        LambdaQueryWrapper<TemplateGroupMateriel> materielLambdaQueryWrapper = Wrappers
                .lambdaQuery(TemplateGroupMateriel.class)
                .eq(TemplateGroupMateriel::getProjectType, resources.getProjectType())
                .eq(TemplateGroupMateriel::getStoreType, resources.getStoreType())
                .eq(TemplateGroupMateriel::getBrand, resources.getRegion());
        List<TemplateGroupMateriel> materiels = templateGroupMaterielRepository.selectList(materielLambdaQueryWrapper);
        final List<ProjectMtoBaseMateriel> list = new ArrayList<>();
        for (TemplateGroupMateriel materiel : materiels) {
            LambdaQueryWrapper<MtoBaseMateriel> wrapper = Wrappers.lambdaQuery(MtoBaseMateriel.class)
                    .eq(MtoBaseMateriel::getMaterielNo, materiel.getMaterielNo());
            MtoBaseMateriel one = mtoBaseMaterielRepository.selectOne(wrapper);
            ProjectMtoBaseMateriel mtoBaseMateriel = new ProjectMtoBaseMateriel();
            mtoBaseMateriel.setMaterielId(one.getId());
            mtoBaseMateriel.setMaterielName(one.getMaterielName());
            mtoBaseMateriel.setClassId(one.getClassId());
            mtoBaseMateriel.setMaterielNo(one.getMaterielNo());
            mtoBaseMateriel.setMaterielName(one.getMaterielName());
            mtoBaseMateriel.setCreateTime(materiel.getCreateTime());
            mtoBaseMateriel.setCreateUser(materiel.getCreateUser());
            mtoBaseMateriel.setCreateDept(materiel.getCreateDept());
            mtoBaseMateriel.setUpdateTime(materiel.getUpdateTime());
            mtoBaseMateriel.setUpdateUser(materiel.getUpdateUser());
            mtoBaseMateriel.setIsDelete(materiel.getIsDelete());
            mtoBaseMateriel.setTenantId(materiel.getTenantId());
            mtoBaseMateriel.setProjectId(resources.getProjectId());
            mtoBaseMateriel.setIsMustMined(materiel.getIsMustMined());
            list.add(mtoBaseMateriel);
        }
        projectMtoBaseMaterielService.saveBatch(list);
    }

    /**
     * 当传入的userId为“--”时,String str="--"
     */
    private Long NullUserId(String str) {
        Long value;
        try {
            value = Long.valueOf(str);
            System.out.println("转换结果：" + value);
        } catch (NumberFormatException e) {
            System.out.println("无法将字符串转换为Long类型。");
            return 0L;
        }
        return 0L;
    }

    /**
     * 根据用户选的conditions查询对应的模板
     */
    @Override
    public List<TemplateCollection> findTemplateByConditionCode(ProjectInfoDto resources, String... conditions)
            throws IllegalAccessException {
        //产品
        ConditionsRelation product = null;
        if (ObjectUtil.isNotEmpty(resources.getProductCode())) {
            LambdaQueryWrapper<ConditionsRelation> query = Wrappers.lambdaQuery(ConditionsRelation.class)
                    .eq(ConditionsRelation::getConditionTypeCode, resources.getProductCode())
                    .eq(ConditionsRelation::getConditionType, AtourSystemEnum.conditionType.PRODUCT.getKey());
            product = conditionsRelationRepository.selectOne(query);
        }
        //战区
        ConditionsRelation region = null;
        if (ObjectUtil.isNotEmpty(resources.getRegion())) {
            LambdaQueryWrapper<ConditionsRelation> query = Wrappers.lambdaQuery(ConditionsRelation.class)
                    .eq(ConditionsRelation::getConditionTypeCode, resources.getRegion())
                    .eq(ConditionsRelation::getConditionType, AtourSystemEnum.conditionType.WAR_ZONE.getKey());
            region = conditionsRelationRepository.selectOne(query);
        }
        //品牌
        ConditionsRelation brand = null;
        if (ObjectUtil.isNotEmpty(resources.getBrandCode())) {
            LambdaQueryWrapper<ConditionsRelation> query = Wrappers.lambdaQuery(ConditionsRelation.class)
                    .eq(ConditionsRelation::getConditionTypeCode, resources.getBrandCode())
                    .eq(ConditionsRelation::getConditionType, AtourSystemEnum.conditionType.BRAND.getKey());
            brand = conditionsRelationRepository.selectOne(query);
        }
        //项目类型
        ConditionsRelation projectType = null;
        if (ObjectUtil.isNotEmpty(resources.getProjectType())) {
            LambdaQueryWrapper<ConditionsRelation> query = Wrappers.lambdaQuery(ConditionsRelation.class)
                    .eq(ConditionsRelation::getConditionTypeCode, resources.getProjectType())
                    .eq(ConditionsRelation::getConditionType, AtourSystemEnum.conditionType.PROJECT_TYPE.getKey());
            projectType = conditionsRelationRepository.selectOne(query);
        }

        TemplateCollectionRelation collectionRelation = null;
        if (ObjectUtil.isNotEmpty(product) && ObjectUtil.isNotEmpty(product.getConditionCode()) &&
                ObjectUtil.isNotEmpty(region) && ObjectUtil.isNotEmpty(region.getConditionCode()) &&
                ObjectUtil.isNotEmpty(brand) && ObjectUtil.isNotEmpty(brand.getConditionCode()) &&
                ObjectUtil.isNotEmpty(projectType) && ObjectUtil.isNotEmpty(projectType.getConditionCode())
        ) {
            collectionRelation = templateCollectionRelationRepository.selectCollectionRelation(product.getConditionCode(), region.getConditionCode(), brand.getConditionCode(), projectType.getConditionCode());
        }

        if (ObjectUtil.isNotEmpty(collectionRelation) && ObjectUtil.isNotEmpty(collectionRelation.getGroupId())) {
            //获取立项模板，并排除签证报备，因为签证报备不需要项目立项就创建
            LambdaQueryWrapper<TemplateCollection> templateCollectionLambdaQueryWrapper = Wrappers
                    .lambdaQuery(TemplateCollection.class)
                    .eq(ObjectUtil.isNotEmpty(collectionRelation) && ObjectUtil.isNotEmpty(collectionRelation.getGroupId())
                            , TemplateCollection::getGroupId, collectionRelation.getGroupId());
            List<TemplateCollection> templateCollections = templateConditionRepository
                    .selectList(templateCollectionLambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(templateCollections)) {
                return templateCollections;
                //将【基础物料表】选择的数据插入到【项目样板间材料表】 暂时不用
//                saveProjectMtoBaseMateriel(resources);
            } else {
                throw new BadRequestException("当前条件未配置模板");
            }
        }
        //将宣讲附件基础信息插入项目宣讲附件表【筹建启动会】的【重要宣讲部分】
        //将证照管理基础信息插入【样板间验收】的【证照管理】；将安全文明施工基础信息插入【样板间验收】的【安全文明施工】
        //立项的时候传入查询条件，给返回列表json数据的

        return null;
    }

    //插入动态表
    @Override
    public List<ProjectTableNodeInfo> getDynamicMaskClick(Long projectId, String nodeCode) {
        List<ProjectTableNodeInfo> list = new ArrayList<>();
        //根据项目信息，查询项目动态列表存在数据么
        Long submeterProjectId = projectNodeInfoService.getSubmeterProjectId(projectId);
        LambdaQueryWrapper<ProjectInfo> queryWrapper = Wrappers.lambdaQuery(ProjectInfo.class);
        queryWrapper.eq(ProjectInfo::getProjectId, submeterProjectId)
                .eq(ProjectInfo::getIsDelete, Boolean.FALSE)
                .last("limit 1");
        ProjectInfo projectInfo = projectInfoRepository.selectOne(queryWrapper);
        //如果为【弱电整改】的整改清单eng-00141009，带出【竣工自检】的整改清单内容eng-00133046
        if (ObjectUtils.isNotEmpty(projectInfo)) {
            util.initialize(submeterProjectId);
            final TemplateTableRelationDto[] tableRelations = {null};
            //如果是弱电验收申请的选择验收标准版本
            if (ObjectUtil.isNotEmpty(nodeCode) && nodeCode.contains("eng-00139")) {
                tableRelations[0] = weakCurrentVersion(projectId, nodeCode, projectInfo, tableRelations, AtourSystemEnum.WeakCurrentVersion.ENG00137064.getKey());
            }
            //如果是竣工验收申请的选择验收标准版本
            else if (ObjectUtil.isNotEmpty(nodeCode) && nodeCode.contains("eng-00133")) {
                tableRelations[0] = weakCurrentVersion(projectId, nodeCode, projectInfo, tableRelations, AtourSystemEnum.WeakCurrentVersion.ENG00129105.getKey());
            } else {
                tableRelations[0] = weakCurrentVersion(projectId, nodeCode, projectInfo, tableRelations, nodeCode);
            }
            if (ObjectUtils.isNotEmpty(tableRelations[0])) {
                LambdaQueryWrapper<ProjectTableNodeInfo> wrapper = Wrappers.lambdaQuery(ProjectTableNodeInfo.class)
                        .eq(ProjectTableNodeInfo::getProjectId, projectId)
                        .eq(ProjectTableNodeInfo::getProjectTableRelationId, tableRelations[0].getTemplateTableGroupId())
                        .orderByAsc(ProjectTableNodeInfo::getNodeIndex);
                List<ProjectTableNodeInfo> projectTableNodeInfos = projectTableNodeInfoRepository.selectList(wrapper);
                if (ObjectUtils.isNotEmpty(projectTableNodeInfos)) {
                    return projectTableNodeInfos;
                } else {
                    //若存在蒙版弹窗动态列表模板入库，则复制当前节点数据
                    return projectTableNodeInfoService.copyProjectTableNodeInfo(projectId, tableRelations[0]);
                }
            }
        }
        return list;
    }

    private TemplateTableRelationDto weakCurrentVersion(Long projectId, String nodeCode, ProjectInfo projectInfo, TemplateTableRelationDto[] tableRelations
            , String key) {
        final LambdaQueryWrapper<ProjectNodeInfo> last = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectId)
                .eq(ProjectNodeInfo::getNodeCode, key).last("limit 1");
        final ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectOne(last);
        final boolean[] present = {false};
        final TemplateTableRelation[] templateTableRelation = {null};
        Optional.ofNullable(nodeInfo).flatMap(n -> Optional.ofNullable(n.getRemark())).ifPresent(e -> {
            //final LambdaQueryWrapper<TemplateTableGroup> lambdaQueryWrapper = Wrappers.lambdaQuery(TemplateTableGroup.class)
            //        .eq(TemplateTableGroup::getGroupName, e).eq(TemplateTableGroup::getRelevancyNodeCode, nodeCode).last("limit 1");
            //final TemplateTableGroup templateTableGroup = templateTableGroupRepository.selectOne(lambdaQueryWrapper);
            //Optional.ofNullable(templateTableGroup).ifPresent(t->{
            final LambdaQueryWrapper<TemplateTableRelation> eq = Wrappers.lambdaQuery(TemplateTableRelation.class)
                    .eq(TemplateTableRelation::getTemplateTableGroupId, Long.valueOf(e))
                    .eq(TemplateTableRelation::getThreeNodeCode, nodeCode).last("limit 1");
            templateTableRelation[0] = templateTableRelationRepository.selectOne(eq);
            present[0] = Optional.ofNullable(templateTableRelation[0]).isPresent();
            //});
        });
        if (present[0]) {
            tableRelations[0] = templateTableRelationMapper.toDto(templateTableRelation[0]);
        } else {
            tableRelations[0] = templateTableRelationService.getTemplateTable(projectInfo.getBrandCode(), projectInfo.getProductCode(), nodeCode);
        }
        return tableRelations[0];
    }

    @Override
    public Boolean saveDynamicMaskClick(List<ProjectTableNodeInfo> projectTableNodeInfos) {
        boolean b = projectTableNodeInfoService.saveOrUpdateBatch(projectTableNodeInfos);

        if (ObjectUtil.isNotEmpty(projectTableNodeInfos)) {
            ProjectTableNodeInfo projectTableNodeInfo = projectTableNodeInfos.get(0);
            if (projectTableNodeInfo.getNodeCode().contains(tertiaryKey.ZIJIANBIAO1.getKey())) {
                //客房平面及效果图自检表
                this.updateSelfTest(projectTableNodeInfo.getProjectId(), tertiaryKey.ZIJIANBIAO1.getKey(), tertiaryKey.SAME1.getKey(), tertiaryKey.NO_SAME1.getKey(), tertiaryKey.ZIJIANBIAO_STATUS1.getKey());
            } else if (projectTableNodeInfo.getNodeCode().contains(tertiaryKey.ZIJIANBIAO2.getKey())) {
                //客房装饰施工图
                this.updateSelfTest(projectTableNodeInfo.getProjectId(), tertiaryKey.ZIJIANBIAO2.getKey(), tertiaryKey.SAME2.getKey(), tertiaryKey.NO_SAME2.getKey(), tertiaryKey.ZIJIANBIAO_STATUS2.getKey());
            } else if (projectTableNodeInfo.getNodeCode().contains(tertiaryKey.ZIJIANBIA3_1.getKey())) {
                //公区概念方案
                this.updateSelfTest(projectTableNodeInfo.getProjectId(), tertiaryKey.ZIJIANBIA3_1.getKey(), tertiaryKey.SAME3_1.getKey(), tertiaryKey.NO_SAME3_1.getKey(), tertiaryKey.ZIJIANBIAO_STATUS3_1.getKey());
            } else if (projectTableNodeInfo.getNodeCode().contains(tertiaryKey.ZIJIANBIA3_2.getKey())) {
                //公区概念方案
                this.updateSelfTest(projectTableNodeInfo.getProjectId(), tertiaryKey.ZIJIANBIA3_2.getKey(), tertiaryKey.SAME3_2.getKey(), tertiaryKey.NO_SAME3_2.getKey(), tertiaryKey.ZIJIANBIAO_STATUS3_2.getKey());
            } else if (projectTableNodeInfo.getNodeCode().contains(tertiaryKey.ZIJIANBIA3_3.getKey())) {
                //公区概念方案
                this.updateSelfTest(projectTableNodeInfo.getProjectId(), tertiaryKey.ZIJIANBIA3_3.getKey(), tertiaryKey.SAME3_3.getKey(), tertiaryKey.NO_SAME3_3.getKey(), tertiaryKey.ZIJIANBIAO_STATUS3_3.getKey());
            } else if (projectTableNodeInfo.getNodeCode().contains(tertiaryKey.ZIJIANBIA4.getKey())) {
                //公区装饰施工图
                this.updateSelfTest(projectTableNodeInfo.getProjectId(), tertiaryKey.ZIJIANBIA4.getKey(), tertiaryKey.SAME4.getKey(), tertiaryKey.NO_SAME4.getKey(), tertiaryKey.ZIJIANBIAO_STATUS4.getKey());
            }
        }
        return b;
    }

    /*已废弃   动态列表 竣工验收验收单 */
    private void summaryTheReceipt(List<ProjectTableNodeInfo> projectTableNodeInfos, Long projectId) {
        List<ProjectInfoTableCheckReceiptDTO> checkReceipts = new ArrayList<>();
        util.initialize(projectNodeInfoService.getSubmeterProjectId(projectId));
        for (ProjectTableNodeInfo tableNodeInfo : projectTableNodeInfos) {
            if (tableNodeInfo.getNodeType().equals(JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_VALUE.getValue())) {
                //查询合格不合格数据
                JSONArray remarkTable = JSON.parseArray(tableNodeInfo.getRemark());
                ProjectInfoTableCheckReceiptDTO checkReceipt = new ProjectInfoTableCheckReceiptDTO();
                for (Object object : remarkTable) {
                    JSONObject obj = (JSONObject) object;
                    if (obj.get("tertiaryKey").equals(JhSystemEnum.elementHeaderEnum.ACCEPTANCE.getKey())) {
                        checkReceipt.setAcceptance(obj.get("tertiaryValue").toString());
                    }
                    if (obj.get("tertiaryKey").equals(JhSystemEnum.elementHeaderEnum.TOTAL_ITEM.getKey())) {
                        checkReceipt.setTotalItem(obj.get("tertiaryValue").toString());
                    }
                    if (obj.get("tertiaryKey").equals(JhSystemEnum.elementHeaderEnum.CONTENT.getKey())) {
                        checkReceipt.setContent(obj.get("tertiaryValue").toString());
                    }
                    if (obj.get("tertiaryKey").equals(JhSystemEnum.elementHeaderEnum.STANDARD_SCORE.getKey())) {
                        checkReceipt.setStandardScore(ObjectUtil.isNotEmpty(obj.get("tertiaryValue")) ? Double.parseDouble(obj.get("tertiaryValue").toString()) : 0);
                    }
                    if (obj.get("tertiaryKey").equals(JhSystemEnum.elementHeaderEnum.GET_SCORE.getKey())) {
                        checkReceipt.setGetScore(ObjectUtil.isNotEmpty(obj.get("tertiaryValue")) ? Double.parseDouble(obj.get("tertiaryValue").toString()) : 0);
                    }
                    if (obj.get("tertiaryKey").equals(JhSystemEnum.elementHeaderEnum.QUALIFICATION_RATE.getKey())) {
                        checkReceipt.setQualificationRate(ObjectUtil.isNotEmpty(obj.get("tertiaryValue")) ? obj.get("tertiaryValue").toString() : "0");
                    }
                }
                checkReceipts.add(checkReceipt);
                //赋值给三级节点
                this.threeLevelAssignment(projectId, checkReceipt);
            }
        }

        if (ObjectUtil.isNotEmpty(checkReceipts)) {
            Map<String, ProjectInfoTableCheckReceiptDTO> infoTableCheckReceiptMap = new HashMap<>();
            try {

                // 对分组后的集合进行计算各项的总和  Double
                Map<String, List<ProjectInfoTableCheckReceiptDTO>> collectDouble = checkReceipts.stream().collect(Collectors.groupingBy(ProjectInfoTableCheckReceiptDTO::getTotalItem));
                for (Map.Entry<String, List<ProjectInfoTableCheckReceiptDTO>> entry : collectDouble.entrySet()) {

                    // 分组后计算 (此时id、age为Double类型)
                    ProjectInfoTableCheckReceiptDTO temp2 = MyMathUtil.mapperSum(ProjectInfoTableCheckReceiptDTO.class,
                            entry.getValue(),
                            ProjectInfoTableCheckReceiptDTO::getStandardScore,
                            ProjectInfoTableCheckReceiptDTO::getGetScore);
                    temp2.setQualificationRate((int) Math.floor((temp2.getGetScore() / (double) temp2.getStandardScore()) * 100) + "%");
                    infoTableCheckReceiptMap.put(entry.getKey(), temp2);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            //根据项目ID和 "验收单各总项汇总" 的 nodeCode，查询动态列表数据
            LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .like(ProjectNodeInfo::getNodeCode, JhSystemEnum.threeNodeCodeEnum.NODE_ENG133041.getKey())
                    .eq(ProjectNodeInfo::getNodeType, JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_VALUE.getValue());
            List<ProjectNodeInfo> infos = projectNodeInfoRepository.selectList(wrapper);
            final List<ProjectNodeInfo> list = new ArrayList<>();
            for (ProjectNodeInfo info : infos) {
                cn.hutool.json.JSONArray jsonArray = new cn.hutool.json.JSONArray(info.getRemark());
                //Gson gson = new Gson(); // 创建Gson对象
                //JsonArray jsonArray = gson.fromJson(info.getRemark(), JsonArray.class); // 解析JSON字符串为JsonArray对象
                for (int i = 0; i < jsonArray.size(); i++) {
                    //JsonObject jsonObject = jsonArray.get(i).getAsJsonObject(); // 获取JsonArray中的JsonObject对象
                    final cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(i);
                    if (jsonObject.get("tertiaryKey").toString().equals(JhSystemEnum.elementHeaderEnum.PROJECT_COMPLETION_ACCEPTANCE.getKey())) {
                        String tertiaryValue = jsonObject.get("tertiaryValue").toString();
                        ProjectInfoTableCheckReceiptDTO tertiaryKey = infoTableCheckReceiptMap.get(tertiaryValue);
                        if (ObjectUtil.isNotEmpty(tertiaryKey)) {
                            // 如果tertiaryKey等于project_completion_acceptance，给total_score_of_sub_items、sub_item_score、qualification_rate的tertiaryValue赋值
                            for (int j = 0; j < jsonArray.size(); j++) {
                                final cn.hutool.json.JSONObject object = jsonArray.getJSONObject(j);
                                if (object.get("tertiaryKey").equals(JhSystemEnum.elementHeaderEnum.TOTAL_SCORE_OF_SUB_ITEMS.getKey())) {
                                    object.set("tertiaryValue", tertiaryKey.getStandardScore()); // 将tertiaryValue设置为新的值
                                }
                                if (object.get("tertiaryKey").equals(JhSystemEnum.elementHeaderEnum.SUB_ITEM_SCORE.getKey())) {
                                    object.set("tertiaryValue", tertiaryKey.getGetScore()); // 将tertiaryValue设置为新的值
                                }
                                if (object.get("tertiaryKey").equals(JhSystemEnum.elementHeaderEnum.QUALIFICATION_RATE.getKey())) {
                                    object.set("tertiaryValue", tertiaryKey.getQualificationRate()); // 将tertiaryValue设置为新的值
                                }
                            }
                        }
                    }
                }
                System.out.println(jsonArray); // 打印修改后的JsonArray对象
                info.setRemark(jsonArray.toString());
                list.add(info);
            }
            projectNodeInfoService.saveOrUpdateBatch(list);
        }
    }


    private void threeLevelAssignment(Long projectId, ProjectInfoTableCheckReceiptDTO checkReceipt) {
        //根据验收单的内容带出到三级节点中
        JhSystemEnum.completionContentEnum contentEnum = JhSystemEnum.completionContentEnum.getCompletionContent(checkReceipt.getContent());
        if (ObjectUtils.isNotEmpty(contentEnum)) {
            LambdaUpdateWrapper<ProjectNodeInfo> wrapper = Wrappers.lambdaUpdate(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(ProjectNodeInfo::getNodeCode, contentEnum.getNodeCode());
            ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectOne(wrapper);
            String acceptance = JhSystemEnum.acceptance.getAcceptance(checkReceipt.getAcceptance());
            nodeInfo.setRemark(acceptance);
            int update = projectNodeInfoRepository.updateById(nodeInfo);
        }
    }

    @Override
    public List<ProjectTableNodeInfo> getDynamicMaskRectificationList(Long projectId, Long parentId, String nodeCode) {
        List<ProjectTableNodeInfo> projectTableNodeInfos = new ArrayList<>();
        //根据项目信息，查询项目动态列表存在数据么
        Long submeterProjectId = projectNodeInfoService.getSubmeterProjectId(projectId);
        LambdaQueryWrapper<ProjectInfo> queryWrapper = Wrappers.lambdaQuery(ProjectInfo.class);
        queryWrapper.eq(ProjectInfo::getProjectId, submeterProjectId)
                .eq(ProjectInfo::getIsDelete, Boolean.FALSE)
                .last("limit 1");
        ProjectInfo projectInfo = projectInfoRepository.selectOne(queryWrapper);
        if (ObjectUtils.isNotEmpty(projectInfo)) {
            List<TemplateTableRelationDto> tableRelations = templateTableRelationService
                    .getTemplateTableList(projectInfo.getBrandCode(), projectInfo.getProductCode(), parentId);
            if (ObjectUtil.isNotEmpty(tableRelations)) {
                List<Long> collect = tableRelations.stream().map(TemplateTableRelationDto::getTemplateTableGroupId).collect(Collectors.toList());
                final List<ProjectTableNodeInfo> tableNodeInfos = projectTableNodeInfoRepository
                        .selectListByProjectId(projectId, collect, JhSystemEnum.acceptance.UNQUALIFIED.getValue());
                if (ObjectUtil.isNotEmpty(tableNodeInfos)) {
                    if (ObjectUtil.isNotEmpty(nodeCode) && nodeCode.contains("eng-00139")
                            || ObjectUtil.isNotEmpty(nodeCode) && nodeCode.contains("eng-00141")) {
                        projectTableNodeInfos.addAll(tableNodeInfos);
                    }
                    if (ObjectUtil.isNotEmpty(nodeCode) && nodeCode.contains("eng-00133")
                            || ObjectUtil.isNotEmpty(nodeCode) && nodeCode.contains("eng-00135")) {
                        //客房区域
                        addJgysTableNode(projectId, projectTableNodeInfos, collect, tableNodeInfos
                                , JhSystemEnum.completionTotalItems.GUEST_ROOM_AREA.getKey()
                                , JhSystemEnum.completionTotalItems.GUEST_ROOM_AREA.getSpec());
                        //公共区域「外立面、大堂区域」
                        addJgysTableNode(projectId, projectTableNodeInfos, collect, tableNodeInfos
                                , JhSystemEnum.completionTotalItems.PUBLIC_AREAS.getKey()
                                , JhSystemEnum.completionTotalItems.PUBLIC_AREAS.getSpec());
                        //机电设施「强电、弱电、给排水、暖通」
                        addJgysTableNode(projectId, projectTableNodeInfos, collect, tableNodeInfos
                                , JhSystemEnum.completionTotalItems.ELECTROMECHANICAL_FACILITIES.getKey()
                                , JhSystemEnum.completionTotalItems.ELECTROMECHANICAL_FACILITIES.getSpec());
                        //风险管理「消防、安防、监控、电梯」
                        addJgysTableNode(projectId, projectTableNodeInfos, collect, tableNodeInfos
                                , JhSystemEnum.completionTotalItems.RISK_MANAGEMENT.getKey()
                                , JhSystemEnum.completionTotalItems.RISK_MANAGEMENT.getSpec());
                        //功能区「厨房、消毒间、布草间、仓库」
                        addJgysTableNode(projectId, projectTableNodeInfos, collect, tableNodeInfos
                                , JhSystemEnum.completionTotalItems.FUNCTIONAL_AREA.getKey()
                                , JhSystemEnum.completionTotalItems.FUNCTIONAL_AREA.getSpec());
                    }
                }
            }
        }
        return projectTableNodeInfos;
    }

    private void addJgysTableNode(Long projectId, List<ProjectTableNodeInfo> projectTableNodeInfos, List<Long> collect, List<ProjectTableNodeInfo> tableNodeInfos, String key, String spec) {
        final List<ProjectTableNodeInfo> list = tableNodeInfos.stream().filter(t -> t.getRemark().contains(key)).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(list)) {
            projectTableNodeInfos.addAll(projectTableNodeInfoRepository.selectListByProjectId(projectId, collect, spec));
            projectTableNodeInfos.addAll(list);
        }
    }

    //private void insertEngIssues(ProjectNodeInfo thirdprojectNodeInfo, ProjectInfoDto resources) {
    //    breakOutQualityControl(thirdprojectNodeInfo, resources);
    //    breakOutSystemSelfInspection(thirdprojectNodeInfo);
    //    breakOutRoutineSelfInspection(thirdprojectNodeInfo);
    //    breakOutDesignSampleRoom(thirdprojectNodeInfo);
    //}

    private void breakOutDesignSampleRoom(ProjectNodeInfo thirdprojectNodeInfo, ProjectTableNodeInfoDto
            projectTableNodeInfo) {
        util.initialize(projectTableNodeInfo.getProjectId());
        final LambdaQueryWrapper<ProjectNodeInfo> queryWrapper = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                .eq(ProjectNodeInfo::getProjectId, projectTableNodeInfo.getProjectId())
                .eq(ProjectNodeInfo::getNodeCode, projectTableNodeInfo.getNodeCode().substring(0, 12));
        final ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectOne(queryWrapper);
        final AtourSystemEnum.EngineeringRectificationIssuesNodeThree[] codeDSR = AtourSystemEnum.EngineeringRectificationIssuesNodeThree.codeDSR();
        for (int i = 0; i < codeDSR.length; i++) {
            if (codeDSR[i].getKey().equals(thirdprojectNodeInfo.getNodeCode())) {
                if (0 == i) {
                    thirdprojectNodeInfo.setRemark(nodeInfo.getNodeName());
                }
                if (1 == i) {
                    thirdprojectNodeInfo.setRemark(getListRemarkDto(projectTableNodeInfo, 0, "$.tertiaryValue"));
                }
                if (2 == i) {
                    thirdprojectNodeInfo.setRemark(getListRemarkDto(projectTableNodeInfo, 1, "$.tertiaryValue"));
                }
                if (3 == i) {
                    thirdprojectNodeInfo.setRemark(getListRemarkDto(projectTableNodeInfo, 2, "$.tertiaryValue"));
                }
                if (4 == i) {
                    thirdprojectNodeInfo.setRemark(getListRemarkDto(projectTableNodeInfo, 3, "$.tertiaryValue"));
                }
                if (5 == i) {
                    thirdprojectNodeInfo.setRemark(getListRemarkDto(projectTableNodeInfo, 4, "$.tertiaryValue"));
                }
                //现场照片
                if (6 == i) {
                    final List<LocalStorage> storages = localStorageRepository.findByNodeId(nodeInfo.getNodeId() + "_site_photos");
                    storages.stream().forEach(s -> {
                        final LocalStorage localStorage = new LocalStorage();
                        BeanUtil.copyProperties(s, localStorage, CopyOptions.create().setIgnoreNullValue(true));
                        localStorage.setId(null);
                        localStorage.setNodeId(thirdprojectNodeInfo.getNodeId() + "_site_photos");
                        localStorageRepository.insert(localStorage);
                    });
                }
            }
        }
    }

    private String getListRemark(ProjectTableNodeInfo nodeInfo, int key, String path) {
        cn.hutool.json.JSONArray jsonArray = new cn.hutool.json.JSONArray(nodeInfo.getRemark());
        cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(key);
        final String read = JSONPath.read(jsonObject.toString(), path).toString();
        return read;
    }

    private String getListRemarkDto(ProjectTableNodeInfoDto nodeInfo, int key, String path) {
        cn.hutool.json.JSONArray jsonArray = new cn.hutool.json.JSONArray(nodeInfo.getRemark());
        cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(key);
        final String read = JSONPath.read(jsonObject.toString(), path).toString();


        if (1 == key) {
            DictDetail dictDetail = dictDetailRepository.findDictDetailByValueA(read, 155L);
            return ObjectUtil.isNotEmpty(dictDetail) ? dictDetail.getLabel() : "";
        } else if (4 == key && ObjectUtil.isNotEmpty(read)) {
            User one = userRepository.getOne(Long.valueOf(read));
            return ObjectUtil.isNotEmpty(one) ? one.getNickName() : "";
        }

        return read;
    }

    private void breakOutRoutineSelfInspection(ProjectNodeInfo thirdprojectNodeInfo, QualityControlDto
            routineSelfInspection) {
        final AtourSystemEnum.EngineeringRectificationIssuesNodeThree[] codeRSI = AtourSystemEnum.EngineeringRectificationIssuesNodeThree.codeSSI();
        final LambdaQueryWrapper<TemplateGroup> wrapper = Wrappers.lambdaQuery(TemplateGroup.class)
                .eq(TemplateGroup::getNodeCode, routineSelfInspection.getNodeCode().substring(0, 9))
                .eq(TemplateGroup::getNodeLevel, "2")
                .last("limit 1");
        final TemplateGroup templateGroup = templateGroupRepository.selectOne(wrapper);
        for (int i = 0; i < codeRSI.length; i++) {
            if (codeRSI[i].getKey().equals(thirdprojectNodeInfo.getNodeCode())) {
                if (2 == i) {
                    thirdprojectNodeInfo.setRemark(routineSelfInspection.getFunctionalRegion());
                }
                if (3 == i) {
                    thirdprojectNodeInfo.setRemark(routineSelfInspection.getQualityName());
                }
                if (4 == i) {
                    thirdprojectNodeInfo.setRemark(routineSelfInspection.getQualityDescribe());
                }
                if (5 == i) {
                    thirdprojectNodeInfo.setRemark(routineSelfInspection.getIssueImportance());
                }
                //描述附件
                if (6 == i) {
                    thirdprojectNodeInfo.setRemark(routineSelfInspection.getDescribeFile());
                }
                if (7 == i) {
                    thirdprojectNodeInfo.setRemark(routineSelfInspection.getIsNonStandardApproval());
                }
                if (0 == i || 1 == i) {
                    breakOutNodeMes(thirdprojectNodeInfo, codeRSI[0].getKey()
                            , codeRSI[1].getKey()
                            , AtourSystemEnum.AdditionalTemplatesEnum.ROUTINE_SELF_INSPECTION.getSpec()
                            , templateGroup);
                }
            }
        }
    }

    private void breakOutSystemSelfInspection(ProjectNodeInfo thirdprojectNodeInfo, ProjectSystemSelfInspectionDto
            projectSystemSelfInspection) {
        final AtourSystemEnum.EngineeringRectificationIssuesNodeThree[] codeSSI = AtourSystemEnum.EngineeringRectificationIssuesNodeThree.codeRSI();
        final LambdaQueryWrapper<TemplateGroup> wrapper = Wrappers.lambdaQuery(TemplateGroup.class)
                .eq(TemplateGroup::getNodeCode, projectSystemSelfInspection.getNodeCode().substring(0, 9))
                .eq(TemplateGroup::getNodeLevel, "2").last("limit 1");
        final TemplateGroup templateGroup = templateGroupRepository.selectOne(wrapper);
        for (int i = 0; i < codeSSI.length; i++) {
            if (codeSSI[i].getKey().equals(thirdprojectNodeInfo.getNodeCode())) {
                if (0 == i || 1 == i) {
                    breakOutNodeMes(thirdprojectNodeInfo, codeSSI[0].getKey()
                            , codeSSI[1].getKey()
                            , AtourSystemEnum.AdditionalTemplatesEnum.SYSTEM_SELF_INSPECTION.getSpec()
                            , templateGroup);
                }
                if (2 == i) {
                    thirdprojectNodeInfo.setRemark(projectSystemSelfInspection.getSelfProjectName());
                }
                if (3 == i) {
                    thirdprojectNodeInfo.setRemark(projectSystemSelfInspection.getSelfContent());
                }
                if (4 == i) {
                    thirdprojectNodeInfo.setRemark(projectSystemSelfInspection.getConclusion());
                }
                if (5 == i) {
                    thirdprojectNodeInfo.setRemark(projectSystemSelfInspection.getExplains());
                }
            }
        }
    }

    private void breakOutQualityControl(ProjectNodeInfo thirdprojectNodeInfo, ProjectInfoDto resources) {
        QualityControlDto controlDto = resources.getQualityControlDto();
        final AtourSystemEnum.EngineeringRectificationIssuesNodeThree[] nodeThrees = AtourSystemEnum.EngineeringRectificationIssuesNodeThree.codeERI();
        final LambdaQueryWrapper<TemplateGroup> wrapper = Wrappers.lambdaQuery(TemplateGroup.class)
                .eq(TemplateGroup::getNodeCode, resources.getQualityControlDto().getNodeCode().substring(0, 9))
                .eq(TemplateGroup::getNodeLevel, "2")
                .last("limit 1");
        final TemplateGroup templateGroup = templateGroupRepository.selectOne(wrapper);
        for (AtourSystemEnum.EngineeringRectificationIssuesNodeThree nodeThree : nodeThrees) {
            if (thirdprojectNodeInfo.getNodeCode().equals(nodeThree.getKey())) {
                switch (nodeThree.getFieldName()) {
                    case "functionalRegion":
                        thirdprojectNodeInfo.setRemark(Optional.ofNullable(controlDto.getFunctionalRegion()).orElse(null));
                        break;
                    case "qualityName":
                        thirdprojectNodeInfo.setRemark(Optional.ofNullable(controlDto.getQualityName()).orElse(null));
                        break;
                    case "qualityDescribe":
                        thirdprojectNodeInfo.setRemark(Optional.ofNullable(controlDto.getQualityDescribe()).orElse(null));
                        break;
                    case "issueImportance":
                        thirdprojectNodeInfo.setRemark(Optional.ofNullable(controlDto.getIssueImportance()).orElse(null));
                        break;
                    case "describeFile":
                        thirdprojectNodeInfo.setRemark(Optional.ofNullable(controlDto.getDescribeFile()).orElse(null));
                        break;
                }
                breakOutNodeMes(thirdprojectNodeInfo, AtourSystemEnum.EngineeringRectificationIssuesNodeThree.ERI00103002.getKey()
                        , AtourSystemEnum.EngineeringRectificationIssuesNodeThree.ERI00103003.getKey()
                        , AtourSystemEnum.AdditionalTemplatesEnum.ENGINEERING_RECTIFCATION_ISSUES.getSpec()
                        , templateGroup);
            }
        }
    }

    private void breakOutNodeMes(ProjectNodeInfo thirdprojectNodeInfo, String nodeCodeA, String nodeCodeB, String
            templateName, TemplateGroup templateGroup) {
        if (nodeCodeA.equals(thirdprojectNodeInfo.getNodeCode())) {
            thirdprojectNodeInfo.setRemark(templateName);
        }
        if (nodeCodeB.equals(thirdprojectNodeInfo.getNodeCode())) {
            thirdprojectNodeInfo.setRemark(Optional.ofNullable(templateGroup.getNodeName()).orElse(null));
        }
    }

    /**
     * 将开发系统同步到三级节点
     */
    private void insertDevDes(ProjectInfoExpansion projectInfoExpansion, ProjectNodeInfo nodeInfo, ProjectInfoDto
            resources) {
//            if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.DES00101037.getKey())) {
//                nodeInfo.setRemark(projectInfoExpansion.getSurroundingConditions());
//            }
//            if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.DES00101040.getKey())) {
//                nodeInfo.setRemark(projectInfoExpansion.getFacade());
//            }
//            if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.DES00101043.getKey())) {
//                nodeInfo.setRemark(projectInfoExpansion.getWeakCurrent());
//            }
//            if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.DES00101046.getKey())) {
//                nodeInfo.setRemark(projectInfoExpansion.getAirCirculation());
//            }
//            if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.DES00101049.getKey())) {
//                nodeInfo.setRemark(projectInfoExpansion.getElevator());
//            }
//            if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.DES00101052.getKey())) {
//                nodeInfo.setRemark(projectInfoExpansion.getStrongWeakElectricity());
//            }
//            if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.DES00101055.getKey())) {
//                nodeInfo.setRemark(projectInfoExpansion.getProjectVersion());
//            }
//            if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.DES00101058.getKey())) {
//                nodeInfo.setRemark(projectInfoExpansion.getRoof());
//            }
//            if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.DES00101062.getKey())) {
//                nodeInfo.setRemark(projectInfoExpansion.getRestaurant());
//            }
//            if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.DES00101065.getKey())) {
//                nodeInfo.setRemark(projectInfoExpansion.getKtv());
//            }
//            if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.DES00101068.getKey())) {
//                nodeInfo.setRemark(projectInfoExpansion.getBathing());
//            }
    }

    private void breakOutApplicationCompletionAcceptance(ProjectNodeInfo nodeInfo, ProjectInfoDto resources) {
        //同步竣工验收申请任务的数据
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.completionAcceptanceEnum.ENG00129112.getKey())) {
            //项目决策意见(截图)  带出营建启动会的 上会次数、决策状态、决策概述
            LambdaQueryWrapper<ProjectNodeInfo> lambdaQueryWrapperNum = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, resources.getProjectId())
                    .eq(ProjectNodeInfo::getNodeCode, "eng-00103140")
                    .last("limit 1");
            ProjectNodeInfo nodeInfoNum = projectNodeInfoRepository.selectOne(lambdaQueryWrapperNum);
            String remark = nodeInfoNum.getNodeName() + "：" + nodeInfoNum.getRemark();

            LambdaQueryWrapper<ProjectNodeInfo> lambdaQueryWrapperState = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, resources.getProjectId())
                    .eq(ProjectNodeInfo::getNodeCode, "eng-00103141")
                    .last("limit 1");
            ProjectNodeInfo nodeInfoState = projectNodeInfoRepository.selectOne(lambdaQueryWrapperState);
            remark += "</br>" + nodeInfoState.getNodeName() + "：" + nodeInfoState.getRemark();

            LambdaQueryWrapper<ProjectNodeInfo> lambdaQueryWrapperSummarize = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, resources.getProjectId())
                    .eq(ProjectNodeInfo::getNodeCode, "eng-00103142")
                    .last("limit 1");
            ProjectNodeInfo nodeInfoSummarize = projectNodeInfoRepository.selectOne(lambdaQueryWrapperSummarize);
            remark += "</br>" + nodeInfoSummarize.getNodeName() + "：" + nodeInfoSummarize.getRemark();
            nodeInfo.setRemark(remark);
        }

        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.completionAcceptanceEnum.ENG00129196.getKey())) {
            //资料审核人
            LambdaQueryWrapper<ProjectStakeholders> wrapper = Wrappers.lambdaQuery(ProjectStakeholders.class);
            wrapper.eq(ProjectStakeholders::getRoleCode, AtourSystemEnum.engineeringRoleCodeEnum.ZLSH.getKey())
                    .eq(ProjectStakeholders::getProjectId, resources.getProjectId())
                    .eq(ProjectStakeholders::getShakeholderStatus, "in_term").last("limit 1");
            ProjectStakeholders stakeholders = projectStakeholdersRepository.selectOne(wrapper);
            if (ObjectUtil.isNotEmpty(stakeholders) && ObjectUtil.isNotEmpty(stakeholders.getUserId())) {
                nodeInfo.setRemark(stakeholders.getUserId() + "");
            }
        }
    }

    private void insertDevEngB(ProjectInfoExpansion projectInfoExpansion, ProjectNodeInfo nodeInfo, ProjectInfoDto
            resources) {
        ////特许商营建对接人
        //if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103051.getKey())) {
        //    final LambdaQueryWrapper<HlmProjectInfoOwner> eq = Wrappers.lambdaQuery(HlmProjectInfoOwner.class)
        //            .eq(HlmProjectInfoOwner::getProjectId, resources.getProjectHlmId())
        //            .last("limit 1");
        //    final HlmProjectInfoOwner hlmProjectInfoOwner = hlmProjectInfoOwnerRepository.selectOne(eq);
        //    if (ObjectUtils.isNotEmpty(hlmProjectInfoOwner) && ObjectUtils.isNotEmpty(hlmProjectInfoOwner.getUsername())) {
        //        final User byUsername = userRepository.findByUsername(hlmProjectInfoOwner.getUsername());
        //        if (ObjectUtils.isNotEmpty(byUsername) && ObjectUtils.isNotEmpty(byUsername.getNickName())) {
        //            nodeInfo.setRemark(hlmProjectInfoOwner.getUsername() + "-" + byUsername.getNickName());
        //        }
        //    }
        //}
        ////特许商营建对接人的联系电话
        //if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103052.getKey())) {
        //    final LambdaQueryWrapper<HlmProjectInfoOwner> eq = Wrappers.lambdaQuery(HlmProjectInfoOwner.class)
        //            .eq(HlmProjectInfoOwner::getProjectId, resources.getProjectHlmId())
        //            .last("limit 1");
        //    final HlmProjectInfoOwner hlmProjectInfoOwner = hlmProjectInfoOwnerRepository.selectOne(eq);
        //    if (ObjectUtils.isNotEmpty(hlmProjectInfoOwner)) {
        //        nodeInfo.setRemark(Optional.ofNullable(hlmProjectInfoOwner.getMobile()).orElse(null));
        //    }
        //}
        ////特许商营建对接人的联系邮箱
        //if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103053.getKey())) {
        //    final LambdaQueryWrapper<HlmProjectInfoOwner> eq = Wrappers.lambdaQuery(HlmProjectInfoOwner.class)
        //            .eq(HlmProjectInfoOwner::getProjectId, resources.getProjectHlmId())
        //            .last("limit 1");
        //    final HlmProjectInfoOwner hlmProjectInfoOwner = hlmProjectInfoOwnerRepository.selectOne(eq);
        //    if (ObjectUtils.isNotEmpty(hlmProjectInfoOwner)) {
        //        nodeInfo.setRemark(Optional.ofNullable(hlmProjectInfoOwner.getEmail()).orElse(null));
        //    }
        //}
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103091.getKey())) {
            nodeInfo.setRemark(Optional.ofNullable(projectInfoExpansion.getMeetingStateDesc()).orElse(null));
        }
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103092.getKey())) {
            nodeInfo.setRemark(Optional.ofNullable(projectInfoExpansion.getNoticePreparation()).orElse(null));
        }
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103093.getKey())) {
            nodeInfo.setRemark(Optional.ofNullable(projectInfoExpansion.getRedLineImage()).orElse(null));
        }
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103094.getKey())) {
            nodeInfo.setRemark(Optional.ofNullable(projectInfoExpansion.getConcession()).orElse(null));
        }
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103095.getKey())) {
            nodeInfo.setRemark(Optional.ofNullable(projectInfoExpansion.getLegalAgreementContent()).orElse(null));
        }
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103096.getKey())) {
//                nodeInfo.setRemark(projectInfoExpansion.getMemorandum());
        }
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103097.getKey())) {
            nodeInfo.setRemark(Optional.ofNullable(projectInfoExpansion.getMemorandum()).orElse(null));
        }
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103098.getKey())) {
            nodeInfo.setRemark(Optional.ofNullable(projectInfoExpansion.getRenovationList()).orElse(null));
        }
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103099.getKey())) {
            nodeInfo.setRemark(Optional.ofNullable(projectInfoExpansion.getProjectVersion()).orElse(null));
        }
    }

    private void insertDevEng(ProjectInfoExpansion projectInfoExpansion, ProjectNodeInfo nodeInfo, ProjectInfoDto
            resources) throws IOException {
        //酒店ID
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103169.getKey()) && ObjectUtil.isNotEmpty(projectInfoExpansion.getHotelId())) {
            nodeInfo.setRemark(String.valueOf(Optional.ofNullable(projectInfoExpansion.getHotelId()).orElse(null)));
        }
        //项目id
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103170.getKey()) && ObjectUtil.isNotEmpty(projectInfoExpansion.getProjectId())) {
            nodeInfo.setRemark(String.valueOf(Optional.ofNullable(projectInfoExpansion.getProjectId()).orElse(null)));
        }
        //项目名称
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103132.getKey()) && ObjectUtil.isNotEmpty(projectInfoExpansion.getProjectName())) {
            final String orElse = Optional.ofNullable(projectInfoExpansion.getProjectName()).orElse(resources.getProjectName());
            nodeInfo.setRemark(orElse);
        }
        //项目性质
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103106.getKey()) && ObjectUtil.isNotEmpty(projectInfoExpansion.getProjectNature())) {
            nodeInfo.setRemark(Optional.ofNullable(projectInfoExpansion.getProjectNature()).orElse(null));
        }
        //城市
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103127.getKey()) && ObjectUtil.isNotEmpty(projectInfoExpansion.getCity())) {
            final Object orElse = Optional.ofNullable(projectInfoExpansion.getCity()).orElse(resources.getCityName());
            nodeInfo.setRemark(String.valueOf(orElse));
        }
        //品牌名称
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103002.getKey()) && ObjectUtil.isNotEmpty(projectInfoExpansion.getBrandName())) {
            final String orElse = Optional.ofNullable(projectInfoExpansion.getBrandName()).orElse(resources.getBrandName());
            nodeInfo.setRemark(orElse.replaceAll("\\s", ""));
        }
        //产品名称
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103107.getKey()) && ObjectUtil.isNotEmpty(projectInfoExpansion.getProductName())) {
            final String orElse = Optional.ofNullable(projectInfoExpansion.getProductName()).orElse(resources.getProductName());
            nodeInfo.setRemark(orElse);
        }
        //战区
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103108.getKey()) && ObjectUtil.isNotEmpty(resources.getRegion())) {
            nodeInfo.setRemark(Optional.ofNullable(resources.getRegion()).orElse(null));
        }
        //运营城区
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103109.getKey()) && ObjectUtil.isNotEmpty(projectInfoExpansion.getOperateArea())) {
            nodeInfo.setRemark(Optional.ofNullable(projectInfoExpansion.getOperateArea()).orElse(null));
        }
        //生效定名
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103110.getKey()) && ObjectUtil.isNotEmpty(projectInfoExpansion.getEffectiveName())) {
            nodeInfo.setRemark(Optional.ofNullable(projectInfoExpansion.getEffectiveName()).orElse(null));
        }
        //是否翻牌
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103111.getKey()) && ObjectUtil.isNotEmpty(projectInfoExpansion.getIsFlipCard())) {
            nodeInfo.setRemark(Optional.ofNullable(projectInfoExpansion.getIsFlipCard()).orElse(null));
        }
        //开发经理
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103112.getKey()) && ObjectUtil.isNotEmpty(resources.getDevelopmentManager())) {
            String remark = "";
            for (String developmentManager : resources.getDevelopmentManager().split(",")) {
                final User one = userRepository.getOne(Long.parseLong(developmentManager));
                if (ObjectUtils.isNotEmpty(one) && ObjectUtils.isNotEmpty(one.getUsername()) && ObjectUtils.isNotEmpty(one.getNickName())) {
                    remark += ObjectUtil.isNotEmpty(remark) ? ("," + one.getUsername() + "-" + one.getNickName()) : (one.getUsername() + "-" + one.getNickName());
                }
            }
            nodeInfo.setRemark(remark);
        }
        //特许商对接人
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103113.getKey())) {
            final HlmProjectInfoOwner hlmProjectInfoOwner = getHlmProjectInfoOwner(projectInfoExpansion, resources);
            if (ObjectUtils.isNotEmpty(hlmProjectInfoOwner) && ObjectUtil.isNotEmpty(hlmProjectInfoOwner.getMobile())) {
                final User user = userRepository.findByPhone(hlmProjectInfoOwner.getMobile());
                if (ObjectUtils.isNotEmpty(user)) {
                    nodeInfo.setRemark(Optional.ofNullable(user.getUsername()).orElse(null) + "-" + Optional.ofNullable(user.getNickName()).orElse(null));
                }
            }
        }
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103102.getKey())) {
            final HlmProjectInfoOwner hlmProjectInfoOwner = getHlmProjectInfoOwner(projectInfoExpansion, resources);
            if (ObjectUtils.isNotEmpty(hlmProjectInfoOwner) && ObjectUtil.isNotEmpty(hlmProjectInfoOwner.getMobile())) {
                final User user = userRepository.findByPhone(hlmProjectInfoOwner.getMobile());
                if (ObjectUtils.isNotEmpty(user)) {
                    nodeInfo.setRemark(Optional.ofNullable(user.getUsername()).orElse(null) + "-" + Optional.ofNullable(user.getNickName()).orElse(null));
                }
            }
        }
        //对接人手机号
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103114.getKey())) {
            final HlmProjectInfoOwner hlmProjectInfoOwner = getHlmProjectInfoOwner(projectInfoExpansion, resources);
            if (ObjectUtils.isNotEmpty(hlmProjectInfoOwner)) {
                nodeInfo.setRemark(Optional.ofNullable(hlmProjectInfoOwner.getMobile()).orElse(null));
            }
        }
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103103.getKey())) {
            final HlmProjectInfoOwner hlmProjectInfoOwner = getHlmProjectInfoOwner(projectInfoExpansion, resources);
            if (ObjectUtils.isNotEmpty(hlmProjectInfoOwner)) {
                nodeInfo.setRemark(Optional.ofNullable(hlmProjectInfoOwner.getMobile()).orElse(null));
            }
        }
        //对接人邮箱
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103115.getKey())) {
            final HlmProjectInfoOwner hlmProjectInfoOwner = getHlmProjectInfoOwner(projectInfoExpansion, resources);
            if (ObjectUtils.isNotEmpty(hlmProjectInfoOwner)) {
                nodeInfo.setRemark(Optional.ofNullable(hlmProjectInfoOwner.getEmail()).orElse(null));
            }
        }
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103104.getKey())) {
            final HlmProjectInfoOwner hlmProjectInfoOwner = getHlmProjectInfoOwner(projectInfoExpansion, resources);
            if (ObjectUtils.isNotEmpty(hlmProjectInfoOwner)) {
                nodeInfo.setRemark(Optional.ofNullable(hlmProjectInfoOwner.getEmail()).orElse(null));
            }
        }
        //签约日期
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103116.getKey())) {
            nodeInfo.setRemark(String.valueOf(Optional.ofNullable(projectInfoExpansion.getEffectiveDate()).orElse(null)));
        }
        //签约房量
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103117.getKey())) {
            nodeInfo.setRemark(String.valueOf(Optional.ofNullable(projectInfoExpansion.getRoomsNumber()).orElse(null)));
        }

        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103175.getKey())) {
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String format = null;
            if (ObjectUtil.isNotEmpty(projectInfoExpansion.getProjectStartTime())) {
                format = sdf.format(projectInfoExpansion.getProjectStartTime());
            }
            nodeInfo.setRemark(format);
        }

        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103118.getKey())) {
            //上会附件
            this.getHlmOssFile(projectInfoExpansion.getProjectId(), nodeInfo);
        }


        //上会次数
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103140.getKey())) {
            final LambdaQueryWrapper<HlmProjectInfoStartWillResult> eq = Wrappers.lambdaQuery(HlmProjectInfoStartWillResult.class).eq(HlmProjectInfoStartWillResult::getProjectId, resources.getProjectHlmId());
            List<HlmProjectInfoStartWillResult> hlmProjectInfoStartWillResults = hlmProjectInfoStartWillResultRepository.selectList(eq);
            if (ObjectUtil.isNotEmpty(hlmProjectInfoStartWillResults)) {
                /*项目资料-勾选的上会结论列表*/
                HlmProjectInfoStartWillResult willResult = hlmProjectInfoStartWillResults.stream().max((a, b) -> a.getCount().compareTo(b.getCount())).get();
                if (ObjectUtils.isNotEmpty(willResult)) {
                    nodeInfo.setRemark(String.valueOf(Optional.ofNullable(willResult.getCount()).orElse(null)));
                }
            }
        }
        //决策状态
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103141.getKey())) {
            final LambdaQueryWrapper<HlmProjectInfoStartWillResult> eq = Wrappers.lambdaQuery(HlmProjectInfoStartWillResult.class)
                    .eq(HlmProjectInfoStartWillResult::getProjectId, resources.getProjectHlmId());
            final List<HlmProjectInfoStartWillResult> hlmProjectInfoStartWillResult = hlmProjectInfoStartWillResultRepository.selectList(eq);
            if (ObjectUtils.isNotEmpty(hlmProjectInfoStartWillResult)) {
                /*项目资料-勾选的上会结论列表*/
                HlmProjectInfoStartWillResult willResult = hlmProjectInfoStartWillResult.stream().max((a, b) -> a.getCount().compareTo(b.getCount())).get();
                if (ObjectUtils.isNotEmpty(willResult)) {
                    nodeInfo.setRemark(String.valueOf(Optional.ofNullable(willResult.getMeetingState()).orElse(null)));
                }
            }
        }
        //决策概述
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103142.getKey())) {
            final LambdaQueryWrapper<HlmProjectInfoStartWillResult> eq = Wrappers.lambdaQuery(HlmProjectInfoStartWillResult.class).eq(HlmProjectInfoStartWillResult::getProjectId, resources.getProjectHlmId());
            final List<HlmProjectInfoStartWillResult> hlmProjectInfoStartWillResult = hlmProjectInfoStartWillResultRepository.selectList(eq);
            if (ObjectUtils.isNotEmpty(hlmProjectInfoStartWillResult)) {
                HlmProjectInfoStartWillResult willResult = hlmProjectInfoStartWillResult.stream().max((a, b) -> a.getCount().compareTo(b.getCount())).get();
                nodeInfo.setRemark(Optional.ofNullable(willResult.getDescription()).orElse(null));
            }
        }
        //法务约定工程内容
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103014.getKey())) {
            nodeInfo.setRemark(Optional.ofNullable(projectInfoExpansion.getLegalAgreementContent()).orElse(null));
        }
        //红线描述
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103027.getKey())) {
            nodeInfo.setRemark(Optional.ofNullable(projectInfoExpansion.getRedLineDescription()).orElse(null));
        }
        //营建备忘录特殊事项描述
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103030.getKey())) {
            nodeInfo.setRemark(Optional.ofNullable(projectInfoExpansion.getConstructSpecialRemark()).orElse(null));
        }
        //业主方
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103054.getKey())) {
            User byUsername = userRepository.getByUsername(projectInfoExpansion.getOwnerProjectManager());
            OwnerUserDto byUserId = ownerUserService.findByUserId(byUsername.getId());
            OwnerDto byId = ownerService.findById(byUserId.getOwnerId());
            if (byId != null) {
                nodeInfo.setRemark(Optional.ofNullable(byId.getOwnerName()).orElse(null));
            }
        }
        //变更人员为
        if (nodeInfo.getNodeCode().equals(AtourSystemEnum.DevelopmentSystem.ENG00103056.getKey())) {
            User byUsername = userRepository.getByUsername(projectInfoExpansion.getOwnerProjectManager());
            if (ObjectUtils.isNotEmpty(byUsername)) {
                nodeInfo.setRemark(Optional.ofNullable(byUsername.getUsername()).orElse(null));
            }
        }
    }

    private void synchronizeSpotCheck(ProjectNodeInfo nodeInfo, ProjectSpotCheck projectSpotCheck, ProjectInfoDto infoDto) {
        //抽查任务同步原二级任务数据
        Map<String, ProjectNodeInfo> nodeCodeInfo = projectSpotCheck.getNodeCodeInfo();
        ProjectNodeInfo projectNodeInfo = nodeCodeInfo.get(nodeInfo.getNodeCode());
        //判断节点等级，若等级为1,2 则不可以修改 跳过
        if (null != nodeInfo.getNodeLevel() && nodeInfo.getNodeLevel() > 2) {
            if (JhSystemEnum.NodeType.TEXTAREA.getValue().equals(nodeInfo.getNodeType()) ||
                    JhSystemEnum.NodeType.FORM_SELECT.getValue().equals(nodeInfo.getNodeType()) ||
                    JhSystemEnum.NodeType.FORM_DATE.getValue().equals(nodeInfo.getNodeType()) ||
                    JhSystemEnum.NodeType.INPUT_SHOW_BLOCK.getValue().equals(nodeInfo.getNodeType()) ||
                    JhSystemEnum.NodeType.RADIO_BUTTON.getValue().equals(nodeInfo.getNodeType()) ||
                    JhSystemEnum.NodeType.SINGLE.getValue().equals(nodeInfo.getNodeType()) ||
                    JhSystemEnum.NodeType.DIGITAL_SHOW_BLOCK.getValue().equals(nodeInfo.getNodeType()) ||
                    JhSystemEnum.NodeType.TABLE_VALUE.getValue().equals(nodeInfo.getNodeType()) ||
                    JhSystemEnum.NodeType.FUZZY_SEARCH.getValue().equals(nodeInfo.getNodeType()) ||
                    JhSystemEnum.NodeType.MULTIPLE.getValue().equals(nodeInfo.getNodeType()) ||
                    JhSystemEnum.NodeType.FUZZY_MULTI_INTER.getValue().equals(nodeInfo.getNodeType()) ||
                    JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_VALUE.getValue().equals(nodeInfo.getNodeType()) ||
                    JhSystemEnum.NodeType.FORM_SELECT_FILE.getValue().equals(nodeInfo.getNodeType()) ||
                    JhSystemEnum.NodeType.ROOM_MASK_POPUP.getValue().equals(nodeInfo.getNodeType())
            ) {
                nodeInfo.setRemark(ObjectUtil.isNotEmpty(projectNodeInfo) ? projectNodeInfo.getRemark() : null);
            }
        }
    }

    private void synchronizeVisaFiling(ProjectNodeInfo nodeInfo, ProjectVisaFiling projectVisaFilingDTO, ProjectInfoDto infoDto) {

        if ((projectVisaFilingDTO.getNodeCode() + "03").equals(nodeInfo.getNodeCode())) {
            nodeInfo.setRemark(projectVisaFilingDTO.getPresentationOndition());
        } else if ((projectVisaFilingDTO.getNodeCode() + "05").equals(nodeInfo.getNodeCode())) {// 标题
            // 将Timestamp转换为LocalDateTime
            LocalDateTime dateTime = projectVisaFilingDTO.getCreateTime().toLocalDateTime();
            // 使用DateTimeFormatter格式化日期时间
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String date = dateTime.format(formatter); // 格式化为年月日字符串
            nodeInfo.setRemark("营建报备流程-" + projectVisaFilingDTO.getCreateBy().split(":")[0] + "-" + date);
        } else if ((projectVisaFilingDTO.getNodeCode() + "06").equals(nodeInfo.getNodeCode())) {// 流程编号
            LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(ProjectVisaFiling.class)
                    .eq(ProjectVisaFiling::getProjectId, projectVisaFilingDTO.getProjectId());
            Long aLong = projectVisaFilingRepository.selectCount(wrapper);
            String formattedNumber = String.format("%03d", aLong);
            nodeInfo.setRemark("VF" + infoDto.getProjectNo() + formattedNumber);
        } else if ((projectVisaFilingDTO.getNodeCode() + "08").equals(nodeInfo.getNodeCode())) {// 报备人
            if (ObjectUtil.isNotEmpty(projectVisaFilingDTO.getCreateBy())) {
                nodeInfo.setRemark(projectVisaFilingDTO.getCreateBy().split(":")[0]);
            }
        } else if ((projectVisaFilingDTO.getNodeCode() + "09").equals(nodeInfo.getNodeCode())) {// 报备角色
            nodeInfo.setRemark(projectVisaFilingDTO.getRoleCode());
        } else if ((projectVisaFilingDTO.getNodeCode() + "10").equals(nodeInfo.getNodeCode())) {// 报备时间
            // 将Timestamp转换为LocalDateTime
            LocalDateTime dateTime = projectVisaFilingDTO.getCreateTime().toLocalDateTime();
            // 使用DateTimeFormatter格式化日期时间
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
            String date = dateTime.format(formatter); // 格式化为时分字符串
            nodeInfo.setRemark(date);
        } else if ((projectVisaFilingDTO.getNodeCode() + "11").equals(nodeInfo.getNodeCode())) {// 报备项目
            nodeInfo.setRemark(infoDto.getProjectName());
        } else if ((projectVisaFilingDTO.getNodeCode() + "12").equals(nodeInfo.getNodeCode())) {// 所属战区
            LambdaQueryWrapper<ProjectInfoExpansion> queryWrapper = Wrappers.lambdaQuery(ProjectInfoExpansion.class)
                    .eq(ProjectInfoExpansion::getHotelId, infoDto.getProjectNo());
            ProjectInfoExpansion projectInfoExpansion = projectInfoExpansionRepository.selectOne(queryWrapper);
            if (ObjectUtil.isNotEmpty(projectInfoExpansion) && ObjectUtil.isNotEmpty(projectInfoExpansion.getOperateArea())) {
                DictDetail dictDetailByValue = dictDetailRepository.findDictDetailByValueA(projectInfoExpansion.getOperateArea(), 196L);
                if (dictDetailByValue != null) {
                    nodeInfo.setRemark(dictDetailByValue.getLabel());
                }
            }
        } else if ((projectVisaFilingDTO.getNodeCode() + "13").equals(nodeInfo.getNodeCode())) {// 开发战区
            if (infoDto.getRegion() != null) {
                DictDetail dictDetailByValue = dictDetailRepository.findDictDetailByValueRegion(infoDto.getRegion());
                if (dictDetailByValue != null) {
                    nodeInfo.setRemark(dictDetailByValue.getLabel());
                }
            }
        } else if ((projectVisaFilingDTO.getNodeCode() + "14").equals(nodeInfo.getNodeCode())) {// 项目品牌
            if (infoDto.getBrandCode() != null) {
                DictDetail dictDetailByValue = dictDetailRepository.findDictDetailByValueA(infoDto.getBrandCode(), 168L);
                if (dictDetailByValue != null) {
                    nodeInfo.setRemark(dictDetailByValue.getLabel());
                }
            }

        }
//        else if ((projectVisaFilingDTO.getNodeCode() + "15").equals(nodeInfo.getNodeCode())) {// 项目状态
//            nodeInfo.setRemark(projectVisaFilingDTO.getPresentationOndition());
//        }
        else if ((projectVisaFilingDTO.getNodeCode() + "16").equals(nodeInfo.getNodeCode())) {// 开发经理
            if (ObjectUtil.isNotEmpty(projectVisaFilingDTO.getStringStringMap())) {
                nodeInfo.setRemark(projectVisaFilingDTO.getStringStringMap().get(JhSystemEnum.VFNodeCode.CODE7.getRoleCode()));
            }
        } else if ((projectVisaFilingDTO.getNodeCode() + "17").equals(nodeInfo.getNodeCode())) {// 项目经理
            if (ObjectUtil.isNotEmpty(projectVisaFilingDTO.getStringStringMap())) {
                nodeInfo.setRemark(projectVisaFilingDTO.getStringStringMap().get(JhSystemEnum.VFNodeCode.CODE8.getRoleCode()));
            }
        } else if ((projectVisaFilingDTO.getNodeCode() + "18").equals(nodeInfo.getNodeCode())) {// 设计师
            if (ObjectUtil.isNotEmpty(projectVisaFilingDTO.getStringStringMap())) {
                nodeInfo.setRemark(projectVisaFilingDTO.getStringStringMap().get(JhSystemEnum.VFNodeCode.CODE9.getRoleCode()));
            }
        } else if ((projectVisaFilingDTO.getNodeCode() + "19").equals(nodeInfo.getNodeCode())) {// 机电工程师
            if (ObjectUtil.isNotEmpty(projectVisaFilingDTO.getStringStringMap())) {
                nodeInfo.setRemark(projectVisaFilingDTO.getStringStringMap().get(JhSystemEnum.VFNodeCode.CODE10.getRoleCode()));
            }
        } else if ((projectVisaFilingDTO.getNodeCode() + "20").equals(nodeInfo.getNodeCode())) {// 弱电工程师
            if (ObjectUtil.isNotEmpty(projectVisaFilingDTO.getStringStringMap())) {
                nodeInfo.setRemark(projectVisaFilingDTO.getStringStringMap().get(JhSystemEnum.VFNodeCode.CODE11.getRoleCode()));
            }
        } else if ((projectVisaFilingDTO.getNodeCode() + "21").equals(nodeInfo.getNodeCode())) {// 报备大类
            nodeInfo.setRemark(projectVisaFilingDTO.getReportingCategory());
        } else if ((projectVisaFilingDTO.getNodeCode() + "22").equals(nodeInfo.getNodeCode())) {// 报备内容
            nodeInfo.setRemark(projectVisaFilingDTO.getReportContent());
        } else if ((projectVisaFilingDTO.getNodeCode() + "35").equals(nodeInfo.getNodeCode())) {// 酒店ID
            nodeInfo.setRemark(infoDto.getProjectNo());
        }
    }

    private void synchronizeSpotCheckFile(ArrayList<ProjectNodeInfo> projectNodeInfos, ProjectSpotCheck projectSpotCheck) {
        //分表查询
        util.setProjectTableName(projectSpotCheck.getProjectId());
        Map<String, ProjectNodeInfo> nodeCodeInfo = projectSpotCheck.getNodeCodeInfo();
        Map<String, ProjectNodeInfo> collect = projectNodeInfos.stream().collect(Collectors.toMap(i -> i.getNodeCode(), j -> j, (k1, k2) -> k1));
        for (ProjectNodeInfo projectNodeInfo : projectNodeInfos) {
            if (ObjectUtil.isNotEmpty(projectNodeInfo.getNodeLevel()) && 3 == projectNodeInfo.getNodeLevel()
                    && JhSystemEnum.NodeType.FILE_UPLOAD.getValue().equals(projectNodeInfo.getNodeType()) && ObjectUtil.isNotEmpty(projectNodeInfo)) {
                ProjectNodeInfo projectNodeInfoOld = nodeCodeInfo.get(projectNodeInfo.getNodeCode());
                List<LocalStorage> byNodeId = localStorageService.findByNodeId(projectNodeInfoOld.getNodeId().toString());
                for (LocalStorage localStorageOld : byNodeId) {
                    LocalStorage localStorage = new LocalStorage();
                    BeanUtil.copyProperties(localStorageOld, localStorage, CopyOptions.create().setIgnoreNullValue(true));
                    localStorage.setId(null);
                    localStorage.setNodeId(String.valueOf(projectNodeInfo.getNodeId()));
                    localStorageRepository.insert(localStorage);
                }
            } else if (JhSystemEnum.NodeType.DYNAMIC_CONDITION_LIST_VALUE.getValue().equals(projectNodeInfo.getNodeType())) {
                ProjectNodeInfo projectNodeInfoOld = nodeCodeInfo.get(projectNodeInfo.getNodeCode());
                if (ObjectUtil.isNotEmpty(projectNodeInfoOld)) {
                    projectNodeInfo.setRemark(ObjectUtil.isNotEmpty(projectNodeInfoOld) ? projectNodeInfoOld.getRemark() : null);
                }
                projectNodeInfoRepository.updateById(projectNodeInfo);
            }
        }
        JhSystemEnum.twoNodeCodeTEnum twoNodeCodeT = JhSystemEnum.twoNodeCodeTEnum.getTwoNodeCodeT(projectSpotCheck.getNodeCode());
        if (ObjectUtil.isNotEmpty(twoNodeCodeT)) {
            if (ObjectUtil.isNotEmpty(twoNodeCodeT.getIsProjectRoom())) {
                //赋值样板间确定数据
                List<ProjectRoom> projectRooms = projectRoomService.listByComplete(projectSpotCheck.getProjectId());
                for (ProjectRoom projectRoom : projectRooms) {
                    projectRoom.setProjectId(projectSpotCheck.getId());
                    projectRoom.setId(null);
                }
                projectRoomService.saveOrUpdateBatch(projectRooms);
            }
            if (ObjectUtil.isNotEmpty(twoNodeCodeT.getIsProjectCGZP())) {
                //赋值 施工照片 数据
                ConstructionPhotographQueryCriteria criteria = new ConstructionPhotographQueryCriteria();
                criteria.setProjectId(projectSpotCheck.getProjectId());
                List<ConstructionPhotographDto> constructionPhotographDtos = constructionPhotographService.queryAll(criteria);
                List<ConstructionPhotograph> constructionPhotographs = new ArrayList<>();
                for (ConstructionPhotographDto photographDto : constructionPhotographDtos) {
                    ConstructionPhotograph constructionPhotograph = new ConstructionPhotograph();
                    BeanUtil.copyProperties(photographDto, constructionPhotograph, CopyOptions.create().setIgnoreNullValue(true));
                    constructionPhotograph.setProjectId(projectSpotCheck.getId());
                    constructionPhotograph.setConstructionId(null);
                    constructionPhotographs.add(constructionPhotograph);
                }
                constructionPhotographService.saveOrUpdateBatch(constructionPhotographs);
            }
            if (ObjectUtil.isNotEmpty(twoNodeCodeT.getNodeCodeCG())) {
                //常规检查不为空，赋值
                QualityControlQueryCriteria criteria = new QualityControlQueryCriteria();
                criteria.setProjectId(projectSpotCheck.getProjectId());
                criteria.setNodeCode(twoNodeCodeT.getNodeCodeCG());
                List<QualityControlDto> controlDtos = qualityControlService.queryByProjectId(criteria);
                List<QualityControl> controls = new ArrayList<>();
                for (QualityControlDto controlDto : controlDtos) {
                    QualityControl qualityControl = new QualityControl();
                    BeanUtil.copyProperties(controlDto, qualityControl, CopyOptions.create().setIgnoreNullValue(true));
                    qualityControl.setQualityControlId(null);
                    qualityControl.setProjectId(projectSpotCheck.getId());
                    controls.add(qualityControl);
                }
                qualityControlService.saveOrUpdateBatch(controls);
            }
            if (ObjectUtil.isNotEmpty(twoNodeCodeT.getNodeCodeCZ())) {
                /* 擅自施工 */
                UnauthorizedConstructionQueryCriteria queryCriteria = new UnauthorizedConstructionQueryCriteria();
                queryCriteria.setProjectId(projectSpotCheck.getProjectId());
                queryCriteria.setNodeCode(twoNodeCodeT.getNodeCodeCZ());
                List<UnauthorizedConstructionDto> unauthorizedConstructionDtos = unauthorizedConstructionService.queryAll(queryCriteria);
                List<UnauthorizedConstruction> unauthorizedConstructions = new ArrayList<>();
                for (UnauthorizedConstructionDto constructionDto : unauthorizedConstructionDtos) {
                    UnauthorizedConstruction unauthorizedConstruction = new UnauthorizedConstruction();
                    BeanUtil.copyProperties(constructionDto, unauthorizedConstruction, CopyOptions.create().setIgnoreNullValue(true));
                    unauthorizedConstruction.setUnauthorizedId(null);
                    unauthorizedConstruction.setProjectId(projectSpotCheck.getId());
                    unauthorizedConstructions.add(unauthorizedConstruction);
                }
                unauthorizedConstructionService.saveOrUpdateBatch(unauthorizedConstructions);
            }
            if (ObjectUtil.isNotEmpty(twoNodeCodeT.getNodeCodeWZ())) {
                ProjectNodeInfo nodeInfo = collect.get(twoNodeCodeT.getNodeCodeWZ());
                if (ObjectUtil.isNotEmpty(nodeInfo)) {
                    nodeInfo.setIsShow(0);
                }
                projectNodeInfoRepository.updateById(nodeInfo);

                /* 物资管理材料 */
                ProjectMaterialManagementQueryCriteria criteria1 = new ProjectMaterialManagementQueryCriteria();
                criteria1.setProjectId(projectSpotCheck.getProjectId());
                criteria1.setNodeCode(twoNodeCodeT.getNodeCodeWZ());
                List<ProjectMaterialManagementDto> projectMaterialManagements = projectMaterialManagementService.queryAll(criteria1);
                List<ProjectMaterialManagement> projectMaterialManagementList = new ArrayList<>();
                for (ProjectMaterialManagementDto materialManagement : projectMaterialManagements) {
                    ProjectMaterialManagement projectMaterialManagement = new ProjectMaterialManagement();
                    BeanUtil.copyProperties(materialManagement, projectMaterialManagement, CopyOptions.create().setIgnoreNullValue(true));
                    projectMaterialManagement.setProjectManagementId(null);
                    projectMaterialManagement.setProjectId(projectSpotCheck.getId());
                    projectMaterialManagementList.add(projectMaterialManagement);
                }
                projectMaterialManagementService.saveOrUpdateBatch(projectMaterialManagementList);
            }
            if (ObjectUtil.isNotEmpty(twoNodeCodeT.getNodeCodeZZ())) {
                /*证照管理集合*/
                ProjectCateInfo projectCateInfo = new ProjectCateInfo();
                projectCateInfo.setProjectId(projectSpotCheck.getProjectId());
                projectCateInfo.setThreeNodeCode(twoNodeCodeT.getNodeCodeZZ());
                projectCateInfo.setTwoNodeCode(projectSpotCheck.getNodeCode());
                List<ProjectCateInfo> projectCateInfoList = projectCateInfoService.queryAllList(projectCateInfo);
                for (ProjectCateInfo cateInfo : projectCateInfoList) {
                    cateInfo.setCateId(null);
                    cateInfo.setProjectId(projectSpotCheck.getId());
                }
                projectCateInfoService.saveOrUpdateBatch(projectCateInfoList);
            }


            if (ObjectUtil.isNotEmpty(twoNodeCodeT.getIsProjectRDSCSB())) {
                /*弱电设施设备信息表*/
                ProjectWeakCurrentQueryCriteria criter = new ProjectWeakCurrentQueryCriteria();
                criter.setProjectId(projectSpotCheck.getProjectId());
                List<ProjectWeakCurrentDto> projectWeakCurrentDtos = projectWeakCurrentService.queryAll(criter);
                List<ProjectWeakCurrent> projectWeakCurrents = new ArrayList<>();
                for (ProjectWeakCurrentDto weakCurrentDto : projectWeakCurrentDtos) {
                    ProjectWeakCurrent projectWeakCurrent = new ProjectWeakCurrent();
                    BeanUtil.copyProperties(weakCurrentDto, projectWeakCurrent, CopyOptions.create().setIgnoreNullValue(true));
                    projectWeakCurrent.setProjectWeakCurrentId(null);
                    projectWeakCurrent.setProjectId(projectSpotCheck.getId());
                    projectWeakCurrents.add(projectWeakCurrent);
                }
                projectWeakCurrentService.saveOrUpdateBatch(projectWeakCurrents);
            }


            if (ObjectUtil.isNotEmpty(twoNodeCodeT.getNodeCodeRDYSD())) {
                //弱电验收 - 验收单
                List<ProjectTableNodeInfo> dynamicMaskClick = projectInfoService.getDynamicMaskClick(projectSpotCheck.getProjectId(), twoNodeCodeT.getNodeCodeRDYSD());
                for (ProjectTableNodeInfo tableNodeInfo : dynamicMaskClick) {
                    tableNodeInfo.setNodeId(null);
                    tableNodeInfo.setProjectId(projectSpotCheck.getId());
                }
                projectInfoService.saveDynamicMaskClick(dynamicMaskClick);
            }

            if (ObjectUtil.isNotEmpty(twoNodeCodeT.getNodeCodeRDZGQD())) {
                //弱电整改 - 整改清单
                List<ProjectTableNodeInfo> rectificationList = this.getDynamicMaskRectificationList(projectSpotCheck.getProjectId(), 1700404548427971231L, twoNodeCodeT.getNodeCodeRDZGQD());
                for (ProjectTableNodeInfo tableNodeInfo : rectificationList) {
                    tableNodeInfo.setNodeId(null);
                    tableNodeInfo.setProjectId(projectSpotCheck.getId());
                }
                projectInfoService.saveDynamicMaskClick(rectificationList);
            }


            if (ObjectUtil.isNotEmpty(twoNodeCodeT.getNodeCodeJGZJ())) {
                /*竣工系统自检表*/
                ProjectSystemSelfInspectionQueryCriteria criteria = new ProjectSystemSelfInspectionQueryCriteria();
                criteria.setProjectId(projectSpotCheck.getProjectId());
                criteria.setNodeCode(twoNodeCodeT.getNodeCodeJGZJ());
                List<ProjectSystemSelfInspectionDto> systemSelfInspectionDto = projectSystemSelfInspectionService.getList(criteria);
                List<ProjectSystemSelfInspection> projectSystemSelfInspections = new ArrayList<>();
                for (ProjectSystemSelfInspectionDto inspectionDto : systemSelfInspectionDto) {
                    ProjectSystemSelfInspection inspection = new ProjectSystemSelfInspection();
                    BeanUtil.copyProperties(inspectionDto, inspection, CopyOptions.create().setIgnoreNullValue(true));
                    inspection.setSystemSelfInspectionId(null);
                    inspection.setProjectId(projectSpotCheck.getId());
                    projectSystemSelfInspections.add(inspection);
                }
                projectSystemSelfInspectionService.saveOrUpdateBatch(projectSystemSelfInspections);
            }

            if (ObjectUtil.isNotEmpty(twoNodeCodeT.getIsProjectYSDSM())) {
                /*竣工验收 - 验收说明*/
                ProjectCompletionReceiptDescriptionQueryCriteria criteria = new ProjectCompletionReceiptDescriptionQueryCriteria();
                criteria.setProjectId(projectSpotCheck.getProjectId());
                ProjectCompletionReceiptDescription byProjectId = projectCompletionReceiptDescriptionService.getByProjectId(criteria);
                byProjectId.setId(null);
                byProjectId.setProjectId(projectSpotCheck.getId());
                projectCompletionReceiptDescriptionService.saveOrUpdate(byProjectId);
            }

            if (ObjectUtil.isNotEmpty(twoNodeCodeT.getIsProjectYSHG())) {
                /*竣工验收 - 验收合格*/
                ProjectCompletionReceiptSummary summary = projectCompletionReceiptSummaryService.selectByProjectId(projectSpotCheck.getProjectId());
                summary.setId(null);
                summary.setProjectId(projectSpotCheck.getId());
                projectCompletionReceiptSummaryService.saveOrUpdate(summary);
            }

            if (ObjectUtil.isNotEmpty(twoNodeCodeT.getIsProjectYSD())) {
                /*竣工验收 - 验收单 验收实拍照片 其他照片*/
                ProjectCompletionReceiptQueryCriteria criteria = new ProjectCompletionReceiptQueryCriteria();
                criteria.setProjectId(projectSpotCheck.getProjectId());
                ProjectCompletionReceiptListDto listBySubItem = projectCompletionReceiptService.getListBySubItem(criteria);
                //验收单
                List<ProjectCompletionReceipt> receiptDtos = listBySubItem.getReceiptDtos();
                for (ProjectCompletionReceipt receipt : receiptDtos) {
                    receipt.setProjectReceiptId(null);
                    receipt.setProjectId(projectSpotCheck.getId());
                }
                projectCompletionReceiptService.saveOrUpdateBatch(receiptDtos);
                //必拍现场照片
                List<ProjectCompletionReceipt> mustPhotoDtos = listBySubItem.getMustPhotoDtos();
                for (ProjectCompletionReceipt photoDto : mustPhotoDtos) {
                    photoDto.setProjectReceiptId(null);
                    photoDto.setProjectId(projectSpotCheck.getId());
                }
                projectCompletionReceiptService.saveOrUpdateBatch(mustPhotoDtos);
                //竣工验收照片
                List<ProjectCompletionPhoto> photoDtos = listBySubItem.getPhotoDtos();
                for (ProjectCompletionPhoto photoDto : photoDtos) {
                    photoDto.setProjectReceiptId(null);
                    photoDto.setProjectId(projectSpotCheck.getId());
                }
                projectCompletionPhotoService.saveOrUpdateBatch(photoDtos);
            }

            if (ObjectUtil.isNotEmpty(twoNodeCodeT.getIsProjectYSDZG())) {
                /*竣工验收整改 - 验收整改清单*/
                ProjectCompletionReceiptQueryCriteria criteria = new ProjectCompletionReceiptQueryCriteria();
                criteria.setProjectId(projectSpotCheck.getProjectId());

                /**
                 * 查询所有数据不分页
                 *
                 * @param criteria 条件参数
                 * @return List<ProjectCompletionReceiptDto>
                 */
                List<ProjectCompletionReceiptDto> completionReceiptDtos = projectCompletionReceiptService.queryAll(criteria);
                List<ProjectCompletionReceipt> completionReceipts = new ArrayList<>();
                //验收单
                for (ProjectCompletionReceiptDto receipt : completionReceiptDtos) {
                    ProjectCompletionReceipt receipt1 = new ProjectCompletionReceipt();
                    BeanUtil.copyProperties(receipt, receipt1, CopyOptions.create().setIgnoreNullValue(true));
                    receipt1.setProjectReceiptId(null);
                    receipt1.setProjectId(projectSpotCheck.getId());
                    completionReceipts.add(receipt1);
                }
                projectCompletionReceiptService.saveOrUpdateBatch(completionReceipts);
            }

            if (ObjectUtil.isNotEmpty(twoNodeCodeT.getNodeCodeTXS())) {
                /*竣工验收整改 - 特许商特殊需求*/
                ProjectSpecialNeedsFranchisorsQueryCriteria criteria = new ProjectSpecialNeedsFranchisorsQueryCriteria();
                criteria.setProjectId(projectSpotCheck.getProjectId());
                List<ProjectSpecialNeedsFranchisorsDto> franchisorsDtos = projectSpecialNeedsFranchisorsService.queryAll(criteria);
                List<ProjectSpecialNeedsFranchisors> franchisorsList = new ArrayList<>();
                for (ProjectSpecialNeedsFranchisorsDto franchisorsDto : franchisorsDtos) {
                    ProjectSpecialNeedsFranchisors franchisors = new ProjectSpecialNeedsFranchisors();
                    BeanUtil.copyProperties(franchisorsDto, franchisors, CopyOptions.create().setIgnoreNullValue(true));
                    franchisors.setFranchisorsId(null);
                    franchisors.setProjectId(projectSpotCheck.getId());
                    franchisorsList.add(franchisors);
                }
                projectSpecialNeedsFranchisorsService.saveOrUpdateBatch(franchisorsList);
            }
        }
    }

    //    ProjectNodeInfo nodeInfo
    private void synchronizeVisaFilingFile(ArrayList<ProjectNodeInfo> projectNodeInfos, ProjectVisaFiling projectVisaFilingDTO) {
        for (ProjectNodeInfo projectNodeInfo : projectNodeInfos) {
            if (ObjectUtil.isNotEmpty(projectNodeInfo.getNodeLevel()) && 3 == projectNodeInfo.getNodeLevel()) {
                if (projectNodeInfo.getNodeType().equals(JhSystemEnum.NodeType.FILE_UPLOAD.getValue())
                        && ObjectUtil.isNotEmpty(projectVisaFilingDTO.getAttachment())) {
                    for (String localId : projectVisaFilingDTO.getAttachment().split(",")) {
                        LocalStorageDto localStorageDto = localStorageService.findById(Long.valueOf(localId));
                        LocalStorage localStorage = new LocalStorage();
                        BeanUtil.copyProperties(localStorageDto, localStorage, CopyOptions.create().setIgnoreNullValue(true));
                        localStorage.setId(null);
                        localStorage.setNodeId(String.valueOf(projectNodeInfo.getNodeId()));
                        localStorageRepository.insert(localStorage);
                        projectNodeInfo.setRemark(projectVisaFilingDTO.getAttachment());
                    }
                }
            }
        }
    }

    private void insertDevDec(ProjectInfoExpansion projectInfoExpansion, ProjectNodeInfo nodeInfo) {
        if (AtourSystemEnum.SurveyReport.DEC00501002.getOutKey().equals(nodeInfo.getNodeCode())) {
            List<HlmProjectInfoOssFileVO> hlmProjectInfoOssFiles = hlmProjectInfoOssFileRepository.getByProjectId(projectInfoExpansion.getProjectId());
            Optional.ofNullable(hlmProjectInfoOssFiles).ifPresent(hlm -> {
                //TODO contains("筹建")
                final String collect = hlm.stream().filter(f -> f.getFileName().contains("筹建")).map(HlmProjectInfoOssFileVO::getFileId).collect(Collectors.joining(","));
                nodeInfo.setRemark(collect);
            });
        }

        if (AtourSystemEnum.SurveyReport.DEC00701002.getOutKey().equals(nodeInfo.getNodeCode())) {
            List<HlmProjectInfoOssFileVO> hlmProjectInfoOssFiles = hlmProjectInfoOssFileRepository.getByProjectId(projectInfoExpansion.getProjectId());
            Optional.ofNullable(hlmProjectInfoOssFiles).ifPresent(hlm -> {
                final String collect = hlm.stream().filter(f -> f.getFileName().contains("项目决策报告")).map(HlmProjectInfoOssFileVO::getFileId).collect(Collectors.joining(","));
                nodeInfo.setRemark(collect);
            });
        }
    }

    public void getHlmOssFile(Integer projectHlmId, ProjectNodeInfo nodeInfo) throws IOException {
        List<HlmProjectInfoOssFileVO> hlmProjectInfoOssFiles = hlmProjectInfoOssFileRepository.getByProjectId(projectHlmId);
        if (ObjectUtils.isNotEmpty(hlmProjectInfoOssFiles)) {
            //1220前端要逗号拼接的格式，方便附件展示
            String collect = hlmProjectInfoOssFiles.stream().map(HlmProjectInfoOssFileVO::getFileId).collect(Collectors.joining(","));
            nodeInfo.setRemark(collect);
        }

    }

    private HlmProjectInfoOwner getHlmProjectInfoOwner(ProjectInfoExpansion projectInfoExpansion, ProjectInfoDto
            resources) {
        final User one = userRepository.getOne(Long.valueOf(projectInfoExpansion.getOwnerProjectManager()));
        final LambdaQueryWrapper<HlmProjectInfoOwner> eq = Wrappers.lambdaQuery(HlmProjectInfoOwner.class)
                .eq(ObjectUtils.isNotEmpty(resources.getProjectHlmId()), HlmProjectInfoOwner::getProjectId, resources.getProjectHlmId())
                .eq(ObjectUtils.isNotEmpty(projectInfoExpansion.getHotelId()), HlmProjectInfoOwner::getHotelId, projectInfoExpansion.getHotelId())
                .eq(ObjectUtil.isNotEmpty(one) && ObjectUtil.isNotEmpty(one.getPhone()), HlmProjectInfoOwner::getMobile, one.getPhone())
                .last("limit 1");
        return hlmProjectInfoOwnerRepository.selectOne(eq);
    }

    @Override
    public Boolean createAdjustProject(ProjectInfoDto resources) {

//        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
//        Long storeMasterParam = resources.getStoreMasterId();
//        String projectType1 = resources.getProjectType();
//        Long projectId = snowflake.nextId();
//        if (resources.getProjectId() != null) {
//            projectId = resources.getProjectId();
//        } else {
//            resources.setProjectId(projectId);
//        }
////        String code = RandomUtil.randomNumbers (8);
//        String current = cn.hutool.core.date.DateUtil.format(new Date(), "yyyyMMdd");
////        redisTemplate.
//
////        String projectType1 = resources.getProjectType();
//        String projectType = resources.getProjectType();
//
//        String key = PROJECT_NO_PREFIX + projectType + current;
//        Integer value = (Integer) redisUtils.get(key);
//
//        String num = "";
//        String prefix = "";
//        StoreMasterInfoDto storeMasterInfoDto = new StoreMasterInfoDto();
//
//        if (KidsSystemEnum.ProcessType.Adjust.getValue().equals(projectType)) {
//            if (resources.getStoreMasterId() != null) {
//                resources.setStoreId(resources.getStoreMasterId());
//            }
//            if (resources.getStoreNo() != null) {
//                resources.setStoreNo(resources.getStoreNo());
//            }
//            Long storeMasterId = resources.getStoreMasterId();
//            storeMasterInfoDto = storeMasterInfoService.findById(storeMasterId);
//        }
//
//        if (KidsSystemEnum.ProcessType.NEW.getValue().equals(projectType)) {
//            prefix = "XD";
//
//            //入主档信息
//            StoreMasterInfo storeMasterInfo = new StoreMasterInfo();
//            BeanUtils.copyProperties(resources, storeMasterInfo);
//            Snowflake snowflakemaster = IdUtil.getSnowflake(1, 1);
//            Long masterId = snowflakemaster.nextId();
//            //todo
//            resources.setStoreId(masterId);
//            storeMasterInfo.setStoreMasterId(masterId);
//            storeMasterInfoService.saveOrUpdate(storeMasterInfo);
//
//        } else if (KidsSystemEnum.ProcessType.CLOSE.getValue().equals(projectType)) {
//            prefix = "CL";
//        } else if (KidsSystemEnum.ProcessType.MAJOR.getValue().equals(projectType)
//                || KidsSystemEnum.ProcessType.MINOR.getValue().equals(projectType)
//                || KidsSystemEnum.ProcessType.REFORM.getValue().equals(projectType)) {
//            prefix = "GZ";
//
//            //处理改造项目数据
//            if (resources.getStoreMasterId() != null) {
//                Long storeMasterId = resources.getStoreMasterId();
//                storeMasterInfoDto = storeMasterInfoService.findById(storeMasterId);
//            }
//
//        }
//
//
//        if (null == value) {
//            redisUtils.set(key, 1, 86400, TimeUnit.SECONDS);
//            num = "01";
//        } else {
//            Long increment = redisUtils.increment(key);
//            StringBuilder s = new StringBuilder(increment.toString());
//            for (int i = s.length(); i < 2; i++) {
//                s.insert(0, "0");
//            }
//            num = s.toString();
//        }
//
//
//        String code = "SJ" + current + num;
//
//        resources.setIsDelete(false);
//        resources.setProjectNo(code);
//        resources.setIsOverdue(false);
//        resources.setTaskPhase(JhSystemEnum.TaskPhaseEnum.PREPARE_PHASE.getKey());
//        resources.setProjectStatus(JhSystemEnum.ProjectStatusEnum.PROJECT_PREPARE.getKey());
//        //城市code
//        Long city = resources.getCity();
//        ProjectInfo projectInfo = projectInfoMapper.toEntity(resources);
//        if (storeMasterInfoDto != null && storeMasterInfoDto.getStoreMasterId() != null) {
//            BeanUtils.copyProperties(storeMasterInfoDto, projectInfo, "createBy", "createTime", "updateBy", "updateTime", "isDelete", "isActive", "isEnabled");
//            projectInfo.setStoreId(storeMasterInfoDto.getStoreMasterId());
//        }
//
//
//        //1立项信息入库
//        this.save(projectInfo);
//
//        //2入干系人
//        if (resources.getConstructionManager() != null) {
//            //工程经理
//            createProjectStakeholders(projectId, JhSystemEnum.JobEnum.GCJL.getKey(), resources.getConstructionManager(), null);
//        } else {
//            createProjectStakeholders(projectId, JhSystemEnum.JobEnum.GCJL.getKey(), 0L, null);
//        }
//        if (resources.getRegionNetManager() != null) {
//            //区域经理
//            createProjectStakeholders(projectId, JhSystemEnum.JobEnum.QYJL.getKey(), resources.getRegionNetManager(), null);
//        } else {
//            createProjectStakeholders(projectId, JhSystemEnum.JobEnum.QYJL.getKey(), 0L, null);
//        }
//        if (resources.getEngineerDirector() != null) {
//            //总部经理
//            createProjectStakeholders(projectId, JhSystemEnum.JobEnum.ZBJL.getKey(), resources.getEngineerDirector(), null);
//        } else {
//            createProjectStakeholders(projectId, JhSystemEnum.JobEnum.ZBJL.getKey(), 0L, null);
//        }
//        //中心负责人
//        createIsNotShowSta(projectId, city, JhSystemEnum.JobEnum.ZBZXFZR.getKey(), Boolean.FALSE);
//        //风险管控
//        createIsNotShowSta(projectId, city, JhSystemEnum.JobEnum.FXGK.getKey(), Boolean.FALSE);
//        //设计经理
//        createIsNotShowSta(projectId, city, JhSystemEnum.JobEnum.SJJL.getKey(), Boolean.FALSE);
//        //设计负责人
//        createIsNotShowSta(projectId, city, JhSystemEnum.JobEnum.SJFZR.getKey(), Boolean.FALSE);
//        //甲供材经理
//        createIsNotShowSta(projectId, city, JhSystemEnum.JobEnum.JGCJL.getKey(), Boolean.FALSE);
//        //设计负责人
//        createIsNotShowSta(projectId, city, JhSystemEnum.JobEnum.JGCZL.getKey(), Boolean.FALSE);
//        //工程财务
//        createIsNotShowSta(projectId, city, JhSystemEnum.JobEnum.GCCW.getKey(), Boolean.FALSE);
//
//
//        //3根据门店信息查找模板
//        //门店类型
//        String storeType = resources.getStoreType();
//        // 获取创建项目的projectGroupId
//        Long createProjectProjectGroupId = null;
//
//        LambdaQueryWrapper<StoreTemplateRelation> storeTemplateRelationLambdaQueryWrapper = Wrappers.lambdaQuery(StoreTemplateRelation.class);
//        storeTemplateRelationLambdaQueryWrapper.eq(StoreTemplateRelation::getStoreType, storeType).eq(StoreTemplateRelation::getProjectType, projectType);
//        StoreTemplateRelation storeTemplateRelation = storeTemplateRelationRepository.selectOne(storeTemplateRelationLambdaQueryWrapper);
//        if (storeTemplateRelation != null) {
//            String templateCode = storeTemplateRelation.getTemplateCode();
//            Long templateId = storeTemplateRelation.getTemplateId();
//            if (templateCode != null) {
//                String lOneDate = DateUtil.getNowDate();
//                String lTwoDate = DateUtil.getNowDate();
//                if (resources.getProjectCreateDate() != null) {
//                    lTwoDate = DateUtil.changeDate(resources.getProjectCreateDate());
//                }
//
//                NodeInfo nodeInfo = new NodeInfo();
//                nodeInfo.setBegin(lTwoDate);
//                //3查找相应模板
//                LambdaQueryWrapper<TemplateGroup> templateGroupLambdaQueryWrapper = Wrappers.lambdaQuery(TemplateGroup.class).eq(TemplateGroup::getTemplateCode, templateCode);
//                List<TemplateGroup> templateGroups = templateGroupRepository.selectList(templateGroupLambdaQueryWrapper);
//                List<ProjectGroup> groupInfoForCount = new LinkedList<>();
//                //4模板插入相应node表
//                ArrayList<ProjectNodeInfo> projectNodeInfos = new ArrayList<>();
//                for (TemplateGroup group : templateGroups) {
//
//                    ProjectGroup projectGroup = new ProjectGroup();
//                    BeanUtils.copyProperties(group, projectGroup);
//                    projectGroup.setProjectId(projectId);
//                    projectGroup.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
//                    projectGroup.setIsDelete(false);
//                    projectGroup.setIsEnabled(true);
//                    projectGroup.setCreateBy(null);
//                    projectGroup.setCreateTime(null);
//                    Snowflake snowflake1 = IdUtil.getSnowflake(1, 1);
//                    Long projectGroupId = snowflake1.nextId();
//                    projectGroup.setProjectGroupId(projectGroupId);
//                    if (projectGroup.getNodeCode().equals("con-00101") || projectGroup.getNodeCode().equals("con-00146") || projectGroup.getNodeCode().equals("con-00701")) {
//                        createProjectProjectGroupId = projectGroupId;
//                    }
//                    //解析紧前json
//                    if (group.getNodeLevel() == 2) {
//                        // 审批模板入库
//                        List<ProjectTemplateApproveRelationDto> appRelations = projectTemplateApproveRelationService.getAppRelation(group.getTemplateId(), group.getTemplateGroupId());
//                        //若存在审批节点，则复制当前节点数据
//                        if (ObjectUtils.isNotNull(appRelations)) {
//                            projectAppTemplateService.copyApproveTemplate(projectGroup, appRelations.get(0));
//                        }
//                        String frontWbsConfig = group.getFrontWbsConfig();
//                        if (frontWbsConfig != null) {
//                               /* List<FrontWbsConfigDto> frontList=new ArrayList<>();
//                                JSONArray jsonArray = new JSONArray(frontWbsConfig);
//                                for(int i=0;i<jsonArray.size();i++){
//                                    FrontWbsConfigDto frontDto=new FrontWbsConfigDto();
//                                    JSONObject object=jsonArray.getJSONObject(i);
//                                    String type=object.getStr("type");
//                                    String wbs=object.getStr("wbs");
//                                    if("FS".equals(type)&&"con-00101".equals(wbs)){
//                                        projectGroup.setIsOpen(Boolean.TRUE);
//                                    }
//
//                                }*/
//
//                        } else {
//                            //出待办
//                            projectTaskService.generateTodoTask(projectGroup);
//                            projectGroup.setIsOpen(Boolean.TRUE);
//                            projectGroup.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS6.getKey());
//                        }
//
//                    }
//
//
//                    groupInfoForCount.add(projectGroup);
//                    if (group.getTemplateQueueId() != null) {
//                        //获取二级三级数据
//                        LambdaQueryWrapper<TemplateQueue> parentLambdaQueryWrapper = Wrappers.lambdaQuery(TemplateQueue.class).eq(TemplateQueue::getTemplateQueueId, group.getTemplateQueueId());
//                        TemplateQueue templateQueue = templateQueueRepository.selectOne(parentLambdaQueryWrapper);
//                        if (templateQueue == null) {
//                            throw new BadRequestException("二级节点为空，请重新选择数据！");
//                        }
//                        //查找二级对应的节点
//                        LambdaQueryWrapper<ProjectTemplate> twoLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTemplate.class).eq(ProjectTemplate::getTemplateId, templateQueue.getTemplateId());
//                        ProjectTemplate template = projectTemplateRepository.selectOne(twoLambdaQueryWrapper);
//                        ProjectNodeInfo projectNodeInfo = new ProjectNodeInfo();
//                        BeanUtils.copyProperties(template, projectNodeInfo);
//                        projectNodeInfo.setTemplateQueueId(templateQueue.getTemplateQueueId());
//                        projectNodeInfo.setProjectId(projectId);
//                        projectNodeInfo.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
//                        projectNodeInfo.setIsDelete(false);
//                        projectNodeInfo.setIsEnabled(true);
//                        projectNodeInfo.setCreateBy(null);
//                        projectNodeInfo.setCreateTime(null);
//
//
//                        projectNodeInfos.add(projectNodeInfo);
//                        if (group.getNodeLevel() == 2) {
//                            //根据templateQueue查找子集
//                            LambdaQueryWrapper<TemplateQueue> queueLambdaQueryWrapper = Wrappers.lambdaQuery(TemplateQueue.class).eq(TemplateQueue::getParentId, template.getTemplateId()).eq(TemplateQueue::getTemplateCode, templateCode);
//                            List<TemplateQueue> templateQueues = templateQueueRepository.selectList(queueLambdaQueryWrapper);
//                            for (TemplateQueue queue : templateQueues) {
//                                LambdaQueryWrapper<ProjectTemplate> thirdLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTemplate.class).eq(ProjectTemplate::getTemplateId, queue.getTemplateId());
//                                ProjectTemplate thirdtemplate = projectTemplateRepository.selectOne(thirdLambdaQueryWrapper);
//                                ProjectNodeInfo thirdprojectNodeInfo = new ProjectNodeInfo();
//                                BeanUtils.copyProperties(thirdtemplate, thirdprojectNodeInfo);
//                                thirdprojectNodeInfo.setParentId(template.getTemplateId());
//                                thirdprojectNodeInfo.setTemplateQueueId(queue.getTemplateQueueId());
//                                thirdprojectNodeInfo.setProjectId(projectId);
//                                thirdprojectNodeInfo.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
//                                thirdprojectNodeInfo.setNodeIndex(queue.getNodeIndex());
//                                thirdprojectNodeInfo.setIsDelete(false);
//                                thirdprojectNodeInfo.setIsEnabled(true);
//                                thirdprojectNodeInfo.setCreateBy(null);
//                                thirdprojectNodeInfo.setCreateTime(null);
//                                //立项数据复制
//                                thirdprojectNodeInfo = copyProjectNodeInfo(thirdprojectNodeInfo, resources);
//                                thirdprojectNodeInfo = masterInfoToProjectNodeInfo(thirdprojectNodeInfo, resources);
////                                //固定值录入
////                                if (JhSystemEnum.NodeCodeSEEnum.isCreateApplyNo(group.getNodeCode())) {
////                                    thirdprojectNodeInfo = saveContractApplyInfo(thirdprojectNodeInfo);
////                                }
//
//                                //项目勘察中的项目编号从项目表中拿值
//                                if (JhSystemEnum.NodeCodeSEEnum.NODE_102.getKey().equals(projectNodeInfo.getNodeCode())) {
//                                    if (JhSystemEnum.NodeCodeEnum.NODE_10202.getKey().equals(thirdprojectNodeInfo.getNodeCode())) {
//                                        thirdprojectNodeInfo.setRemark(projectInfo.getProjectNo());
//                                    }
//                                }
//                                //门店主档涉及相关信息入pro_info
//                                if (projectType1.equals(KidsSystemEnum.ProcessType.MAJOR.getValue()) || projectType1.equals(KidsSystemEnum.ProcessType.MINOR.getValue()) || projectType1.equals(KidsSystemEnum.ProcessType.REFORM.getValue())) {
//                                    projectToMasterService.byStoreMasterUpdateProjectInfo(storeMasterParam, queue.getNodeCode(), thirdprojectNodeInfo, queue);
//                                }
//
//                                projectNodeInfos.add(thirdprojectNodeInfo);
//                            }
//
//
//                        }
//
//
//                    }
//
//                }
//                projectGroupService.saveBatch(groupInfoForCount);
//                projectNodeInfoService.saveBatch(projectNodeInfos);
////                    projectNodeInfoRepository.insert(projectNodeInfo);
//
//                // 提交项目节点
//                ProjectNodeInfoDto projectNodeInfoD = new ProjectNodeInfoDto();
//                projectNodeInfoD.setProjectId(projectId.toString());
//                projectNodeInfoD.setProjectGroupId(createProjectProjectGroupId.toString());
//                projectNodeInfoService.adjustSubmit(projectNodeInfoD);
//
//                return Boolean.TRUE;
//            } else {
//                return Boolean.FALSE;
//            }
//        }
        return Boolean.TRUE;
    }

    @Override
    public ProjectInfoDto findByIdForTitle(Long projectId) {
        // 使用java.time.LocalDateTime获取当前日期和时间
        LocalDateTime currentDateTime = LocalDateTime.now();
        ProjectInfoDto projectInfoDto = new ProjectInfoDto();
        //判断当前登录人是否在审批中
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        //todo 若没有项目id暂时任何信息都不展示
        if (projectId != null) {
            //初始化项目ID
            projectId = projectNodeInfoService.getSubmeterProjectId(projectId);
            //获取项目信息
            ProjectInfo projectInfo = Optional.ofNullable(getById(projectId)).orElseGet(ProjectInfo::new);
            ValidationUtil.isNull(projectId, getEntityClass().getSimpleName(), "projectId", projectId);

            //查询项目在项干系人
            LambdaQueryWrapper<ProjectStakeholders> projectStakeholdersWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class)
                    .eq(ProjectStakeholders::getProjectId, projectId)
                    .in(ProjectStakeholders::getRoleCode, JhSystemEnum.TitleNodeCode.getTitleNodeCode())
                    .eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
            List<ProjectStakeholders> projectStakeholders = projectStakeholdersRepository.selectList(projectStakeholdersWrapper);
            projectStakeholders.forEach(s -> {
                if (JhSystemEnum.TitleNodeCode.CODE1.getRoleCode().equals(s.getRoleCode())) {
                    projectInfo.setOperationsTheaterLeader(s.getUserId());
                }
                if (JhSystemEnum.TitleNodeCode.CODE2.getRoleCode().equals(s.getRoleCode())) {
                    projectInfo.setOpeningManager(s.getUserId());
                }
                if (JhSystemEnum.TitleNodeCode.CODE3.getRoleCode().equals(s.getRoleCode())) {
                    projectInfo.setOperationsManager(s.getUserId());
                }
                if (JhSystemEnum.TitleNodeCode.CODE4.getRoleCode().equals(s.getRoleCode())) {
                    projectInfo.setFranchiseManager(s.getUserId());
                }
                if (JhSystemEnum.TitleNodeCode.CODE5.getRoleCode().equals(s.getRoleCode())) {
                    projectInfo.setDevelopmentTheaterLeader(s.getUserId());
                }
                if (JhSystemEnum.TitleNodeCode.CODE6.getRoleCode().equals(s.getRoleCode())) {
                    projectInfo.setDevelopmentZoneLeader(s.getUserId());
                }
                if (JhSystemEnum.TitleNodeCode.CODE12.getRoleCode().equals(s.getRoleCode())) {
                    projectInfo.setDesignCoManagementPersonnel(s.getUserId());
                }
                if (JhSystemEnum.TitleNodeCode.CODE13.getRoleCode().equals(s.getRoleCode())) {
                    projectInfo.setWeakCurrentAcceptancePersonnel(s.getUserId());
                }
                if (JhSystemEnum.TitleNodeCode.CODE14.getRoleCode().equals(s.getRoleCode())) {
                    projectInfo.setMechanicalAndElectricalAcceptancePersonnel(s.getUserId());
                }
                if (JhSystemEnum.TitleNodeCode.CODE16.getRoleCode().equals(s.getRoleCode())) {
                    projectInfo.setCompletionAcceptancePersonnel(s.getUserId());
                }
                if (JhSystemEnum.TitleNodeCode.CODE17.getRoleCode().equals(s.getRoleCode())) {
                    projectInfo.setProcurementMarketing(s.getUserId());
                }

//                if (JhSystemEnum.TitleNodeCode.CODE7.getRoleCode().equals(s.getRoleCode())) {
//                    projectInfo.setDevelopmentManager(s.getUserId());
//                }
//                if (JhSystemEnum.TitleNodeCode.CODE8.getRoleCode().equals(s.getRoleCode())) {
//                    projectInfo.setProjectManager(s.getUserId());
//                }
//                if (JhSystemEnum.TitleNodeCode.CODE9.getRoleCode().equals(s.getRoleCode())) {
//                    projectInfo.setDesigner(s.getUserId());
//                }
//                if (JhSystemEnum.TitleNodeCode.CODE10.getRoleCode().equals(s.getRoleCode())) {
//                    projectInfo.setMechanicalAndElectricalEngineer(s.getUserId());
//                }
//                if (JhSystemEnum.TitleNodeCode.CODE11.getRoleCode().equals(s.getRoleCode())) {
//                    projectInfo.setWeakCurrentEngineer(s.getUserId());
//                }
//                if (JhSystemEnum.TitleNodeCode.CODE15.getRoleCode().equals(s.getRoleCode())) {
//                    projectInfo.setFlightQualityInspectionPersonnel(s.getUserId());
//                }
            });

            projectInfoDto = projectInfoMapper.toDto(projectInfo);
            //，ljp 项目启动计划日期
            projectInfoDto.setProjectPlanStart(projectInfo.getProjectPlanStart());
            //查询当前角色的【筹建启动菜单】有没有提交按钮
            projectInfoDto.setPrepareSubmitted(this.setPrepareSubmitted(currentUserId, projectInfo, projectId));


            String nodeCodes = "eng-00107,eng-00127,eng-00133";
            List<ProjectNodeInfoDto> dtos = projectNodeInfoRepository.getAllNodeInfo(projectId, null, null, null, nodeCodes);
            for (ProjectNodeInfoDto dto : dtos) {
                if ("eng-00107".equals(dto.getNodeCode())) {
                    //正式开工 计划开工日期 调整开工日期 实际开工日期
                    projectInfoDto.setPlannedStartDate(dto.getPlanEndDate());
                    projectInfoDto.setAdjustingThestartDate(dto.getPredictEndDate());
                    projectInfoDto.setActualcommencementDate(dto.getActualEndDate());
                }
                if ("eng-00127".equals(dto.getNodeCode())) {
                    //竣工自检 计划完工日期 调整完工日期 实际完工日期
                    projectInfoDto.setPlannedCompletionDate(dto.getPlanEndDate());
                    projectInfoDto.setAdjustCompletionDate(dto.getPredictEndDate());
                    projectInfoDto.setActualCompletionDate(dto.getActualEndDate());
                }
                if ("eng-00133".equals(dto.getNodeCode())) {
                    //竣工验收 计划开业日期 调整开业日期
                    projectInfoDto.setPlanOpenDate(dto.getPlanEndDate());
                    projectInfoDto.setAdiustingTheOpeningDate(dto.getPredictEndDate());
//                    projectInfoDto.setActualopenDate(dto.getActualEndDate());
                }
            }
            LambdaQueryWrapper<ProjectInfoExpansion> wrapper = Wrappers.lambdaQuery(ProjectInfoExpansion.class);
            wrapper.eq(ProjectInfoExpansion::getProjectId, projectInfo.getProjectHlmId())
                    .eq(ProjectInfoExpansion::getHotelId, projectInfo.getProjectNo())
                    .last("limit 1");
            ProjectInfoExpansion infoExpansion = projectInfoExpansionRepository.selectOne(wrapper);
            if (ObjectUtils.isNotEmpty(infoExpansion)) {
                //签约房量  是否为保密项目 1=是 0=否 默认为0  签约日期
                projectInfoDto.setRoomsNumber(infoExpansion.getRoomsNumber());
                projectInfoDto.setSecurityProject(infoExpansion.getSecurityProject());
                projectInfoDto.setEffectiveDate(new java.sql.Date(infoExpansion.getEffectiveDate().getTime()));
            }
            //获取基本信息【楼层】，需要【客房平面及效果图】的项目总楼层
            if (ObjectUtil.isEmpty(projectInfoDto.getNumberFloorLevels())) {
                final LambdaQueryWrapper<ProjectNodeInfo> eq = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                        .eq(ProjectNodeInfo::getProjectId, projectId)
                        .eq(ProjectNodeInfo::getNodeCode, "des-00107029");
                final ProjectNodeInfo nodeInfo = projectNodeInfoRepository.selectOne(eq);
                if (ObjectUtil.isNotEmpty(nodeInfo) && ObjectUtil.isNotEmpty(nodeInfo.getRemark())) {
                    String num = nodeInfo.getRemark().replaceAll("\\D", "");
                    if (ObjectUtil.isNotEmpty(num)) {
                        projectInfoDto.setNumberFloorLevels(Integer.valueOf(num));
                    } else {
                        projectInfoDto.setNumberFloorLevels(0);
                    }
                }
            }


            if (projectInfoDto.getProvince() != null) {
                Area province = areaRepository.getAreaInfo(projectInfoDto.getProvince());
                if (province != null) {
                    projectInfoDto.setProvinceName(province.getName());
                }
            }
            if (projectInfoDto.getCity() != null) {
                Area city = areaRepository.getAreaInfo(projectInfoDto.getCity());
                if (city != null) {
                    projectInfoDto.setCityName(city.getName());
                }
            }
            if (projectInfoDto.getCounty() != null) {
                Area country = areaRepository.getAreaInfo(projectInfoDto.getCounty());
                if (country != null) {
                    projectInfoDto.setCountyName(country.getName());
                }
            }
            if (projectInfoDto.getCityCompany() != null) {
                DictDetail dictDetailByValue = dictDetailRepository.findDictDetailByValue(projectInfoDto.getCityCompany());
                if (dictDetailByValue != null) {
                    projectInfoDto.setCityCompanyName(dictDetailByValue.getLabel());
                }
            }
            if (projectInfoDto.getRegion() != null) {
                DictDetail dictDetailByValue = dictDetailRepository.findDictDetailByValueRegion(projectInfoDto.getRegion());
                if (dictDetailByValue != null) {
                    projectInfoDto.setRegion(dictDetailByValue.getLabel());
                }
            }
            if (projectInfoDto.getConstructionManager() != null) {
                User one = userRepository.getOne(projectInfoDto.getConstructionManager());
                if (one != null) {
                    projectInfoDto.setConstructionManagerName(one.getNickName() + "/" + one.getPhone());
                }
            }
            if (projectInfoDto.getDesigner() != null) {
                User one = userRepository.getOne(projectInfoDto.getDesigner());
                if (one != null) {
                    projectInfoDto.setDesignName(one.getNickName() + "/" + one.getPhone());
                }
            }
        }
        return projectInfoDto;
    }

    private boolean setPrepareSubmitted(Long currentUserId, ProjectInfo projectInfo, Long projectId) {
        //查询当前用户,并获取对应干系人的roleId
        List<String> roleCodesByUserId = roleRepository.findRoleCodesByUserId(currentUserId, projectId);
        if (ObjectUtil.isNotEmpty(roleCodesByUserId)) {
            String key = AtourSystemEnum.engineeringRoleCodeEnum.GCJL.getKey();
            String prepareSubmitted = roleControlElementRepository.getPrepareSubmitted(projectId, roleCodesByUserId);
            if (ObjectUtil.isNotEmpty(prepareSubmitted)) {
                for (String s : prepareSubmitted.split(",")) {
                    switch (s) {
                        case "gcjl":
                            if (ObjectUtil.isEmpty(projectInfo.getProjectManager())) {
                                return Boolean.TRUE;
                            }
                            break;
                        case "sjfzr":
                            if (ObjectUtil.isEmpty(projectInfo.getDesigner())) {
                                return Boolean.TRUE;
                            }
                            break;
                        case "fxzjry":
                            if (ObjectUtil.isEmpty(projectInfo.getFlightQualityInspectionPersonnel())) {
                                return Boolean.TRUE;
                            }
                            break;
                        case "jdgcs":
                            if (ObjectUtil.isEmpty(projectInfo.getMechanicalAndElectricalEngineer())) {
                                return Boolean.TRUE;
                            }
                            break;
                        case "rdgcs":
                            if (ObjectUtil.isEmpty(projectInfo.getWeakCurrentEngineer())) {
                                return Boolean.TRUE;
                            }
                            break;
                        case "rzsj":
                            if (ObjectUtil.isEmpty(projectInfo.getSoftDecorationDesign())) {
                                return Boolean.TRUE;
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public void deleteFlag(Long id, String deleteFlag) {
        if (id == null) {
            throw new BadRequestException("删除项目id为空,请重新请求");
        }
        ProjectInfo projectInfo = projectInfoRepository.selectById(id);
        projectInfo.setIsDelete(true);
        projectInfo.setProjectStatus(JhSystemEnum.ProjectStatusEnum.PROJECT_DELETE.getKey());
        projectInfoRepository.updateById(projectInfo);

    }

    @Override
    public Map<String, Integer> getTotalStatistics() {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        //查询权限
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        List<ProjectInfoDto> allProject = null;
        List<Long> projectIds = new LinkedList<>();
        Integer totalProject = 0;
        Integer buildProject = 0;
        Integer thisYearProject = 0;
        Integer finishProject = 0;
        Integer overDueProject = 0;
        //根据权限查询project数据
        if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
            //获取所有的城市权限有的项目
            allProject = projectInfoRepository.getAllProjectByCity(currentUserId);
            //根据需求计算数值
        } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
            //干系人权限
            allProject = projectInfoRepository.getAllProjectBySta(currentUserId);
            //根据需求计算数值

        }
        for (ProjectInfoDto dto : allProject) {
            //根据需求计算数值
            //所有未删除项目
            totalProject = totalProject + 1;
            Timestamp createTime = dto.getCreateTime();
            if (DateUtil.isThisYear(createTime)) {
                //本年项目
                thisYearProject = thisYearProject + 1;
            }
//            if (JhSystemEnum.ProjectStatusEnum.PROJECT_GOON.getKey().equals(dto.getProjectStatus())) {
//                //在建项目
//                buildProject = buildProject + 1;
//            }
            if (JhSystemEnum.ProjectStatusEnum.PROJECT_FINISH.getKey().equals(dto.getProjectStatus())) {
                //完成项目
                finishProject = finishProject + 1;
            }
            if (dto.getIsOverdue() != null && dto.getIsOverdue()) {
                overDueProject = overDueProject + 1;
            }

        }
        Map<String, Integer> projectMap = new HashMap<>();
        projectMap.put("totalProject", totalProject);
        projectMap.put("buildProject", buildProject);
        projectMap.put("thisYearProject", thisYearProject);
        projectMap.put("finishProject", finishProject);
        projectMap.put("overDueProject", overDueProject);
        return projectMap;
    }

    @Override
    public ProjectNodePhaseDto getOverDuePercent(String date) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        ProjectNodePhaseDto projectNodePhaseDto = new ProjectNodePhaseDto();
        int sitNum = 0;
        int leaseNum = 0;
        int designNum = 0;
        int tendersNum = 0;
        int conprepareNum = 0;
        int conimplementNum = 0;
        List<Long> overDuePercent = new LinkedList<>();

        //查询权限
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        //根据权限查询project数据
        if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
            //获取所有的城市权限有的项目
            overDuePercent = projectInfoRepository.getOverDuePercentForCity(date, currentUserId);

        } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
            //获取所有干系人权限的项目
            overDuePercent = projectInfoRepository.getOverDuePercentForSta(date, currentUserId);
        }
        //城市权限
        //干系人权限
        if (overDuePercent.size() > 0) {
            projectNodePhaseDto.setProjectTotal(overDuePercent.size());

            //查询每个项目逾期在哪个节点上
            for (Long projectId : overDuePercent) {
                double nodeIndex = 0.0;
                //按节点查询逾期
                ProjectNodeInfoDto dueNode = new ProjectNodeInfoDto();
                List<ProjectNodeInfoDto> overDueNode = projectNodeInfoRepository.getOverDueNode(projectId);
                for (ProjectNodeInfoDto dto : overDueNode) {
                    if (nodeIndex == 0) {
                        nodeIndex = dto.getNodeIndex();
                        dueNode = dto;
                    } else {
                        if (nodeIndex < dto.getNodeIndex()) {
                            nodeIndex = dto.getNodeIndex();
                            dueNode = dto;
                        }
                    }
                }
//                if(JhSystemEnum.TaskPhaseEnum.PREPARE_PHASE.getSpec().equals(dueNode.getNodeName())){
//                    sitNum=sitNum+1;
//                }
//                if(JhSystemEnum.TaskPhaseEnum.LEASE_PHASE.getSpec().equals(dueNode.getNodeName())){
//                    leaseNum=leaseNum+1;
//                }
//                if(JhSystemEnum.TaskPhaseEnum.DESIGN_PHASE.getSpec().equals(dueNode.getNodeName())){
//                    designNum=designNum+1;
//                }
//                if(JhSystemEnum.TaskPhaseEnum.TENDERS_PHASE.getSpec().equals(dueNode.getNodeName())){
//                    tendersNum=tendersNum+1;
//                }
//                if(JhSystemEnum.TaskPhaseEnum.CONPREPARE_PHASE.getSpec().equals(dueNode.getNodeName())){
//                    conprepareNum=conprepareNum+1;
//                }
//                if(JhSystemEnum.TaskPhaseEnum.CONIMPLEMENT_PHASE.getSpec().equals(dueNode.getNodeName())){
//                    conimplementNum=conimplementNum+1;
//                }
            }
//            projectNodePhaseDto.setSiteCode(JhSystemEnum.TaskPhaseEnum.SITE_PHASE.getSpec());
//            projectNodePhaseDto.setLeaseCode(JhSystemEnum.TaskPhaseEnum.LEASE_PHASE.getSpec());
//            projectNodePhaseDto.setDesignCode(JhSystemEnum.TaskPhaseEnum.DESIGN_PHASE.getSpec());
//            projectNodePhaseDto.setTendersCode(JhSystemEnum.TaskPhaseEnum.TENDERS_PHASE.getSpec());
//            projectNodePhaseDto.setConprepareCode(JhSystemEnum.TaskPhaseEnum.CONPREPARE_PHASE.getSpec());
//            projectNodePhaseDto.setConimplementCode(JhSystemEnum.TaskPhaseEnum.CONIMPLEMENT_PHASE.getSpec());
            projectNodePhaseDto.setSitNum(sitNum);
            projectNodePhaseDto.setLeaseNum(leaseNum);
            projectNodePhaseDto.setDesignNum(designNum);
            projectNodePhaseDto.setTendersNum(tendersNum);
            projectNodePhaseDto.setConprepareNum(conprepareNum);
            projectNodePhaseDto.setConimplementNum(conimplementNum);
        }

        return projectNodePhaseDto;
    }

    @Override
    public ProjectNodePhaseDto getPhasePercent(String date) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        ProjectNodePhaseDto projectNodePhaseDto = new ProjectNodePhaseDto();
        int sitNum = 0;
        int leaseNum = 0;
        int designNum = 0;
        int tendersNum = 0;
        int conprepareNum = 0;
        int conimplementNum = 0;
        List<ProjectInfo> phasePercent = new LinkedList<>();
        //phasePercent.stream().filter(p->p.getProjectStatus().equals(1)).collect(Collectors.toList())
        //查询权限
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        //根据权限查询project数据
        if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
            //获取所有的城市权限有的项目
            phasePercent = projectInfoRepository.getPhasePercentForCity(date, currentUserId);
        } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
            //获取所有干系人权限的项目
            phasePercent = projectInfoRepository.getPhasePercentForSta(date, currentUserId);
        }
        if (phasePercent.size() > 0) {
            projectNodePhaseDto.setProjectTotal(phasePercent.size());
            Integer nodeIndex = 0;
            //查询每个项目逾期在哪个节点上
            for (ProjectInfo projectInfo : phasePercent) {

//                if(JhSystemEnum.TaskPhaseEnum.SITE_PHASE.getKey().equals(projectInfo.getTaskPhase())){
//                    sitNum=sitNum+1;
//                }
//                if(JhSystemEnum.TaskPhaseEnum.LEASE_PHASE.getKey().equals(projectInfo.getTaskPhase())){
//                    leaseNum=leaseNum+1;
//                }
//                if(JhSystemEnum.TaskPhaseEnum.DESIGN_PHASE.getKey().equals(projectInfo.getTaskPhase())){
//                    designNum=designNum+1;
//                }
//                if(JhSystemEnum.TaskPhaseEnum.TENDERS_PHASE.getKey().equals(projectInfo.getTaskPhase())){
//                    tendersNum=tendersNum+1;
//                }
//                if(JhSystemEnum.TaskPhaseEnum.CONPREPARE_PHASE.getKey().equals(projectInfo.getTaskPhase())){
//                    conprepareNum=conprepareNum+1;
//                }
//                if(JhSystemEnum.TaskPhaseEnum.CONIMPLEMENT_PHASE.getKey().equals(projectInfo.getTaskPhase())){
//                    conimplementNum=conimplementNum+1;
//                }
            }
//            projectNodePhaseDto.setSiteCode(JhSystemEnum.TaskPhaseEnum.SITE_PHASE.getSpec());
//            projectNodePhaseDto.setLeaseCode(JhSystemEnum.TaskPhaseEnum.LEASE_PHASE.getSpec());
//            projectNodePhaseDto.setDesignCode(JhSystemEnum.TaskPhaseEnum.DESIGN_PHASE.getSpec());
//            projectNodePhaseDto.setTendersCode(JhSystemEnum.TaskPhaseEnum.TENDERS_PHASE.getSpec());
//            projectNodePhaseDto.setConprepareCode(JhSystemEnum.TaskPhaseEnum.CONPREPARE_PHASE.getSpec());
//            projectNodePhaseDto.setConimplementCode(JhSystemEnum.TaskPhaseEnum.CONIMPLEMENT_PHASE.getSpec());
            projectNodePhaseDto.setSitNum(sitNum);
            projectNodePhaseDto.setLeaseNum(leaseNum);
            projectNodePhaseDto.setDesignNum(designNum);
            projectNodePhaseDto.setTendersNum(tendersNum);
            projectNodePhaseDto.setConprepareNum(conprepareNum);
            projectNodePhaseDto.setConimplementNum(conimplementNum);
        }

        return projectNodePhaseDto;
    }


    @Override
    public ProjectPhaseCountVo getPhasePercentWithProjectType(String projectType) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        ProjectPhaseCountVo projectPhaseCountVo = new ProjectPhaseCountVo();
        int engineeringNum = 0;
        int designNum = 0;
        List<ProjectInfo> phasePercent = new LinkedList<>();
        List<ProjectInfo> projectInfos = new LinkedList<>();
        //查询权限
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        //根据权限查询project数据
        if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
            //获取所有的城市权限有的项目
            projectInfos = projectInfoRepository.getPercentForCity(currentUserId);
        } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
            //获取所有干系人权限的项目
            projectInfos = projectInfoRepository.getPercentForSta(currentUserId);
        }
        if (projectInfos.size() > 0) {
            if (ObjectUtils.isNotEmpty(projectType)) {
                for (ProjectInfo projectInfo : projectInfos) {
                    if (projectType.equals(projectInfo.getProjectType())) {
                        phasePercent.add(projectInfo);
                    }
                    if (KidsSystemEnum.ProjectTypeOEnum.REFORM.getValue().equals(projectType)) {
                        if (KidsSystemEnum.ProjectTypeEnum.MAJOR.getValue().equals(projectInfo.getProjectType())) {
                            phasePercent.add(projectInfo);
                        }
                    }
                }
            } else {
                phasePercent = projectInfos;
            }

            Integer projStepCount = 0;
            //查询每个项目逾期在哪个节点上
            for (ProjectInfo projectInfo : phasePercent) {
                if (ObjectUtil.isNotEmpty(projectInfo.getProjectType()) && "design_adjust".equals(projectInfo.getProjectType())) {
                    continue;
                }
                if (ObjectUtil.isNotEmpty(projectInfo.getProjectStatus()) && JhSystemEnum.ProjectStatusEnum.isNoFinish(projectInfo.getProjectStatus())) {
                    String taskPhase = projectInfo.getTaskPhase();
                    if (JhSystemEnum.TaskPhaseEnum.ENGINEERING.getKey().equals(taskPhase)
                            || taskPhase.contains(JhSystemEnum.TaskPhaseEnum.ENGINEERING.getKey() + "(")) {
                        engineeringNum = engineeringNum + 1;
                    }
                    if (JhSystemEnum.TaskPhaseEnum.DESIGN.getKey().equals(taskPhase)
                            || taskPhase.contains(JhSystemEnum.TaskPhaseEnum.DESIGN.getKey() + "(")) {
                        designNum = designNum + 1;
                    }
                    projStepCount++;
                }
            }
            projectPhaseCountVo.setEngineering(JhSystemEnum.TaskPhaseEnum.ENGINEERING.getSpec());
            projectPhaseCountVo.setDesign(JhSystemEnum.TaskPhaseEnum.DESIGN.getSpec());

            projectPhaseCountVo.setEngineeringNum(engineeringNum);
            projectPhaseCountVo.setDesignNum(designNum);
            projectPhaseCountVo.setProjectTotal(projStepCount);
        }

        return projectPhaseCountVo;
    }

    @Override
    public ProjectTypeCountVo getPercentByProjectType() {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        ProjectTypeCountVo projectTypeCountVo = new ProjectTypeCountVo();
        int newNum = 0;
        int reformNum = 0;
        int closeNum = 0;
        List<ProjectInfo> phasePercent = new LinkedList<>();
        //phasePercent.stream().filter(p->p.getProjectStatus().equals(1)).collect(Collectors.toList())
        //查询权限
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        //根据权限查询project数据
        if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
            //获取所有的城市权限有的项目
            phasePercent = projectInfoRepository.getPercentForCity(currentUserId);
        } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
            //获取所有干系人权限的项目
            phasePercent = projectInfoRepository.getPercentForSta(currentUserId);
        }
        List<ProjectNodeStatusVo> projectNodeStatus = projectGroupRepository.getProjectNodeStatus();
        Map<Long, ProjectNodeStatusVo> statusMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(projectNodeStatus) && projectNodeStatus.size() > 0) {
            statusMap = projectNodeStatus.stream().collect(Collectors.toMap(ProjectNodeStatusVo::getProjectId, n -> n));
        }

        Integer typeCount = 0;
        if (phasePercent.size() > 0) {
            //统计项目类型
            for (ProjectInfo projectInfo : phasePercent) {
                if (ObjectUtil.isNotEmpty(projectInfo.getProjectType()) && "design_adjust".equals(projectInfo.getProjectType())) {
                    continue;
                }
                if (ObjectUtil.isNotEmpty(projectInfo.getProjectStatus()) && JhSystemEnum.ProjectStatusEnum.isNoFinish(projectInfo.getProjectStatus())) {
                    if (KidsSystemEnum.ProjectTypeEnum.NEW.getValue().equals(projectInfo.getProjectType())) {
                        newNum = newNum + 1;
                    }
                    if (KidsSystemEnum.ProjectTypeEnum.MAJOR.getValue().equals(projectInfo.getProjectType())) {
                        reformNum = reformNum + 1;
                    }
                    if (KidsSystemEnum.ProjectTypeEnum.MINOR.getValue().equals(projectInfo.getProjectType())) {
                        reformNum = reformNum + 1;
                    }
                    if (KidsSystemEnum.ProjectTypeOEnum.REFORM.getValue().equals(projectInfo.getProjectType())) {
                        reformNum = reformNum + 1;
                    }
                    if (KidsSystemEnum.ProjectTypeEnum.CLOSE.getValue().equals(projectInfo.getProjectType())) {
                        closeNum = closeNum + 1;
                    }
                    //统计正在进行中的项目总数
                    typeCount++;
                }
//                ProjectNodeStatusVo nodeStatusResult = statusMap.get(projectInfo.getProjectId());
//                if (ObjectUtil.isNotEmpty(nodeStatusResult)){
//                    if (ObjectUtil.isNotEmpty(nodeStatusResult.getStatus2()) && nodeStatusResult.getStatus2() == 1){
//
//                    }
//                }

            }
            projectTypeCountVo.setNewType(KidsSystemEnum.ProjectTypeEnum.NEW.getLabel());
            projectTypeCountVo.setReformType(KidsSystemEnum.ProjectTypeOEnum.REFORM.getLabel());
            projectTypeCountVo.setCloseType(KidsSystemEnum.ProjectTypeEnum.CLOSE.getLabel());
            projectTypeCountVo.setNewNum(newNum);
            projectTypeCountVo.setReformNum(reformNum);
            projectTypeCountVo.setCloseNum(closeNum);
            projectTypeCountVo.setProjectTotal(typeCount);
        }

        return projectTypeCountVo;
    }

    @Override
    public List<CityProjectStatisticsDto> getCityProjectStatistics() {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        List<CityProjectStatisticsDto> cityProjectStatisticsDtos = new LinkedList<>();
        List<ProjectInfoDto> cityLongs = new LinkedList<>();
        List<StoreMasterInfoDto> storeMasterLongs = new LinkedList<>();
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        //根据权限查询project数据
        if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
            //获取所有的城市权限有的项目
            cityLongs = projectInfoRepository.getCityLongsByCity(currentUserId);
            storeMasterLongs = storeMasterInfoRepository.getCityLongsByCity(currentUserId);
        } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
            //获取所有干系人权限的项目
            cityLongs = projectInfoRepository.getCityLongsBySta(currentUserId);
            storeMasterLongs = storeMasterInfoRepository.getCityLongsBySta(currentUserId);
           /* if(cityLongs!=null&&cityLongs.size()>0){
                for(Area area:cityLongs){
                    CityProjectStatisticsDto cityProjectStatisticsDto=new CityProjectStatisticsDto();
                    Integer projectTotal = projectInfoRepository.getProjectTotal(area.getAreaCode());
                    if(projectTotal!=0){
                        Integer projectCreate = projectInfoRepository.getProjectCreate(area.getAreaCode());
                        Integer projectGoon = projectInfoRepository.getProjectGoon(area.getAreaCode());
                        Integer projectFinish = projectInfoRepository.getProjectFinish(area.getAreaCode());
                        Integer projectOverdue = projectInfoRepository.getProjectOverdue(area.getAreaCode());
                        cityProjectStatisticsDto.setCity(area.getName());
                        cityProjectStatisticsDto.setCityId(area.getAreaCode());
                        cityProjectStatisticsDto.setProjectTotal(projectTotal);
                        cityProjectStatisticsDto.setProjectCreate(projectCreate);
                        cityProjectStatisticsDto.setProjectGoon(projectGoon);
                        cityProjectStatisticsDto.setProjectFinish(projectFinish);
                        cityProjectStatisticsDto.setProjectOverdue(projectOverdue);
                        cityProjectStatisticsDtos.add(cityProjectStatisticsDto);
                    }


                }
            }*/
        }

        if (cityLongs != null && cityLongs.size() > 0) {
            Map<String, List<ProjectInfoDto>> collect = cityLongs.stream().collect(Collectors.groupingBy(ProjectInfoDto::getCityCompany));
            Iterator<Map.Entry<String, List<ProjectInfoDto>>> entries = collect.entrySet().iterator();
            while (entries.hasNext()) {
                Map.Entry<String, List<ProjectInfoDto>> entry = entries.next();
                CityProjectStatisticsDto cityProjectStatisticsDto = new CityProjectStatisticsDto();
                Integer projectTotal = 0;
                Integer projectCreate = 0;
                Integer projectGoon = 0;
                Integer projectFinish = 0;
                Integer projectOverdue = 0;
                //System.out.println("key=" + entry.getKey() + ",value=" + entry.getValue());
                List<ProjectInfoDto> value = entry.getValue();
//                projectGoon = Optional.ofNullable(value.stream().filter(p -> p.getProjectStatus().equals(JhSystemEnum.ProjectStatusEnum.PROJECT_GOON.getKey())).collect(Collectors.toList()).size()).orElse(0);
                projectTotal = Optional.ofNullable(value.stream().collect(Collectors.toList()).size()).orElse(0);
                //                projectCreate = Optional.ofNullable(value.stream().filter(p -> p.getProjectStatus().equals(JhSystemEnum.ProjectStatusEnum.PROJECT_CREATE.getKey())).collect(Collectors.toList()).size()).orElse(0);
                projectFinish = Optional.ofNullable(value.stream().filter(p -> p.getProjectStatus().equals(JhSystemEnum.ProjectStatusEnum.PROJECT_FINISH.getKey())).collect(Collectors.toList()).size()).orElse(0);
                projectOverdue = Optional.ofNullable(value.stream().filter(p -> p.getIsOverdue() != null && p.getIsOverdue().equals(Boolean.TRUE)).collect(Collectors.toList()).size()).orElse(0);
                // Area areaInfo = areaRepository.getAreaInfo(entry.getKey());
                //查找相应的城市公司名字
                DictDetail dictDetailByValue = dictDetailRepository.findDictDetailByValue(entry.getKey());
                cityProjectStatisticsDto.setCity(dictDetailByValue.getLabel());
                cityProjectStatisticsDto.setCityCompany(entry.getKey());
                cityProjectStatisticsDto.setProjectTotal(projectTotal.intValue());
                cityProjectStatisticsDto.setProjectCreate(projectCreate.intValue());
                cityProjectStatisticsDto.setProjectGoon(projectGoon.intValue());
                cityProjectStatisticsDto.setProjectFinish(projectFinish.intValue());
                cityProjectStatisticsDto.setProjectOverdue(projectOverdue.intValue());
                cityProjectStatisticsDtos.add(cityProjectStatisticsDto);
            }

        }
        return cityProjectStatisticsDtos;
    }

    @Override
    public List<CityProjectStatisticsVo> getCityStoreProjectStatistics() {
        Long currentUserId = SecurityUtils.getCurrentUserId();
//        Long currentUserId = 98789516592022056L;
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        List<CityProjectStatisticsVo> cityProjectStatisticsVos = new LinkedList<>();
        Map<String, CityProjectStatisticsVo> cityMap = new HashMap<>();
        List<ProjectInfoDto> cityLongs = new LinkedList<>();
        List<StoreMasterInfoDto> storeMasterLongs = new LinkedList<>();
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        //根据权限查询project数据
        if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
            //获取所有的城市权限有的项目
            cityLongs = projectInfoRepository.getCityLongsByCity(currentUserId);
            storeMasterLongs = storeMasterInfoRepository.getCityLongsByCity(currentUserId);

        } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
            //获取所有干系人权限的项目
            cityLongs = projectInfoRepository.getCityLongsBySta(currentUserId);
            storeMasterLongs = storeMasterInfoRepository.getCityLongsBySta(currentUserId);
           /* if(cityLongs!=null&&cityLongs.size()>0){
                for(Area area:cityLongs){
                    CityProjectStatisticsDto cityProjectStatisticsDto=new CityProjectStatisticsDto();
                    Integer projectTotal = projectInfoRepository.getProjectTotal(area.getAreaCode());
                    if(projectTotal!=0){
                        Integer projectCreate = projectInfoRepository.getProjectCreate(area.getAreaCode());
                        Integer projectGoon = projectInfoRepository.getProjectGoon(area.getAreaCode());
                        Integer projectFinish = projectInfoRepository.getProjectFinish(area.getAreaCode());
                        Integer projectOverdue = projectInfoRepository.getProjectOverdue(area.getAreaCode());
                        cityProjectStatisticsDto.setCity(area.getName());
                        cityProjectStatisticsDto.setCityId(area.getAreaCode());
                        cityProjectStatisticsDto.setProjectTotal(projectTotal);
                        cityProjectStatisticsDto.setProjectCreate(projectCreate);
                        cityProjectStatisticsDto.setProjectGoon(projectGoon);
                        cityProjectStatisticsDto.setProjectFinish(projectFinish);
                        cityProjectStatisticsDto.setProjectOverdue(projectOverdue);
                        cityProjectStatisticsDtos.add(cityProjectStatisticsDto);
                    }


                }
            }*/
        }
        List<StoreMasterInfoDto> storeMaster = new LinkedList<>();
        for (StoreMasterInfoDto storeMasterInfoDto : storeMasterLongs) {
            if ("store".equals(storeMasterInfoDto.getStoreType()) && !KidsSystemEnum.StoreStatusEnum.CLOSE.getValue().equals(storeMasterInfoDto.getStoreStatus())) {
                storeMaster.add(storeMasterInfoDto);
            }
        }
        Integer storeTotal = 0;
        if (CollectionUtils.isNotEmpty(storeMaster)) {
            Map<String, List<StoreMasterInfoDto>> collect = storeMaster.stream().collect(Collectors.groupingBy(x -> StringUtils.isEmpty(x.getCityCompany()) ? "" : x.getCityCompany()));
            KidsSystemEnum.CityCompanyEnum[] values = KidsSystemEnum.CityCompanyEnum.values();
            for (KidsSystemEnum.CityCompanyEnum value : values) {
                storeTotal = 0;
                CityProjectStatisticsVo cityProjectStatisticsVo = new CityProjectStatisticsVo();
                DictDetail dictDetailByValue = Optional.ofNullable(dictDetailRepository.findDictDetailByValue(value.getCityCompany())).orElseGet(DictDetail::new);
                cityProjectStatisticsVo.setCity(dictDetailByValue.getLabel());
                cityProjectStatisticsVo.setCityCompany(value.getCityCompany());
                cityProjectStatisticsVo.setIndex(value.getIndex());
                if (CollectionUtils.isNotEmpty(collect.get(value.getCityCompany()))) {
                    storeTotal = collect.get(value.getCityCompany()).size();
                }
                cityProjectStatisticsVo.setStoreTotal(storeTotal);
                cityProjectStatisticsVo.setProjectPrepare(0);
                cityProjectStatisticsVo.setProjectConimplement(0);
                cityProjectStatisticsVo.setProjectSettlement(0);
                cityMap.put(value.getCityCompany(), cityProjectStatisticsVo);
            }
        }

        if (cityLongs != null && cityLongs.size() > 0) {
            Map<String, List<ProjectInfoDto>> collect = cityLongs.stream().collect(Collectors.groupingBy(x -> StringUtils.isEmpty(x.getCityCompany()) ? "" : x.getCityCompany()));
            Iterator<Map.Entry<String, List<ProjectInfoDto>>> entries = collect.entrySet().iterator();
            while (entries.hasNext()) {
                Map.Entry<String, List<ProjectInfoDto>> entry = entries.next();
//                CityProjectStatisticsVo cityProjectStatisticsVo = new CityProjectStatisticsVo();
                CityProjectStatisticsVo cityProjectStatisticsVo = cityMap.get(entry.getKey());
                if (ObjectUtils.isEmpty(cityProjectStatisticsVo)) {
                    continue;
                }
//                Integer storeTotal = 0;
                Integer projectPrepare = 0;
                Integer projectConimplement = 0;
                Integer projectSettlement = 0;
                //System.out.println("key=" + entry.getKey() + ",value=" + entry.getValue());
                List<ProjectInfoDto> value = entry.getValue();
                Set<String> projectIds = new HashSet<>();
                value.forEach(v -> projectIds.add(v.getProjectId().toString()));
                projectPrepare = Optional.ofNullable(value.stream().filter(p -> JhSystemEnum.ProjectStatusEnum.PROJECT_PREPARE.getKey().equals(p.getProjectStatus())).collect(Collectors.toList()).size()).orElse(0);
//                storeTotal = Optional.ofNullable(projectIds.stream().collect(Collectors.toList()).size()).orElse(0);
                projectConimplement = Optional.ofNullable(value.stream().filter(p -> JhSystemEnum.ProjectStatusEnum.PROJECT_CONIMPLEMENT.getKey().equals(p.getProjectStatus())).collect(Collectors.toList()).size()).orElse(0);
                projectSettlement = Optional.ofNullable(value.stream().filter(p -> JhSystemEnum.ProjectStatusEnum.PROJECT_SETTLEMENT.getKey().equals(p.getProjectStatus())).collect(Collectors.toList()).size()).orElse(0);
                // Area areaInfo = areaRepository.getAreaInfo(entry.getKey());
                //查找相应的城市公司名字
//                DictDetail dictDetailByValue = Optional.ofNullable(dictDetailRepository.findDictDetailByValue(entry.getKey())).orElseGet(DictDetail::new);
//                cityProjectStatisticsVo.setCity(dictDetailByValue.getLabel());
//                cityProjectStatisticsVo.setCityCompany(entry.getKey());
//                cityProjectStatisticsVo.setStoreTotal(storeTotal);
                cityProjectStatisticsVo.setProjectPrepare(projectPrepare);
                cityProjectStatisticsVo.setProjectConimplement(projectConimplement);
                cityProjectStatisticsVo.setProjectSettlement(projectSettlement);
//                cityProjectStatisticsVos.add(cityProjectStatisticsVo);
            }
        }
        Collection<CityProjectStatisticsVo> values = cityMap.values();
        for (CityProjectStatisticsVo value : values) {
            cityProjectStatisticsVos.add(value);
        }
        Collections.sort(cityProjectStatisticsVos, new Comparator<CityProjectStatisticsVo>() {
            @Override
            public int compare(CityProjectStatisticsVo o1, CityProjectStatisticsVo o2) {
                return Integer.parseInt(String.valueOf(o1.getIndex())) - Integer.parseInt(String.valueOf(o2.getIndex()));
            }
        });
        return cityProjectStatisticsVos;
    }

    /**
     * 相应干系人入干系人表
     *
     * @param projectId
     * @param roleName
     * @param userid
     * @param downCode
     */
    @Override
    public void createProjectStakeholders(Long projectId, String roleName, Long userid, String downCode) {
        //TODO 当点击同一个任务的提交-创建干系人，会给录入两次
        ProjectStakeholders projectStakeholders = new ProjectStakeholders();

//        Long uuid = SnowFlakeUtil.getInstance().nextLongId();
//        projectStakeholders.setStakeholderId(uuid);
        //查询库里最大的id
        Long maxId = projectStakeholdersRepository.getMaxId();
        projectStakeholders.setStakeholderId(maxId + 1);

        projectStakeholders.setProjectId(projectId);
        projectStakeholders.setDownCode(downCode);
        if (userid != 0) {
            projectStakeholders.setUserId(userid);
            //若干系人选择了人，则将干系人状态改为正常
            projectStakeholders.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
            if (JhSystemEnum.JobEnum.ZBHTZRR.getKey().equals(roleName)) {
                projectStakeholders.setIsNotshow(Boolean.TRUE);
            }
        } else if (JhSystemEnum.JobEnum.ZBHTZRR.getKey().equals(roleName) && userid == 0) {
            RoleAndAreaUser roleAndAreaUser = new RoleAndAreaUser();
            roleAndAreaUser.setProjectId(projectId);
            roleAndAreaUser.setJobName(JhSystemEnum.JobEnum.SELQ.getKey());
            RoleAndAreaUser userInfo = checkStakeholdersService.getUserInfo(roleAndAreaUser);
            List<RoleUser> roleUsers = userInfo.getUser();
            for (RoleUser roleUser : roleUsers) {
                projectStakeholders.setUserId(roleUser.getUserId());
                //若干系人选择了人，则将干系人状态改为正常
                projectStakeholders.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
                projectStakeholders.setIsNotshow(Boolean.TRUE);
            }


        }

//        Role role = projectInfoRepository.getRoleByRoleCode(roleName);

        List<Role> roles = projectInfoRepository.getRoleByRoleCodeList(roleName.split(","));

        //Job job =  projectInfoRepository.getJobByJobCode(roleName);
        /*if(job!=null){
            projectStakeholders.setRoleId(job.getId());
            projectStakeholders.setRoleCode(job.getJobCode());
            projectStakeholders.setRoleName(job.getName());
        }*/

        if (ObjectUtil.isNotEmpty(roles)) {
            for (Role role : roles) {

                projectStakeholders.setRoleId(role.getId());
                projectStakeholders.setRoleCode(role.getRoleCode());
                projectStakeholders.setRoleName(role.getName());
                //查找之前是否有插入
                LambdaQueryWrapper<ProjectStakeholders> projectStakeholdersLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class);
                projectStakeholdersLambdaQueryWrapper.eq(ProjectStakeholders::getRoleCode, role.getRoleCode())
                        .eq(ProjectStakeholders::getProjectId, projectId)
                        .eq(ProjectStakeholders::getShakeholderStatus, "in_term")
                        .isNull(ProjectStakeholders::getOrderId);
                ProjectStakeholders stakeholdersQuery = projectStakeholdersRepository.selectOne(projectStakeholdersLambdaQueryWrapper);
                if (stakeholdersQuery != null) {
                    Long oldUserId = stakeholdersQuery.getUserId();
                    if (oldUserId != null) {
                        //当前人跟之前的人不一样
                        if (!oldUserId.equals(userid)) {
                            //若当前操作是换人
                            Timestamp nowTime = new Timestamp(System.currentTimeMillis());
                            //修改状态为离项
                            stakeholdersQuery.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS1.getKey());
                            stakeholdersQuery.setReason("系统页面更换干系人");
                            stakeholdersQuery.setLeaveTime(nowTime);
                            projectStakeholdersRepository.updateById(stakeholdersQuery);


                            //新增干系人
                            projectStakeholders.setStakeholderId(null);
                            projectStakeholders.setReason(null);
                            projectStakeholders.setJoinTime(nowTime);
                            projectStakeholders.setIsDelete(Boolean.FALSE);
                            projectStakeholdersRepository.insert(projectStakeholders);
                            //更换node负责人
                            // projectNodeInfoService.updateRoleCode(projectId, userid, oldUserId);
                            //更换当前人的所有任务
                            projectTaskService.changeTaskUser(projectId, null, userid, oldUserId);
                            projectApproveDetailService.changeApproveUser(projectId, null, userid, oldUserId);

                        }
                    } else {
                        //当前操作是把人插入
                        projectStakeholders.setJoinTime(Timestamp.valueOf(DateUtil.parseLocalDateTimeFormatyMd(DateUtil.getNowDate())));
                        projectStakeholders.setIsDelete(false);
                        projectStakeholders.setIsApprove(false);
                        projectStakeholders.setStakeholderId(stakeholdersQuery.getStakeholderId());
                        projectStakeholdersRepository.updateById(projectStakeholders);
                    }
                } else {
                    projectStakeholders.setJoinTime(Timestamp.valueOf(DateUtil.parseLocalDateTimeFormatyMd(DateUtil.getNowDate())));
                    projectStakeholders.setIsDelete(false);
                    projectStakeholders.setIsApprove(false);
                    try {
                        projectStakeholdersRepository.insert(projectStakeholders);
                    } catch (Exception e) {
                        projectStakeholders.setStakeholderId(maxId + 2);
                        projectStakeholdersRepository.insert(projectStakeholders);
                    }
                }
            }
        }
    }

    @Override
    public Boolean updateAllProject() {
        LambdaQueryWrapper<ProjectInfo> query = Wrappers.lambdaQuery(ProjectInfo.class);
        List<ProjectInfo> projectInfos = projectInfoRepository.selectList(query);
        for (ProjectInfo p : projectInfos) {
            String projectNo = p.getProjectNo();
            if (StringUtils.isBlank(projectNo) || projectNo.length() > 8) {
                String code = RandomUtil.randomNumbers(8);
                p.setProjectNo(code);
                projectInfoRepository.updateById(p);
            }
        }
        return null;
    }

    @Override
    public void createIsNotShowSta(Long projectId, Long city, String roleCode, Boolean isNotShow) {
        //查找当前project
        if (city == null) {
            throw new BadRequestException("当前项目未选择城市");
        }
        //根据角色找人
        List<RoleUser> byJobName = roleUserService.findByRoleName(roleCode, city);
        if (byJobName.size() > 0) {
            Role role = projectInfoRepository.getRoleByRoleCode(roleCode);
            for (RoleUser user : byJobName) {
                if (role != null) {
                    ProjectStakeholders projectStakeholders = new ProjectStakeholders();
                    Long uuid = SnowFlakeUtil.getInstance().nextLongId();
                    projectStakeholders.setStakeholderId(uuid);
                    projectStakeholders.setProjectId(projectId);
                    projectStakeholders.setRoleId(role.getId());
                    projectStakeholders.setRoleCode(role.getRoleCode());
                    projectStakeholders.setRoleName(role.getName());
                    projectStakeholders.setUserId(user.getUserId());
                    //若干系人选择了人，则将干系人状态改为正常
                    projectStakeholders.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
                    projectStakeholders.setJoinTime(Timestamp.valueOf(DateUtil.parseLocalDateTimeFormatyMd(DateUtil.getNowDate())));
                    projectStakeholders.setIsDelete(false);
                    projectStakeholders.setIsApprove(false);
                    //设置当前干系人不可见
                    projectStakeholders.setIsNotshow(isNotShow);
                    projectStakeholdersRepository.insert(projectStakeholders);
                }
            }

        }

    }

    @Override
    public Map<String, Integer> getTotalStatisticsAccount() {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        //查询权限
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        List<ProjectInfoDto> allProject = null;
        List<Long> projectIds = new LinkedList<>();
        Integer totalProject = 0;
        Integer accountProject = 0;
        Integer thisYearProject = 0;
        Integer finishProject = 0;
        Integer overDueProject = 0;
        //根据权限查询project数据
        if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
            //获取所有的城市权限有的项目
            allProject = projectInfoRepository.getAllProjectByCity(currentUserId);
            //根据需求计算数值
        } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
            //干系人权限
            allProject = projectInfoRepository.getAllProjectBySta(currentUserId);
            //根据需求计算数值

        }

        for (ProjectInfoDto dto : allProject) {
            //根据需求计算数值
            //所有未删除项目
            totalProject = totalProject + 1;
            Timestamp createTime = dto.getCreateTime();
//            if (JhSystemEnum.ProjectStatusEnum.PROJECT_ACCOUNT.getKey().equals(dto.getProjectStatus())) {
//                //结算完成项目
//                finishProject = finishProject + 1;
//            }


//            if ((JhSystemEnum.TaskPhaseEnum.SETTLEMENT_PHASE.getKey().equals(dto.getTaskPhase()) || JhSystemEnum.ProjectStatusEnum.PROJECT_FINISH.getKey().equals(dto.getProjectStatus()) || ((dto.getAccountPhase() != null) && (!"--".equals(dto.getAccountPhase())))) && (!JhSystemEnum.ProjectStatusEnum.PROJECT_ACCOUNT.getKey().equals(dto.getProjectStatus()))) {
//                if (DateUtil.isThisYear(createTime)) {
//                    //本年项目
//                    thisYearProject = thisYearProject + 1;
//                }
//                if (JhSystemEnum.TaskPhaseEnum.SETTLEMENT_PHASE.getKey().equals(dto.getTaskPhase())) {
//                    //结算阶段项目
//                    accountProject = accountProject + 1;
//                }
//            }
            if ((JhSystemEnum.TaskPhaseEnum.SETTLEMENT_PHASE.getKey().equals(dto.getTaskPhase()) || JhSystemEnum.ProjectStatusEnum.PROJECT_FINISH.getKey().equals(dto.getProjectStatus()) || ((dto.getAccountPhase() != null) && (!"--".equals(dto.getAccountPhase()))))) {
                if (dto.getAccountOverdue() != null && dto.getAccountOverdue()) {
                   /* int overdue=0;
                    //结算逾期
                    LambdaQueryWrapper<ProjectNodeInfo> nodeInfoLambdaQueryWrapper=Wrappers.lambdaQuery(ProjectNodeInfo.class);
                    nodeInfoLambdaQueryWrapper.eq(ProjectNodeInfo::getProjectId,dto.getProjectId()).eq(ProjectNodeInfo::getNodeCode,"con-007");
                    ProjectNodeInfo projectNodeInfo = projectNodeInfoRepository.selectOne(nodeInfoLambdaQueryWrapper);
                    LambdaQueryWrapper<ProjectNodeInfo> project = Wrappers.lambdaQuery(ProjectNodeInfo.class).eq(ProjectNodeInfo::getProjectId, dto.getProjectId()).eq(ProjectNodeInfo::getParentId, projectNodeInfo.getTemplateId());
                    List<ProjectNodeInfo> list = projectNodeInfoRepository.selectList(project);
                    for(ProjectNodeInfo projectNodeInfo1:list){
                        if(projectNodeInfo1.getDelayDay()>0){
                            overdue=1;
                        }
                    }
                    if(overdue>0){*/
                    overDueProject = overDueProject + 1;
                    //  }

                }
            }


        }
        Map<String, Integer> projectMap = new HashMap<>();
        projectMap.put("totalProject", totalProject);
        projectMap.put("accoutProject", accountProject);
        projectMap.put("thisYearProject", thisYearProject);
        projectMap.put("finishProject", finishProject);
        projectMap.put("overDueProject", overDueProject);
        return projectMap;
    }

    @Override
    public ProjectAccountNodePhaseDto getAccountOverDuePercent(String date) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        ProjectAccountNodePhaseDto projectAccountNodePhaseDto = new ProjectAccountNodePhaseDto();
        int accountSubmitNum = 0;
        int accountConfirmNum = 0;
        int accountFinishNum = 0;
        int budgetFinishNum = 0;
        int budgetRechargeNum = 0;
        int prApprovalNum = 0;
        int contractFinishNum = 0;
        int totalNum = 0;
        List<Long> overDuePercent = new LinkedList<>();

        //查询权限
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        //根据权限查询project数据
        if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
            //获取所有的城市权限有的项目
            overDuePercent = projectInfoRepository.getAccountOverDuePercentForCity(date, currentUserId);

        } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
            //获取所有干系人权限的项目
            overDuePercent = projectInfoRepository.getAccountOverDuePercentForSta(date, currentUserId);
        }
        //城市权限
        //干系人权限
        if (overDuePercent.size() > 0) {


            //查询每个项目逾期在哪个节点上
            for (Long projectId : overDuePercent) {
                double nodeIndex = 0;
                //按节点查询逾期
                ProjectNodeInfoDto dueNode = new ProjectNodeInfoDto();
                List<ProjectNodeInfoDto> overDueNode = projectNodeInfoRepository.getAccountOverDueNode(projectId);
                for (ProjectNodeInfoDto dto : overDueNode) {
                    if (nodeIndex == 0) {
                        nodeIndex = dto.getNodeIndex();
                        dueNode = dto;
                    } else {
                        if (nodeIndex < dto.getNodeIndex()) {
                            nodeIndex = dto.getNodeIndex();
                            dueNode = dto;
                        }
                    }
                }
                if (dueNode != null) {
                    totalNum = totalNum + 1;
                }
                if (JhSystemEnum.AccountCodeEnum.CON_00701.getKey().equals(dueNode.getNodeCode())) {
                    accountSubmitNum = accountSubmitNum + 1;
                }
                if (JhSystemEnum.AccountCodeEnum.CON_00702.getKey().equals(dueNode.getNodeCode())) {
                    accountConfirmNum = accountConfirmNum + 1;
                }
                if (JhSystemEnum.AccountCodeEnum.CON_00703.getKey().equals(dueNode.getNodeCode())) {
                    accountFinishNum = accountFinishNum + 1;
                }
                if (JhSystemEnum.AccountCodeEnum.CON_00704.getKey().equals(dueNode.getNodeCode())) {
                    budgetFinishNum = budgetFinishNum + 1;
                }
                if (JhSystemEnum.AccountCodeEnum.CON_00705.getKey().equals(dueNode.getNodeCode())) {
                    budgetRechargeNum = budgetRechargeNum + 1;
                }
                if (JhSystemEnum.AccountCodeEnum.CON_00706.getKey().equals(dueNode.getNodeCode())) {
                    prApprovalNum = prApprovalNum + 1;
                }
                if (JhSystemEnum.AccountCodeEnum.CON_00707.getKey().equals(dueNode.getNodeCode())) {
                    contractFinishNum = contractFinishNum + 1;
                }
            }

            projectAccountNodePhaseDto.setAccountSubmitCode(JhSystemEnum.AccountCodeEnum.CON_00701.getSpec());
            projectAccountNodePhaseDto.setAccountConfirmCode(JhSystemEnum.AccountCodeEnum.CON_00702.getSpec());
            projectAccountNodePhaseDto.setAccountFinishCode(JhSystemEnum.AccountCodeEnum.CON_00703.getSpec());
            projectAccountNodePhaseDto.setBudgetFinishCode(JhSystemEnum.AccountCodeEnum.CON_00704.getSpec());
            projectAccountNodePhaseDto.setBudgetRechargeCode(JhSystemEnum.AccountCodeEnum.CON_00705.getSpec());
            projectAccountNodePhaseDto.setPrApprovalCode(JhSystemEnum.AccountCodeEnum.CON_00706.getSpec());
            projectAccountNodePhaseDto.setContractFinishCode(JhSystemEnum.AccountCodeEnum.CON_00707.getSpec());


            projectAccountNodePhaseDto.setAccountSubmitNum(accountSubmitNum);
            projectAccountNodePhaseDto.setAccountConfirmNum(accountConfirmNum);
            projectAccountNodePhaseDto.setAccountFinishNum(accountFinishNum);
            projectAccountNodePhaseDto.setBudgetFinishNum(budgetFinishNum);
            projectAccountNodePhaseDto.setBudgetRechargeNum(budgetRechargeNum);
            projectAccountNodePhaseDto.setPrApprovalNum(prApprovalNum);
            projectAccountNodePhaseDto.setContractFinishNum(contractFinishNum);

        }

        return projectAccountNodePhaseDto;
    }

    @Override
    public ProjectAccountNodePhaseDto getAccountPhasePercent(String date) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        ProjectAccountNodePhaseDto projectAccountNodePhaseDto = new ProjectAccountNodePhaseDto();
        int accountSubmitNum = 0;
        int accountConfirmNum = 0;
        int accountFinishNum = 0;
        int budgetFinishNum = 0;
        int budgetRechargeNum = 0;
        int prApprovalNum = 0;
        int contractFinishNum = 0;
        List<ProjectInfo> phasePercent = new LinkedList<>();
        //phasePercent.stream().filter(p->p.getProjectStatus().equals(1)).collect(Collectors.toList())
        //查询权限
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        //根据权限查询project数据
        if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
            //获取所有的城市权限有的项目
            phasePercent = projectInfoRepository.getAccountPhasePercentForCity(date, currentUserId);
        } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
            //获取所有干系人权限的项目
            phasePercent = projectInfoRepository.getAccountPhasePercentForSta(date, currentUserId);
        }
        if (phasePercent.size() > 0) {
            projectAccountNodePhaseDto.setProjectTotal(phasePercent.size());
            Integer nodeIndex = 0;
            //查询每个项目逾期在哪个节点上
            for (ProjectInfo projectInfo : phasePercent) {
                List<ProjectNodeInfoDto> overDueNode = projectNodeInfoRepository.getIsNotDoneNode(projectInfo.getProjectId());
                ProjectNodeInfoDto projectNodeInfoDto = overDueNode.get(0);

                log.info("节点为" + projectNodeInfoDto.getNodeCode());
                if (JhSystemEnum.AccountCodeEnum.CON_00701.getKey().equals(projectNodeInfoDto.getNodeCode())) {
                    accountSubmitNum = accountSubmitNum + 1;
                }
                if (JhSystemEnum.AccountCodeEnum.CON_00702.getKey().equals(projectNodeInfoDto.getNodeCode())) {
                    accountConfirmNum = accountConfirmNum + 1;
                }
                if (JhSystemEnum.AccountCodeEnum.CON_00703.getKey().equals(projectNodeInfoDto.getNodeCode())) {
                    accountFinishNum = accountFinishNum + 1;
                }
                if (JhSystemEnum.AccountCodeEnum.CON_00704.getKey().equals(projectNodeInfoDto.getNodeCode())) {
                    budgetFinishNum = budgetFinishNum + 1;
                }
                if (JhSystemEnum.AccountCodeEnum.CON_00705.getKey().equals(projectNodeInfoDto.getNodeCode())) {
                    budgetRechargeNum = budgetRechargeNum + 1;
                }
                if (JhSystemEnum.AccountCodeEnum.CON_00706.getKey().equals(projectNodeInfoDto.getNodeCode())) {
                    prApprovalNum = prApprovalNum + 1;
                }
                if (JhSystemEnum.AccountCodeEnum.CON_00707.getKey().equals(projectNodeInfoDto.getNodeCode())) {
                    contractFinishNum = contractFinishNum + 1;
                }
            }
            projectAccountNodePhaseDto.setAccountSubmitCode(JhSystemEnum.AccountCodeEnum.CON_00701.getSpec());
            projectAccountNodePhaseDto.setAccountConfirmCode(JhSystemEnum.AccountCodeEnum.CON_00702.getSpec());
            projectAccountNodePhaseDto.setAccountFinishCode(JhSystemEnum.AccountCodeEnum.CON_00703.getSpec());
            projectAccountNodePhaseDto.setBudgetFinishCode(JhSystemEnum.AccountCodeEnum.CON_00704.getSpec());
            projectAccountNodePhaseDto.setBudgetRechargeCode(JhSystemEnum.AccountCodeEnum.CON_00705.getSpec());
            projectAccountNodePhaseDto.setPrApprovalCode(JhSystemEnum.AccountCodeEnum.CON_00706.getSpec());
            projectAccountNodePhaseDto.setContractFinishCode(JhSystemEnum.AccountCodeEnum.CON_00707.getSpec());


            projectAccountNodePhaseDto.setAccountSubmitNum(accountSubmitNum);
            projectAccountNodePhaseDto.setAccountConfirmNum(accountConfirmNum);
            projectAccountNodePhaseDto.setAccountFinishNum(accountFinishNum);
            projectAccountNodePhaseDto.setBudgetFinishNum(budgetFinishNum);
            projectAccountNodePhaseDto.setBudgetRechargeNum(budgetRechargeNum);
            projectAccountNodePhaseDto.setPrApprovalNum(prApprovalNum);
            projectAccountNodePhaseDto.setContractFinishNum(contractFinishNum);
        }

        return projectAccountNodePhaseDto;
    }

    @Override
    public List<CityProjectAccountStatisticsDto> getCityProjectAccountStatistics() {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        List<CityProjectAccountStatisticsDto> cityProjectAccountStatisticsDtos = new LinkedList<>();
        List<ProjectInfoDto> cityLongs = new LinkedList<>();
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        //根据权限查询project数据
        if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(role.getIsCity())) {
            //获取所有的城市权限有的项目
            cityLongs = projectInfoRepository.getCityLongsByCity(currentUserId);

        } else if (role.getIsCity() != null && JhSystemEnum.rolePermissionEnum.STA_POWER.getKey().equals(role.getIsCity())) {
            //获取所有干系人权限的项目
            cityLongs = projectInfoRepository.getCityLongsBySta(currentUserId);

        }

        if (cityLongs != null && cityLongs.size() > 0) {
            Map<String, List<ProjectInfoDto>> collect = cityLongs.stream().collect(Collectors.groupingBy(ProjectInfoDto::getCityCompany));
            Iterator<Map.Entry<String, List<ProjectInfoDto>>> entries = collect.entrySet().iterator();
            while (entries.hasNext()) {
                Map.Entry<String, List<ProjectInfoDto>> entry = entries.next();
                CityProjectAccountStatisticsDto cityProjectAccountStatisticsDto = new CityProjectAccountStatisticsDto();
                Integer projectTotal = 0;
                Integer projectIsAccount = 0;
                Integer projectAccountFinish = 0;
                Integer projectAccountOverdue = 0;
                //System.out.println("key=" + entry.getKey() + ",value=" + entry.getValue());
                List<ProjectInfoDto> value = entry.getValue();
//                projectIsAccount = Optional.ofNullable(value.stream().filter(p -> ((p.getTaskPhase() != null && p.getTaskPhase().equals(JhSystemEnum.TaskPhaseEnum.SETTLEMENT_PHASE.getKey())) || p.getProjectStatus().equals(JhSystemEnum.ProjectStatusEnum.PROJECT_FINISH.getKey()) || (p.getAccountPhase() != null && (!p.getAccountPhase().equals("--")))) && (!p.getProjectStatus().equals(JhSystemEnum.ProjectStatusEnum.PROJECT_ACCOUNT.getKey()))).collect(Collectors.toList()).size()).orElse(0);
                projectTotal = Optional.ofNullable(value.stream().collect(Collectors.toList()).size()).orElse(0);
                //                projectAccountFinish = Optional.ofNullable(value.stream().filter(p -> p.getProjectStatus() != null && p.getProjectStatus().equals(JhSystemEnum.ProjectStatusEnum.PROJECT_ACCOUNT.getKey())).collect(Collectors.toList()).size()).orElse(0);
                projectAccountOverdue = Optional.ofNullable(value.stream().filter(p -> p.getAccountOverdue() != null && p.getAccountOverdue().equals(Boolean.TRUE)).collect(Collectors.toList()).size()).orElse(0);
//                List<ProjectInfoDto> collect1 = value.stream().filter(p -> p.getProjectStatus() != null && p.getProjectStatus().equals(JhSystemEnum.ProjectStatusEnum.PROJECT_ACCOUNT.getKey())).filter(p -> p.getTaskPhase() != null && p.getTaskPhase().equals(JhSystemEnum.TaskPhaseEnum.SETTLEMENT_PHASE.getKey())).collect(Collectors.toList());

             /*   for(ProjectInfoDto dto:collect1){
                    List<ProjectNodeInfoDto> accountOverDueNode = projectNodeInfoRepository.getAccountOverDueNode(dto.getProjectId());
                    if(accountOverDueNode.size()>0){
                        projectAccountOverdue=projectAccountOverdue+1;
                    }
                }*/
                // Area areaInfo = areaRepository.getAreaInfo(entry.getKey());
                //查找相应的城市公司名字
                DictDetail dictDetailByValue = dictDetailRepository.findDictDetailByValue(entry.getKey());
                cityProjectAccountStatisticsDto.setCity(dictDetailByValue.getLabel());
                cityProjectAccountStatisticsDto.setCityCompany(entry.getKey());
                cityProjectAccountStatisticsDto.setProjectTotal(projectTotal.intValue());
                cityProjectAccountStatisticsDto.setProjectIsAccount(projectIsAccount);
                cityProjectAccountStatisticsDto.setProjectAccountFinish(projectAccountFinish);
                cityProjectAccountStatisticsDto.setProjectAccountOverdue(projectAccountOverdue);
                cityProjectAccountStatisticsDtos.add(cityProjectAccountStatisticsDto);
            }

        }
        return cityProjectAccountStatisticsDtos;
    }

    @Override
    public Map<String, Object> importProjectExcel(MultipartFile file) throws Exception {
        Map<String, Object> result = new HashMap<>();
        String importResult = "";
        Map<String, String> titleMap = new HashMap<>();
        titleMap.put("序号", "no");
        titleMap.put("项目编号", "projectNo");
        titleMap.put("项目名称", "projectName");
        titleMap.put("门店类型", "storeType");
        titleMap.put("大区", "region");
        titleMap.put("城市公司", "cityCompany");
        titleMap.put("创建时间", "projectCreateDate");
        titleMap.put("省份", "provinceName");
        titleMap.put("城市", "cityName");
        titleMap.put("区", "countyName");
        titleMap.put("地址", "projectAddress");
        titleMap.put("立项日期", "establishTime");
        titleMap.put("预计开业日期", "planOpenDate");
        titleMap.put("预计进场日期", "planApproachDate");
        titleMap.put("备注", "remark");
        titleMap.put("总部网发", "generalNetworkName");
        titleMap.put("城市公司网发经理", "regionNetManagerName");
        titleMap.put("城市公司财务", "regionaFinanceManagerName");
        titleMap.put("SE营建经理", "constructionManagerName");

        List<ProjectInfoDto> projectInfoDtoList = FileUtil.importFile(file, titleMap, ProjectInfoDto.class, "");
        List<ProjectInfoDto> list = new LinkedList<>();
        Boolean createFlag = Boolean.FALSE;
        //校验
        for (ProjectInfoDto dto : projectInfoDtoList) {
            ProjectInfoDto importDto = new ProjectInfoDto();
            //BeanUtils.copyProperties(dto, importDto);
            if (StringUtils.isBlank(dto.getProjectName()) || StringUtils.isBlank(dto.getStoreType()) || StringUtils.isBlank(dto.getCityName())) {
                continue;
            }

            importDto.setNo(dto.getNo());
            importDto.setProjectNo(dto.getProjectNo());
            importDto.setProjectName(dto.getProjectName());
            importDto.setProjectAddress(dto.getProjectAddress());
            importDto.setRemark(dto.getRemark());
            importDto.setProjectCreateDate(dto.getProjectCreateDate());
            importDto.setEstablishTime(dto.getEstablishTime());
            importDto.setPlanOpenDate(dto.getPlanOpenDate());
            importDto.setPlanApproachDate(dto.getPlanApproachDate());
            //判断门店类型，大区是否符合码值
            if (dto.getStoreType() != null) {
                DictDetail storeDict = dictDetailRepository.findDictDetailByLabel(dto.getStoreType());
                if (storeDict != null) {
                    importDto.setStoreType(storeDict.getValue());
                }
            }
            /*if(dto.getRegion()!=null){
                DictDetail regionDict = dictDetailRepository.findDictDetailByLabel(dto.getRegion());
                if(regionDict!=null){
                    importDto.setRegion(regionDict.getValue());
                }
            }
            if(dto.getCityCompany()!=null){
                DictDetail companyDict = dictDetailRepository.findDictDetailByLabel(dto.getCityCompany());
                if(companyDict!=null){
                    importDto.setCityCompany(companyDict.getValue());
                }
            }*/
            //根据城市查找其他各种信息
            if (dto.getCityName() != null) {
                Area allAreaForProject = areaRepository.getAllAreaForProject(dto.getCityName());
                if (allAreaForProject != null) {
                    importDto.setRegion(allAreaForProject.getRegion());
                    importDto.setCityCompany(allAreaForProject.getCityCompany());
                    importDto.setCity(allAreaForProject.getAreaCode());
                    importDto.setProvince(allAreaForProject.getParentCode());
                    Area districtForProject = areaRepository.getDistrictForProject(allAreaForProject.getAreaCode(), dto.getCountyName());
                    if (districtForProject != null) {
                        importDto.setCounty(districtForProject.getAreaCode());
                    }
                } else {

                    continue;
                }

            } else {
                importResult = importResult + " " + dto.getNo() + " 城市信息为空";
                continue;
            }
            //时间处理
            if (dto.getProjectCreateDate() == null) {
                //设置当前时间为创建时间
                importDto.setProjectCreateDate(DateUtil.stringToDate(DateUtil.getNowDate()));
            }
            //人员处理
            Long city = importDto.getCity();
            if (city != null) {
                //总部网发
                if (dto.getGeneralNetworkName() != null) {
                    User generalNetworkUser = areaRepository.getUserByCity(city, dto.getGeneralNetworkName());
                    if (generalNetworkUser != null) {
                        importDto.setGeneralNetwork(generalNetworkUser.getId());
                    }
                }
                if (dto.getRegionNetManagerName() != null) {
                    User regionNetManagerUser = areaRepository.getUserByCity(city, dto.getRegionNetManagerName());
                    if (regionNetManagerUser != null) {
                        importDto.setRegionNetManager(regionNetManagerUser.getId());
                    }
                }
                if (dto.getRegionaFinanceManagerName() != null) {
                    User regionaFinanceManagerUser = areaRepository.getUserByCity(city, dto.getRegionaFinanceManagerName());
                    if (regionaFinanceManagerUser != null) {
                        importDto.setRegionaFinanceManager(regionaFinanceManagerUser.getId());
                    }
                }
                if (dto.getConstructionManagerName() != null) {
                    User constructionManagerUser = areaRepository.getUserByCity(city, dto.getConstructionManagerName());
                    if (constructionManagerUser != null) {
                        importDto.setConstructionManager(constructionManagerUser.getId());
                    }
                }
            }
            createFlag = createProject(importDto, null);
            list.add(importDto);
        }
        if (createFlag) {
            result.put("result", "导入成功");
            result.put("msg", importResult);
        } else {
            result.put("result", "导入失败");
            result.put("msg", importResult);
            throw new BadRequestException("导入失败");
        }
        return result;
    }

    @Override
    public List<ProjectInfoDto> getProjectInfoByStore(ProjectInfoQueryCriteria criteria) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        List<Long> projectIds = projectInfoRepository.getProjectIdsByCity(currentUserId, Boolean.FALSE, null, criteria.getProjectTaskPhase(), criteria.getTaskPhase());
        List<ProjectInfoDto> list = new ArrayList<>();
        if (projectIds.size() > 0) {
            criteria.setIsDelete(false);
            criteria.setProjectIds(projectIds);
            list = projectInfoMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectInfo.class, criteria)));
        }
        return list;
    }

    @Override
    public Map<String, String> getConstructionManagerAndPhone(Long projectId) {
        ProjectInfoDto byId = findById(projectId);
        Map<String, String> result = new HashMap<>();
        String constructionManager = "";
        String constructionPhone = "";
        if (ObjectUtils.isNotEmpty(byId)) {
            User one = userRepository.getOne(byId.getConstructionManager());
            if (ObjectUtils.isNotEmpty(one)) {
                constructionManager = one.getNickName();
                constructionPhone = one.getPhone();
            }
            result.put("工程经理id", one.getId().toString());
        }

        result.put("工程经理名称", constructionManager);
        result.put("工程经理电话", constructionPhone);
        return result;
    }

    @Override
    public List<ProjectInfoDto> getProjectInfoByStoreNoAndName(ProjectInfoQueryCriteria criteria) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        //查询是城市权限还是干系人权限
        Role role = this.getRolePermission(currentUserId);
        Long storeId = criteria.getStoreId();
        List<ProjectInfoDto> list = projectInfoMapper.toDto(projectInfoRepository.getDistinctInfoByStoreNoAndName(storeId));
        return list;
    }

    /**
     * 创建信息入node表
     *
     * @param projectNodeInfo
     * @param projectInfoDto
     * @return
     */
    private ProjectNodeInfo copyProjectNodeInfo(ProjectNodeInfo projectNodeInfo, ProjectInfoDto projectInfoDto) {
        if (projectNodeInfo.getNodeCode() != null) {
//            ProjectInfoDto byIdForTitle = findByIdForTitle(projectNodeInfo.getProjectId());
//            if ("con-0010102".equals(projectNodeInfo.getNodeCode()) || "con-0014602".equals(projectNodeInfo.getNodeCode())) {
//                projectNodeInfo.setRemark(projectInfoDto.getStoreType());
//            } else if ("con-0010103".equals(projectNodeInfo.getNodeCode()) || "con-0014603".equals(projectNodeInfo.getNodeCode())) {
//                projectNodeInfo.setRemark(projectInfoDto.getStoreName());
//            } else if ("con-0010104".equals(projectNodeInfo.getNodeCode()) || "con-0014604".equals(projectNodeInfo.getNodeCode())) {
//                projectNodeInfo.setRemark(projectInfoDto.getProjectName());
//            } else if ("con-0010105".equals(projectNodeInfo.getNodeCode()) || "con-0014605".equals(projectNodeInfo.getNodeCode())) {
//                projectNodeInfo.setRemark(projectInfoDto.getProjectType());
//            } else if ("con-0010106".equals(projectNodeInfo.getNodeCode()) || "con-0014606".equals(projectNodeInfo.getNodeCode())) {
//                projectNodeInfo.setRemark(byIdForTitle.getProvinceName());
//                //插入省市区数据
//                /*ProjectInfoDto byIdForTitle = findByIdForTitle(projectNodeInfo.getProjectId());
//                String cityName=byIdForTitle.getProvinceName()+" "+byIdForTitle.getCityName()+" "+byIdForTitle.getCountyName();
//                if(byIdForTitle.getCountyName()!=null){
//                    cityName=cityName+" "+byIdForTitle.getCountyName();
//                }
//                projectNodeInfo.setRemark(cityName);*/
//            } else if ("con-0010107".equals(projectNodeInfo.getNodeCode()) || "con-0014607".equals(projectNodeInfo.getNodeCode())) {
//                projectNodeInfo.setRemark(byIdForTitle.getCityName());
//            } else if ("con-0010108".equals(projectNodeInfo.getNodeCode()) || "con-0014608".equals(projectNodeInfo.getNodeCode())) {
//                projectNodeInfo.setRemark(projectInfoDto.getProjectAddress());
//            } else if ("con-0010109".equals(projectNodeInfo.getNodeCode()) || "con-0014609".equals(projectNodeInfo.getNodeCode())) {
//                projectNodeInfo.setRemark(projectInfoDto.getBusinessNature());
//            } else if ("con-0010110".equals(projectNodeInfo.getNodeCode())) {
//                String establishTime = changeDate(projectInfoDto.getEstablishTime());
//                projectNodeInfo.setRemark(establishTime);
//            } else if ("con-0010111".equals(projectNodeInfo.getNodeCode()) || "con-0014611".equals(projectNodeInfo.getNodeCode())) {
//                if (projectInfoDto.getConstructionManager() != null) {
//                    projectNodeInfo.setRemark(projectInfoDto.getConstructionManager().toString());
//                }
//            } else if ("con-0010112".equals(projectNodeInfo.getNodeCode()) || "con-0014612".equals(projectNodeInfo.getNodeCode())) {
//                if (projectInfoDto.getRegionNetManager() != null) {
//                    projectNodeInfo.setRemark(projectInfoDto.getRegionNetManager().toString());
//                }
//            } else if ("con-0010113".equals(projectNodeInfo.getNodeCode()) || "con-0014613".equals(projectNodeInfo.getNodeCode())) {
//                if (projectInfoDto.getEngineerDirector() != null) {
//                    projectNodeInfo.setRemark(projectInfoDto.getEngineerDirector().toString());
//
//                }
//
//            } else if ("con-0030108".equals(projectNodeInfo.getNodeCode())) {
//                //分公司入Node
//                projectNodeInfo.setRemark(byIdForTitle.getCityCompanyName());
//            }
        }
        return projectNodeInfo;
    }

    private ProjectNodeInfo masterInfoToProjectNodeInfo(ProjectNodeInfo projectNodeInfo, ProjectInfoDto
            projectInfoDto) {
        Long storeId = projectInfoDto.getStoreId();
        if (ObjectUtil.isNotEmpty(storeId)) {
            LambdaQueryWrapper<StoreMasterInfo> storeMasterQuery = Wrappers.lambdaQuery(StoreMasterInfo.class)
                    .eq(StoreMasterInfo::getStoreMasterId, storeId)
                    .eq(StoreMasterInfo::getIsDelete, false);
            StoreMasterInfo storeMasterInfo = storeMasterInfoRepository.selectOne(storeMasterQuery);
            if (ObjectUtil.isNotEmpty(storeMasterInfo)) {
                LambdaQueryWrapper<MasterDrawingInfo> masterDrawingQuery = Wrappers.lambdaQuery(MasterDrawingInfo.class)
                        .eq(MasterDrawingInfo::getStoreId, storeId)
                        .eq(MasterDrawingInfo::getIsDelete, false);
                List<MasterDrawingInfo> masterDrawingInfos = masterDrawingInfoRepository.selectList(masterDrawingQuery);
                Map<String, Long> drawingMap = new HashMap<>();
                drawingMap = masterDrawingInfos.stream().filter(n -> ObjectUtil.isNotEmpty(n.getDrawingId())).collect(Collectors.toMap(MasterDrawingInfo::getDrawingName, MasterDrawingInfo::getDrawingId, (a, b) -> b));
                LambdaQueryWrapper<MasterAreaInfo> masterAreaInfoQuery = Wrappers.lambdaQuery(MasterAreaInfo.class)
                        .eq(MasterAreaInfo::getStoreId, storeId)
                        .eq(MasterAreaInfo::getIsDelete, false);
                MasterAreaInfo masterAreaInfo = masterAreaInfoRepository.selectOne(masterAreaInfoQuery);
                if (projectNodeInfo.getNodeCode() != null) {
                    //图纸信息
                    if ("con-0070208".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("zred_line_CAD"))) {
                            projectNodeInfo.setRemark(drawingMap.get("zred_line_CAD").toString());
                        }
                    } else if ("con-0070209".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("signed_version_upload"))) {
                            projectNodeInfo.setRemark(drawingMap.get("signed_version_upload").toString());
                        }
                    } else if ("con-0070302".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("pred_line_CAD"))) {
                            projectNodeInfo.setRemark(drawingMap.get("pred_line_CAD").toString());
                        }
                    } else if ("con-0070303".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("plane_PDF"))) {
                            projectNodeInfo.setRemark(drawingMap.get("plane_PDF").toString());
                        }
                    } else if ("con-0070304".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("signed_version_plane"))) {
                            projectNodeInfo.setRemark(drawingMap.get("signed_version_plane").toString());
                        }
                    } else if ("con-0070402".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("original_building_drawing"))) {
                            projectNodeInfo.setRemark(drawingMap.get("original_building_drawing").toString());
                        }
                    } else if ("con-0070404".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("fire_approve_CAD"))) {
                            projectNodeInfo.setRemark(drawingMap.get("fire_approve_CAD").toString());
                        }
                    } else if ("con-0070407".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("capital_drawing_CAD"))) {
                            projectNodeInfo.setRemark(drawingMap.get("capital_drawing_CAD").toString());
                        }
                    } else if ("con-0070408".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("plane_construction_CAD"))) {
                            projectNodeInfo.setRemark(drawingMap.get("plane_construction_CAD").toString());
                        }
                    } else if ("con-0070409".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("facade_construction_CAD"))) {
                            projectNodeInfo.setRemark(drawingMap.get("facade_construction_CAD").toString());
                        }
                    } else if ("con-0070410".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("weak_current_CAD"))) {
                            projectNodeInfo.setRemark(drawingMap.get("weak_current_CAD").toString());
                        }
                    } else if ("con-0070412".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("doorhead_effect"))) {
                            projectNodeInfo.setRemark(drawingMap.get("doorhead_effect").toString());
                        }
                    } else if ("con-0070413".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("doorhead_effect_signed"))) {
                            projectNodeInfo.setRemark(drawingMap.get("doorhead_effect_signed").toString());
                        }
                    } else if ("con-0070414".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("doorhead_construction_CAD"))) {
                            projectNodeInfo.setRemark(drawingMap.get("doorhead_construction_CAD").toString());
                        }
                    } else if ("con-0080104".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("joint_brand_effect"))) {
                            projectNodeInfo.setRemark(drawingMap.get("joint_brand_effect").toString());
                        }
                    } else if ("con-0080105".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("joint_brand_construction"))) {
                            projectNodeInfo.setRemark(drawingMap.get("joint_brand_construction").toString());
                        }
                    } else if ("con-0080305".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("business_red_line"))) {
                            projectNodeInfo.setRemark(drawingMap.get("business_red_line").toString());
                        }
                    } else if ("con-0080306".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("business_effect"))) {
                            projectNodeInfo.setRemark(drawingMap.get("business_effect").toString());
                        }
                    } else if ("con-0080307".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(drawingMap.get("business_construction"))) {
                            projectNodeInfo.setRemark(drawingMap.get("business_construction").toString());
                        }
                    }//物业名称
                    else if ("con-0070202".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(storeMasterInfo.getPropertyName())) {
                            projectNodeInfo.setRemark(storeMasterInfo.getPropertyName());
                        }
                    }//合同租赁面积
                    else if ("con-0070203".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(storeMasterInfo.getLeaseContractArea())) {
                            projectNodeInfo.setRemark(storeMasterInfo.getLeaseContractArea().toString());
                        }
                    }//净使用面积
                    else if ("con-0070204".equals(projectNodeInfo.getNodeCode()) && ObjectUtil.isNotEmpty(masterAreaInfo)) {
                        if (ObjectUtil.isNotEmpty(masterAreaInfo.getNetUsableArea())) {
                            projectNodeInfo.setRemark(masterAreaInfo.getNetUsableArea().toString());
                        }
                    }//公摊面积
                    else if ("con-0070205".equals(projectNodeInfo.getNodeCode()) && ObjectUtil.isNotEmpty(masterAreaInfo)) {
                        if (ObjectUtil.isNotEmpty(masterAreaInfo.getSharedAreaProperty())) {
                            projectNodeInfo.setRemark(masterAreaInfo.getSharedAreaProperty().toString());
                        }
                    }//得房率
                    else if ("con-0070206".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(storeMasterInfo.getRoomAcquisitionRate())) {
                            projectNodeInfo.setRemark(storeMasterInfo.getRoomAcquisitionRate());
                        }
                    }//设计定位
                    else if ("con-0070103".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(storeMasterInfo.getDesignPosition())) {
                            projectNodeInfo.setRemark(storeMasterInfo.getDesignPosition());
                        }
                    }//装修设计等级
                    else if ("con-0070104".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(storeMasterInfo.getDecorateGrade())) {
                            projectNodeInfo.setRemark(storeMasterInfo.getDecorateGrade());
                        }
                    }//门店版本
                    else if ("con-0070105".equals(projectNodeInfo.getNodeCode())) {
                        if (ObjectUtil.isNotEmpty(storeMasterInfo.getStoreVersion())) {
                            projectNodeInfo.setRemark(storeMasterInfo.getStoreVersion());
                        }
                    }
                    if (ObjectUtil.isNotEmpty(masterAreaInfo)) {
                        if ("con-0070503".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getKidShoes())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getKidShoes().toString());
                            }
                        } else if ("con-0070504".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getLingerieHomeTextile())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getLingerieHomeTextile().toString());
                            }
                        } else if ("con-0070506".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getToys())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getToys().toString());
                            }
                        } else if ("con-0070507".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getCulturalEdu())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getCulturalEdu().toString());
                            }
                        } else if ("con-0070509".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getArticles())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getArticles().toString());
                            }
                        } else if ("con-0070511".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getLatheChair())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getLatheChair().toString());
                            }
                        } else if ("con-0070513".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getPowderedMilk())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getPowderedMilk().toString());
                            }
                        } else if ("con-0070514".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getDiapers())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getDiapers().toString());
                            }
                        } else if ("con-0070515".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getSnackFood())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getSnackFood().toString());
                            }
                        } else if ("con-0070517".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getPrivateBrand())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getPrivateBrand().toString());
                            }
                        } else if ("con-0070519".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getPregnantMothers())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getPregnantMothers().toString());
                            }
                        } else if ("con-0070521".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getStoreAreaReserveOne())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getStoreAreaReserveOne().toString());
                            }
                        } else if ("con-0070523".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getStoreAreaReserveTwo())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getStoreAreaReserveTwo().toString());
                            }
                        } else if ("con-0070526".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getAmusement())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getAmusement().toString());
                            }
                        } else if ("con-0070527".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getGrowthTestStation())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getGrowthTestStation().toString());
                            }
                        } else if ("con-0070528".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getOtherAmusement())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getOtherAmusement().toString());
                            }
                        } else if ("con-0070530".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getInteractiveExperience())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getInteractiveExperience().toString());
                            }
                        } else if ("con-0070532".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getMaternityExperienceEoom())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getMaternityExperienceEoom().toString());
                            }
                        } else if ("con-0070533".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getSelectionShop())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getSelectionShop().toString());
                            }
                        } else if ("con-0070535".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getLocal())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getLocal().toString());
                            }
                        } else if ("con-0070537".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getServiceStation())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getServiceStation().toString());
                            }
                        } else if ("con-0070539".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getMomClass())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getMomClass().toString());
                            }
                        } else if ("con-0070541".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getBlackGoldZone())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getBlackGoldZone().toString());
                            }
                        } else if ("con-0070543".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getServiceAreaReserveOne())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getServiceAreaReserveOne().toString());
                            }
                        } else if ("con-0070545".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getServiceAreaReserveTwo())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getServiceAreaReserveTwo().toString());
                            }
                        } else if ("con-0070547".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getServiceAreaReserveThree())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getServiceAreaReserveThree().toString());
                            }
                        } else if ("con-0070549".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getValueAddOne())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getValueAddOne().toString());
                            }
                        } else if ("con-0070550".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getValueAddTwo())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getValueAddTwo().toString());
                            }
                        } else if ("con-0070551".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getValueAddThree())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getValueAddThree().toString());
                            }
                        } else if ("con-0070552".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getValueAddFour())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getValueAddFour().toString());
                            }
                        } else if ("con-0070553".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getValueAddFix())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getValueAddFix().toString());
                            }
                        } else if ("con-0070556".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getCustomerService())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getCustomerService().toString());
                            }
                        } else if ("con-0070558".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getDisplayShow())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getDisplayShow().toString());
                            }
                        } else if ("con-0070560".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getMomBabyRoom())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getMomBabyRoom().toString());
                            }
                        } else if ("con-0070562".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getMainChannel())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getMainChannel().toString());
                            }
                        } else if ("con-0070564".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getCommodityWarehouse())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getCommodityWarehouse().toString());
                            }
                        } else if ("con-0070565".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getTextileWarehouse())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getTextileWarehouse().toString());
                            }
                        } else if ("con-0070566".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getGiftWarehouse())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getGiftWarehouse().toString());
                            }
                        } else if ("con-0070567".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getMarketLibrary())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getMarketLibrary().toString());
                            }
                        } else if ("con-0070568".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getAdministrativeWarehouse())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getAdministrativeWarehouse().toString());
                            }
                        } else if ("con-0070570".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getOffice())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getOffice().toString());
                            }
                        } else if ("con-0070571".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getFinanceOffice())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getFinanceOffice().toString());
                            }
                        } else if ("con-0070572".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getTrainingRoom())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getTrainingRoom().toString());
                            }
                        } else if ("con-0070573".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getStaffLounge())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getStaffLounge().toString());
                            }
                        } else if ("con-0070574".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getStaffLockerRoom())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getStaffLockerRoom().toString());
                            }
                        } else if ("con-0070575".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getStrongElectricityRoom())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getStrongElectricityRoom().toString());
                            }
                        } else if ("con-0070576".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getInformationRoom())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getInformationRoom().toString());
                            }
                        } else if ("con-0070577".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getCleaningRoom())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getCleaningRoom().toString());
                            }
                        } else if ("con-0070578".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getCartonRecyclingArea())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getCartonRecyclingArea().toString());
                            }
                        } else if ("con-0070579".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getAccessOfficeArea())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getAccessOfficeArea().toString());
                            }
                        } else if ("con-0070581".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getAuxiliaryAreaReserveOne())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getAuxiliaryAreaReserveOne().toString());
                            }
                        } else if ("con-0070583".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getComputerRoom())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getComputerRoom().toString());
                            }
                        } else if ("con-0070584".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getWallRedLine())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getWallRedLine().toString());
                            }
                        } else if ("con-0070585".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getElevatorRedLine())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getElevatorRedLine().toString());
                            }
                        } else if ("con-0070586".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getPublicCorridorRedLine())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getPublicCorridorRedLine().toString());
                            }
                        } else if ("con-0070587".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getStairsRedOut())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getStairsRedOut().toString());
                            }
                        } else if ("con-0070588".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getElevatorRedOut())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getElevatorRedOut().toString());
                            }
                        } else if ("con-0070589".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getPropertySharing())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getPropertySharing().toString());
                            }
                        } else if ("con-0070591".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getGeneralManagersOffice())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getGeneralManagersOffice().toString());
                            }
                        } else if ("con-0070592".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getFinanceBranchOffice())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getFinanceBranchOffice().toString());
                            }
                        } else if ("con-0070593".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getOpenOfficeArea())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getOpenOfficeArea().toString());
                            }
                        } else if ("con-0070594".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getInformationBranchRoom())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getInformationBranchRoom().toString());
                            }
                        } else if ("con-0070595".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getPhotocopyRoom())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getPhotocopyRoom().toString());
                            }
                        } else if ("con-0070596".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getTeaRoom())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getTeaRoom().toString());
                            }
                        } else if ("con-0070597".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getReceptionArea())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getReceptionArea().toString());
                            }
                        } else if ("con-0070598".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getAdministrativeCangku())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getAdministrativeCangku().toString());
                            }
                        } else if ("con-0070599".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getConventionHall())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getConventionHall().toString());
                            }
                        } else if ("con-0070622".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getNegotiationRoom())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getNegotiationRoom().toString());
                            }
                        } else if ("con-0070600".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getSmallMeetingOne())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getSmallMeetingOne().toString());
                            }
                        } else if ("con-0070601".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getSmallMeetingTwo())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getSmallMeetingTwo().toString());
                            }
                        } else if ("con-0070602".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getNegotiationRoomOne())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getNegotiationRoomOne().toString());
                            }
                        } else if ("con-0070603".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getNegotiationRoomTwo())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getNegotiationRoomTwo().toString());
                            }
                        } else if ("con-0070604".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getSharedAreaProperty())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getSharedAreaProperty().toString());
                            }
                        } else if ("con-0070606".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getContractLeaseArea())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getContractLeaseArea().toString());
                            }
                        } else if ("con-0070607".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getNetUsableArea())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getNetUsableArea().toString());
                            }
                        } else if ("con-0070608".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getBusinessArea())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getBusinessArea().toString());
                            }
                        } else if ("con-0070609".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getCommodityArea())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getCommodityArea().toString());
                            }
                        } else if ("con-0070610".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getServiceArea())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getServiceArea().toString());
                            }
                        } else if ("con-0070611".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getInvestmentArea())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getInvestmentArea().toString());
                            }
                        } else if ("con-0070612".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getAuxiliaryArea())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getAuxiliaryArea().toString());
                            }
                        } else if ("con-0070613".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getWarehouseArea())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getWarehouseArea().toString());
                            }
                        } else if ("con-0070614".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getBackcourtOffice())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getBackcourtOffice().toString());
                            }
                        } else if ("con-0070615".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getStorePublicArea())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getStorePublicArea().toString());
                            }
                        } else if ("con-0070616".equals(projectNodeInfo.getNodeCode())) {
                            if (ObjectUtil.isNotEmpty(masterAreaInfo.getBranchOfficeArea())) {
                                projectNodeInfo.setRemark(masterAreaInfo.getBranchOfficeArea().toString());
                            }
                        }
                    }
                }
            }
        }
        return projectNodeInfo;
    }

    private String changeTime(Timestamp time) {
        if (null != time) {
            return new SimpleDateFormat("yyyy-MM-dd").format(time);
        } else {
            return "";
        }

    }

    private String changeDate(Date time) {
        if (null != time) {
            return new SimpleDateFormat("yyyy-MM-dd").format(time);
        } else {
            return "";
        }

    }

    private NodeInfo getGantt(ProjectTemplate projectTemplate, List<ProjectNodeInfo> list, NodeInfo nowNodeInfo) {
        String countdate = "";
        NodeInfo nodeInfo = new NodeInfo();
        String wbsCode = "";
        if (projectTemplate.getIsKey() != null && projectTemplate.getIsKey() && projectTemplate.getKeyFrontWbs() != null) {
            wbsCode = projectTemplate.getKeyFrontWbs();
        } else {
            wbsCode = projectTemplate.getFrontWbsConfig();
        }

        int planDay = projectTemplate.getPlanDay();
        int totalDay = projectTemplate.getTotalDay();
        int addDay = 0;
        logger.info("模板----------{}", projectTemplate);
        if (wbsCode != null && (!"".equals(wbsCode))) {
            //判断是结束开始
            if (wbsCode.contains("FS")) {
                String nodecode = "";
                if (wbsCode.contains("+")) {
                    String[] split = wbsCode.split("\\+");
                    nodecode = split[0].replace("FS", "");
                    addDay = Integer.parseInt(split[1]);
                } else {
                    nodecode = wbsCode.replace("FS", "");
                }

                //查找紧前任务的结束日期
                for (ProjectNodeInfo projectNodeInfo : list) {
                    if (nodecode.equals(projectNodeInfo.getNodeCode())) {
                        countdate = changeDate(projectNodeInfo.getPlanEndDate());
                        break;
                    }
                }
                String begin = "";
                String end = "";
                //计算开始日期和结束日期
                try {
                    begin = DateUtil.addDateForNode(countdate, planDay, totalDay, "begin", addDay);
                    end = DateUtil.addDateForNode(countdate, planDay, totalDay, "end", addDay);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                nodeInfo.setWbs(nodecode);
                nodeInfo.setBegin(begin);
                nodeInfo.setEnd(end);
            }
        } else {
            //若没有紧前
            countdate = nowNodeInfo.getBegin();
            String begin = "";
            String end = "";
            //计算开始日期和结束日期
            try {
                begin = countdate;
                end = DateUtil.addDateForNode(begin, planDay, totalDay, "end", addDay);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            nodeInfo.setBegin(begin);
            nodeInfo.setEnd(end);
        }

        //System.out.println(nodeInfo.getNodeCode()+"---"+nodeInfo.getBegin()+"---------"+nodeInfo.getEnd());
        return nodeInfo;
    }

    public void setNodeRoleCode(Long projectId, ProjectTemplate template, ProjectNodeInfo projectNodeInfo) {
        //任务负责人
        //获取role
        String roleCode = projectNodeInfo.getRoleCode();
        String[] roles = roleCode.split(",");
        List<String> nodeRole = new ArrayList<>();
        JhSystemEnum.JobEnum[] roleValues = JhSystemEnum.JobEnum.values();
        for (String role : roles) {
            for (JhSystemEnum.JobEnum roleValue : roleValues) {
                if (roleValue.getKey().equals(role)) {
                    nodeRole.add(roleValue.getSpec());
                    break;
                }
            }
        }
        LambdaQueryWrapper<ProjectStakeholders> query = Wrappers.lambdaQuery(ProjectStakeholders.class).
                eq(ProjectStakeholders::getProjectId, projectId).in(ProjectStakeholders::getRoleName, nodeRole);
        List<ProjectStakeholders> stakeholders = projectStakeholdersService.list(query);
        if (CollectionUtil.isNotEmpty(stakeholders)) {
            String collect = stakeholders.stream().filter(s -> null != s.getUserId()).map(s -> String.valueOf(s.getUserId())).collect(Collectors.joining(","));
            projectNodeInfo.setRemark(collect);
        }
    }


    @Override
    public Map<String, Object> queryNoSheetList(ProjectInfoQueryCriteria criteria, Pageable pageable) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
//        Long currentUserId = 1L;
        //查询是城市权限还是干系人权限
//        Role role = this.getRolePermission(currentUserId);
//        List<Long> projectIds = new LinkedList<>();

        getPage(pageable);
        PageInfo<ProjectInfo> page = new PageInfo<>();
        criteria.setIsDelete(false);
        List<ProjectInfo> infos = list(QueryHelpPlus.getPredicate(ProjectInfo.class, criteria));
//        List<ProjectInfo> list = infos.stream().filter(o -> {
//            if (ObjectUtils.isNotEmpty(o.getIsSheet()) && o.getIsSheet()) {
//                LambdaQueryWrapper queryWrapper = Wrappers.lambdaQuery(ProjectApprove.class)
//                        .eq(ProjectApprove::getOrderId, o.getSheetId())
//                        .eq(ProjectApprove::getApproveResult, JhSystemEnum.approveResultEnum.APPROVE_REFUSE.getKey());
//                ProjectApprove projectApprove = projectApproveRepository.selectOne(queryWrapper);
//                if (ObjectUtils.isNotEmpty(projectApprove)) {
//                    o.setIsSheet(false);
//                    o.setSheetId(null);
//                    projectInfoRepository.updateById(o);
//                } else {
//                    return false;
//                }
//            }
//            return true;
//        }).collect(Collectors.toList());
        page = new PageInfo<>(infos);


        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public Role getRolePermission(Long currentUserId) {
        Role role = new Role();
        role.setIsCity(JhSystemEnum.rolePermissionEnum.STA_POWER.getKey());
        role.setIsStakeholder(Boolean.FALSE);
//        List<Role> permissionmode = projectInfoRepository.getPermissionmode(currentUserId);
        List<Role> permissionmode = roleRepository.getPermissionmode(currentUserId);
        for (Role r : permissionmode) {
            if (ObjectUtil.isNotEmpty(r.getIsCity()) && JhSystemEnum.rolePermissionEnum.CITY_POWER.getKey().equals(r.getIsCity())) {
                role.setIsCity(r.getIsCity());
            }
            if (ObjectUtil.isNotEmpty(r.getIsStakeholder()) && r.getIsStakeholder()) {
                role.setIsStakeholder(Boolean.TRUE);
            }
        }
        return role;
    }

    @Override
    public String getFilePath(DownloadDocVo downloadDocVo) {

        String docType = downloadDocVo.getDocType() == null ? KidsSystemEnum.DocType.Contract.getValue() : downloadDocVo.getDocType();
        String path = null;
        if (docType.equals(KidsSystemEnum.DocType.Contract.getValue())) {
            path = wordUtils.createDoc("contract.ftl", downloadDocVo.getContract());
        } else {
            path = wordUtils.createDoc("noticeToProceed.ftl", downloadDocVo.getNoticeToProceed());
        }
        return path;
    }

    @Override
    public void test(String nodeCode) {
        LambdaQueryWrapper<ProjectNodeInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectNodeInfo::getNodeCode, nodeCode);
        List<ProjectNodeInfo> projectNodeInfos = projectNodeInfoRepository.selectList(lambdaQueryWrapper);
        String nodeName = projectNodeInfos.get(0).getNodeName();
        JSONArray jsonArray = JSONArray.parseArray(nodeName);
        List<LicenceVo> licenceVos = JSONObject.parseArray(nodeName, LicenceVo.class);
//        JSONObject.parseArray(js,JSONObject.class);
        System.out.println(jsonArray);
    }

    @Override
    public MasterDrawingInfoDto getDrawingInfo(Long drawId) {
        LambdaQueryWrapper<MasterDrawingInfo> masterDrawingInfoQuery = Wrappers.lambdaQuery(MasterDrawingInfo.class)
                .eq(MasterDrawingInfo::getDrawingId, drawId)
                .eq(MasterDrawingInfo::getIsDelete, false);
        MasterDrawingInfo masterDrawingInfo = masterDrawingInfoRepository.selectOne(masterDrawingInfoQuery);
        if (ObjectUtil.isNull(masterDrawingInfo)) {
            throw new BadRequestException("查询结果为空，请校验参数");
        }
        Long storeId = masterDrawingInfo.getStoreId();
        LambdaQueryWrapper<ProjectInfo> projectInfoQuery = Wrappers.lambdaQuery(ProjectInfo.class)
                .eq(ProjectInfo::getStoreId, storeId)
                .in(ProjectInfo::getProjectType, "new", "minor", "major", "reform")
                .orderByDesc(ProjectInfo::getCreateTime)
                .eq(ProjectInfo::getIsDelete, false);
        List<ProjectInfo> projectInfoList = projectInfoRepository.selectList(projectInfoQuery);
        String drawingName = masterDrawingInfo.getDrawingName();
        Map<String, String> drawingMap = new HashMap<>();
        drawingMap.put("zred_line_CAD", "con-0070208");
        drawingMap.put("signed_version_upload", "con-0070209");
        drawingMap.put("pred_line_CAD", "con-0070302");
        drawingMap.put("plane_PDF", "con-0070303");
        drawingMap.put("signed_version_plane", "con-0070304");
        drawingMap.put("original_building_drawing", "con-0070402");
        drawingMap.put("fire_approve_CAD", "con-0070404");
        drawingMap.put("capital_drawing_CAD", "con-0070407");
        drawingMap.put("plane_construction_CAD", "con-0070408");
        drawingMap.put("facade_construction_CAD", "con-0070409");
        drawingMap.put("weak_current_CAD", "con-0070410");
        drawingMap.put("doorhead_effect", "con-0070412");
        drawingMap.put("doorhead_effect_signed", "con-0070413");
        drawingMap.put("doorhead_construction_CAD", "con-0070414");
        drawingMap.put("joint_brand_effect", "con-0080104");
        drawingMap.put("joint_brand_construction", "con-0080105");
        drawingMap.put("business_red_line", "con-0080305");
        drawingMap.put("business_effect", "con-0080306");
        drawingMap.put("business_construction", "con-0080307");
        MasterDrawingInfoDto masterDrawingInfoDto = masterDrawingInfoMapper.toDto(masterDrawingInfo);
        String nodeDode = drawingMap.get(drawingName);
        if (ObjectUtil.isNotEmpty(projectInfoList) && projectInfoList.size() > 0) {
            ProjectInfo projectInfo = projectInfoList.get(0);
            Long projectId = projectInfo.getProjectId();
            LambdaQueryWrapper<ProjectNodeInfo> projectNodeInfoQuery = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getNodeCode, nodeDode)
                    .eq(ProjectNodeInfo::getProjectId, projectId);
            ProjectNodeInfo projectNodeInfo = projectNodeInfoRepository.selectOne(projectNodeInfoQuery);
            Long nodeId = projectNodeInfo.getNodeId();
            masterDrawingInfoDto.setNodeId(nodeId.toString());
        }
        return masterDrawingInfoDto;
    }

    /**
     * 存入审批人/操作人   废弃暂时不用
     *
     * @param resources
     * @throws IllegalAccessException
     */
    @Override
    public void saveProjectStakeholders(ProjectInfo resources) throws IllegalAccessException {
        ProjectStakeholders projectStakeholders = new ProjectStakeholders();
        projectStakeholders.setProjectId(resources.getProjectId());
        projectStakeholders.setIsApprove(false);
        projectStakeholders.setShakeholderStatus(JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
        projectStakeholders.setIsDelete(false);
        projectStakeholders.setJoinTime(new Timestamp(System.currentTimeMillis()));

//        将选择的运营战区负责人存进干系人表，项目ID、当前选择的用户ID、角色code
        if (ObjectUtil.isNotEmpty(resources.getOperationsTheaterLeader())) {
            projectStakeholders.setUserId(resources.getOperationsTheaterLeader());
            projectStakeholders.setRoleCode(AtourSystemEnum.engineeringRoleCodeEnum.YYZQFZR.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.YYZQFZR.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }

//            开业经理存进干系人表
        if (ObjectUtil.isNotEmpty(resources.getOpeningManager())) {
            projectStakeholders.setUserId(resources.getOpeningManager());
            projectStakeholders.setRoleCode(AtourSystemEnum.engineeringRoleCodeEnum.KYJL.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.KYJL.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }

//            运营经理存进干系人表
        if (ObjectUtil.isNotEmpty(resources.getOperationsManager())) {
            projectStakeholders.setUserId(resources.getOperationsManager());
            projectStakeholders.setRoleCode(AtourSystemEnum.engineeringRoleCodeEnum.YYJL.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.YYJL.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }

//            特许经理存进干系人表
        if (ObjectUtil.isNotEmpty(resources.getFranchiseManager())) {
            projectStakeholders.setUserId(resources.getFranchiseManager());
            projectStakeholders.setRoleCode(AtourSystemEnum.engineeringRoleCodeEnum.TXJL.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.TXJL.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }

//            开发战区负责人存进干系人表
        if (ObjectUtil.isNotEmpty(resources.getDevelopmentTheaterLeader())) {
            projectStakeholders.setUserId(resources.getDevelopmentTheaterLeader());
            projectStakeholders.setRoleCode(AtourSystemEnum.engineeringRoleCodeEnum.KFZQFZR.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.KFZQFZR.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }

//            开发分区负责人存进干系人表
        if (ObjectUtil.isNotEmpty(resources.getDevelopmentZoneLeader())) {
            projectStakeholders.setUserId(resources.getDevelopmentZoneLeader());
            projectStakeholders.setRoleCode(AtourSystemEnum.engineeringRoleCodeEnum.KFFQFZR.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.KFFQFZR.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }

//            开发经理存进干系人表
//        if (ObjectUtil.isNotEmpty(resources.getDevelopmentManager())) {
//            projectStakeholders.setUserId(resources.getDevelopmentManager());
//            projectStakeholders.setRoleCode(AtourSystemEnum.engineeringRoleCodeEnum.KFJL.getKey());
//            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.KFJL.getSpec());
//            projectStakeholdersService.create(projectStakeholders);
//        }

//            营建区域负责人(总部中心负责人)存进干系人表
        if (ObjectUtil.isNotEmpty(resources.getConstructionAreaLeader())) {
            projectStakeholders.setUserId(resources.getConstructionAreaLeader());
            projectStakeholders.setRoleCode(JhSystemEnum.JobEnum.ZBZXFZR.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.YJQYFZR.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }

//            项目经理（工程经理）存进干系人表
        if (ObjectUtil.isNotEmpty(resources.getDevelopmentZoneLeader())) {
            projectStakeholders.setUserId(resources.getDevelopmentZoneLeader());
            projectStakeholders.setRoleCode(JhSystemEnum.JobEnum.GCJL.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.GCJL.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }

//            设计师（设计负责人）存进干系人表
        if (ObjectUtil.isNotEmpty(resources.getDesigner())) {
            projectStakeholders.setUserId(resources.getDesigner());
            projectStakeholders.setRoleCode(JhSystemEnum.JobEnum.SJFZR.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.SJS.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }

//            机电工程师存进干系人表
        if (ObjectUtil.isNotEmpty(resources.getMechanicalAndElectricalEngineer())) {
            projectStakeholders.setUserId(resources.getMechanicalAndElectricalEngineer());
            projectStakeholders.setRoleCode(AtourSystemEnum.engineeringRoleCodeEnum.JDGCS.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.JDGCS.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }

//            弱电工程师存进干系人表
        if (ObjectUtil.isNotEmpty(resources.getWeakCurrentEngineer())) {
            projectStakeholders.setUserId(resources.getWeakCurrentEngineer());
            projectStakeholders.setRoleCode(AtourSystemEnum.engineeringRoleCodeEnum.RDGCS.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.RDGCS.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }

//            设计共管人员存进干系人表
        if (ObjectUtil.isNotEmpty(resources.getDesignCoManagementPersonnel())) {
            projectStakeholders.setUserId(resources.getDesignCoManagementPersonnel());
            projectStakeholders.setRoleCode(AtourSystemEnum.engineeringRoleCodeEnum.SJGGRY.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.SJGGRY.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }

//            弱电验收人员存进干系人表
        if (ObjectUtil.isNotEmpty(resources.getWeakCurrentAcceptancePersonnel())) {
            projectStakeholders.setUserId(resources.getWeakCurrentAcceptancePersonnel());
            projectStakeholders.setRoleCode(AtourSystemEnum.engineeringRoleCodeEnum.RDYSRY.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.RDYSRY.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }
//            机电验收人员存进干系人表
        if (ObjectUtil.isNotEmpty(resources.getMechanicalAndElectricalAcceptancePersonnel())) {
            projectStakeholders.setUserId(resources.getMechanicalAndElectricalAcceptancePersonnel());
            projectStakeholders.setRoleCode(AtourSystemEnum.engineeringRoleCodeEnum.JDYSRY.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.JDYSRY.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }

//            飞行质检人员存进干系人表
        if (ObjectUtil.isNotEmpty(resources.getFlightQualityInspectionPersonnel())) {
            projectStakeholders.setUserId(resources.getFlightQualityInspectionPersonnel());
            projectStakeholders.setRoleCode(AtourSystemEnum.engineeringRoleCodeEnum.FXZJRY.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.FXZJRY.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }

//            竣工验收人员存进干系人表
        if (ObjectUtil.isNotEmpty(resources.getCompletionAcceptancePersonnel())) {
            projectStakeholders.setUserId(resources.getCompletionAcceptancePersonnel());
            projectStakeholders.setRoleCode(AtourSystemEnum.engineeringRoleCodeEnum.JGYSRY.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.JGYSRY.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }

//            采购营销存进干系人表
        if (ObjectUtil.isNotEmpty(resources.getProcurementMarketing())) {
            projectStakeholders.setUserId(resources.getProcurementMarketing());
            projectStakeholders.setRoleCode(AtourSystemEnum.engineeringRoleCodeEnum.CGXS.getKey());
            projectStakeholders.setRoleName(AtourSystemEnum.engineeringRoleCodeEnum.CGXS.getSpec());
            projectStakeholdersService.create(projectStakeholders);
        }
    }

    /**
     * 插入用户选的干系人
     */
    @Override
    public Boolean saveProjectStakeholdersUsers(StakeholdersReq resources) {
        List<String> list = new ArrayList<>();
        list.add(AtourSystemEnum.engineeringRoleCodeEnum.JFZXGXZC.getKey());
        list.add(AtourSystemEnum.engineeringRoleCodeEnum.YZXMJL.getKey());
        list.add(AtourSystemEnum.engineeringRoleCodeEnum.SJDW.getKey());
        //判断list里是否包含用户输入的roleCode
        for (String roleCode : resources.getRoleCode()) {
            if (list.contains(roleCode)) {
                //插入干系人入 t_project_stakeholders表里
                return projectStakeholdersService.insertStakeholders(resources);
            }
        }
        return false;
    }

    @Override
    public void downloadProjectByNode(HttpServletResponse response, ProjectInfoQueryCriteria criteria) {
        String nodeCode = criteria.getNodeCode();
        Long projectId = criteria.getProjectId();
        String nodeCodeQuery = "";
        String configType = "";
        Integer sheetStart = 0;
        if (JhSystemEnum.NodeCodeSEEnum.NODE_144.getKey().equals(nodeCode)) {
            configType = KidsSystemEnum.TemplateConfigType.DW004.getValue();
            sheetStart = KidsSystemEnum.TemplateConfigType.DW004.getSheetStart();
            //在结算审定归档下载工程结算审定单的信息
            nodeCodeQuery = JhSystemEnum.NodeCodeSEEnum.NODE_142.getKey();
        }

        TemplateConfig byConfigType = templateConfigService.findByConfigType(configType);
        if (byConfigType == null) {
            throw new BadRequestException("请先配置导入模板");
        }
        Map<String, String> nodeMap = new HashMap<>();
        LambdaQueryWrapper groupQuery = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getProjectId, projectId)
                .eq(ProjectGroup::getNodeCode, nodeCodeQuery);
        List<ProjectGroup> groupList = projectGroupService.list(groupQuery);
        if (ObjectUtil.isNotEmpty(groupList) && groupList.size() > 0) {
            ProjectGroup projectGroup = groupList.get(0);
            LambdaQueryWrapper nodeQuery = Wrappers.lambdaQuery(ProjectNodeInfo.class)
                    .eq(ProjectNodeInfo::getProjectId, projectId)
                    .eq(ProjectNodeInfo::getParentId, projectGroup.getTemplateId());
            List<ProjectNodeInfo> nodeList = projectNodeInfoService.list(nodeQuery);
            if (ObjectUtil.isNotEmpty(nodeList) && nodeList.size() > 0) {
                nodeMap = nodeList.stream().filter(n -> ObjectUtil.isNotEmpty(n.getRemark())).collect(Collectors.toMap(ProjectNodeInfo::getNodeCode, ProjectNodeInfo::getRemark));
            }
        }
        String content = byConfigType.getContent();
        List<ValuePosition> valuePositions = JSONObject.parseArray(content, ValuePosition.class);
        for (ValuePosition valuePosition : valuePositions) {
            String remark = nodeMap.get(valuePosition.getNodeCode());
            if (ObjectUtil.isNotEmpty(remark)) {
                valuePosition.setValue(remark);
            }
        }
        try {
            excelUtils.downloadFile(response, valuePositions, sheetStart);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取该项目的所有计划节点和对应的时间节点
     *
     * @param projectId
     * @return
     */
    @Override
    public List<ProjectNodeInfoDto> getProgressTimeByProjectId(Long projectId) {

        return null;
    }

    @Override
    public Boolean saveProjectSheJi(ProjectInfoInsertStaDto resources) {
        LambdaQueryWrapper<ProjectInfo> wrapper = Wrappers.lambdaQuery(ProjectInfo.class).eq(ProjectInfo::getProjectId, resources.getProjectId());
        ProjectInfo projectInfo = projectInfoService.getOne(wrapper);

        ProjectInfoDto infoDto = new ProjectInfoDto();
        BeanUtil.copyProperties(projectInfo, infoDto, CopyOptions.create().setIgnoreNullValue(true));
        infoDto.setProjectId(projectInfo.getProjectId());

//        ProjectInfoExpansion developmentSystem = projectInfoServiceImpl.getDevelopmentSystem(projectInfo.getProjectHlmId());

        TemplateCollection templateCollection = new TemplateCollection();
        templateCollection.setTemplateId(1700413133576765454L);
        templateCollection.setTemplateCode("design");
        if (ObjectUtil.isNotEmpty(templateCollection) && ObjectUtil.isNotEmpty(projectInfo)) {
            try {
                projectInfoService.saveProject(templateCollection, resources.getProjectId(), "major", infoDto, null
                        , projectInfo, projectInfo.getProjectType(), null, null);
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            throw new BadRequestException("当前条件未配置模板");
        }
        return true;
    }

    @Override
    public Boolean queryUnqualifiedDownloadState() throws InterruptedException {
        File file = new File("excel" + File.separator + "项目信息.xlsx");
        return file.exists();
    }

    @Override
    public void downloadProject(HttpServletRequest request, HttpServletResponse response) throws IOException {
//        List<ProjectInfoDto> all = this.baseMapper.getDownloadData();
//
//        long t1 = System.currentTimeMillis();
//        List<Map<String, Object>> list = new ArrayList<>();
//        for (ProjectInfoDto projectInfo : all) {
//            Map<String, Object> map = new LinkedHashMap<>();
//            map.put("酒店id", projectInfo.getProjectNo());
//            map.put("项目名称", projectInfo.getProjectName());
//            map.put("品牌", projectInfo.getBrandName());
//            map.put("产品名称", projectInfo.getProductName());
//            map.put("省份 ", projectInfo.getProvinceName());
//            map.put("城市", projectInfo.getCityName());
//            map.put("签约日期", DateUtil.changeDate(projectInfo.getEffectiveDate()));
//            map.put("营建状态", projectInfo.getProjectStatus());
//
//            map.put("施工单位项目项目经理", projectInfo.getConstructionManagerName());
//            map.put("施工单位", projectInfo.getConstructionManagerName());
//            map.put("客房装饰设计师", projectInfo.getDecorationDesignerName());
//            map.put("客房设计单位", projectInfo.getDesignUnitName());
//            map.put("公区装饰设计师", projectInfo.getAreaDesignUnit());
//            map.put("公区设计单位", projectInfo.getAreaDesigner());
//            map.put("客房机电设计师", projectInfo.getMechanicalAndElectricalDesignerName());
//            map.put("客房机电单位", projectInfo.getMechanicalAndElectricalDesignerUnit());
//            map.put("公区机电设计师", projectInfo.getElectricalDesignerName());
//            map.put("公区机电单位", projectInfo.getElectricalDesignerUnit());
//            map.put("开发战区负责人", projectInfo.getDevelopmentTheaterLeaderName());
//            map.put("营建区域负责人", projectInfo.getConstructionAreaLeaderName());
//            map.put("项目经理", projectInfo.getProjectManagerName());
//            map.put("设计师", projectInfo.getDesignerName());
//            map.put("弱电工程师", projectInfo.getWeakCurrentEngineerName());
//            map.put("机电工程师", projectInfo.getMechanicalAndElectricalEngineerName());
//            map.put("软装设计师", projectInfo.getSoftDesigner());
//            map.put("签约房量", projectInfo.getRoomsNumber());
//            map.put("设计节点", projectInfo.getDesignNodeTask());
//            map.put("工程节点", projectInfo.getEngineeringNodeTask());
//            map.put("预计开工时间", DateUtil.changeDate(projectInfo.getProjectPlanStart()));
//            map.put("实际开工时间", DateUtil.changeDate(projectInfo.getProjectActualStart()));
//            map.put("预计客房交底时间", DateUtil.changeDate(projectInfo.getProjectPlanEnd()));
//            map.put("实际客房交底时间", DateUtil.changeDate(projectInfo.getProjectActualEnd()));
//            map.put("预计公区交底时间", DateUtil.changeDate(projectInfo.getProjectPlanEnd()));
//            map.put("实际工区交底时间", DateUtil.changeDate(projectInfo.getProjectActualEnd()));
//            map.put("预计隐蔽验收时间", DateUtil.changeDate(projectInfo.getProjectPlanEnd()));
//            map.put("实际隐蔽验收时间", DateUtil.changeDate(projectInfo.getProjectActualEnd()));
//            map.put("预计开业时间", DateUtil.changeDate(projectInfo.getProjectPlanEnd()));
//            map.put("实际开业时间", DateUtil.changeDate(projectInfo.getProjectActualEnd()));
//            map.put("使用面积", projectInfo.getUsedArea());
//            map.put("装修面积", projectInfo.getDecorateArea());
//            map.put("项目类型", projectInfo.getProjectType());
//            map.put("项目状态", projectInfo.getProjectStatus());
//            map.put("当前任务阶段", AtourSystemEnum.AdditionalTemplatesEnum.getByKey(projectInfo.getTaskPhase()));
//            map.put("是否逾期", projectInfo.getOverdue());
//            map.put("结算任务阶段", projectInfo.getAccountPhase());
//            map.put("结算是否逾期", projectInfo.getAccountOverdue());
//            map.put("项目备注", projectInfo.getRemark());
//            map.put("是否生效", projectInfo.getIsActive());
//            map.put("是否可用", projectInfo.getIsEnabled());
//            map.put("楼层", projectInfo.getFloor());
//            map.put("设计定位", projectInfo.getDesignPosition());
//            list.add(map);
//            for (String k: map.keySet()){
//                map.putIfAbsent(k, "");
//            }
//        }
//        long t2 = System.currentTimeMillis();
//        log.info("耗时：{}", t2 - t1);
//        FileUtil.downloadExcelProject(list, request, response);
        response.setCharacterEncoding(request.getCharacterEncoding());
        response.setContentType("application/octet-stream");
        InputStream fis = null;
        File file = null;
        try {
            file = new File(new ClassPathResource("excel" + File.separator + "项目信息.xlsx").getPath());
            fis = new FileInputStream(file);
            response.setHeader("Content-Disposition", "attachment; filename=供应商模板.xlsx");
            IOUtils.copy(fis, response.getOutputStream());
            response.flushBuffer();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (fis != null) {
                try {
                    if (file != null) {
                        file.delete();
                    }
                    fis.close();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
    }

}
