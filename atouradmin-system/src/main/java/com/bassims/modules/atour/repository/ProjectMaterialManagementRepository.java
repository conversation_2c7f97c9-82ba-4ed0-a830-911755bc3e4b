/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bassims.modules.atour.domain.ProjectMaterialManagement;
import com.bassims.modules.atour.domain.ProjectRoom;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2023-12-04
 **/
@Repository
public interface ProjectMaterialManagementRepository extends BaseMapper<ProjectMaterialManagement> {

    Integer updateRoomNumberNot(@Param("projectId") Long projectId, @Param("nodeCode") String nodeCode, @Param("collects") List<String> collects);

    Integer updateRoomNumber(@Param("projectId") Long projectId, @Param("nodeCode") String nodeCode, @Param("modelRooms") List<ProjectRoom> modelRooms);


    void deleteByIds(List<Long> collects);
}