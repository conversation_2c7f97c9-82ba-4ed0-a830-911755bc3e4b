/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.bsEnum.AtourSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.domain.vo.AreaVo;
import com.bassims.modules.atour.domain.vo.SupplierManagerParam;
import com.bassims.modules.atour.domain.vo.SupplierManagerVo;
import com.bassims.modules.atour.repository.SupplierCaseRepository;
import com.bassims.modules.atour.repository.SupplierInfoRepository;
import com.bassims.modules.atour.service.*;
import com.bassims.modules.atour.service.dto.SupplierContactsQueryCriteria;
import com.bassims.modules.atour.service.dto.SupplierInfoDto;
import com.bassims.modules.atour.service.dto.SupplierInfoQueryCriteria;
import com.bassims.modules.atour.service.dto.SupplierPmDto;
import com.bassims.modules.atour.service.mapstruct.SupplierInfoMapper;
import com.bassims.modules.atourWithout.domain.AtosSubjectRequest;
import com.bassims.modules.atourWithout.domain.AtosUserRequest;
import com.bassims.modules.atourWithout.service.AtosSubjectInfoService;
import com.bassims.modules.system.domain.User;
import com.bassims.modules.system.repository.AreaRepository;
import com.bassims.modules.system.repository.RoleRepository;
import com.bassims.modules.system.service.AreaService;
import com.bassims.modules.system.service.DictDetailService;
import com.bassims.modules.system.service.UserService;
import com.bassims.modules.system.service.dto.DictDetailDto;
import com.bassims.modules.system.service.dto.UserDto;
import com.bassims.utils.*;
import com.github.pagehelper.PageInfo;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-09-15
 **/
@Service
public class SupplierInfoServiceImpl extends BaseServiceImpl<SupplierInfoRepository, SupplierInfo> implements SupplierInfoService {

    private static final Logger logger = LoggerFactory.getLogger(SupplierInfoServiceImpl.class);

    @Autowired
    private SupplierInfoRepository supplierInfoRepository;
    @Autowired
    private SupplierInfoMapper supplierInfoMapper;
    @Autowired
    private SupplierContactsService supplierContactsService;
    @Autowired
    private AreaRepository areaRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private AtosSubjectInfoService atosSubjectInfoService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private SupplierCaseRepository supplierCaseRepository;
    @Autowired
    private SupplierPmService supplierPmService;
    @Autowired
    private DictDetailService dictDetailService;
    @Autowired
    private ProjectInfoService projectInfoService;
    @Autowired
    private AreaService areaService;

    @Override
    public Map<String, Object> queryAll(SupplierInfoQueryCriteria criteria, Pageable pageable) {
        if (StringUtils.isEmpty(criteria.getStatus())) {
        criteria.setStatus(JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey());
    }
        getPage(pageable);
        QueryWrapper queryWrapper = QueryHelpPlus.getPredicate(SupplierInfo.class, criteria);
        queryWrapper.apply(StrUtil.isNotBlank(criteria.getSupplierType()), "FIND_IN_SET ('" + criteria.getSupplierType() + "',supplier_type)");
        PageInfo<SupplierInfo> page = new PageInfo<>(list(queryWrapper));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", supplierInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<SupplierInfoDto> queryAll(SupplierInfoQueryCriteria criteria) {
        QueryWrapper queryWrapper = QueryHelpPlus.getPredicate(SupplierInfo.class, criteria);
        queryWrapper.apply(StrUtil.isNotBlank(criteria.getSupplierType()), "FIND_IN_SET ('" + criteria.getSupplierType() + "',supplier_type)");
        List<SupplierInfo> supplierInfoList = list(queryWrapper);
        List<SupplierInfoDto> supplierInfoDtos = supplierInfoMapper.toDto(supplierInfoList);
        supplierInfoDtos.forEach(supplierInfoDto -> {
            // TODO
            SupplierContactsQueryCriteria supplierContactsQueryCriteria = new SupplierContactsQueryCriteria();
            supplierContactsQueryCriteria.setSupplierId(Long.valueOf(supplierInfoDto.getId()));
            supplierInfoDto.setSupplierContactsDtoList(supplierContactsService.queryAll(supplierContactsQueryCriteria));
        });
        return supplierInfoDtos;
    }

    @Override
    public List<SupplierInfoDto> queryAllNeedData(SupplierInfoQueryCriteria criteria) {
        QueryWrapper queryWrapper = QueryHelpPlus.getPredicate(SupplierInfo.class, criteria);
        queryWrapper.apply(StrUtil.isNotBlank(criteria.getSupplierType()), "FIND_IN_SET ('" + criteria.getSupplierType() + "',supplier_type)");
        List<SupplierInfo> supplierInfoList = list(queryWrapper);
        List<SupplierInfoDto> supplierInfoDtos = supplierInfoMapper.toDto(supplierInfoList);
        supplierInfoDtos.forEach(supplierInfoDto -> {
            // TODO
            SupplierContactsQueryCriteria supplierContactsQueryCriteria = new SupplierContactsQueryCriteria();
            supplierContactsQueryCriteria.setSupplierId(Long.valueOf(supplierInfoDto.getId()));
            supplierInfoDto.setSupplierContactsDtoList(supplierContactsService.queryAll(supplierContactsQueryCriteria));
        });
        return supplierInfoDtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierInfoDto findById(Long id) {
        Set<Long> areaCodeBySupId = areaRepository.getAreaCodeBySupId(id);
        SupplierInfo supplierInfo = Optional.ofNullable(getById(id)).orElseGet(SupplierInfo::new);
        Long updateUser = supplierInfo.getUpdateUser();
        Long createUser = supplierInfo.getCreateUser();
        supplierInfo.setServiceAreas(areaCodeBySupId);
        ValidationUtil.isNull(supplierInfo.getId(), getEntityClass().getSimpleName(), "id", id);
        SupplierInfoDto supplierInfoDto = supplierInfoMapper.toDto(supplierInfo);
        LambdaQueryWrapper<SupplierCase> queryWrapper = Wrappers.lambdaQuery(SupplierCase.class)
                .eq(SupplierCase::getSupplierId, id);
        List<SupplierCase> cases = supplierCaseRepository.selectList(queryWrapper);
        if (cases != null && cases.size() > 0) {
            supplierInfoDto.setCaseStudy(JSONArray.toJSONString(cases));
        }
        if (ObjectUtil.isNotEmpty(updateUser)) {
            UserDto byId = userService.findById(updateUser);
            String nickName = byId.getNickName();
            supplierInfoDto.setUpdateUserName(nickName);
        }
        if (ObjectUtil.isNotEmpty(createUser)) {
            UserDto byId = userService.findById(createUser);
            String nickName = byId.getNickName();
            supplierInfoDto.setCreateUserName(nickName);
        }
        return supplierInfoDto;
    }

    @Override
    public SupplierInfoDto saveSupplierInfo(SupplierInfo resources) {
        return this.create(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierInfoDto create(SupplierInfo resources) {
//        resources.setSupplierNum(Long.parseLong(IdUtil.randomUUID()));

        //系统增加重复校验，根据供应商「税号」判断唯一
//        final List<SupplierInfo> supplierInfos = this.list();
//        final List<String> collect = supplierInfos.stream().map(SupplierInfo::getTaxNumber).collect(Collectors.toList());

        LambdaQueryWrapper<SupplierInfo> queryWrapper = Wrappers.lambdaQuery(SupplierInfo.class);
        queryWrapper.eq(SupplierInfo::getTaxNumber, resources.getTaxNumber());
        queryWrapper.eq(SupplierInfo::getIsDelete, 0);
        List<SupplierInfo> infos = supplierInfoRepository.selectList(queryWrapper);
        if (ObjectUtil.isNotEmpty(infos)) {
            List<String> collect = infos.stream().map(SupplierInfo::getSupNameCn).collect(Collectors.toList());
            throw new BadRequestException("录入失败！该税号已存在于" + collect + "公司，请重新录入！");
        }

        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setId(snowflake.nextId());
        Map<String, Object> paramMap = new HashMap<>();
//        paramMap.put("supplier_num", resources.getSupplierNum());
//        List<SupplierInfo> supplierInfos = supplierInfoRepository.selectByMap(paramMap);
//        if (ObjectUtils.isNotEmpty(supplierInfos) && supplierInfos.size() > 0) {
//            throw new BadRequestException("厂商编号重复！！！");
//        }
        Integer suppernum = supplierInfoRepository.getMaxNumFromSupplierInfo();
        if (suppernum == null || suppernum < 100) {
            suppernum = 100;
        }
        resources.setSupplierNum("ATVD" + (suppernum < 1000 ? "0" : "") + (suppernum + 1));
        resources.setSupplierCode(resources.getSupplierNum());
        Long currentUserId = SecurityUtils.getCurrentUserId();
        //插入厂商联系人表
        SupplierContacts supplierContacts = new SupplierContacts();
        supplierContacts.setSupplierId(resources.getId());
        supplierContacts.setContact(resources.getContact());
        supplierContacts.setPhone(resources.getPhone());
        supplierContacts.setFax(resources.getFax());
        supplierContacts.setEmail(resources.getEmail());
        supplierContacts.setIsDelete(0);
        supplierContactsService.create(supplierContacts);
        Set<Long> serviceAreas = resources.getServiceAreas();
        resources.setCreateUser(currentUserId);
        resources.setIsDelete(0);
        resources.setSupStatus(AtourSystemEnum.SupStatusEnum.COOPERATION.getKey());
        String current = cn.hutool.core.date.DateUtil.format(new Date(), "yyyyMMdd");
        String key = "agent" + current;
        Integer value = (Integer) redisUtils.get(key);

        String num = "";
        String prefix = "agent";
        if (null == value) {
            redisUtils.set(key, 1, 86400, TimeUnit.SECONDS);
            num = "01";
        } else {
            Long increment = redisUtils.increment(key);
            StringBuilder s = new StringBuilder(increment.toString());
            for (int i = s.length(); i < 2; i++) {
                s.insert(0, "0");
            }
            num = s.toString();
        }


        String code = prefix + current + num;
        resources.setTkId(code);
        if ("non_platform".equals(resources.getPlatformProviderNot())) {
            resources.setStatus(JhSystemEnum.approveResultEnum.PENDING_APPROVAL.getKey());
        }
        save(resources);

        //插入省份和城市
        if (ObjectUtils.isNotEmpty(serviceAreas) && serviceAreas.size() > 0) {
            supplierInfoRepository.insertSupplierCity(serviceAreas, resources.getId());
        }
        if (StringUtils.isNotEmpty(resources.getCaseStudy()) && resources.getCaseStudy().length() > 5) {
            List<HashMap> list = JSONArray.parseArray(resources.getCaseStudy(), HashMap.class);
            for (HashMap map : list) {
                SupplierCase mycase = new SupplierCase();
                mycase.setSupplierId(resources.getId());
                mycase.setRemark(map.get("remark") == null ? null : map.get("remark").toString());
                mycase.setImg(map.get("img") == null ? null : map.get("img").toString());
                mycase.setFiles(map.get("files") == null ? null : map.get("files").toString());
                supplierCaseRepository.insert(mycase);
            }
        }

        if (resources.getPmList() != null) {
            resources.getPmList().forEach(r->{
                r.setSupplierId(resources.getId());
                if ("non_platform".equals(resources.getPlatformProviderNot())) {
                    r.setStatus(JhSystemEnum.approveResultEnum.PENDING_APPROVAL.getKey());
                }
                supplierPmService.createPm(r, resources.getId());
            });
        }
        //新增审批
        if ("non_platform".equals(resources.getPlatformProviderNot())) {
            //非合作单位
            projectInfoService.createSupplierProject(JhSystemEnum.SupplierTypeEnum.SUPPLIER, resources.getId());
            this.saveSupplierPm(resources, true);
        } else {
            //供应商联系人创建用户和供应商项目经理
            this.saveSupplierPm(resources, false);
            ExecutorService executor = Executors.newFixedThreadPool(2);
            executor.submit(() -> {
                try {
                    atosSubjectInfoService.pullSubjectInfoForAtos(resources);
                } catch (Exception ignored) {
                }
            });
            executor.shutdown();
        }
        return findById(resources.getId());
    }

    private void saveSupplierPm(SupplierInfo resources, Boolean isCreate) {
        //联系人创建用户和供应商项目经理
        SupplierPmDto dto = new SupplierPmDto();
        dto.setPmName(resources.getContact());
        dto.setSupplierId(resources.getId());
        dto.setPhone(resources.getPhone());
        dto.setEmail(resources.getEmail());
        dto.setFisyear(resources.getReferrer());
        dto.setIsPullUserInfoForAtos("1");
        if (isCreate){
            dto.setStatus(JhSystemEnum.approveResultEnum.PENDING_APPROVAL.getKey());
        }
        supplierPmService.createPm(dto, resources.getId());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SupplierInfo resources) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        Set<Long> supAreaCodes = areaRepository.getAreaCodeBySupId(resources.getId());
        if (ObjectUtils.isNotEmpty(resources.getServiceAreas())) {
            if (resources.getServiceAreas().size() != supAreaCodes.size()) {
                areaRepository.deleteSupplierCitysBySupplierId(resources.getId());
                supplierInfoRepository.insertSupplierCity(resources.getServiceAreas(), resources.getId());
            }
        }
        SupplierInfo supplierInfo = Optional.ofNullable(getById(resources.getId())).orElseGet(SupplierInfo::new);
        supplierInfo.setUpdateUser(currentUserId);
        ValidationUtil.isNull(supplierInfo.getId(), "SupplierInfo", "id", resources.getId());
        supplierInfo.copy(resources);
        if (!resources.getPhone().equals(supplierInfo.getPhone())) {
            //判断联系电话(公司法人) 是否存在变化，如果有变动，供应商联系人创建用户和供应商项目经理
            this.saveSupplierPm(resources, false);
        }
        updateById(supplierInfo);
        if (StringUtils.isNotEmpty(resources.getCaseStudy()) && resources.getCaseStudy().length() > 5) {
            LambdaQueryWrapper<SupplierCase> queryWrapper = Wrappers.lambdaQuery(SupplierCase.class)
                    .eq(SupplierCase::getSupplierId, resources.getId());
            supplierCaseRepository.delete(queryWrapper);
            List<HashMap> list = JSONArray.parseArray(resources.getCaseStudy(), HashMap.class);
            for (HashMap map : list) {
                SupplierCase mycase = new SupplierCase();
                mycase.setSupplierId(resources.getId());
                mycase.setRemark(ObjectUtil.isNotEmpty(map.get("remark")) ? map.get("remark").toString() : null);
                mycase.setImg(ObjectUtil.isNotEmpty(map.get("img")) ? map.get("img").toString() : null);
                mycase.setFiles(ObjectUtil.isNotEmpty(map.get("files")) ? map.get("files").toString() : null);
                supplierCaseRepository.insert(mycase);
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(SupplierInfo resources) {
        Long currentUserId = SecurityUtils.getCurrentUserId();

        SupplierInfo supplierInfo = Optional.ofNullable(getById(resources.getId())).orElseGet(SupplierInfo::new);
        supplierInfo.setUpdateUser(currentUserId);
        ValidationUtil.isNull(supplierInfo.getId(), "SupplierInfo", "id", resources.getId());

        if (!resources.getSupStatus().equals(supplierInfo.getSupStatus())) {
            //当前供应商状态发生变更，推送到atos系统
            atosSubjectInfoService.pullUpdateStatusSubjectInfoForAtos(resources);
            if (resources.getSupStatus().equals(AtourSystemEnum.SupStatusEnum.NONCOOPERATION.getKey())) {
                //查询状态正常的供应商人员，改为禁用状态
                LambdaQueryWrapper<SupplierPm> queryWrapper = Wrappers.lambdaQuery(SupplierPm.class);
                queryWrapper.eq(SupplierPm::getSupplierId, resources.getId())
                        .eq(SupplierPm::getPmStatus, AtourSystemEnum.SupStatusEnum.COOPERATION.getKey());
                List<SupplierPm> supplierPms = supplierPmService.list(queryWrapper);
                supplierPms.forEach(supplierPm -> {
                    supplierPm.setPmStatus(AtourSystemEnum.SupStatusEnum.NONCOOPERATION.getKey());
                });
                supplierPmService.updateSupplierPmStatus(supplierPms);
            }
        }
        supplierInfo.copy(resources);
        updateById(supplierInfo);
    }

    @Override
    public void deleteStatus(SupplierInfo resources) {
        Long currentUserId = SecurityUtils.getCurrentUserId();

        SupplierInfo supplierInfo = Optional.ofNullable(getById(resources.getId())).orElseGet(SupplierInfo::new);
        supplierInfo.setUpdateUser(currentUserId);
        ValidationUtil.isNull(supplierInfo.getId(), "SupplierInfo", "id", resources.getId());


        //查询未被删除的供应商人员，改为删除
        LambdaQueryWrapper<SupplierPm> queryWrapper = Wrappers.lambdaQuery(SupplierPm.class);
        queryWrapper.eq(SupplierPm::getSupplierId, resources.getId())
                .eq(SupplierPm::getIsDelete, Boolean.TRUE);
        List<SupplierPm> supplierPms = supplierPmService.list(queryWrapper);
        supplierPms.forEach(supplierPm -> {
            supplierPm.setIsDelete(1);
        });
        supplierPmService.deleteSupplierPmStatus(supplierPms);

        resources.setIsDelete(1);
        supplierInfo.copy(resources);
        updateById(supplierInfo);
        //当前供应商状态发生变更，推送到atos系统
        atosSubjectInfoService.pullDeleteSubjectInfoForAtos(resources);

    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            supplierInfoRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<SupplierInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SupplierInfoDto supplierInfo : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("厂商NO", supplierInfo.getSupplierNum());
            map.put(" supplierVersionId", supplierInfo.getSupplierVersionId());
            map.put("厂商编号", supplierInfo.getSupplierCode());
            map.put("厂商中文名称", supplierInfo.getSupNameCn());
            map.put("厂商英文名称", supplierInfo.getSupNameEn());
            map.put("厂商简称", supplierInfo.getSupShortName());
            map.put("省份", supplierInfo.getSupProvince());
            map.put("城市", supplierInfo.getSupCity());
            map.put(" supPostCode", supplierInfo.getSupPostCode());
            map.put("厂商注册日期", supplierInfo.getCompRegDate());
            map.put("厂商注册地址", supplierInfo.getCompRegAddr());
            map.put(" effectStatus", supplierInfo.getEffectStatus());
            map.put(" versionStatus", supplierInfo.getVersionStatus());
            map.put("厂商状态", supplierInfo.getSupStatus());
            map.put("是否一次性厂商", supplierInfo.getIsDisposable());
            map.put("联系人(公司法人)", supplierInfo.getContact());
            map.put("联系电话(公司法人)", supplierInfo.getPhone());
            map.put("邮箱(公司法人)", supplierInfo.getEmail());
            map.put("传真(公司法人)", supplierInfo.getFax());
            map.put(" bizlicExpDate", supplierInfo.getBizlicExpDate());
            map.put(" sppExpDate", supplierInfo.getSppExpDate());
            map.put("税务登记属性", supplierInfo.getTaxRegAttr());
            map.put(" taxSaleRate", supplierInfo.getTaxSaleRate());
            map.put("税率(服务)", supplierInfo.getTaxServiceRate());
            map.put(" trcExpDate", supplierInfo.getTrcExpDate());
            map.put(" logisticsTerms", supplierInfo.getLogisticsTerms());
            map.put(" parentId", supplierInfo.getParentId());
            map.put("项目id", supplierInfo.getProjectId());
            map.put("是否激活", supplierInfo.getIsActive());
            map.put("创建人", supplierInfo.getCreateUser());
            map.put("创建时间", supplierInfo.getCreateTime());
            map.put("更新人", supplierInfo.getUpdateUser());
            map.put("更新时间", supplierInfo.getUpdateTime());
            map.put("是否删除", supplierInfo.getIsDelete());
            map.put("营业执照有效期到期日", supplierInfo.getBusinessExpDate());
            map.put("服务开始时间", supplierInfo.getServiceStartDate());
            map.put("服务范围", supplierInfo.getServiceScope());
            map.put("服务区域", supplierInfo.getServiceAreas());
            map.put("评级", supplierInfo.getSupplierStarbucksRating());
            map.put("平均质量评分", supplierInfo.getAvgQualityScore());
            map.put("开户名字(中文)", supplierInfo.getBankAccountName());
            map.put("银行名字(中文，银行全称)", supplierInfo.getBankNameCn());
            map.put("银行名字(英文)", supplierInfo.getBankNameEn());
            map.put("银行支行名字(中文)", supplierInfo.getBankBranchCn());
            map.put("银行支行城市(中文)", supplierInfo.getBankBranchCityCn());
            map.put("账户号", supplierInfo.getBankAccountNumber());
            map.put("税号", supplierInfo.getTaxNumber());
            map.put("大盘类型", supplierInfo.getMarketType());
            map.put("推荐人", supplierInfo.getReferrer());
            map.put("联系方式", supplierInfo.getContactWay());
            map.put("已缴纳质保金(元)", supplierInfo.getPayRetentionMoney());
            map.put("已退还质保金(元)", supplierInfo.getRefundWarrantyDeposit());
            map.put("是否甲供材单位", supplierInfo.getIsFirstMaterialSupplier());
            map.put("是否需要测试", supplierInfo.getIsTest());
            map.put("厂商所属法人公司", supplierInfo.getLegalEntity());
            map.put("联系人(项目召集人)", supplierInfo.getProjectConvenor());
            map.put("联系电话(项目召集人)", supplierInfo.getProjectConvenorPhone());
            map.put("邮箱(项目召集人)", supplierInfo.getProjectConvenorEmail());
            map.put("施工资质(装修)", supplierInfo.getDecorationQualification());
            map.put("证照有效期到期日(装修)", supplierInfo.getDecorationExpDate());
            map.put("施工资质(机电)", supplierInfo.getElectromechanicalQualification());
            map.put("证照有效期到期日(机电)", supplierInfo.getElectromechanicalExpDate());
            map.put("安全生产许可证有效期到期日", supplierInfo.getSplExpDate());
            map.put("税务登记证有效期到期日", supplierInfo.getTralExpDate());
            map.put("厂商工程范围", supplierInfo.getServiceContent());
            map.put("批准项目经理数", supplierInfo.getProjectManagerAmount());
            map.put("是否使用", supplierInfo.getIsUsed());
            map.put("传真(项目召集人)", supplierInfo.getProjectConvenorFax());
            map.put("其他施工资质证照有效期到期日", supplierInfo.getOtherLicenceExpDate());
            map.put("变更说明", supplierInfo.getChangeDesc());
            map.put("厂商邮编", supplierInfo.getPostalCode());
            map.put("付款条件", supplierInfo.getPayTerm());
            map.put("厂商地址", supplierInfo.getSupplierAddress());
            map.put("atos主键id", supplierInfo.getTkId());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    private String getdict(List<DictDetailDto> dicts, String key) {
        StringBuilder result = new StringBuilder();
        if (StringUtils.isEmpty(key)) {
            return "";
        }
        dicts.forEach(d -> {
            for (String s : key.split(",")) {
                if (s.equals(d.getValue())) {
                    result.append(d.getLabel()).append(" ");
                }
            }
        });
        return result.toString();
    }

    private String getdictValue(List<DictDetailDto> dicts, String key) {
        StringBuilder builder = new StringBuilder();
        if (StringUtils.isEmpty(key)) {
            return "";
        }
        dicts.forEach(d -> {
            for (String s : key.split(",")) {
                if (s.equals(d.getLabel())) {
                    builder.append(d.getValue()).append(",");
                }
            }
        });
        String result = builder.toString();
        if (result.endsWith(",")) {
            return result.substring(0, result.length()-1);
        } else {
            return result;
        }
    }

    @Override
    public List<SupplierInfoDto> supplierByName(String supplierName) {
        LambdaQueryWrapper<SupplierInfo> queryWrapper = Wrappers.lambdaQuery(SupplierInfo.class)
                .eq(SupplierInfo::getSupNameCn, supplierName)
//                .eq(SupplierInfo::getSupStatus,"1")
                .eq(SupplierInfo::getIsDelete, false);
        List<SupplierInfo> supplierInfos = supplierInfoRepository.selectList(queryWrapper);
        return supplierInfoMapper.toDto(supplierInfos);
    }

    @Override
    public List<SupplierManagerVo> supplierManagerRelate() {
        return supplierInfoRepository.supplierManagerRelate();
    }

    @Override
    public List<SupplierInfoDto> getSupplierInfo() {
        LambdaQueryWrapper supInfoQuery = Wrappers.lambdaQuery(SupplierInfo.class)
                .eq(SupplierInfo::getIsDelete, 0);
        List<SupplierInfo> list = supplierInfoRepository.selectList(supInfoQuery);
        return supplierInfoMapper.toDto(list);
    }

    @Override
    public void relateSave(SupplierManagerParam supplierManagerParam) {
        SupplierInfo supplierInfo = Optional.ofNullable(supplierInfoRepository.selectById(Long.valueOf(supplierManagerParam.getSupplierId()))).orElseGet(SupplierInfo::new);
//        String userIds = supplierInfo.getUserId();
//        String userIdStr = String.valueOf(supplierManagerParam.getUserId());
//        if (StringUtils.isNotEmpty(userIds)){
//            if (userIds.contains(userIdStr)){
//                return ;
//            }
//            supplierInfo.setUserId(userIds + "," + userIdStr);
//        }else{
//            supplierInfo.setUserId(userIdStr);
//        }
        String userId = String.valueOf(supplierManagerParam.getUserId());
        String userIdOrigin = supplierInfo.getUserId();
        if (ObjectUtils.isNotEmpty(userIdOrigin) && userIdOrigin.equals(userId)) {
            throw new BadRequestException("你已关联该厂商！！！");
        } else if (ObjectUtils.isNotEmpty(userIdOrigin)) {
            throw new BadRequestException("该厂商已有管理员关联！！！");
        } else if (ObjectUtils.isEmpty(userIdOrigin)) {
            LambdaQueryWrapper userIdQuery = Wrappers.lambdaQuery(SupplierInfo.class)
                    .eq(SupplierInfo::getUserId, userId);
            List<SupplierInfo> supplierInfos = supplierInfoRepository.selectList(userIdQuery);
            if (ObjectUtils.isEmpty(supplierInfos)) {
                supplierInfo.setUserId(userId);
                supplierInfoRepository.updateById(supplierInfo);
            } else if (ObjectUtils.isNotEmpty(supplierInfos) && supplierInfos.size() > 0) {
                for (SupplierInfo info : supplierInfos) {
                    LambdaUpdateWrapper update = Wrappers.lambdaUpdate(SupplierInfo.class)
                            .eq(SupplierInfo::getId, info.getId())
                            .set(SupplierInfo::getUserId, null);
                    update(update);
                }
                supplierInfo.setUserId(userId);
                supplierInfoRepository.updateById(supplierInfo);
            }
        }
    }

    @Override
    public List<SupplierInfoDto> supplierFirstByName(String supplierName) {
        LambdaQueryWrapper<SupplierInfo> queryWrapper = Wrappers.lambdaQuery(SupplierInfo.class)
                .like(SupplierInfo::getSupNameCn, supplierName)
                .eq(SupplierInfo::getIsFirstMaterialSupplier, true)
//                .eq(SupplierInfo::getSupStatus,"1")
                .eq(SupplierInfo::getIsDelete, false);
        List<SupplierInfo> supplierInfos = supplierInfoRepository.selectList(queryWrapper);
        return supplierInfoMapper.toDto(supplierInfos);
    }

    @Override
    public SupplierInfoDto supplierInfoByName(String supplierName) {
        LambdaQueryWrapper<SupplierInfo> queryWrapper = Wrappers.lambdaQuery(SupplierInfo.class)
                .eq(SupplierInfo::getSupNameCn, supplierName)
//                .eq(SupplierInfo::getSupStatus,"1")
                .eq(SupplierInfo::getIsDelete, false);
        List<SupplierInfo> supplierInfos = supplierInfoRepository.selectList(queryWrapper);
        SupplierInfo supplierInfo = supplierInfos.get(0);
        return supplierInfoMapper.toDto(supplierInfo);
    }

    @Override
    public List<SupplierInfoDto> getSupplierList(SupplierInfoQueryCriteria criteria) {
        //根据区域和供应商类型查询供应商下拉数据
        if (criteria.getServiceScope() != null) {
            switch (criteria.getServiceScope()) {
                case "air":
                    criteria.setSupplierType(null);
                    criteria.setServiceScope("air");
                    break;
                case "weak_current":
                    criteria.setSupplierType(null);
                    criteria.setServiceScope("weak_current");
                    break;
                case "fire":
                    criteria.setSupplierType(null);
                    criteria.setServiceScope("fire");
                    break;
                case "renovation":
                    criteria.setSupplierType(null);
                    criteria.setServiceScope("renovation");
                    break;
                default:
                    criteria.setSupplierType("wzdw-xmjl");
                    criteria.setServiceScope(null);
            }
        }
        if (ObjectUtil.isNotEmpty(criteria.getSupplierType())) {
            criteria.setServiceScope(null);
            if (criteria.getSupplierType().equals("sjs")) {
                //施工单位的数据查询公司类型为【总包单位】【装修单位】
//               criteria.setSupplierType1("wldw-xmjl");
                criteria.setSupplierType(criteria.getSupplierType() + ",wldw-xmjl");
            }
        }
        //任务中【施工单位】去掉城市和品牌的限制
        criteria.setCity(null);
        criteria.setProductCode(null);
        return supplierInfoRepository.getSupplierList(criteria);
    }

    @Override
    public void saveOrUpdateFromAtos(AtosSubjectRequest atosSubjectRequest) {
        //查询主体是否存在
        String tk = atosSubjectRequest.getTk();
        String tk_name = atosSubjectRequest.getTk_name();
        AtosUserRequest adminInfo = atosSubjectRequest.getAdminInfo();
        LambdaQueryWrapper<SupplierInfo> queryWrapper = Wrappers.lambdaQuery(SupplierInfo.class)
                .eq(SupplierInfo::getTkId, tk).or().eq(SupplierInfo::getSupNameCn, tk_name);
        SupplierInfo supplierInfo = supplierInfoRepository.selectOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(supplierInfo)) {
            //更新供应商
            supplierInfo.setSupNameCn(tk_name);
            supplierInfoRepository.updateById(supplierInfo);
        } else {
            //新增供应商
            Snowflake snowflake = IdUtil.getSnowflake(1, 1);
            long supplier = snowflake.nextId();
            Snowflake adminInfoId = IdUtil.getSnowflake(1, 1);
            Long adminId = adminInfoId.nextId();

            supplierInfo.setSupNameCn(tk_name);
            supplierInfo.setTkId(tk);
            supplierInfo.setSupplierVersionId(supplier);
            supplierInfo.setIsDelete(0);
            //新增admin用户
            User user = new User();
            user.setId(adminId);
            user.setUsername(adminInfo.getMobile());
            user.setPhone(adminInfo.getMobile());
            user.setEmail(adminInfo.getEmail());
            user.setOpenId(adminInfo.getUk());
            user.setPassword(passwordEncoder.encode("yaduo201203.acms"));

            userService.create(user);
            supplierInfoRepository.insert(supplierInfo);

        }
    }

    @Override
    public void deleteFromAtos(AtosSubjectRequest atosSubjectRequest) {
        //查询主体是否存在
        String tk = atosSubjectRequest.getTk();
        String tk_name = atosSubjectRequest.getTk_name();
        AtosUserRequest adminInfo = atosSubjectRequest.getAdminInfo();
        LambdaQueryWrapper<SupplierInfo> queryWrapper = Wrappers.lambdaQuery(SupplierInfo.class)
                .eq(SupplierInfo::getTkId, tk).or().eq(SupplierInfo::getSupNameCn, tk_name);
        SupplierInfo supplierInfo = supplierInfoRepository.selectOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(supplierInfo)) {
            //更新供应商
            supplierInfo.setIsDelete(1);
            supplierInfoRepository.updateById(supplierInfo);
        }
    }

    @Override
    public void downloadNew(List<SupplierInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        List<DictDetailDto> supplierType = dictDetailService.getDictByName("supplier_type");
        List<DictDetailDto> platformProviderNot = dictDetailService.getDictByName("platform_provider_not");
        List<DictDetailDto> referrer = dictDetailService.getDictByName("execution_standards");
        for (SupplierInfoDto supplierInfo : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("供应商名称", supplierInfo.getSupNameCn());
            map.put("公司类型", getdict(supplierType, supplierInfo.getSupplierType()));
            map.put("公司地址", supplierInfo.getCompRegAddr());
            map.put("合作情况", getdict(platformProviderNot, supplierInfo.getPlatformProviderNot()));
            map.put("负责人", supplierInfo.getContact());
            map.put("联系电话", supplierInfo.getPhone());
            map.put("联系邮箱", supplierInfo.getEmail());
            map.put("公司等级", supplierInfo.getSupPostCode());
            map.put("承接品牌", getdict(referrer, supplierInfo.getReferrer()));
            map.put("安心筹开单位", "true".equals(supplierInfo.getSupShortName()) ? "是" : "否");
            map.put("营业执照类型", "long".equals(supplierInfo.getBusinessExpDate()) ? "长期" : "短期");
//            map.put("营业执照时间", supplierInfo.getBusinessExpDate());
            map.put("安全施工许可证有效期", supplierInfo.getDecorationExpDate());
            map.put("税号", supplierInfo.getTaxNumber());
            map.put("开户银行", supplierInfo.getBankNameCn());
            map.put("银行账户", supplierInfo.getBankAccountNumber());
            map.put("电话", supplierInfo.getBankNameEn());
            map.put("单位地址", supplierInfo.getBankBranchCityCn());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setCharacterEncoding(request.getCharacterEncoding());
        response.setContentType("application/octet-stream");
        InputStream fis = null;
        try {
            fis = new ClassPathResource("excel"+File.separator+"供应商模板.xlsx").getInputStream();
            response.setHeader("Content-Disposition", "attachment; filename=供应商模板.xlsx");
            IOUtils.copy(fis, response.getOutputStream());
            response.flushBuffer();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
    }

    @Override
    public Long getByName(String name) {
        return this.count(Wrappers.lambdaQuery(SupplierInfo.class).eq(SupplierInfo::getSupNameCn, name));
    }

    @Override
    public List<SupplierCase> caseList(Long supplierId) {
        return this.baseMapper.getSupplierCaseList(supplierId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean supplierImport(MultipartFile file) throws IOException {
        List<DictDetailDto> supplierType = dictDetailService.getDictByName("supplier_type");
        List<DictDetailDto> platformProviderNot = dictDetailService.getDictByName("platform_provider_not");
        List<DictDetailDto> referrer = dictDetailService.getDictByName("execution_standards");
        List<DictDetailDto> serviceScope = dictDetailService.getDictByName("service_scope");
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream(), 0);
        Map<String, String> aliasMap = new HashMap<>();
        aliasMap.put("*供应商名称", "supNameCn");
        aliasMap.put("*公司类型", "supplierType");
        aliasMap.put("*公司地址", "compRegAddr");
        aliasMap.put("*合作情况", "platformProviderNot");
        aliasMap.put("*负责人", "contact");
        aliasMap.put("*联系电话", "phone");
        aliasMap.put("*联系邮箱", "email");
        aliasMap.put("*公司等级", "supPostCode");
        aliasMap.put("*承接品牌", "referrer");
        aliasMap.put("安心筹开单位", "supShortName");
        aliasMap.put("*营业执照类型", "businessExpDate");
//        aliasMap.put("营业执照时间","businessExpDate");
        aliasMap.put("安全施工许可证有效期", "decorationExpDate");
        aliasMap.put("*税号", "taxNumber");
        aliasMap.put("开户银行", "bankNameCn");
        aliasMap.put("银行账户", "bankAccountNumber");
        aliasMap.put("电话", "bankNameEn");
        aliasMap.put("单位地址", "bankBranchCityCn");
        aliasMap.put("备注", "remark");
        aliasMap.put("公司简介", "marketType");
//        aliasMap.put("营业期限", "businessExpDate");
        aliasMap.put("负责专业", "serviceScope");
        reader.setHeaderAlias(aliasMap);
        List<SupplierInfo> readMto = reader.read(1, 3, SupplierInfo.class);
        Set<Long> serviceAreas = new HashSet<>();
        List<AreaVo> areaVos = areaService.getAllProvinceAndCity();
        areaVos.forEach(vo->{
            if (!vo.getAreas().isEmpty()) {
                vo.getAreas().forEach(v->{
                    serviceAreas.add(v.getAreaCode());
                });
            }
        });
        for (SupplierInfo ob : readMto) {
            long c = this.count(Wrappers.lambdaQuery(SupplierInfo.class).eq(SupplierInfo::getSupNameCn, ob.getSupNameCn()));
            if (c > 0) {
                throw new BadRequestException("录入失败！供应商【" + ob.getSupNameCn() + "】已存在，请重新录入！");
            }
            ob.setSupplierType(getdictValue(supplierType, ob.getSupplierType().trim()));
            ob.setPlatformProviderNot(getdictValue(platformProviderNot, ob.getPlatformProviderNot()).trim());
            ob.setReferrer(getdictValue(referrer, ob.getReferrer().trim()));
            ob.setServiceScope(getdictValue(serviceScope, ob.getServiceScope().trim()));
            ob.setSupShortName("是".equals(ob.getSupShortName().trim()) ? "true" : "false");
            ob.setBusinessExpDate("长期".equals(ob.getBusinessExpDate().trim()) ? "long" : "short");
            ob.setSupStatus("cooperation");
//            ob.setServiceAreas();
            this.save(ob);
            supplierInfoRepository.insertSupplierCity(serviceAreas, ob.getId());
        }
        return null;
    }
}