/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-12-16
**/
@Data
public class ProjectSpecialNeedsFranchisorsDto implements Serializable {

    /** 主键id */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long franchisorsId;

    /** 项目id */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    /** 问题描述 */
    private String problemDescription;

    /** 是否需审批(0不需要、1需要) */
    private String whetherApprovalRequired;

    /** 处理方案建议 */
    private String treatmentProposal;

    /** 约定完成时间 */
    private Timestamp agreedCompletionTime;

    /** 图片 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private String picture;

    /** 是否可用 */
    private Boolean isEnabled;

    /** 是否删除 */
    private Boolean isDelete;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新时间 */
    private Timestamp updateTime;

    private String createBy;

    private String updateBy;
}