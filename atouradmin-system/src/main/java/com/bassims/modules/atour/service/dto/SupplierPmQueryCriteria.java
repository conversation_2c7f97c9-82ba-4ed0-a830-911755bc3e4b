/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bassims.annotation.QueryPlus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-09-15
 **/
@Data
public class SupplierPmQueryCriteria {

    /**
     * 厂商Id
     */
    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "supplierId")
    private Long supplierId;

    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "isDelete")
    private Integer isDelete = 0;

    /**
     * 供应商人员角色(码值)
     */
    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "supplierPersonnelRole")
    private String supplierPersonnelRole;



    @QueryPlus(propName = "supNameCn")
    private String supNameCn;

    @QueryPlus(type = QueryPlus.Type.INNER_LIKE, propName = "pmName")
    private String pmName;


    @QueryPlus(type = QueryPlus.Type.EQUAL, propName = "finishAmount")
    private String finishAmount;

    /*财年(承接品牌)*/
    @QueryPlus(type = QueryPlus.Type.INNER_LIKE)
    private String fisyear;
    /*最新的前端用的(承接品牌)*/
    @QueryPlus(type = QueryPlus.Type.INNER_LIKE)
    private String productCode;

    /*联系电话*/
    @QueryPlus(type = QueryPlus.Type.INNER_LIKE)
    private String phone;
    @QueryPlus(type = QueryPlus.Type.EQUAL)
    private String status;
}