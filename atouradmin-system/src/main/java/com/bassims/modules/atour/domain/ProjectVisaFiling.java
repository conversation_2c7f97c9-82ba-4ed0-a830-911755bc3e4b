/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.io.Serializable;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-04-23
**/
@Data
@TableName(value="t_project_visa_filing")
public class ProjectVisaFiling implements Serializable {

    @TableId(value = "id")
    @ApiModelProperty(value = "主键id")
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id")
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    @TableField(value = "reporting_category")
    @ApiModelProperty(value = "报备大类")
    private String reportingCategory;

    @TableField(value = "report_content")
    @ApiModelProperty(value = "报备内容")
    private String reportContent;

    @TableField(value = "node_code")
    @ApiModelProperty(value = "签证报备二级code")
    private String nodeCode;

    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_by",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新人")
    private Timestamp updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;



    @TableField(value = "presentation_ondition")
    @ApiModelProperty(value = "情况说明")
    private String presentationOndition;

    @TableField(value = "attachment")
    @ApiModelProperty(value = "附件")
    private String attachment;

    @TableField(exist = false)
    @ApiModelProperty(value = "模板code")
    private String templateCode;

    @TableField(exist = false)
    @ApiModelProperty(value = "角色code")
    private String roleCode;
    
    @TableField(exist = false)
    @ApiModelProperty(value = "用户角色")
    private  Map<String, String> stringStringMap;

    public void copy(ProjectVisaFiling source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}