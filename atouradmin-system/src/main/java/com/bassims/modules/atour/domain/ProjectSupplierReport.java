/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.math.BigDecimal;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-03-16
**/
@Data
@TableName(value="project_supplier_report")
public class ProjectSupplierReport implements Serializable {

    @ApiModelProperty(value = "年度")
    private String supYear;

    @ApiModelProperty(value = "供应商")
    private String supName;

//    @ApiModelProperty(value = "供应商")
//    @TableField(exist = false)
//    private String supName;

    @ApiModelProperty(value = "新店数量")
    private Integer newNum;

    @ApiModelProperty(value = "新店投入")
    private BigDecimal decorateTotalNew;

    @ApiModelProperty(value = "改造数量")
    private Integer reformNum;

    @ApiModelProperty(value = "改造投入")
    private BigDecimal decorateTotalReform;

    @ApiModelProperty(value = "总投入万元")
    @TableField(exist = false)
    private Integer total;

    @ApiModelProperty(value = "占比")
    @TableField(exist = false)
    private BigDecimal proportion;

    public void copy(ProjectSupplierReport source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}