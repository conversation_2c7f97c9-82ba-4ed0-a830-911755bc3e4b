/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.math.BigDecimal;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-09-14
**/
@Data
@TableName(value="t_supplier_materiel_info")
public class SupplierMaterielInfo implements Serializable {

    @TableId(value = "id")
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "sup_contract_id")
    @ApiModelProperty(value = "supContractId")
    private Long supContractId;

    @TableField(value = "supplier_id")
    @ApiModelProperty(value = "supplierId")
    private Long supplierId;

    @TableField(value = "materiel_id")
    @ApiModelProperty(value = "materielId")
    private Long materielId;

    @TableField(value = "materiel_name")
    @ApiModelProperty(value = "物料名称")
    private String materielName;

    @TableField(value = "materiel_code")
    @ApiModelProperty(value = "编号")
    private String materielCode;

    @TableField(value = "finance_code")
    @ApiModelProperty(value = "资产编号")
    private String financeCode;

    @TableField(value = "product_name")
    @ApiModelProperty(value = "产品名称")
    private String productName;

    @TableField(value = "product_code")
    @ApiModelProperty(value = "产品编号")
    private String productCode;

    @TableField(value = "materiel_class")
    @ApiModelProperty(value = "物料种类")
    private String materielClass;

    @TableField(value = "sku")
    @ApiModelProperty(value = "sku")
    private Long sku;

    @TableField(value = "sale_price")
    @ApiModelProperty(value = "salePrice")
    private BigDecimal salePrice;

    @TableField(value = "buy_price")
    @ApiModelProperty(value = "buyPrice")
    private BigDecimal buyPrice;

    @TableField(value = "sku_attr")
    @ApiModelProperty(value = "skuAttr")
    private String skuAttr;

    @TableField(value = "supply_cycle")
    @ApiModelProperty(value = "supplyCycle")
    private String supplyCycle;

    @TableField(value = "CREATE_USER")
    @ApiModelProperty(value = "创建人")
    private Long createUser;

    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "UPDATE_USER")
    @ApiModelProperty(value = "更新人")
    private Long updateUser;

    @TableField(value = "UPDATE_TIME" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "IS_DELETE")
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    public void copy(SupplierMaterielInfo source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}