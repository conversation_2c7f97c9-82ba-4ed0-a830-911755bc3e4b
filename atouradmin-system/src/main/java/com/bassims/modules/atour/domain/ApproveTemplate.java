/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-03-30
**/
@Data
@TableName(value="t_approve_template")
public class ApproveTemplate implements Serializable {

    @TableId(value = "approve_template_id")
    @ApiModelProperty(value = "审批模板表主键")
    private Long approveTemplateId;

    @TableField(value = "app_templete_code")
    @ApiModelProperty(value = "审批编码")
    private String appTempleteCode;

    @TableField(value = "app_template_name")
    @ApiModelProperty(value = "审批名称")
    private String appTemplateName;

    @TableField(value = "app_template_remark")
    @ApiModelProperty(value = "备注说明")
    private String appTemplateRemark;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "create_by",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_by",fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    public void copy(ApproveTemplate source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}