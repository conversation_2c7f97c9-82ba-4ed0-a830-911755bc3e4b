/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.modules.atour.domain.ProjectGroup;
import com.bassims.modules.atour.domain.ProjectInfoAbarbeitung;
import com.bassims.modules.atour.domain.ProjectNodeInfo;
import com.bassims.modules.atour.repository.ProjectGroupRepository;
import com.bassims.modules.atour.repository.ProjectNodeInfoRepository;
import com.bassims.modules.atour.service.ProjectGroupService;
import com.bassims.modules.atour.service.ProjectNodeInfoService;
import com.bassims.modules.atour.service.dto.ProjectApproveDto;
import com.bassims.modules.atour.util.NoteInfoMappingUtil;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.ProjectInfoAbarbeitungRepository;
import com.bassims.modules.atour.service.ProjectInfoAbarbeitungService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.ProjectInfoAbarbeitungDto;
import com.bassims.modules.atour.service.dto.ProjectInfoAbarbeitungQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.ProjectInfoAbarbeitungMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2023-11-28
 **/
@Service
public class ProjectInfoAbarbeitungServiceImpl extends BaseServiceImpl<ProjectInfoAbarbeitungRepository, ProjectInfoAbarbeitung> implements ProjectInfoAbarbeitungService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectInfoAbarbeitungServiceImpl.class);

    @Autowired
    private ProjectInfoAbarbeitungRepository projectInfoAbarbeitungRepository;
    @Autowired
    private ProjectInfoAbarbeitungMapper projectInfoAbarbeitungMapper;
    @Autowired
    private ProjectGroupRepository projectGroupRepository;
    @Autowired
    private ProjectNodeInfoService projectNodeInfoService;
    @Autowired
    private ProjectNodeInfoRepository projectNodeInfoRepository;

    @Autowired
    private NoteInfoMappingUtil util;

    @Override
    public Map<String, Object> queryAll(ProjectInfoAbarbeitungQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<ProjectInfoAbarbeitung> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(ProjectInfoAbarbeitung.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", projectInfoAbarbeitungMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectInfoAbarbeitungDto> queryAll(ProjectInfoAbarbeitungQueryCriteria criteria) {
        return projectInfoAbarbeitungMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectInfoAbarbeitung.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectInfoAbarbeitungDto findById(Long abarbeitungId) {
        ProjectInfoAbarbeitung projectInfoAbarbeitung = Optional.ofNullable(getById(abarbeitungId)).orElseGet(ProjectInfoAbarbeitung::new);
        ValidationUtil.isNull(projectInfoAbarbeitung.getAbarbeitungId(), getEntityClass().getSimpleName(), "abarbeitungId", abarbeitungId);
        return projectInfoAbarbeitungMapper.toDto(projectInfoAbarbeitung);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectInfoAbarbeitungDto create(ProjectInfoAbarbeitung resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setAbarbeitungId(snowflake.nextId());
        save(resources);
        return findById(resources.getAbarbeitungId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectInfoAbarbeitung resources) {
        ProjectInfoAbarbeitung projectInfoAbarbeitung = Optional.ofNullable(getById(resources.getAbarbeitungId())).orElseGet(ProjectInfoAbarbeitung::new);
        ValidationUtil.isNull(projectInfoAbarbeitung.getAbarbeitungId(), "ProjectInfoAbarbeitung", "id", resources.getAbarbeitungId());
        projectInfoAbarbeitung.copy(resources);
        updateById(projectInfoAbarbeitung);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long abarbeitungId : ids) {
            projectInfoAbarbeitungRepository.deleteById(abarbeitungId);
        }
    }

    @Override
    public void download(List<ProjectInfoAbarbeitungDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectInfoAbarbeitungDto projectInfoAbarbeitung : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("项目id", projectInfoAbarbeitung.getProjectId());
            map.put("问题类型", projectInfoAbarbeitung.getAbarbeitungType());
            map.put("项目备注", projectInfoAbarbeitung.getRemark());
            map.put("创建时间", projectInfoAbarbeitung.getCreateTime());
            map.put("创建人", projectInfoAbarbeitung.getCreateBy());
            map.put("更新时间", projectInfoAbarbeitung.getUpdateTime());
            map.put("更新人", projectInfoAbarbeitung.getUpdateBy());
            map.put("是否可用", projectInfoAbarbeitung.getIsEnabled());
            map.put("是否删除", projectInfoAbarbeitung.getIsDelete());
            map.put("三级nodeCode", projectInfoAbarbeitung.getNodeCode());
            map.put("文件的nodeId", projectInfoAbarbeitung.getFileNodeId());
            map.put("描述", projectInfoAbarbeitung.getProblemDescription());
            map.put("解决方案", projectInfoAbarbeitung.getSolution());
            map.put("状态", projectInfoAbarbeitung.getStatus());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

}