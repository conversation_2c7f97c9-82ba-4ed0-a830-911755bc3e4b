package com.bassims.modules.atour.service.impl;


import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.constant.bsEnum.AtourSystemEnum;
import com.bassims.constant.bsEnum.KidsSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.domain.vo.SendNotifyCenterSmsVo;
import com.bassims.modules.atour.domain.vo.SendNotifyCenterTargetsVo;
import com.bassims.modules.atour.repository.NoticeTemplateRepository;
import com.bassims.modules.atour.repository.ProjectInfoRepository;
import com.bassims.modules.atour.repository.ProjectStakeholdersRepository;
import com.bassims.modules.atour.repository.ProjectTemplateNoticeRelationRepository;
import com.bassims.modules.atour.service.*;
import com.bassims.modules.atour.service.dto.*;
import com.bassims.modules.atour.service.mapstruct.ProjectInfoMapper;
import com.bassims.modules.feishu.domain.MessageParameter;
import com.bassims.modules.feishu.service.FeishuService;
import com.bassims.modules.feishu.service.PortalService;
import com.bassims.modules.system.domain.DictDetail;
import com.bassims.modules.system.domain.Role;
import com.bassims.modules.system.domain.User;
import com.bassims.modules.system.repository.DictDetailRepository;
import com.bassims.modules.system.repository.RoleRepository;
import com.bassims.modules.system.repository.UserRepository;
import com.bassims.utils.DateUtil;
import com.bassims.utils.SnowFlakeUtil;
import com.bassims.utils.StringUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@Service
public class ProjectTaskServiceImpl implements ProjectTaskService {
    private static final Logger logger = LoggerFactory.getLogger(ProjectTaskServiceImpl.class);
    //    @Autowired
//    private ProjectNodeInfoService projectNodeInfoService;
    @Autowired
    private ProjectTaskInfoService projectTaskInfoService;
    @Autowired
    private ProjectInfoService projectInfoService;
    @Autowired
    private NoticeTemplateRepository noticeTemplateRepository;
    @Autowired
    private FeishuService feishuService;
    @Autowired
    private ProjectNoticeService projectNoticeService;
    @Autowired
    private DictDetailRepository dictDetailRepository;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ProjectTemplateNoticeRelationRepository projectTemplateNoticeRelationRepository;
    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private ProjectStakeholdersService projectStakeholdersService;
    @Autowired
    private ProjectStakeholdersRepository projectStakeholdersRepository;
    @Autowired
    private ProjectInfoRepository projectInfoRepository;
    @Autowired
    private ProjectInfoMapper projectInfoMapper;
    @Autowired
    private ProjectGroupService projectGroupService;
    @Autowired
    private OrderInfoService orderInfoService;
    @Autowired
    private StoreMasterInfoService storeMasterInfoService;
    @Autowired
    private UserRepository userRepository;//findUserByGroupRoleCode
    @Autowired
    private PortalService portalService;
    @Autowired
    private ProjectNodeInfoService projectNodeInfoService;
    @Autowired
    private SupplierPmService supplierPmService;
    @Autowired
    private SupplierInfoService supplierInfoService;
//    @Autowired
//    private RocketMQUtil rocketMQUtil;


    @Override
    public Boolean createTask() {
        //获取当前日期
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = sdf.format(date);
        //获取所有的二级节点，过滤掉【已完成】的节点
        LambdaQueryWrapper<ProjectGroup> querychild = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getNodeLevel, 2).eq(ProjectGroup::getIsDelete, false);
        querychild.and(Wrapper -> Wrapper.ne(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey()).or().isNull(ProjectGroup::getNodeStatus));
        List<ProjectGroup> list = projectGroupService.list(querychild);
        final List<ProjectGroup> collect = list.stream().filter(e -> ObjectUtil.isNotEmpty(e.getPlanEndDate())
                && ObjectUtil.isNotEmpty(e.getPlanStartDate())).collect(Collectors.toList());
        //当前任务表
        final LambdaQueryWrapper<ProjectTaskInfo> wrapper = Wrappers.lambdaQuery(ProjectTaskInfo.class)
                .ne(ProjectTaskInfo::getTaskStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey())
                .eq(ProjectTaskInfo::getIsDelete, false);
        final List<ProjectTaskInfo> projectTaskInfos = projectTaskInfoService.list(wrapper);
        final Map<Long, List<ProjectTaskInfo>> listMap = projectTaskInfos.stream().filter(e -> !"(审批)".contains(e.getNodeName())
                && ObjectUtil.isNotEmpty(e.getNodeId()))
                .collect(Collectors.groupingBy(ProjectTaskInfo::getNodeId));
        //审批相关任务,计划完成时间两天前发送逾期
        final Map<Long, List<ProjectTaskInfo>> listMapApp = projectTaskInfos.stream().filter(e -> "(审批)".contains(e.getNodeName())
                && ObjectUtil.isNotEmpty(e.getNodeId()))
                .collect(Collectors.groupingBy(ProjectTaskInfo::getNodeId));
        for (ProjectGroup node : collect) {
            //判断当前是否应该出任务
            Timestamp planEndDate = node.getPlanEndDate();
            Timestamp planStartDate = node.getPlanStartDate();
            //任务计划完成时间的前7天 开始预警
            Integer noticeDay = 0;
            if (node.getNoticeDay() != null) {
                noticeDay = node.getNoticeDay();
            }
            String startDate = planStartDate.toString();
            String endDate = planEndDate.toString();
            String noticeDate = "";
            //应该提醒的日期
            try {
                noticeDate = DateUtil.subDate(endDate, noticeDay);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            // log.info(node.getNodeId()+"-----"+node.getNodeName()+"-----"+dateString+"--"+startDate);
            //判断开始日期是否大于今天（若小于今天，则执行定时任务，若不小于则不考虑）
            if (DateUtil.compareTodate(dateString, startDate) >= 0) {
                RLock lock = redissonClient.getLock("nioTask");
                try {
                    lock.lock();
                    //提醒信息
                    StringBuffer messageTitle = new StringBuffer("");
                    String messageText = "";
                    //判断当前应该出什么任务
                    logger.info("提醒日期：" + noticeDate + ";逾期：" + endDate + ";this：" + startDate);
                    if (DateUtil.compareTodate(dateString, endDate) > 0
                            && ObjectUtil.isNotEmpty(listMap.get(node.getProjectGroupId()))
                            && !JhSystemEnum.TaskNameEnum.OVERDUE_TASK.getKey().equals(listMap.get(node.getProjectGroupId()).get(0).getTaskType())) {
                        messageTitle.append("【营建新系统】逾期提醒：");
                        logger.info(node.getProjectId() + "--" + node.getProjectGroupId() + "-----" + node.getNodeName() + "逾期---------" + endDate);
                        createTask(node, JhSystemEnum.TaskNameEnum.OVERDUE_TASK, null);
                        createNotice(node, JhSystemEnum.TaskNameEnum.OVERDUE_TASK, null);
                        createNoticeCC(node, JhSystemEnum.TaskNameEnum.OVERDUE_TASK);
                    } else if (DateUtil.compareTodate(dateString, noticeDate) >= 0
                            && ObjectUtil.isNotEmpty(listMap.get(node.getProjectGroupId()))
                            && !JhSystemEnum.TaskNameEnum.REMIND_TASK.getKey().equals(listMap.get(node.getProjectGroupId()).get(0).getTaskType())) {
                        //提醒
                        messageTitle.append("【营建新系统】警告提醒：");
                        logger.info(node.getProjectId() + "--" + node.getProjectGroupId() + "-----" + node.getNodeName() + "提醒--------------" + noticeDate);
                        createTask(node, JhSystemEnum.TaskNameEnum.REMIND_TASK, null);
                        createNotice(node, JhSystemEnum.TaskNameEnum.REMIND_TASK, null);
                        createNoticeCC(node, JhSystemEnum.TaskNameEnum.REMIND_TASK);
                    } else if (DateUtil.compareTodate(dateString, startDate) >= 0
                            && ObjectUtil.isEmpty(listMap.get(node.getProjectGroupId()))) {
                        //待办任务
                        messageTitle.append("【营建新系统】待办提醒：");
                        logger.info(node.getProjectId() + "--" + node.getProjectGroupId() + "-----" + node.getNodeName() + "待办-----------" + startDate);
                        //处理进入结算阶段，插入数据
                        //if (JhSystemEnum.AccountCodeEnum.isAccountCode(node.getNodeCode())) {
                        //    updateAccountPhase(node);
                        //}
                        createTask(node, JhSystemEnum.TaskNameEnum.TODO_TASK, null);
                        createNotice(node, JhSystemEnum.TaskNameEnum.TODO_TASK, null);
                        createNoticeCC(node, JhSystemEnum.TaskNameEnum.TODO_TASK);
                    } else if (DateUtil.compareTodate(dateString, endDate) >= Math.negateExact(2)
                            && ObjectUtil.isNotEmpty(listMapApp.get(node.getProjectGroupId()))
                            && !JhSystemEnum.TaskNameEnum.OVERDUE_TASK.getKey().equals(listMapApp.get(node.getProjectGroupId()).get(0).getTaskType())) {
                        messageTitle.append("【营建新系统】审批逾期提醒：");
                        //todo 审批的逾期
                        //逾期
                        //logger.info(node.getProjectId() + "--" + node.getProjectGroupId() + "-----" + listMapApp.get(node.getProjectGroupId()).get(0).getNodeName() + "逾期---------" + endDate);
                        //createTask(node, JhSystemEnum.TaskNameEnum.OVERDUE_TASK,listMapApp.get(node.getProjectGroupId()).get(0).getNodeName());
                        //createNotice(node, JhSystemEnum.TaskNameEnum.OVERDUE_TASK,listMapApp.get(node.getProjectGroupId()).get(0).getNodeName());
                        //createNoticeCC(node, JhSystemEnum.TaskNameEnum.OVERDUE_TASK);
                    }
                    if (messageTitle.length() > 0) {
//                        111111111111
                        //发送信息通知，出错不影响程序运行
                        sendMyMessage(node.getProjectId(),node.getProjectGroupId().toString(), node.getNodeCode(), messageTitle, messageText, DateUtil.stringToDate(String.valueOf(endDate)).toString());
                    }
                } finally {
                    lock.unlock();
                }
            } else {
                continue;
            }
        }

        return true;
    }

    @Override
    public void sendAsynchronous(List<SendNotifyCenterTargetsVo> centerTargetsVos, String content) {
        //发送消息 创建并启动一个线程
        Thread myThread = new Thread(() -> {
            // 异步任务逻辑
            SendNotifyCenterSmsVo centerTargetsVo = new SendNotifyCenterSmsVo();
            centerTargetsVo.setTargets(centerTargetsVos);
            centerTargetsVo.setContent(content);
//        rocketMQUtil.sendAsyncMessage(JSON.toJSONString(centerTargetsVo), "sendTextMessage");
            portalService.sendNotifyCenterSms(centerTargetsVo);
        });
        myThread.start(); // 启动线程
    }

    private void deleteOldTask(ProjectGroup node) {
        final LambdaQueryWrapper<ProjectTaskInfo> eq = Wrappers.lambdaQuery(ProjectTaskInfo.class)
                .eq(ProjectTaskInfo::getNodeId, node.getProjectGroupId());
        final List<ProjectTaskInfo> taskInfos = projectTaskInfoService.list(eq);
        final Set<Long> collect = taskInfos.stream().map(ProjectTaskInfo::getProjectTaskId).collect(Collectors.toSet());
        projectTaskInfoService.deleteAll(collect.toArray(new Long[collect.size()]));
    }


    public void createTask(ProjectGroup projectGroup, JhSystemEnum.TaskNameEnum taskNameEnum, String appName) {
        //获取当前任务是否有其他任务
        Long projectId = projectGroup.getProjectId();
        LambdaQueryWrapper<ProjectInfo> projectQuery = Wrappers.lambdaQuery(ProjectInfo.class)
                .eq(ProjectInfo::getProjectId, projectId)
                .eq(ProjectInfo::getIsDelete, false);
        ProjectInfo projectInfo = projectInfoRepository.selectOne(projectQuery);
        if (ObjectUtil.isNotEmpty(projectInfo)) {
            String storeName = null;
            if (ObjectUtil.isNotEmpty(projectInfo.getStoreName())) {
                storeName = projectInfo.getStoreName();
            }
            Long nodeId = projectGroup.getProjectGroupId();
            String key = taskNameEnum.getKey();
            if (!isNodeTaskInfo(projectId, nodeId, key)) {
                ProjectTaskInfo projectTaskInfoNew = new ProjectTaskInfo();
                //新增任务
                projectTaskInfoNew.setTaskName(taskNameEnum.getSpec());
                projectTaskInfoNew.setTaskType(taskNameEnum.getKey());
                projectTaskInfoNew.setNodeId(projectGroup.getProjectGroupId());
                projectTaskInfoNew.setTaskStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
                if (ObjectUtil.isNotEmpty(appName)) {
                    projectTaskInfoNew.setNodeName(appName);
                } else {
                    projectTaskInfoNew.setNodeName(projectGroup.getNodeName());
                }
                projectTaskInfoNew.setPlanEndDate(new java.sql.Date(projectGroup.getPlanEndDate().getTime()));
                //处理useCase
                if (JhSystemEnum.AccountCodeEnum.isAccountCode(projectGroup.getNodeCode())) {
                    projectTaskInfoNew.setUseCase("2");
                } else {
                    projectTaskInfoNew.setUseCase("1");
                }
                String jobcode = projectGroup.getRoleCode();
                if (StringUtils.isBlank(jobcode)) {
                    return;
                }
                if (jobcode.indexOf(",") != -1) {
                    String[] job = jobcode.split(",");
                    for (String s : job) {
                        List<ProjectTaskInfo> projectTaskInfos = projectTaskInfoService.getProjectTaskInfo(projectId, nodeId, s);
                        for (ProjectTaskInfo projectTaskInfo : projectTaskInfos) {
                            if (projectTaskInfo != null && projectTaskInfo.getUserId() != null) {
                                projectTaskInfoNew.setProjectTaskId(SnowFlakeUtil.getInstance().nextLongId());
                                projectTaskInfoNew.setProjectId(projectTaskInfo.getProjectId());
                                String projectName = projectTaskInfo.getProjectName();
                                if (ObjectUtil.isNotEmpty(storeName)) {
                                    if (ObjectUtil.isNotEmpty(projectName)) {
                                        projectTaskInfo.setProjectName(storeName + "-" + projectName);
                                    } else {
                                        projectTaskInfo.setProjectName(storeName);
                                    }
                                } else {
                                    if (ObjectUtil.isNotEmpty(projectName)) {
                                        projectTaskInfo.setProjectName(projectName);
                                    } else {
                                        projectTaskInfo.setProjectName(null);
                                    }
                                }
                                projectTaskInfoNew.setJobId(projectTaskInfo.getJobId());
                                projectTaskInfoNew.setJobName(projectTaskInfo.getJobName());
                                projectTaskInfoNew.setStoreType(projectTaskInfo.getStoreType());
                                projectTaskInfoNew.setUserId(projectTaskInfo.getUserId());
                                projectTaskInfoNew.setIsDelete(false);
                                projectTaskInfoNew.setIsEnabled(true);
                                projectTaskInfoService.save(projectTaskInfoNew);
                            }
                        }
                    }

                } else {
                    List<ProjectTaskInfo> projectTaskInfos = projectTaskInfoService.getProjectTaskInfo(projectId, nodeId, jobcode);
                    for (ProjectTaskInfo projectTaskInfo : projectTaskInfos) {
                        if (projectTaskInfo != null && projectTaskInfo.getUserId() != null) {
                            projectTaskInfoNew.setProjectId(projectTaskInfo.getProjectId());
                            String projectName = projectTaskInfo.getProjectName();
                            if (ObjectUtil.isNotEmpty(storeName)) {
                                if (ObjectUtil.isNotEmpty(projectName)) {
                                    projectTaskInfo.setProjectName(storeName + "-" + projectName);
                                } else {
                                    projectTaskInfo.setProjectName(storeName);
                                }
                            } else {
                                if (ObjectUtil.isNotEmpty(projectName)) {
                                    projectTaskInfo.setProjectName(projectName);
                                } else {
                                    projectTaskInfo.setProjectName(null);
                                }
                            }
                            projectTaskInfoNew.setJobId(projectTaskInfo.getJobId());
                            projectTaskInfoNew.setJobName(projectTaskInfo.getJobName());
                            projectTaskInfoNew.setStoreType(projectTaskInfo.getStoreType());
                            projectTaskInfoNew.setUserId(projectTaskInfo.getUserId());
                            projectTaskInfoNew.setIsDelete(false);
                            projectTaskInfoNew.setIsEnabled(true);
                            projectTaskInfoService.save(projectTaskInfoNew);
                        }
                    }

                }
            }
        }


    }

    public void createNotice(ProjectGroup projectGroup, JhSystemEnum.TaskNameEnum taskNameEnum, String appName) {
        //获取当前任务是否有其他任务
        Long projectId = projectGroup.getProjectId();
        Long nodeId = projectGroup.getProjectGroupId();
        String key = taskNameEnum.getKey();
        ProjectNotice projectNotice = new ProjectNotice();
        //新增任务
        projectNotice.setNoticeName(taskNameEnum.getSpec());
        projectNotice.setNoticeType(taskNameEnum.getKey());
        projectNotice.setNodeId(projectGroup.getProjectGroupId());
        projectNotice.setNoticeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
        if (ObjectUtil.isNotEmpty(appName)) {
            projectNotice.setNodeName(appName);
        } else {
            projectNotice.setNodeName(projectGroup.getNodeName());
        }
        projectNotice.setPlanEndDate(new java.sql.Date(projectGroup.getPlanEndDate().getTime()));
        projectNotice.setNodeCode(projectGroup.getNodeCode());

        String jobcode = projectGroup.getRoleCode();
        if (StringUtils.isBlank(jobcode)) {
            return;
        }
        if (jobcode.indexOf(",") != -1) {
            String[] job = jobcode.split(",");
            for (String s : job) {
                List<ProjectTaskInfo> projectTaskInfos = projectTaskInfoService.getProjectTaskInfo(projectId, nodeId, s);
                for (ProjectTaskInfo projectTaskInfo : projectTaskInfos) {
                    if (projectTaskInfo != null && projectTaskInfo.getUserId() != null) {
                        LambdaQueryWrapper<ProjectNotice> noticeLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectNotice.class);
                        noticeLambdaQueryWrapper.eq(ProjectNotice::getProjectId, projectId).eq(ProjectNotice::getNodeId, nodeId)
                                .eq(ProjectNotice::getUserId, projectTaskInfo.getUserId()).eq(ProjectNotice::getNoticeType, taskNameEnum.getKey());
                        // 判断当前消息是否已发送
                        List<ProjectNotice> list = projectNoticeService.list(noticeLambdaQueryWrapper);
                        projectNotice.setProjectNoticeId(SnowFlakeUtil.getInstance().nextLongId());
                        projectNotice.setProjectId(projectTaskInfo.getProjectId());
                        projectNotice.setProjectName(projectTaskInfo.getProjectName());
                        projectNotice.setRoleId(projectTaskInfo.getJobId());
                        projectNotice.setRoleName(projectTaskInfo.getJobName());
                        if (ObjectUtil.isNotEmpty(projectTaskInfo.getStoreType())) {
                            DictDetail dictDetailByValue = dictDetailRepository.findDictDetailByValue(projectTaskInfo.getStoreType());
                            projectNotice.setStoreType(dictDetailByValue.getLabel());
                        }
                        projectNotice.setUserId(projectTaskInfo.getUserId());
                        projectNotice.setIsDelete(false);
                        projectNotice.setIsEnabled(true);
                        LambdaQueryWrapper<NoticeTemplate> noticeTemplateLambdaQueryWrapper = Wrappers.lambdaQuery(NoticeTemplate.class);
                        noticeTemplateLambdaQueryWrapper.eq(NoticeTemplate::getNoticeType, taskNameEnum.getKey());
                        NoticeTemplate noticeTemplate = noticeTemplateRepository.selectOne(noticeTemplateLambdaQueryWrapper);
                        if (noticeTemplate != null) {
                            String noticeContent = noticeTemplate.getNoticeContent();
                            noticeContent = noticeContent.replace("{taskName}", "【" + projectTaskInfo.getProjectName() + "-" + projectGroup.getNodeName() + "】");
                            projectNotice.setNoticeContent(noticeContent);
                            projectNotice.setNodeId(nodeId);
                            projectNotice.setNoticeId(noticeTemplate.getNoticeId());
                        }
                        MessageParameter messageParameter = new MessageParameter();
                        if (list.size() == 0) {
                            messageParameter = feishuService.sendMessage(projectNotice);
                        }

                        if (messageParameter.getCode() != null && messageParameter.getCode() == 0) {
                            projectNotice.setNoticeContent(messageParameter.getTextContent());
                            projectNotice.setIsSend(Boolean.TRUE);
                            projectNoticeService.save(projectNotice);
                        }
                    }
                }


            }

        } else {
            List<ProjectTaskInfo> projectTaskInfos = projectTaskInfoService.getProjectTaskInfo(projectId, nodeId, jobcode);
            for (ProjectTaskInfo projectTaskInfo : projectTaskInfos) {
                if (projectTaskInfo != null && projectTaskInfo.getUserId() != null) {
                    LambdaQueryWrapper<ProjectNotice> noticeLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectNotice.class);
                    noticeLambdaQueryWrapper.eq(ProjectNotice::getProjectId, projectId).eq(ProjectNotice::getNodeId, nodeId)
                            .eq(ProjectNotice::getUserId, projectTaskInfo.getUserId()).eq(ProjectNotice::getNoticeType, taskNameEnum.getKey());
                    // 判断当前消息是否已发送
                    List<ProjectNotice> list = projectNoticeService.list(noticeLambdaQueryWrapper);
                    projectNotice.setProjectId(projectTaskInfo.getProjectId());
                    projectNotice.setProjectName(projectTaskInfo.getProjectName());
                    projectNotice.setRoleId(projectTaskInfo.getJobId());
                    projectNotice.setRoleName(projectTaskInfo.getJobName());
                    if (ObjectUtil.isNotEmpty(projectTaskInfo.getStoreType())) {
                        DictDetail dictDetailByValue = dictDetailRepository.findDictDetailByValue(projectTaskInfo.getStoreType());
                        projectNotice.setStoreType(dictDetailByValue.getLabel());
                    }
                    projectNotice.setUserId(projectTaskInfo.getUserId());
                    projectNotice.setIsDelete(false);
                    projectNotice.setIsEnabled(true);
                    LambdaQueryWrapper<NoticeTemplate> noticeTemplateLambdaQueryWrapper = Wrappers.lambdaQuery(NoticeTemplate.class);
                    noticeTemplateLambdaQueryWrapper.eq(NoticeTemplate::getNoticeType, taskNameEnum.getKey());
                    NoticeTemplate noticeTemplate = noticeTemplateRepository.selectOne(noticeTemplateLambdaQueryWrapper);
                    if (noticeTemplate != null) {
                        String noticeContent = noticeTemplate.getNoticeContent();
                        noticeContent = noticeContent.replace("{taskName}", "【" + projectTaskInfo.getProjectName() + "-" + projectGroup.getNodeName() + "】");
                        projectNotice.setNoticeContent(noticeContent);
                        projectNotice.setNodeId(nodeId);
                        projectNotice.setNoticeId(noticeTemplate.getNoticeId());
                    }
                    MessageParameter messageParameter = new MessageParameter();
                    if (list.size() == 0) {
                        messageParameter = feishuService.sendMessage(projectNotice);
                    }
                    if (messageParameter.getCode() != null && messageParameter.getCode() == 0) {
                        projectNotice.setNoticeContent(messageParameter.getTextContent());
                        projectNotice.setIsSend(Boolean.TRUE);
                        projectNoticeService.save(projectNotice);
                    }
                }
            }

        }


    }

    /**
     * 新增审批任务
     *
     * @param projectGroupDto
     * @param taskNameEnum
     */
    @Override
    public void createAppTask(ProjectGroupDto projectGroupDto, JhSystemEnum.TaskNameEnum taskNameEnum, ProjectApproveDetailDto projectApproveDetailDto) {
        //获取当前任务是否有其他任务
        Long projectId = ObjectUtils.isNotEmpty(projectGroupDto.getProjectId()) ? Long.valueOf(projectGroupDto.getProjectId()) : null;
        Long orderId = ObjectUtils.isNotEmpty(projectGroupDto.getOrderId()) ? Long.parseLong(projectGroupDto.getOrderId()) : null;
        Long nodeId = Long.parseLong(projectGroupDto.getProjectGroupId());
        String key = taskNameEnum.getKey();
        String nowDate = DateUtil.getNowDate();
        LocalDateTime nowTime = DateUtil.parseLocalDateTimeFormatyMd(nowDate);

        ProjectTaskInfo projectTaskInfoNew = new ProjectTaskInfo();
        //新增任务
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        projectTaskInfoNew.setProjectTaskId(snowflake.nextId());
        projectTaskInfoNew.setTaskName(taskNameEnum.getSpec());
        projectTaskInfoNew.setTaskType(taskNameEnum.getKey());
        projectTaskInfoNew.setNodeId(nodeId);
        projectTaskInfoNew.setTaskStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
        projectTaskInfoNew.setNodeName(projectGroupDto.getNodeName() + "(审批)");
        //审批添加跳转地址
        projectTaskInfoNew.setAddress(AtourSystemEnum.jumpAddressEnum.SECOND.getKey());

        projectTaskInfoNew.setPlanEndDate(DateUtil.localDateTimeToDate(nowTime));
        ProjectInfoDto projectInfoDto = projectInfoMapper.toDto(Optional.ofNullable(projectInfoRepository.selectById(projectNodeInfoService.getSubmeterProjectId(projectId))).orElseGet(ProjectInfo::new));

//        if(JhSystemEnum.AccountCodeEnum.isAccountCode(projectNodeInfoDto.getNodeCode())){
//            projectTaskInfoNew.setUseCase("2");
//        }else {
//            projectTaskInfoNew.setUseCase("1");
//        }

        projectTaskInfoNew.setUseCase("1");
        projectTaskInfoNew.setProjectId(projectId);
        projectTaskInfoNew.setOrderId(orderId);
        projectTaskInfoNew.setProjectName(projectInfoDto.getProjectName());
        projectTaskInfoNew.setJobId(Long.parseLong(projectApproveDetailDto.getApproveRole()));
        projectTaskInfoNew.setJobName(projectApproveDetailDto.getApproveRoleName());
        projectTaskInfoNew.setStoreType(projectInfoDto.getStoreType());
        projectTaskInfoNew.setProjectType(projectId == null ? projectGroupDto.getTemplateCode() : projectInfoDto.getProjectType());
        projectTaskInfoNew.setUserId(projectApproveDetailDto.getApproveUser());
        projectTaskInfoNew.setIsDelete(false);
        projectTaskInfoNew.setIsEnabled(true);
        if (JhSystemEnum.SupplierTypeEnum.SUPPLIER_PM.getKey().equals(projectGroupDto.getTemplateCode())) {
            SupplierPm pm = supplierPmService.getById(projectGroupDto.getProjectId());
            if (ObjectUtil.isNotEmpty(pm)) projectTaskInfoNew.setProjectName(pm.getPmName());
        }else if (JhSystemEnum.SupplierTypeEnum.SUPPLIER.getKey().equals(projectGroupDto.getTemplateCode())) {
            SupplierInfo si = supplierInfoService.getById(projectGroupDto.getProjectId());
            if (ObjectUtil.isNotEmpty(si)) projectTaskInfoNew.setProjectName(si.getSupNameCn());
        }
        projectTaskInfoService.save(projectTaskInfoNew);

        //发送企微和短信消息
        this.sendMyMessage(Long.valueOf(projectGroupDto.getProjectId()),projectGroupDto.getProjectGroupId(),projectGroupDto.getNodeCode(), new StringBuffer("【营建新系统】审批待办提醒："), projectApproveDetailDto.getApproveUser().toString(), null);

    }

    @Override
    public void createNoticeAppTask(ProjectGroupDto projectGroupDto, JhSystemEnum.TaskNameEnum taskNameEnum, ProjectApproveDetailDto projectApproveDetailDto) {
        //获取当前任务是否有其他任务
        Long projectId = Long.parseLong(projectGroupDto.getProjectId());
        Long nodeId = Long.parseLong(projectGroupDto.getProjectGroupId());
        String key = taskNameEnum.getKey();
        ProjectNotice projectNotice = new ProjectNotice();
        //新增任务
        projectNotice.setNoticeName(taskNameEnum.getSpec());
        projectNotice.setNoticeType(taskNameEnum.getKey());
        projectNotice.setNodeId(Long.parseLong(projectGroupDto.getProjectGroupId()));
        projectNotice.setNoticeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
        projectNotice.setNodeName(projectGroupDto.getNodeName() + "(审批)");
        if (projectGroupDto.getPlanEndDate() != null) {
            projectNotice.setPlanEndDate(DateUtil.stringToDate(String.valueOf(projectGroupDto.getPlanEndDate())));
        }
        projectNotice.setNodeCode(projectGroupDto.getNodeCode());
        ProjectInfoDto projectInfoDto = projectInfoService.findById(projectId);

     /*   LambdaQueryWrapper<ProjectNotice> noticeLambdaQueryWrapper=Wrappers.lambdaQuery(ProjectNotice.class);
        noticeLambdaQueryWrapper.eq(ProjectNotice::getProjectId,projectId).eq(ProjectNotice::getNodeId,nodeId)
                .eq(ProjectNotice::getUserId,projectApproveDetailDto.getApproveUser()).eq(ProjectNotice::getNoticeType,taskNameEnum.getKey());
        // 判断当前消息是否已发送
        List<ProjectNotice> list = projectNoticeService.list(noticeLambdaQueryWrapper);*/
        projectNotice.setProjectId(projectId);
        projectNotice.setProjectName(projectInfoDto.getProjectName());
        projectNotice.setRoleId(Long.parseLong(projectApproveDetailDto.getApproveRole()));
        projectNotice.setRoleName(projectApproveDetailDto.getApproveRoleName());
        DictDetail dictDetailByValue = dictDetailRepository.findDictDetailByValue(projectInfoDto.getStoreType());
        projectNotice.setStoreType(dictDetailByValue.getLabel());
        projectNotice.setUserId(projectApproveDetailDto.getApproveUser());
        projectNotice.setIsDelete(false);
        projectNotice.setIsEnabled(true);
        LambdaQueryWrapper<NoticeTemplate> noticeTemplateLambdaQueryWrapper = Wrappers.lambdaQuery(NoticeTemplate.class);
        noticeTemplateLambdaQueryWrapper.eq(NoticeTemplate::getNoticeType, taskNameEnum.getKey());
        NoticeTemplate noticeTemplate = noticeTemplateRepository.selectOne(noticeTemplateLambdaQueryWrapper);
        if (noticeTemplate != null) {
            String noticeContent = noticeTemplate.getNoticeContent();
            noticeContent = noticeContent.replace("{taskName}", "【" + projectInfoDto.getProjectName() + "-" + projectGroupDto.getNodeName() + "】");
            projectNotice.setNoticeContent(noticeContent);
            projectNotice.setNodeId(noticeTemplate.getNoticeId());
        }
        MessageParameter messageParameter = new MessageParameter();
        //if(list.size()==0){
        messageParameter = feishuService.sendMessage(projectNotice);
        //}
        if (messageParameter.getCode() != null && messageParameter.getCode() == 0) {
            projectNotice.setNoticeContent(messageParameter.getTextContent());
            projectNotice.setIsSend(Boolean.TRUE);
            projectNotice.setNoticeId(noticeTemplate.getNoticeId());
            projectNoticeService.save(projectNotice);
        }

        this.sendMyMessage(Long.valueOf(projectGroupDto.getProjectId()),projectGroupDto.getProjectGroupId(), projectGroupDto.getNodeCode(), new StringBuffer("【营建新系统】审批待办提醒："), projectApproveDetailDto.getApproveUser().toString(), null);

    }

    @Override
    public Boolean isNodeTaskInfo(Long projectId, Long nodeId, String TaskType) {
        Boolean flag = false;
        //获取当前任务是否有其他任务
        LambdaQueryWrapper<ProjectTaskInfo> lambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTaskInfo.class);
        lambdaQueryWrapper.eq(ProjectTaskInfo::getProjectId, projectId).eq(ProjectTaskInfo::getNodeId, nodeId)
                .eq(ProjectTaskInfo::getIsDelete, 0).eq(ProjectTaskInfo::getTaskStatus, "task_unfin");
        List<ProjectTaskInfo> list = projectTaskInfoService.list(lambdaQueryWrapper);
        for (ProjectTaskInfo projectTaskInfo : list) {
            //要改要测
            if (projectTaskInfo != null && (!TaskType.equals(projectTaskInfo.getTaskType()))) {
                //删除当前任务的其他任务
                projectTaskInfo.setIsDelete(true);
                projectTaskInfoService.update(projectTaskInfo);
            } else if (projectTaskInfo != null && (TaskType.equals(projectTaskInfo.getTaskType()))) {
                flag = true;
            }

        }
        return flag;
    }

    /**
     * 更换任务人
     *
     * @param projectId
     * @param oldUserId
     * @param userId
     * @return
     */
    @Override
    public Boolean changeTaskUser(Long projectId, Long orderId, Long userId, Long oldUserId) {
        Boolean flag = false;
        //查询旧干系人所有未完成任务
        LambdaQueryWrapper<ProjectTaskInfo> lambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTaskInfo.class);
        lambdaQueryWrapper.eq(ProjectTaskInfo::getProjectId, projectId)
                .eq(ProjectTaskInfo::getUserId, oldUserId)
                .eq(ProjectTaskInfo::getIsDelete, 0).eq(ProjectTaskInfo::getTaskStatus, "task_unfin");
        if (ObjectUtils.isNotEmpty(orderId)) {
            lambdaQueryWrapper.eq(ProjectTaskInfo::getOrderId, orderId);
        }
        List<ProjectTaskInfo> list = projectTaskInfoService.list(lambdaQueryWrapper);
        //更新所有任务为新人
        if (list.size() > 0) {
            for (ProjectTaskInfo taskInfo : list) {
                taskInfo.setUserId(userId);

            }
            flag = projectTaskInfoService.saveOrUpdateBatch(list);
        }
        return flag;
    }

    @Override
    public void createTaskGreen(ProjectNodeInfo projectNodeInfo, JhSystemEnum.TaskNameEnum taskNameEnum) {
        //获取当前任务是否有其他任务
        Long projectId = projectNodeInfo.getProjectId();
        Long nodeId = projectNodeInfo.getNodeId();
        String key = taskNameEnum.getKey();
        if (!isNodeTaskInfo(projectId, nodeId, key)) {
            ProjectTaskInfo projectTaskInfoNew = new ProjectTaskInfo();
            //新增任务
            projectTaskInfoNew.setTaskName(taskNameEnum.getSpec());
            projectTaskInfoNew.setTaskType(taskNameEnum.getKey());
            projectTaskInfoNew.setNodeId(projectNodeInfo.getNodeId());
            projectTaskInfoNew.setTaskStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
            projectTaskInfoNew.setNodeName(projectNodeInfo.getNodeName());
            projectTaskInfoNew.setPlanEndDate(projectNodeInfo.getPlanEndDate());
            //处理useCase
            if (JhSystemEnum.AccountCodeEnum.isAccountCode(projectNodeInfo.getNodeCode())) {
                projectTaskInfoNew.setUseCase("2");
            } else {
                projectTaskInfoNew.setUseCase("1");
            }
            String jobcode = projectNodeInfo.getRoleCode();
            if (StringUtils.isBlank(jobcode)) {
                return;
            }
            if (jobcode.indexOf(",") != -1) {
                String[] job = jobcode.split(",");
                for (String s : job) {
                    List<ProjectTaskInfo> projectTaskInfos = projectTaskInfoService.getProjectTaskInfo(projectId, nodeId, s);
                    for (ProjectTaskInfo projectTaskInfo : projectTaskInfos) {
                        if (projectTaskInfo != null && projectTaskInfo.getUserId() != null) {
                            projectTaskInfoNew.setProjectTaskId(SnowFlakeUtil.getInstance().nextLongId());
                            projectTaskInfoNew.setProjectId(projectTaskInfo.getProjectId());
                            projectTaskInfoNew.setProjectName(projectTaskInfo.getProjectName());
                            projectTaskInfoNew.setJobId(projectTaskInfo.getJobId());
                            projectTaskInfoNew.setJobName(projectTaskInfo.getJobName());
                            projectTaskInfoNew.setStoreType(projectTaskInfo.getStoreType());
                            projectTaskInfoNew.setUserId(projectTaskInfo.getUserId());
                            projectTaskInfoNew.setCreateBy("系统");
                            projectTaskInfoNew.setIsDelete(false);
                            projectTaskInfoNew.setIsEnabled(true);
                            projectTaskInfoService.save(projectTaskInfoNew);
                        }
                    }


                }

            } else {
                List<ProjectTaskInfo> projectTaskInfos = projectTaskInfoService.getProjectTaskInfo(projectId, nodeId, jobcode);
                for (ProjectTaskInfo projectTaskInfo : projectTaskInfos) {
                    if (projectTaskInfo != null && projectTaskInfo.getUserId() != null) {
                        projectTaskInfoNew.setProjectId(projectTaskInfo.getProjectId());
                        projectTaskInfoNew.setProjectName(projectTaskInfo.getProjectName());
                        projectTaskInfoNew.setJobId(projectTaskInfo.getJobId());
                        projectTaskInfoNew.setJobName(projectTaskInfo.getJobName());
                        projectTaskInfoNew.setStoreType(projectTaskInfo.getStoreType());
                        projectTaskInfoNew.setUserId(projectTaskInfo.getUserId());
                        projectTaskInfoNew.setCreateBy("系统");
                        projectTaskInfoNew.setIsDelete(false);
                        projectTaskInfoNew.setIsEnabled(true);
                        projectTaskInfoService.save(projectTaskInfoNew);
                    }
                }

            }
        }

    }


    public void updateAccountPhase(ProjectGroup projectGroup) {
        Long projectId = projectGroup.getProjectId();
        //查询当前结算阶段是否有值
        ProjectInfo byId = projectInfoService.getById(projectId);
        if (byId != null && (byId.getAccountPhase() == null || "--".equals(byId.getAccountPhase()))) {
            JhSystemEnum.AccountPhaseEnum accountPhaseEnum = JhSystemEnum.AccountPhaseEnum.accountPhase(projectGroup.getNodeCode());
            byId.setAccountPhase(accountPhaseEnum.getKey());
            projectInfoService.update(byId);
        }
    }

    /**
     * 发送抄送人消息
     *
     * @param projectGroup
     * @param taskNameEnum
     */
    public void createNoticeCC(ProjectGroup projectGroup, JhSystemEnum.TaskNameEnum taskNameEnum) {
        //获取当前任务是否有其他任务
        //查找当前任务是否有抄送通知
        List<ProjectTemplateNoticeRelationDto> templateNoticeList = projectTemplateNoticeRelationRepository.getTemplateNoticeList(projectGroup.getTemplateId());
        if (templateNoticeList.size() > 0) {
            ProjectInfo projectInfo = projectInfoService.getById(projectGroup.getProjectId());
            if (ObjectUtil.isNotEmpty(projectInfo)) {
                for (ProjectTemplateNoticeRelationDto relation : templateNoticeList) {
                    Long projectId = projectGroup.getProjectId();
                    Long nodeId = projectGroup.getProjectGroupId();
                    String key = taskNameEnum.getKey();
                    ProjectNotice projectNotice = new ProjectNotice();
                    //新增任务
                    projectNotice.setNoticeName(taskNameEnum.getSpec());
                    projectNotice.setNoticeType(taskNameEnum.getKey());
                    projectNotice.setNodeId(projectGroup.getProjectGroupId());
                    projectNotice.setNoticeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
                    projectNotice.setNodeName(projectGroup.getNodeName());
                    projectNotice.setPlanEndDate(new java.sql.Date(projectGroup.getPlanEndDate().getTime()));
                    projectNotice.setNodeCode(projectGroup.getNodeCode());
                    LambdaQueryWrapper<ProjectNotice> noticeLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectNotice.class);
                    noticeLambdaQueryWrapper.eq(ProjectNotice::getProjectId, projectId).eq(ProjectNotice::getNodeId, nodeId)
                            .eq(ProjectNotice::getUserId, relation.getUserId()).eq(ProjectNotice::getNoticeType, taskNameEnum.getKey());
                    // 判断当前消息是否已发送
                    List<ProjectNotice> list = projectNoticeService.list(noticeLambdaQueryWrapper);
                    projectNotice.setProjectId(projectInfo.getProjectId());
                    projectNotice.setProjectName(projectInfo.getProjectName());
                    projectNotice.setRoleId(relation.getJobId());
                    Long jobId = relation.getJobId();
                    Role one = roleRepository.findRoleByRoleId(jobId);
                    projectNotice.setRoleName(one.getName());
                    DictDetail dictDetailByValue = dictDetailRepository.findDictDetailByValue(projectInfo.getStoreType());
                    projectNotice.setStoreType(dictDetailByValue.getLabel());
                    projectNotice.setUserId(relation.getUserId());
                    projectNotice.setIsDelete(false);
                    projectNotice.setIsEnabled(true);
                    LambdaQueryWrapper<NoticeTemplate> noticeTemplateLambdaQueryWrapper = Wrappers.lambdaQuery(NoticeTemplate.class);
                    noticeTemplateLambdaQueryWrapper.eq(NoticeTemplate::getNoticeType, JhSystemEnum.TaskNameEnum.MESSAGE_NOTICE.getKey()).eq(NoticeTemplate::getNoticeId, relation.getNoticeId());
                    NoticeTemplate noticeTemplate = noticeTemplateRepository.selectOne(noticeTemplateLambdaQueryWrapper);
                    if (noticeTemplate != null) {
                        String noticeContent = noticeTemplate.getNoticeContent();
                        noticeContent = noticeContent.replace("{taskName}", "【" + projectInfo.getProjectName() + "-" + projectGroup.getNodeName() + "】")
                                .replace("{taskStatus}", JhSystemEnum.TaskCCEnum.getTaskCC(taskNameEnum.getKey()).getSpec());
                        projectNotice.setNoticeContent(noticeContent);
                        projectNotice.setNodeId(nodeId);
                        projectNotice.setNoticeId(noticeTemplate.getNoticeId());
                    }
                    MessageParameter messageParameter = new MessageParameter();
                    if (list.size() == 0) {
                        messageParameter = feishuService.sendMessage(projectNotice);
                    }
                    if (messageParameter.getCode() != null && messageParameter.getCode() == 0) {
                        projectNotice.setNoticeContent(messageParameter.getTextContent());
                        projectNotice.setIsSend(Boolean.TRUE);
                        projectNoticeService.save(projectNotice);
                    }


                }
            }
        }

    }


    /**
     * 发送抄送人消息
     *
     * @param projectNodeInfo
     * @param taskNameEnum
     */
    @Override
    public void createNoticeCCForDelete(ProjectNodeInfo projectNodeInfo, JhSystemEnum.TaskNameEnum taskNameEnum) {
        //获取当前任务是否有其他任务
        //查找当前任务是否有抄送通知
        createNoticeCCForDelete(projectNodeInfo, taskNameEnum, JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());

    }

    @Override
    public void createNoticeCCForDelete(ProjectNodeInfo projectNodeInfo, JhSystemEnum.TaskNameEnum taskNameEnum, String noticeStatus) {
        //获取当前任务是否有其他任务
        //查找当前任务是否有抄送通知
        List<ProjectTemplateNoticeRelationDto> templateNoticeList = projectTemplateNoticeRelationRepository.getTemplateNoticeList(projectNodeInfo.getTemplateId());
        if (templateNoticeList.size() > 0) {
            ProjectInfo projectInfo = projectInfoService.getById(projectNodeInfo.getProjectId());
            for (ProjectTemplateNoticeRelationDto relation : templateNoticeList) {
                Long projectId = projectNodeInfo.getProjectId();
                Long nodeId = projectNodeInfo.getNodeId();
                String key = taskNameEnum.getKey();
                ProjectNotice projectNotice = new ProjectNotice();
                //新增任务
                projectNotice.setNoticeName(taskNameEnum.getSpec());
                projectNotice.setNoticeType(taskNameEnum.getKey());
                projectNotice.setNodeId(projectNodeInfo.getNodeId());
                projectNotice.setNoticeStatus(noticeStatus);
                projectNotice.setNodeName(projectNodeInfo.getNodeName());
                projectNotice.setPlanEndDate(projectNodeInfo.getPlanEndDate());
                projectNotice.setNodeCode(projectNodeInfo.getNodeCode());
//                LambdaQueryWrapper<ProjectNotice> noticeLambdaQueryWrapper= Wrappers.lambdaQuery(ProjectNotice.class);
//                noticeLambdaQueryWrapper.eq(ProjectNotice::getProjectId,projectId).eq(ProjectNotice::getNodeId,nodeId)
//                        .eq(ProjectNotice::getUserId,relation.getUserId()).eq(ProjectNotice::getNoticeType,taskNameEnum.getKey());
//                // 判断当前消息是否已发送
//                List<ProjectNotice> list = projectNoticeService.list(noticeLambdaQueryWrapper);
                projectNotice.setProjectId(projectInfo.getProjectId());
                projectNotice.setProjectName(projectInfo.getProjectName());
                projectNotice.setRoleId(relation.getJobId());
                Long jobId = relation.getJobId();

                Role one = roleRepository.findRoleByRoleId(jobId);
                projectNotice.setRoleName(one.getName());
                DictDetail dictDetailByValue = dictDetailRepository.findDictDetailByValue(projectInfo.getStoreType());
                projectNotice.setStoreType(dictDetailByValue.getLabel());
                projectNotice.setUserId(relation.getUserId());
                projectNotice.setIsDelete(false);
                projectNotice.setIsEnabled(true);
                LambdaQueryWrapper<NoticeTemplate> noticeTemplateLambdaQueryWrapper = Wrappers.lambdaQuery(NoticeTemplate.class);
                noticeTemplateLambdaQueryWrapper.eq(NoticeTemplate::getNoticeType, JhSystemEnum.TaskNameEnum.MESSAGE_NOTICE.getKey()).eq(NoticeTemplate::getNoticeId, relation.getNoticeId());
                NoticeTemplate noticeTemplate = noticeTemplateRepository.selectOne(noticeTemplateLambdaQueryWrapper);
                if (noticeTemplate != null) {
                    String noticeContent = noticeTemplate.getNoticeContent();
                    noticeContent = noticeContent.replace("{taskName}", "【" + projectInfo.getProjectName() + "】");
                    projectNotice.setNoticeContent(noticeContent);
                    projectNotice.setNodeId(nodeId);
                    projectNotice.setNoticeId(noticeTemplate.getNoticeId());
                }
                MessageParameter messageParameter = new MessageParameter();
                //todo 2022/10/29 暂时取消三方通知，后续可加
//                if(list.size()==0){
//                    messageParameter = feishuService.sendMessageForDelete(projectNotice);
//                }
//                if(messageParameter.getCode()!=null&&messageParameter.getCode()==0){
//                    projectNotice.setNoticeContent(messageParameter.getTextContent());
                projectNotice.setIsSend(Boolean.TRUE);
                projectNoticeService.save(projectNotice);
//                }


            }
        }

    }

//    @Override
//    public void createNoticeCC(ProjectGroup projectGroup, JhSystemEnum.TaskNameEnum taskNameEnum, String key) {
//        List<ProjectTemplateNoticeRelationDto> templateNoticeList = projectTemplateNoticeRelationRepository.getTemplateNoticeListByGroupId(projectGroup.getTemplateGroupId());
//        if (templateNoticeList.size() > 0) {
//            ProjectInfo projectInfo = projectInfoService.getById(projectGroup.getProjectId());
//            for (ProjectTemplateNoticeRelationDto relation : templateNoticeList) {
//                Long jobId = relation.getJobId();
//                Long projectId = projectGroup.getProjectId();
//
//                LambdaQueryWrapper<ProjectStakeholders> wrapper = Wrappers.lambdaQuery(ProjectStakeholders.class)
//                        .eq(ProjectStakeholders::getProjectId, projectId).eq(ProjectStakeholders::getRoleId, jobId);
//                List<ProjectStakeholders> stakeholders = projectStakeholdersService.list(wrapper);
//                Role one = roleRepository.findRoleByRoleId(jobId);
//                for (ProjectStakeholders stakeholder : stakeholders) {
//                    Long nodeId = projectGroup.getProjectGroupId();
////                String key=taskNameEnum.getKey();
//                    ProjectNotice projectNotice = new ProjectNotice();
//                    //新增任务
//                    projectNotice.setNoticeName(taskNameEnum.getSpec());
//                    projectNotice.setNoticeType(taskNameEnum.getKey());
//                    projectNotice.setNodeId(nodeId);
//                    projectNotice.setNoticeStatus(key);
//                    projectNotice.setNodeName(projectGroup.getNodeName());
//                    projectNotice.setNodeCode(projectGroup.getNodeCode());
////                    LambdaQueryWrapper<ProjectNotice> noticeLambdaQueryWrapper= Wrappers.lambdaQuery(ProjectNotice.class);
////                    noticeLambdaQueryWrapper.eq(ProjectNotice::getProjectId,projectId).eq(ProjectNotice::getNodeId,nodeId)
////                            .eq(ProjectNotice::getUserId,relation.getUserId()).eq(ProjectNotice::getNoticeType,taskNameEnum.getKey());
////                    // 判断当前消息是否已发送
////                    List<ProjectNotice> list = projectNoticeService.list(noticeLambdaQueryWrapper);
//                    projectNotice.setProjectId(projectInfo.getProjectId());
//                    projectNotice.setProjectName(projectInfo.getProjectName());
//                    projectNotice.setRoleId(relation.getJobId());
//
//                    projectNotice.setRoleName(one.getName());
//                    DictDetail dictDetailByValue = dictDetailRepository.findDictDetailByValue(projectInfo.getStoreType());
//                    projectNotice.setStoreType(dictDetailByValue.getLabel());
//                    projectNotice.setUserId(stakeholder.getUserId());
//                    projectNotice.setIsDelete(false);
//                    projectNotice.setIsEnabled(true);
//                    LambdaQueryWrapper<NoticeTemplate> noticeTemplateLambdaQueryWrapper = Wrappers.lambdaQuery(NoticeTemplate.class);
//                    noticeTemplateLambdaQueryWrapper.eq(NoticeTemplate::getNoticeType, JhSystemEnum.TaskNameEnum.MESSAGE_NOTICE.getKey())
//                            .eq(NoticeTemplate::getNoticeId, relation.getNoticeId());
//                    NoticeTemplate noticeTemplate = noticeTemplateRepository.selectOne(noticeTemplateLambdaQueryWrapper);
//                    if (noticeTemplate != null) {
//                        String noticeContent = noticeTemplate.getNoticeContent();
//                        noticeContent = noticeContent.replace("{taskName}", "【" + projectInfo.getProjectName() + "-" + projectGroup.getNodeName() + "】")
//                                .replace("{taskStatus}", JhSystemEnum.TaskCCEnum.getTaskCC(taskNameEnum.getKey()).getSpec());
//                        projectNotice.setNoticeContent(noticeContent);
//                        projectNotice.setNodeId(nodeId);
//                        projectNotice.setNoticeId(noticeTemplate.getNoticeId());
//                    }
//                    MessageParameter messageParameter = new MessageParameter();
////                if(list.size()==0){
////                    messageParameter = feishuService.sendMessage(projectNotice);
////                }
////                if(messageParameter.getCode()!=null&&messageParameter.getCode()==0){
////                    projectNotice.setNoticeContent(messageParameter.getTextContent());
//                    projectNotice.setIsSend(Boolean.TRUE);
//                    projectNoticeService.save(projectNotice);
////                }
//
//                }
//
//            }
//        }
//    }

    @Override
    public void generateTodoTask(ProjectGroup projectGroup) {
        String roleCode = projectGroup.getRoleCode();
        if (StringUtils.isEmpty(roleCode)) {
            return;
        }
        String[] roleCodes = roleCode.split(",");
        //拿到roleCode，去项目干系人查询
        List<String> roleCodeList = Arrays.asList(roleCodes);

        //查询当前二级是否存在【未完成】的消息
        LambdaQueryWrapper<ProjectTaskInfo> projectTaskInfoLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTaskInfo.class)
                .eq(ProjectTaskInfo::getNodeId, projectGroup.getProjectGroupId())
                .eq(ProjectTaskInfo::getTaskStatus, KidsSystemEnum.TaskStatusEnum.UNFINISH.getValue());
        List<ProjectTaskInfo> tasklist = projectTaskInfoService.list(projectTaskInfoLambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(tasklist)) {
            return;
        }
        LambdaQueryWrapper<ProjectStakeholders> stakeholdersQuery = Wrappers.lambdaQuery(ProjectStakeholders.class);
        if (projectGroup.getProjectId() != null && projectGroup.getOrderId() == null) {
            //in_term 正常状态才会发送代办，off_term 离项的人不会发送代办
            stakeholdersQuery.eq(ProjectStakeholders::getProjectId, projectNodeInfoService.getSubmeterProjectId(projectGroup.getProjectId()))
                    .in(ProjectStakeholders::getRoleCode, roleCodeList)
                    .eq(ProjectStakeholders::getIsDelete, 0)
                    .eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
        } else {
            stakeholdersQuery.in(ProjectStakeholders::getRoleCode, roleCodeList)
                    .eq(ProjectStakeholders::getIsDelete, 0)
                    .eq(ProjectStakeholders::getOrderId, projectGroup.getOrderId());
        }


        List<ProjectStakeholders> stakeholders = projectStakeholdersRepository.selectList(stakeholdersQuery);
        if (CollectionUtils.isNotEmpty(stakeholders)) {
            Map<Long, ProjectStakeholders> collect = stakeholders.stream().collect(Collectors.toMap(i -> i.getUserId(), j -> j, (k1, k2) -> k1));
            List<ProjectTaskInfo> list = new ArrayList<>();
            for (Long aLong : collect.keySet()) {
                ProjectStakeholders s = collect.get(aLong);
                ProjectTaskInfo projectTaskInfo = new ProjectTaskInfo();
                projectTaskInfo.setTaskName(KidsSystemEnum.TaskTypeEnum.TODO.getLabel());
                projectTaskInfo.setProjectId(projectGroup.getProjectId() != null ? projectGroup.getProjectId() : null);
                projectTaskInfo.setUserId(s.getUserId());
                projectTaskInfo.setNodeName(projectGroup.getNodeName());
                projectTaskInfo.setTaskType(KidsSystemEnum.TaskTypeEnum.TODO.getValue());
                projectTaskInfo.setNodeId(projectGroup.getProjectGroupId());
                projectTaskInfo.setTaskStatus(KidsSystemEnum.TaskStatusEnum.UNFINISH.getValue());
                projectTaskInfo.setOrderId(projectGroup.getOrderId() != null ? projectGroup.getOrderId() : null);
                projectTaskInfo.setUseCase("1");
                projectTaskInfo.setAddress(AtourSystemEnum.jumpAddressEnum.SECOND.getKey());
                if (projectGroup.getProjectId() != null) {
                    //根据projectGroup数据查询 项目数据
                    Long projectId = projectNodeInfoService.getSubmeterProjectId(projectGroup.getProjectId());
                    ProjectInfo projectInfo = Optional.ofNullable(projectInfoService.getById(projectId)).orElseGet(ProjectInfo::new);
                    if (ObjectUtil.isNotEmpty(projectInfo)) {
                        String storeName = projectInfo.getStoreName();
                        String projectName = projectInfo.getProjectName();
                        if (ObjectUtil.isNotEmpty(storeName)) {
                            if (ObjectUtil.isNotEmpty(projectName)) {
                                projectTaskInfo.setProjectName(storeName + "-" + projectName);
                            } else {
                                projectTaskInfo.setProjectName(storeName);
                            }
                        } else {
                            if (ObjectUtil.isNotEmpty(projectName)) {
                                projectTaskInfo.setProjectName(projectName);
                            } else {
                                projectTaskInfo.setProjectName(null);
                            }
                        }
                        projectTaskInfo.setStoreType(projectInfo.getStoreType());
                        projectTaskInfo.setProjectType(projectInfo.getProjectType());
                    }
                    if (JhSystemEnum.SupplierTypeEnum.SUPPLIER_PM.getKey().equals(projectGroup.getTemplateCode())) {
                        SupplierPm pm = supplierPmService.getById(projectGroup.getProjectId());
                        if (ObjectUtil.isNotEmpty(pm)) projectTaskInfo.setProjectName(pm.getPmName());
                    }else if (JhSystemEnum.SupplierTypeEnum.SUPPLIER.getKey().equals(projectGroup.getTemplateCode())) {
                        SupplierInfo si = supplierInfoService.getById(projectGroup.getProjectId());
                        if (ObjectUtil.isNotEmpty(si)) projectTaskInfo.setProjectName(si.getSupNameCn());
                    }
                } else if (projectGroup.getOrderId() != null) {
                    Long orderId = projectGroup.getOrderId();
                    OrderInfo orderInfo = Optional.ofNullable(orderInfoService.getById(orderId)).orElseGet(OrderInfo::new);
                    Long storeId = orderInfo.getStoreId();
                    StoreMasterInfo storeMasterInfo = Optional.ofNullable(storeMasterInfoService.getById(storeId)).orElseGet(StoreMasterInfo::new);
                    projectTaskInfo.setProjectName(storeMasterInfo.getStoreName());
                    projectTaskInfo.setStoreType(storeMasterInfo.getStoreType());
                    projectTaskInfo.setProjectType(projectGroup.getTemplateCode());
                }
                projectTaskInfo.setJobId(s.getRoleId());
                projectTaskInfo.setJobName(s.getRoleName());
                projectTaskInfo.setPlanEndDate(projectGroup.getPlanEndDate() != null ? DateUtil.stringToDate(String.valueOf(projectGroup.getPlanEndDate())) : null);
                projectTaskInfo.setIsDelete(false);
                projectTaskInfo.setIsEnabled(true);
                list.add(projectTaskInfo);
            }
//            stakeholders.forEach(s -> {
//                ProjectTaskInfo projectTaskInfo = new ProjectTaskInfo();
//                projectTaskInfo.setTaskName(KidsSystemEnum.TaskTypeEnum.TODO.getLabel());
//                projectTaskInfo.setProjectId(projectGroup.getProjectId() != null ? projectGroup.getProjectId() : null);
//                projectTaskInfo.setUserId(s.getUserId());
//                projectTaskInfo.setNodeName(projectGroup.getNodeName());
//                projectTaskInfo.setTaskType(KidsSystemEnum.TaskTypeEnum.TODO.getValue());
//                projectTaskInfo.setNodeId(projectGroup.getProjectGroupId());
//                projectTaskInfo.setTaskStatus(KidsSystemEnum.TaskStatusEnum.UNFINISH.getValue());
//                projectTaskInfo.setOrderId(projectGroup.getOrderId() != null ? projectGroup.getOrderId() : null);
//                projectTaskInfo.setUseCase("1");
//                projectTaskInfo.setAddress(AtourSystemEnum.jumpAddressEnum.SECOND.getKey());
//                if (projectGroup.getProjectId() != null) {
//                    //根据projectGroup数据查询 项目数据
//                    Long projectId = projectNodeInfoService.getSubmeterProjectId(projectGroup.getProjectId());
//                    ProjectInfo projectInfo = Optional.ofNullable(projectInfoService.getById(projectId)).orElseGet(ProjectInfo::new);
//                    if (ObjectUtil.isNotEmpty(projectInfo)) {
//                        String storeName = projectInfo.getStoreName();
//                        String projectName = projectInfo.getProjectName();
//                        if (ObjectUtil.isNotEmpty(storeName)) {
//                            if (ObjectUtil.isNotEmpty(projectName)) {
//                                projectTaskInfo.setProjectName(storeName + "-" + projectName);
//                            } else {
//                                projectTaskInfo.setProjectName(storeName);
//                            }
//                        } else {
//                            if (ObjectUtil.isNotEmpty(projectName)) {
//                                projectTaskInfo.setProjectName(projectName);
//                            } else {
//                                projectTaskInfo.setProjectName(null);
//                            }
//                        }
//                        projectTaskInfo.setStoreType(projectInfo.getStoreType());
//                        projectTaskInfo.setProjectType(projectInfo.getProjectType());
//                    }
//                } else if (projectGroup.getOrderId() != null) {
//                    Long orderId = projectGroup.getOrderId();
//                    OrderInfo orderInfo = Optional.ofNullable(orderInfoService.getById(orderId)).orElseGet(OrderInfo::new);
//                    Long storeId = orderInfo.getStoreId();
//                    StoreMasterInfo storeMasterInfo = Optional.ofNullable(storeMasterInfoService.getById(storeId)).orElseGet(StoreMasterInfo::new);
//                    projectTaskInfo.setProjectName(storeMasterInfo.getStoreName());
//                    projectTaskInfo.setStoreType(storeMasterInfo.getStoreType());
//                    projectTaskInfo.setProjectType(projectGroup.getTemplateCode());
//                }
//                projectTaskInfo.setJobId(s.getRoleId());
//                projectTaskInfo.setJobName(s.getRoleName());
//                projectTaskInfo.setPlanEndDate(projectGroup.getPlanEndDate() != null ? DateUtil.stringToDate(String.valueOf(projectGroup.getPlanEndDate())) : null);
//                projectTaskInfo.setIsDelete(false);
//                projectTaskInfo.setIsEnabled(true);
//                list.add(projectTaskInfo);
//            });
            projectTaskInfoService.saveBatch(list);
            //发送信息，出错有日志，不影响程序运行
            sendMyMessage(projectGroup.getProjectId(),projectGroup.getProjectGroupId().toString(), projectGroup.getNodeCode(), new StringBuffer("【营建新系统】待办提醒："), null, (projectGroup.getPlanEndDate() != null ? DateUtil.stringToDate(String.valueOf(projectGroup.getPlanEndDate())).toString() : null));
        }
    }


    /**
     * @param messageTitle
     */
    private void sendMyMessage(Long projectId, String projectGroupId,String nodeCode, StringBuffer messageTitle, String userId, String endDate) {
        //发送信息对应负责任信息
        try {
            //借用store_name存储 nodeName，借用province_name存储roleCode，借用 remark 存储 nodeId
            projectId = projectNodeInfoService.getSubmeterProjectId(projectId);
            ProjectInfo myInfo = projectInfoRepository.getPrjectNameAndNodeNameRolCode(projectId, nodeCode,projectGroupId);
            if (myInfo != null && StringUtils.isNotEmpty(myInfo.getProvinceName())) {
                List<User> userList = userRepository.findUserByGroupRoleCode(myInfo.getProvinceName(), projectId, userId);
                if (userList != null && userList.size() > 0) {
                    List<String> mobileList = new ArrayList<>();  //手机号发送短信
                    List<SendNotifyCenterTargetsVo> centerTargetsVos = new ArrayList<>();
                    for (User user : userList) {
                        if (StringUtils.isNotEmpty(user.getPhone()) && ObjectUtil.isNotEmpty(user.getTk()) && user.getTk().equals("atour")) {
                            mobileList.add(user.getPhone());
                        }
                        if (StringUtils.isNotEmpty(user.getPhone()) && (ObjectUtil.isEmpty(user.getTk()) || !user.getTk().equals("atour"))) {
                            SendNotifyCenterTargetsVo targetsVo = new SendNotifyCenterTargetsVo();
                            targetsVo.setMobile(user.getPhone());
                            centerTargetsVos.add(targetsVo);
                        }
                    }
                    String description = this.getDescription(myInfo, endDate, null);
                    if (mobileList.size() > 0) {
                        //发送企微
                        portalService.sendMessage(mobileList, messageTitle.toString(), description);
                    }
                    if (ObjectUtil.isNotEmpty(centerTargetsVos)) {
                        //发送短信
                        this.sendAsynchronous(centerTargetsVos, messageTitle.append(description).toString());
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public String getDescription(ProjectInfo myInfo, String endDate, String type) {

            // 开工申请、样板间验收申请、隐蔽验收申请、竣工自检申请(正常提交流程)
        if("01".equals(type)) {
            return "「" + myInfo.getStoreName() + "」的任务已提交。项目名：" + myInfo.getProjectName() + "，酒店ID：" + myInfo.getProjectNo() +
                    ",请您确认信息是否正确。\n系统链接：https://acms.yaduo.com/";
            // 一致性节点 签证报备
        } else if("02".equals(type)) {
            return "「" + myInfo.getStoreName() + "」的任务已提交,由负责人申请报备。项目名：" + myInfo.getProjectName() + "，酒店ID：" + myInfo.getProjectNo() +
                    ",请您发起签证报备。\n系统链接：https://acms.yaduo.com/";
            // 逾期任务(定时任务)
        } else if("03".equals(type)) {
            return "「" + myInfo.getStoreName() + "」的任务已超期。项目名：" + myInfo.getProjectName() + "，酒店ID：" + myInfo.getProjectNo() +
                    ",请您发起签证报备。\n系统链接：https://acms.yaduo.com/";
        }

        if(ObjectUtil.isEmpty(type)){
            if (ObjectUtil.isNotEmpty(endDate)) {
                return "您有新的「" + myInfo.getStoreName() + "」任务。项目名：" + myInfo.getProjectName() + "，酒店ID：" + myInfo.getProjectNo() +
                        "，任务结束时间是：" + endDate +
                        ",请您尽快登录系统进行处理。\n系统链接：https://acms.yaduo.com/";
            } else {
                return "您有新的「" + myInfo.getStoreName() + "」任务。项目名：" + myInfo.getProjectName() + "，酒店ID：" + myInfo.getProjectNo() +
                        ",请您尽快登录系统进行处理。\n系统链接：https://acms.yaduo.com/";
            }
        }
        return null;
    }

    @Override
    public void finishTask(ProjectGroup projectGroup) {
        Timestamp d = new Timestamp(System.currentTimeMillis());
        //查找已出任务，更新为已完成
        LambdaQueryWrapper<ProjectTaskInfo> projectTaskInfoLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectTaskInfo.class);
        projectTaskInfoLambdaQueryWrapper.eq(ProjectTaskInfo::getNodeId, projectGroup.getProjectGroupId());
        List<ProjectTaskInfo> projectTaskInfos = projectTaskInfoService.list(projectTaskInfoLambdaQueryWrapper);
        if (projectTaskInfos.size() > 0) {
            for (ProjectTaskInfo projectTaskInfo : projectTaskInfos) {
                if (!(projectTaskInfo.getNodeName().contains("(审批)"))) {

                    projectTaskInfo.setTaskStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey());
                    projectTaskInfo.setPlanEndDate(new java.sql.Date(d.getTime()));
                    projectTaskInfoService.updateById(projectTaskInfo);
                }
            }
        }
    }


}
