package com.bassims.modules.atour.requestParam;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class WorkFlowContractFormData {
    //申请人工号
    private String submitUserCode;
    //合同类型
    // 1000：装修合同 2000：空调合同 3000：消防合同
    private String contractType;
    //合同类型名称
    //装修合同    空调合同 消防合同
    private String contractTypeName;
    //合同名称
    private String contractName;
    //合同归属编码
    private String contractOwnCode;
    //合同归属名称
    private String contractOwnName;
    //对方单位名称
    private String supplierName;
    //合同开始日期
    private String startDate;
    //合同结束日期
    private String endDate;
    //付款总金额
    private BigDecimal totalAmount;
    //发票类型
    private String invoiceType;
    //发票税率
    private BigDecimal feeFax;
    //合同描述
    private String contractexplain;
    //外部系统编码 101
    private String sysCode="101";
    //外部系统名称 工程营建
    private String sysName="工程营建";
    //合同区域	101:江;102:浙;103:沪;104:徽;999=其他
    private String contractArea;
    //是否付款 yes:是;no:否;
    private String isPay;
    //费用是否在签报、预案限定的标准内 yes:是;no:否;
    private String isFeeStandardIn;
    //是否新店项目 yes:是;no:否;
    private String isNewProject;
    //是否续约合同 yes:是;no:否;
    private String isRenewContract;
    //付款方
    private String payer;
    //付款方式  accounting_period:账期
    private String payType;
    //付款币别  rmb:人民币;dollar:美元;
    private String payCurrency;
    //合同电子件	附件url
    private List<ContactFile> electronicContact;
    //合同送签依据	附件url
    private List<ContactFile> contactSignBasis;
    //申请单号 营建系统申请单号
    private String businessCode;
    //申请人姓名；
    private String name;
    //申请人岗位（待定）；
    private String position;
    //主题；
    private String title;
    //申请人工号；
    private String number;
    //申请人电话
    private String phone;
    //申请人部门
    private String department;

}
