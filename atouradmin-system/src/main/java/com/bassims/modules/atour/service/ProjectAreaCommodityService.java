package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.ProjectAreaCommodity;
import com.bassims.modules.atour.service.dto.ProjectAreaCommodityQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * 商品面积
 */
public interface ProjectAreaCommodityService extends BaseService<ProjectAreaCommodity>{
    /**
     * 下载
     *
     * @param response 响应
     * @param criteria 标准
     */
    void download(HttpServletResponse response, ProjectAreaCommodityQueryCriteria criteria) throws IOException;

    /**
     * 查询明细
     *
     * @param criteria 标准
     * @param pageable
     * @return Map<String,Object>
     */
    Map<String,Object> queryAll(ProjectAreaCommodityQueryCriteria criteria, Pageable pageable);
}
