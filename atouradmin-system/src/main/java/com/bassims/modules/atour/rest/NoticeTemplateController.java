/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.modules.atour.domain.NoticeTemplate;
import com.bassims.modules.atour.service.NoticeTemplateService;
import com.bassims.modules.atour.service.dto.NoticeTemplateQueryCriteria;
import com.bassims.modules.quartz.utils.CreateNoUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-03-30
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_notice_template管理")
@RequestMapping("/api/noticeTemplate")
public class NoticeTemplateController {

    private static final Logger logger = LoggerFactory.getLogger(NoticeTemplateController.class);

    private final NoticeTemplateService noticeTemplateService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, NoticeTemplateQueryCriteria criteria) throws IOException {
        noticeTemplateService.download(noticeTemplateService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity <List<NoticeTemplateDto>>}
    */
    @GetMapping("/list")
//    @Log("查询t_notice_template")
    @ApiOperation("查询t_notice_template")
    @MyDataPermission(title = "消息配置")
    public ResponseEntity<Object> query(NoticeTemplateQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(noticeTemplateService.queryAll(criteria,pageable), HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity <NoticeTemplateDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询t_notice_template")
    @ApiOperation("查询t_notice_template")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(noticeTemplateService.findById(id), HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增t_notice_template")
    @ApiOperation("新增t_notice_template")
    @MyDataPermission(title = "系统管理")
    public ResponseEntity<Object> create(@Validated @RequestBody NoticeTemplate resources){
        String noticeCode = CreateNoUtils.createNo("MB");
        resources.setNoticeCode(noticeCode);
        resources.setIsDelete(false);
        resources.setIsEnabled(true);
        return new ResponseEntity<>(noticeTemplateService.create(resources), HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改t_notice_template")
    @ApiOperation("修改t_notice_template")
    public ResponseEntity<Object> update(@Validated @RequestBody NoticeTemplate resources){
        noticeTemplateService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_notice_template")
    @ApiOperation("删除t_notice_template")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        noticeTemplateService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}