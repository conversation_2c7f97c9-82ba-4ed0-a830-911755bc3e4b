/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2022-09-15
 **/
@Data
public class SupplierInfoDto implements Serializable {

    /**
     * 厂商ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private String id;

    /**
     * 厂商NO
     */
    private String supplierNum;

    private Long supplierVersionId;

    /**
     * 厂商编号
     */
    private String supplierCode;

    /**
     * 厂商中文名称
     */
    private String supNameCn;

    /**
     * 厂商英文名称
     */
    private String supNameEn;

    /**
     * 厂商简称
     */
    private String supShortName;

    /**
     * 省份
     */
    private Long supProvince;

    /**
     * 城市
     */
    private String supCity;

    private String supPostCode;

    /**
     * 厂商注册日期
     */
    private Timestamp compRegDate;

    /**
     * 厂商注册地址
     */
    private String compRegAddr;

    private String effectStatus;

    private String versionStatus;

    /**
     * 厂商状态
     */
    private String supStatus;

    /**
     * 是否一次性厂商
     */
    private String isDisposable;

    /**
     * 联系人(公司法人)
     */
    private String contact;

    /**
     * 联系电话(公司法人)
     */
    private String phone;

    /**
     * 邮箱(公司法人)
     */
    private String email;

    /**
     * 传真(公司法人)
     */
    private String fax;

    private Timestamp bizlicExpDate;

    private Timestamp sppExpDate;

    /**
     * 税务登记属性
     */
    private String taxRegAttr;

    private String taxSaleRate;

    /**
     * 税率(服务)
     */
    private String taxServiceRate;

    private Timestamp trcExpDate;

    private String logisticsTerms;

    private Long parentId;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 是否激活
     */
    private Integer isActive;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 更新人
     */
    private Long updateUser;

    /**
     * 更新时间
     */
    private Timestamp updateTime;

    /**
     * 是否删除
     */
    private Integer isDelete;
    /**
     * 营业执照
     */
    private String businessLicense;
    /**
     * 营业执照开始时间-结束时间
     */
    private String businessExpDate;

    /**
     * 服务开始时间
     */
    private Timestamp serviceStartDate;

    /**
     * 服务范围
     */
    private String serviceScope;

    /**
     * 服务区域
     */
    private Set<Long> serviceAreas;

    /**
     * 评级
     */
    private String supplierStarbucksRating;

    /**
     * 平均质量评分
     */
    private BigDecimal avgQualityScore;

    /**
     * 开户名字(中文)
     */
    private String bankAccountName;

    /**
     * 银行名字(中文，银行全称)
     */
    private String bankNameCn;

    /**
     * 银行名字(英文)
     */
    private String bankNameEn;

    /**
     * 银行支行名字(中文)
     */
    private String bankBranchCn;

    /**
     * 银行支行城市(中文)
     */
    private String bankBranchCityCn;

    /**
     * 账户号
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private String bankAccountNumber;

    /**
     * 税号
     */
//    @JSONField(serializeUsing = ToStringSerializer.class)
    private String taxNumber;

    /**
     * 大盘类型
     */
    private String marketType;

    /**
     * 推荐人
     */
    private String referrer;

    /**
     * 联系方式
     */
    private String contactWay;

    /**
     * 已缴纳质保金(元)
     */
    private BigDecimal payRetentionMoney;

    /**
     * 已退还质保金(元)
     */
    private BigDecimal refundWarrantyDeposit;

    /**
     * 是否甲供材单位
     */
    private Boolean isFirstMaterialSupplier;

    /**
     * 是否需要测试
     */
    private Boolean isTest;

    /**
     * 厂商所属法人公司
     */
    private String legalEntity;

    /**
     * 联系人(项目召集人)
     */
    private String projectConvenor;

    /**
     * 联系电话(项目召集人)
     */
    private String projectConvenorPhone;

    /**
     * 邮箱(项目召集人)
     */
    private String projectConvenorEmail;

    /**
     * 安全施工许可
     */
    private String decorationQualification;

    /**
     * 安全施工许可开始-结束时间
     */
    private String decorationExpDate;

    /**
     * 资质文件
     */
    private String electromechanicalQualification;
    /**
     * 资质文件类型
     */
    private String  electromechanicalQualificationType;

    /**
     * 资质文件开始-结束时间
     */
    private String electromechanicalExpDate;

    /**
     * 安全生产许可证有效期到期日
     */
    private String splExpDate;

    /**
     * 税务登记证有效期到期日
     */
    private Timestamp tralExpDate;

    /**
     * 厂商工程范围
     */
    private String serviceContent;

    /**
     * 批准项目经理数
     */
    private Integer projectManagerAmount;

    /**
     * 是否使用
     */
    private Integer isUsed;

    /**
     * 传真(项目召集人)
     */
    private String projectConvenorFax;

    /**
     * 其他施工资质证照有效期到期日
     */
    private Timestamp otherLicenceExpDate;

    /**
     * 变更说明
     */
    private String changeDesc;

    /**
     * 厂商邮编
     */
    private String postalCode;

    /**
     * 付款条件
     */
    private String payTerm;

    /**
     * 厂商地址
     */
    private String supplierAddress;

    /**
     * 厂商管理员id
     */
    private String userId;

    /**
     * 厂商联系人
     */
    private List<SupplierContactsDto> supplierContactsDtoList;

    /**
     * 累计已完成项目量数
     */
    private Integer finishAmount;

    /**
     * 在建项目数
     */
    private Integer doingAmount;

    /**
     * 合同号
     */
    private String contractNo;

    /**
     * 附件id
     */
    private String fileId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 更新人名称
     */
    private String updateUserName;

    /**
     * 是否平台供应商（ 码值 platform_provider_not）   平台供应商	platform 非平台供应商	non_platform
     */
    private String platformProviderNot;

    /**
     * 供应商类型（码值）
     */
    private String supplierType;

    /** atos主键id */
    private String tkId;

    /**案例介绍JSON*/
    private String caseStudy;

    /**备注*/
    private String remark;
}