/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.BudgetVersion;
import com.bassims.modules.atour.domain.ProjectApprove;
import com.bassims.modules.atour.domain.ProjectGroup;
import com.bassims.modules.atour.service.dto.BudgetVersionDto;
import com.bassims.modules.atour.service.dto.BudgetVersionQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2022-11-30
**/
public interface BudgetVersionService extends BaseService<BudgetVersion> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(BudgetVersionQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<BudgetVersionDto>
    */
    List<BudgetVersionDto> queryAll(BudgetVersionQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param budgetVersionId ID
     * @return BudgetVersionDto
     */
    BudgetVersionDto findById(Long budgetVersionId);

    /**
    * 创建
    * @param resources /
    * @return BudgetVersionDto
    */
    BudgetVersionDto create(BudgetVersion resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(BudgetVersion resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<BudgetVersionDto> all, HttpServletResponse response) throws IOException;

    /**
     * 拒绝存入数据
     * @param projectApprove
     * @return
     */
    Boolean createBudgetVersion(ProjectApprove projectApprove);

    /**
     * 审批通过入数据
     * @param projectGroup
     * @return
     */
    Boolean createLastVersion(ProjectGroup projectGroup);

    /**
     * 获取版本数据
     * @param projectId
     * @param nodeCode
     * @return
     */
    List<BudgetVersionDto> getBudgetVersionList(Long projectId, String nodeCode);
}