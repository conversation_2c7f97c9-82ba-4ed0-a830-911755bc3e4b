/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.TemplateOutputRelation;
import com.bassims.modules.atour.service.TemplateOutputRelationService;
import com.bassims.modules.atour.service.dto.TemplateOutputRelationDto;
import com.bassims.modules.atour.service.dto.TemplateOutputRelationQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-10-23
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "节点的输出关系表管理")
@RequestMapping("/api/templateOutputRelation")
public class TemplateOutputRelationController {

    private static final Logger logger = LoggerFactory.getLogger(TemplateOutputRelationController.class);

    private final TemplateOutputRelationService templateOutputRelationService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, TemplateOutputRelationQueryCriteria criteria) throws IOException {
        templateOutputRelationService.download(templateOutputRelationService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<TemplateOutputRelationDto>>}
    */
    @GetMapping("/list")
    @Log("查询节点的输出关系表")
    @ApiOperation("查询节点的输出关系表")
    public ResponseEntity<Object> query(TemplateOutputRelationQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(templateOutputRelationService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<TemplateOutputRelationDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询节点的输出关系表")
    @ApiOperation("查询节点的输出关系表")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(templateOutputRelationService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增节点的输出关系表")
    @ApiOperation("新增节点的输出关系表")
    public ResponseEntity<Object> create(@Validated @RequestBody TemplateOutputRelation resources){
        return new ResponseEntity<>(templateOutputRelationService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改节点的输出关系表")
    @ApiOperation("修改节点的输出关系表")
    public ResponseEntity<Object> update(@Validated @RequestBody TemplateOutputRelation resources){
        templateOutputRelationService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除节点的输出关系表")
    @ApiOperation("删除节点的输出关系表")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        templateOutputRelationService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}