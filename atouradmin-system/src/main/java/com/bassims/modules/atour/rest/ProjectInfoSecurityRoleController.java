/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.modules.atour.domain.ProjectInfoSecurityRole;
import com.bassims.modules.atour.service.ProjectInfoSecurityRoleService;
import com.bassims.modules.atour.service.dto.ProjectInfoSecurityRoleDto;
import com.bassims.modules.atour.service.dto.ProjectInfoSecurityRoleQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-12-05
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "项目可查看保密项目的角色配置管理")
@RequestMapping("/api/projectInfoSecurityRole")
public class ProjectInfoSecurityRoleController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectInfoSecurityRoleController.class);

    private final ProjectInfoSecurityRoleService projectInfoSecurityRoleService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ProjectInfoSecurityRoleQueryCriteria criteria) throws IOException {
        projectInfoSecurityRoleService.download(projectInfoSecurityRoleService.queryAll(), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<ProjectInfoSecurityRoleDto>>}
    */
    @GetMapping("/list")
    @Log("查询项目可查看保密项目的角色配置")
    @ApiOperation("查询项目可查看保密项目的角色配置")
    @MyDataPermission(title = "营建对接启动,项目推进,数据处理,批量变更干系人,供应商清单,供应商人员清单")
    public ResponseEntity<Object> query(){
        return new ResponseEntity<>(projectInfoSecurityRoleService.queryAll(),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<ProjectInfoSecurityRoleDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询项目可查看保密项目的角色配置")
    @ApiOperation("查询项目可查看保密项目的角色配置")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(projectInfoSecurityRoleService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增项目可查看保密项目的角色配置")
    @ApiOperation("新增项目可查看保密项目的角色配置")
    public ResponseEntity<Object> create(@Validated @RequestBody ProjectInfoSecurityRole resources){
        return new ResponseEntity<>(projectInfoSecurityRoleService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改项目可查看保密项目的角色配置")
    @ApiOperation("修改项目可查看保密项目的角色配置")
    public ResponseEntity<Object> update(@Validated @RequestBody ProjectInfoSecurityRole resources){
        projectInfoSecurityRoleService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除项目可查看保密项目的角色配置")
    @ApiOperation("删除项目可查看保密项目的角色配置")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        projectInfoSecurityRoleService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}