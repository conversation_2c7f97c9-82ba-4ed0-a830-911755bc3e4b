/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bassims.modules.atour.domain.ProjectInfo;
import com.bassims.modules.atour.domain.TemplateGroup;
import com.bassims.modules.atour.domain.TemplateQueue;
import com.bassims.modules.atour.domain.vo.ReceiptInfo;
import com.bassims.modules.atour.service.dto.ProjectInfoDto;
import com.bassims.modules.atour.service.dto.ProjectInfoQueryCriteria;
import com.bassims.modules.system.domain.Job;
import com.bassims.modules.system.domain.Role;
import com.bassims.utils.ValidationUtil;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-03-24
**/
@Repository
public interface ProjectInfoRepository extends BaseMapper<ProjectInfo> {

    List<ProjectInfoDto> getListByCriteria(ProjectInfoQueryCriteria criteria);

    ProjectInfoDto getFindById(Long projectId) ;

    List<Role> getPermissionmode(Long userId);
    List<Long> getProjectIdsByCity(Long userId,Boolean delete, Integer preparationDocking,String projectTaskPhase,String taskPhase);

    List<Long> getAdjustProjectIdsByCity(Long userId,Boolean delete);

    List<ProjectInfo> getDistinctInfoByStoreNoAndName(@Param("storeId") Long storeId);

    /**
     * 借用store_name存储 nodeName，借用province_name存储roleCode
     * @param projectId
     * @param nodeCode
     * @return
     */
    ProjectInfo getPrjectNameAndNodeNameRolCode(@Param("projectId") Long projectId,@Param("nodeCode")String nodeCode,@Param("projectGroupId")String projectGroupId);
    List<Long> getProjectIdsByStakeholders(Long userId,Boolean delete, Integer preparationDocking,String projectTaskPhase,String taskPhase);
    List<ProjectInfoDto> getByStakeholders(Long userId,Long leavingUser);

    List<Long> getAdjustProjectIdsByStakeholders(Long userId,Boolean delete);
    List<Long> getProjectIdsByUser(Long userId,Boolean delete);

    List<Long> getOutPersonAccessIds(Long userId);

    List<ProjectInfo> getProjectTaskUserByNodeCode(@Param("userId") Long userId,@Param("projectIds") List<Long> projectIds,@Param("taskType") String taskType,@Param("nodeCode") String nodeCode);
    List<ProjectInfo> getProjectTaskCityByNodeCode(@Param("userId") Long userId,@Param("projectIds") List<Long> projectIds,@Param("taskType") String taskType,@Param("nodeCode") String nodeCode);
    List<ProjectInfo> getProjectTaskStaByNodeCode(@Param("userId") Long userId,@Param("projectIds") List<Long> projectIds,@Param("taskType") String taskType,@Param("nodeCode") String nodeCode);

    ProjectInfo taskQueryByProjectId(Long projectId);
    Job getJobByJobCode(String jobCode);
    Integer getTotalProject(Long userId);
    Integer getBuildProject(Long userId);
    Integer getThisYearProject(Long userId);
    Integer getFinishProject(Long userId);
    Integer getOverDueProject(Long userId);


    Integer getTotalProjectForSta(Long userId);
    Integer getBuildProjectForSta(Long userId);
    Integer getThisYearProjectForSta(Long userId);
    Integer getFinishProjectForSta(Long userId);
    Integer getOverDueProjectForSta(Long userId);

    //查询所有逾期的项目 城市
    List<Long> getOverDuePercentForCity(String querydate,Long userId);
    //查询所有逾期的项目 干系人
    List<Long> getOverDuePercentForSta(String querydate,Long userId);
    //查询所有阶段的项目 城市
    List<ProjectInfo> getPhasePercentForCity(String querydate,Long userId);
    List<ProjectInfo> getPercentForCity(Long userId);


    List<ProjectInfo> getPhasePercentForSta(String querydate,Long userId);
    List<ProjectInfo> getPercentForSta(Long userId);

    List<ProjectInfoDto> getCityLongsByCity(Long userId);
    List<ProjectInfoDto> getCityLongsBySta(Long userId);


    Role getRoleByRoleCode(String roleCode);
    List<Role> getRoleByRoleCodeList(String[] roleCodes);

    List<ProjectInfoDto> getAllProjectByCity(Long userId);

    List<ProjectInfoDto> getAllProjectBySta(Long userId);

    //查询结算所有逾期的项目 城市
    List<Long> getAccountOverDuePercentForCity(String querydate,Long userId);
    //查询结算所有逾期的项目 干系人
    List<Long> getAccountOverDuePercentForSta(String querydate,Long userId);
    //查询结算所有阶段的项目 城市
    List<ProjectInfo> getAccountPhasePercentForCity(String querydate,Long userId);


    List<ProjectInfo> getAccountPhasePercentForSta(String querydate,Long userId);

    List<Long> getUserIdByRegion(String region,String roleCode);

    List<ProjectInfo> getOldProjectData();

    List<TemplateQueue> getTeamplateQueueTwoThreeByTemplateCode(List<String> templateCodes);

    List<TemplateGroup> getGroupByTemplateCode(List<String> templateCodes);

    ReceiptInfo downloadMyFile(Long projectId);

    String getUserNameByUserId(Long userId);

    List<ProjectInfoDto> getDownloadData();
}