/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.domain.CompletionAcceptancePicture;
import com.bassims.modules.atour.repository.CompletionAcceptancePictureRepository;
import com.bassims.modules.atour.service.CompletionAcceptancePictureService;
import com.bassims.modules.atour.service.dto.CompletionAcceptancePictureDto;
import com.bassims.modules.atour.service.dto.CompletionAcceptancePictureQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.CompletionAcceptancePictureMapper;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-04-20
**/
@Service
public class CompletionAcceptancePictureServiceImpl extends BaseServiceImpl<CompletionAcceptancePictureRepository,CompletionAcceptancePicture> implements CompletionAcceptancePictureService {

    private static final Logger logger = LoggerFactory.getLogger(CompletionAcceptancePictureServiceImpl.class);

    @Autowired
    private CompletionAcceptancePictureRepository completionAcceptancePictureRepository;
    @Autowired
    private CompletionAcceptancePictureMapper completionAcceptancePictureMapper;

    @Override
    public Map<String,Object> queryAll(CompletionAcceptancePictureQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<CompletionAcceptancePicture> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(CompletionAcceptancePicture.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", completionAcceptancePictureMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<CompletionAcceptancePictureDto> queryAll(CompletionAcceptancePictureQueryCriteria criteria){
        return completionAcceptancePictureMapper.toDto(list(QueryHelpPlus.getPredicate(CompletionAcceptancePicture.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CompletionAcceptancePictureDto findById(Long pictureId) {
        CompletionAcceptancePicture completionAcceptancePicture = Optional.ofNullable(getById(pictureId)).orElseGet(CompletionAcceptancePicture::new);
        ValidationUtil.isNull(completionAcceptancePicture.getPictureId(),getEntityClass().getSimpleName(),"pictureId",pictureId);
        return completionAcceptancePictureMapper.toDto(completionAcceptancePicture);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CompletionAcceptancePictureDto create(CompletionAcceptancePicture resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setPictureId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getPictureId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(CompletionAcceptancePicture resources) {
        CompletionAcceptancePicture completionAcceptancePicture = Optional.ofNullable(getById(resources.getPictureId())).orElseGet(CompletionAcceptancePicture::new);
        ValidationUtil.isNull( completionAcceptancePicture.getPictureId(),"CompletionAcceptancePicture","id",resources.getPictureId());
        completionAcceptancePicture.copy(resources);
        updateById(completionAcceptancePicture);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long pictureId : ids) {
            completionAcceptancePictureRepository.deleteById(pictureId);
        }
    }

    @Override
    public void download(List<CompletionAcceptancePictureDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (CompletionAcceptancePictureDto completionAcceptancePicture : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("项目id", completionAcceptancePicture.getProjectId());
            map.put("项目名称", completionAcceptancePicture.getProjectName());
            map.put("验收照片名称", completionAcceptancePicture.getPictureName());
            map.put("上传人", completionAcceptancePicture.getUploadBy());
            map.put("上传时间", completionAcceptancePicture.getUploadTime());
            map.put("附件", completionAcceptancePicture.getFileId());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}