/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.bsEnum.KidsSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.ApproveTemplate;
import com.bassims.modules.atour.domain.OrderDetail;
import com.bassims.modules.atour.domain.OrderInfo;
import com.bassims.modules.atour.domain.OrderNodeInfo;
import com.bassims.modules.atour.domain.ProjectAppTemplate;
import com.bassims.modules.atour.domain.ProjectApprove;
import com.bassims.modules.atour.domain.ProjectApproveDetail;
import com.bassims.modules.atour.domain.ProjectGroup;
import com.bassims.modules.atour.domain.ProjectInfo;
import com.bassims.modules.atour.domain.ProjectStakeholders;
import com.bassims.modules.atour.domain.ProjectTaskInfo;
import com.bassims.modules.atour.domain.SpecialProject;
import com.bassims.modules.atour.domain.SupplierInfo;
import com.bassims.modules.atour.domain.SupplierMaterielInfo;
import com.bassims.modules.atour.repository.ApproveTemplateRepository;
import com.bassims.modules.atour.repository.OrderDetailRepository;
import com.bassims.modules.atour.repository.OrderInfoRepository;
import com.bassims.modules.atour.repository.OrderNodeInfoRepository;
import com.bassims.modules.atour.repository.ProjectApproveDetailRepository;
import com.bassims.modules.atour.repository.ProjectApproveRepository;
import com.bassims.modules.atour.repository.ProjectGroupRepository;
import com.bassims.modules.atour.repository.ProjectTemplateRepository;
import com.bassims.modules.atour.repository.SpecialProjectRepository;
import com.bassims.modules.atour.repository.SupplierInfoRepository;
import com.bassims.modules.atour.repository.SupplierMaterielInfoRepository;
import com.bassims.modules.atour.repository.TemplateGroupRepository;
import com.bassims.modules.atour.repository.TemplateQueueRepository;
import com.bassims.modules.atour.requestParam.IeamOrderSyncRequest;
import com.bassims.modules.atour.service.*;
import com.bassims.modules.atour.service.dto.FrontWbsConfigDto;
import com.bassims.modules.atour.service.dto.NodeInfoDto;
import com.bassims.modules.atour.service.dto.OrderDetailDto;
import com.bassims.modules.atour.service.dto.OrderInfoDto;
import com.bassims.modules.atour.service.dto.OrderNodeInfoDto;
import com.bassims.modules.atour.service.dto.OrderNodeInfoQueryCriteria;
import com.bassims.modules.atour.service.dto.ProjectGroupDto;
import com.bassims.modules.atour.service.dto.ProjectTemplateApproveRelationDto;
import com.bassims.modules.atour.service.dto.SpecialProjectDto;
import com.bassims.modules.atour.service.mapstruct.OrderDetailMapper;
import com.bassims.modules.atour.service.mapstruct.OrderInfoMapper;
import com.bassims.modules.atour.service.mapstruct.OrderNodeInfoMapper;
import com.bassims.modules.atour.service.mapstruct.ProjectGroupMapper;
import com.bassims.modules.atour.service.mapstruct.SpecialProjectMapper;
import com.bassims.modules.system.domain.DictDetail;
import com.bassims.modules.system.domain.Role;
import com.bassims.modules.system.domain.User;
import com.bassims.modules.system.repository.DictDetailRepository;
import com.bassims.modules.system.repository.RoleRepository;
import com.bassims.modules.system.repository.UserRepository;
import com.bassims.modules.system.service.TemplateConfigService;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.RedisUtils;
import com.bassims.utils.SecurityUtils;
import com.bassims.utils.StringUtils;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.excelutil.ExcelUtils;
import com.github.pagehelper.PageInfo;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-09-23
 **/
@Service
public class OrderNodeInfoServiceImpl extends BaseServiceImpl<OrderNodeInfoRepository, OrderNodeInfo> implements OrderNodeInfoService {

    private static final Logger logger = LoggerFactory.getLogger(OrderNodeInfoServiceImpl.class);

    @Autowired
    private OrderNodeInfoRepository orderNodeInfoRepository;
    @Autowired
    private OrderNodeInfoMapper orderNodeInfoMapper;
    @Autowired
    private ProjectGroupRepository projectGroupRepository;
    @Autowired
    private ProjectGroupMapper projectGroupMapper;
    @Autowired
    private ProjectAppTemplateService projectAppTemplateService;
    @Autowired
    private ProjectApproveService projectApproveService;
    @Autowired
    private ProjectGroupService projectGroupService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private ProjectTemplateApproveRelationService projectTemplateApproveRelationService;
    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private ProjectStakeholdersService projectStakeholdersService;
    @Autowired
    private OrderInfoRepository orderInfoRepository;
    @Autowired
    private OrderDetailRepository orderDetailRepository;
    @Autowired
    private SpecialProjectRepository specialProjectRepository;
    @Autowired
    private OrderDetailService orderDetailService;
    @Autowired
    private SpecialProjectService specialProjectService;
    @Autowired
    private ProjectTaskService projectTaskService;
    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Autowired
    private SpecialProjectMapper specialProjectMapper;
    @Autowired
    private OrderInfoService orderInfoService;
    @Autowired
    private ProjectInfoService projectInfoService;
    @Autowired
    private SupplierInfoRepository supplierInfoRepository;
    @Autowired
    private SupplierMaterielInfoRepository supplierMaterielInfoRepository;
    @Autowired
    private OrderStockService orderStockService;
    @Autowired
    private IeamHttpService ieamHttpService;
    @Autowired
    private TemplateConfigService templateConfigService;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private SupplierMaterielInfoService supplierMaterielInfoService;
    @Autowired
    private DictDetailRepository dictDetailRepository;
    @Autowired
    private TemplateGroupRepository templateGroupRepository;
    @Autowired
    private OrderInfoMapper orderInfoMapper;
    @Autowired
    private TemplateQueueRepository templateQueueRepository;
    @Autowired
    private ProjectTemplateRepository projectTemplateRepository;
    @Autowired
    private OrderNodeInfoService orderNodeInfoService;
    @Autowired
    private ProjectApproveRepository projectApproveRepository;
    @Autowired
    private ProjectApproveDetailRepository projectApproveDetailRepository;
    @Autowired
    private ApproveTemplateRepository approveTemplateRepository;
    @Autowired
    private EnterMasterService enterMasterService;
    @Autowired
    private ProjectToMasterService projectToMasterService;
    @Autowired
    private ProjectTaskInfoService projectTaskInfoService;
    @Autowired
    private ProjectJointTaskOnfigurationService projectJointTaskOnfigurationService;


    private static final String ORDER_NO_PREFIX = "order_no::";

    @Override
    public Map<String, Object> queryAll(OrderNodeInfoQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<OrderNodeInfo> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(OrderNodeInfo.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", orderNodeInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<OrderNodeInfoDto> queryAll(OrderNodeInfoQueryCriteria criteria) {
        return orderNodeInfoMapper.toDto(list(QueryHelpPlus.getPredicate(OrderNodeInfo.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderNodeInfoDto findById(Long nodeId) {
        OrderNodeInfo orderNodeInfo = Optional.ofNullable(getById(nodeId)).orElseGet(OrderNodeInfo::new);
        ValidationUtil.isNull(orderNodeInfo.getNodeId(), getEntityClass().getSimpleName(), "nodeId", nodeId);
        return orderNodeInfoMapper.toDto(orderNodeInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderNodeInfoDto create(OrderNodeInfo resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setNodeId(snowflake.nextId());
        save(resources);
        return findById(resources.getNodeId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(OrderNodeInfo resources) {
        OrderNodeInfo orderNodeInfo = Optional.ofNullable(getById(resources.getNodeId())).orElseGet(OrderNodeInfo::new);
        ValidationUtil.isNull(orderNodeInfo.getNodeId(), "OrderNodeInfo", "id", resources.getNodeId());
        orderNodeInfo.copy(resources);
        updateById(orderNodeInfo);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long nodeId : ids) {
            orderNodeInfoRepository.deleteById(nodeId);
        }
    }

    @Override
    public void download(List<OrderNodeInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (OrderNodeInfoDto orderNodeInfo : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("项目id", orderNodeInfo.getProjectId());
            map.put("队列id", orderNodeInfo.getTemplateQueueId());
            map.put("模板主键", orderNodeInfo.getTemplateId());
            map.put("订单id", orderNodeInfo.getOrderId());
            map.put("父节点", orderNodeInfo.getParentId());
            map.put("项目版本号", orderNodeInfo.getProjectVersion());
            map.put("节点编码", orderNodeInfo.getNodeCode());
            map.put("节点名称", orderNodeInfo.getNodeName());
            map.put("计划开始时间", orderNodeInfo.getPlanStartDate());
            map.put("计划结束时间", orderNodeInfo.getPlanEndDate());
            map.put("预估开始日期", orderNodeInfo.getPredictStartDate());
            map.put("预估结束日期", orderNodeInfo.getPredictEndDate());
            map.put("实际完成时间", orderNodeInfo.getActualEndDate());
            map.put("计划需要完成天数", orderNodeInfo.getPlanDay());
            map.put("提醒天数", orderNodeInfo.getNoticeDay());
            map.put("延期天数", orderNodeInfo.getDelayDay());
            map.put("节点序号", orderNodeInfo.getNodeWbs());
            map.put("子节点排序", orderNodeInfo.getNodeIndex());
            map.put("节点等级", orderNodeInfo.getNodeLevel());
            map.put("节点类型", orderNodeInfo.getNodeType());
            map.put("节点状态", orderNodeInfo.getNodeStatus());
            map.put("已完成按钮", orderNodeInfo.getNodeIsfin());
            map.put("前置任务配置", orderNodeInfo.getFrontWbsConfig());
            map.put("是否是关键节点", orderNodeInfo.getIsKey());
            map.put("关键节点前置任务", orderNodeInfo.getKeyFrontWbs());
            map.put("节点备注", orderNodeInfo.getRemark());
            map.put("关联nodecode", orderNodeInfo.getRelationCode());
            map.put("关联的类型", orderNodeInfo.getRelationType());
            map.put("下拉列表角色名称", orderNodeInfo.getDownCode());
            map.put("干系人角色名称", orderNodeInfo.getJobCode());
            map.put("使用场景", orderNodeInfo.getUseCase());
            map.put("节点是否打开", orderNodeInfo.getIsOpen());
            map.put(" isDelete", orderNodeInfo.getIsDelete());
            map.put("创建时间", orderNodeInfo.getCreateTime());
            map.put(" createBy", orderNodeInfo.getCreateBy());
            map.put("修改时间", orderNodeInfo.getUpdateTime());
            map.put("修改人", orderNodeInfo.getUpdateBy());
            map.put("是否可用", orderNodeInfo.getIsEnabled());
            map.put("dict转码用", orderNodeInfo.getStartSign());
            map.put("结束标志（甘特图）", orderNodeInfo.getEndSign());
            map.put("总工期", orderNodeInfo.getTotalDay());
            map.put("是否是手机端", orderNodeInfo.getIsMobile());
            map.put("责任人角色code", orderNodeInfo.getRoleCode());
            map.put("是否可以编辑", orderNodeInfo.getIsEdit());
            map.put("占位", orderNodeInfo.getSeat());
            map.put("小程序标志", orderNodeInfo.getIcon());
            map.put("公式", orderNodeInfo.getFormula());
            map.put("影响的code", orderNodeInfo.getFormulaCode());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void downloadSettleData(HttpServletResponse response, OrderNodeInfoQueryCriteria criteria) throws IOException {
        String fileName = "订单结算单(三方盖章扫描件).xlsx";

//        fileName = new String(fileName.getBytes("iso8859-1"), "utf-8");

//        fileName = URLEncoder.encode(fileName,"utf-8");
//        response.addHeader("Content-Disposition", "attachment;filename="+fileName);
//        response.setContentType("application/octet-stream");
//        response.setHeader("Pragma", "No-cache");
//        response.setHeader("Cache-Control", "no-cache");
//        response.setDateHeader("Expires", 0);
//        ServletOutputStream outputStream = response.getOutputStream();


//        fileName = URLEncoder.encode(fileName, "UTF-8");
//        response.setHeader("Content-disposition", "attachment; filename=" + fileName);
//        response.setContentType("application/msexcel;charset=UTF-8");
//        response.setHeader("Pragma", "No-cache");
//        response.setHeader("Cache-Control", "no-cache");
//        response.setDateHeader("Expires", 0);

        OutputStream outputStream = response.getOutputStream();
        String fileNameURL = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileNameURL + ";" + "filename*=utf-8''" + fileNameURL);
        response.setContentType("application/octet-stream");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        response.setCharacterEncoding("UTF-8");

        ExcelWriter excelWriter = null;
        String nodeCode = criteria.getNodeCode();
        Long orderId = criteria.getOrderId();
        Long nodeId = criteria.getNodeId();
        Boolean isMobile = criteria.getIsMobile();
        try {
//        String useCase = criteria.getUseCase();
            OrderNodeInfoDto orderNodeInfoDto = new OrderNodeInfoDto();
            if (ObjectUtils.isNotEmpty(nodeCode) && ObjectUtils.isNotEmpty(orderId)) {
                orderNodeInfoDto = this.getOrderNodeByNodeCode(orderId, nodeCode, isMobile);
            }
//        Integer sheetStart = KidsSystemEnum.TemplateConfigType.ORDER001.getSheetStart();
//        String configType = KidsSystemEnum.TemplateConfigType.ORDER001.getValue();
//        TemplateConfig byConfigType = templateConfigService.findByConfigType(configType);
//        if (byConfigType == null) {
//            throw new BadRequestException("请先配置导入模板");
//        }
//        Timestamp actualEndDate = orderNodeInfoDto.getActualEndDate();
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//        if (ObjectUtils.isEmpty(actualEndDate)){
//            throw new BadRequestException("请先完成节点");
//        }
            Map<String, String> resultMap = new HashMap<>();
//        resultMap.put("A1","门店名称");
//        resultMap.put("A2","项目名称");
//        resultMap.put("A3","供应商名称");
//        resultMap.put("A4","费用类型");
//        resultMap.put("A5","结算时间");
//        resultMap.put("A6","工程经理");
//        resultMap.put("B5",dateFormat.format(actualEndDate));
//        resultMap.put("K2","原订单金额(元)");
//        resultMap.put("K3","原订单金额(元)");
            excelWriter = ExcelUtil.getBigWriter(1000);
            List<OrderNodeInfoDto> list = orderNodeInfoDto.getList();
//            SXSSFSheet sheet = (SXSSFSheet) excelWriter.getSheet();
//            sheet.trackAllColumnsForAutoSizing();
//            excelWriter.autoSizeColumnAll();
//            excelWriter.setColumnWidth(0, 13);
//            excelWriter.setColumnWidth(1, 13);
//            excelWriter.setColumnWidth(2, 13);
//            excelWriter.setColumnWidth(3, 13);
//            excelWriter.setColumnWidth(4, 13);
//            excelWriter.setColumnWidth(5, 13);
//            excelWriter.setColumnWidth(6, 13);
//            excelWriter.setColumnWidth(7, 13);
//            excelWriter.setColumnWidth(8, 13);
//            excelWriter.setColumnWidth(9, 13);
//            excelWriter.setColumnWidth(10, 13);
//            excelWriter.setColumnWidth(11, 13);
//            excelWriter.setColumnWidth(12, 13);
//            excelWriter.setColumnWidth(13, 13);
//            excelWriter.setColumnWidth(14, 13);
//            excelWriter.setColumnWidth(15, 13);
//            excelWriter.setColumnWidth(16, 13);
//            excelWriter.setColumnWidth(17, 13);
//            excelWriter.setColumnWidth(18, 13);
//            excelWriter.setColumnWidth(19, 13);
//            excelWriter.setRowHeight(0, 21);
//            excelWriter.setRowHeight(1, 21);
//            excelWriter.setRowHeight(2, 21);
//            excelWriter.setRowHeight(3, 21);
//            excelWriter.setRowHeight(4, 21);
//            excelWriter.setRowHeight(5, 21);
//            excelWriter.setRowHeight(6, 21);
//        CellStyle cellStyle = excelWriter.getCellStyle();
//        cellStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
//        Font font = excelWriter.createFont();
//        font.setBold(true);
//        CellStyle headCellStyle = excelWriter.getCellStyle();
//        headCellStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
//        headCellStyle.setFont(font);
//        excelWriter.setStyle(cellStyle,0,0);
            ExcelUtils.setCellValue(excelWriter, 0, 0, "门店名称", false);
//        excelWriter.setStyle(cellStyle,0,1);
            ExcelUtils.setCellValue(excelWriter, 0, 1, "项目名称", false);
//        excelWriter.setStyle(cellStyle,0,2);
            ExcelUtils.setCellValue(excelWriter, 0, 2, "供应商名称", false);
//        excelWriter.setStyle(cellStyle,0,3);
            ExcelUtils.setCellValue(excelWriter, 0, 3, "费用类型", false);
//        excelWriter.setStyle(cellStyle,0,4);
            ExcelUtils.setCellValue(excelWriter, 0, 4, "结算时间", false);
//        excelWriter.setStyle(cellStyle,0,5);
            ExcelUtils.setCellValue(excelWriter, 0, 5, "工程经理", false);
            //结算时间
            LambdaQueryWrapper groupQuery = Wrappers.lambdaQuery(ProjectGroup.class)
                    .eq(ProjectGroup::getOrderId,orderId)
                    .eq(ProjectGroup::getNodeCode,"con-00909");
            ProjectGroup group = Optional.ofNullable(projectGroupService.getOne(groupQuery)).orElseGet(ProjectGroup::new);
            LambdaQueryWrapper approveQuery = Wrappers.lambdaQuery(ProjectApprove.class)
                    .eq(ProjectApprove::getOrderId,orderId)
                    .eq(ProjectApprove::getNodeId,group.getProjectGroupId())
                    .eq(ProjectApprove::getApproveStatus,JhSystemEnum.approveStatusEnum.APPROVE_COMPLETE.getKey())
                    .eq(ProjectApprove::getApproveResult,JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey());
            ProjectApprove approve = Optional.ofNullable(projectApproveService.getOne(approveQuery)).orElseGet(ProjectApprove::new);
            if (ObjectUtils.isNotEmpty(approve.getApproveEnd())) {
                excelWriter.merge(4, 4, 1, 3, approve.getApproveEnd().toString(), false);
            }

            ExcelUtils.setCellValue(excelWriter, 5, 0, "总价", false);
//        excelWriter.setStyle(cellStyle,10,1);
            ExcelUtils.setCellValue(excelWriter, 5, 1, "原订单金额", false);
//        excelWriter.setStyle(cellStyle,10,2);
            ExcelUtils.setCellValue(excelWriter, 5, 2, "送审总金额", false);
//        excelWriter.setStyle(cellStyle,10,3);
            ExcelUtils.setCellValue(excelWriter, 5, 3, "结算总金额", false);
//        excelWriter.setStyle(cellStyle,10,4);
            ExcelUtils.setCellValue(excelWriter, 5, 4, "订单创建人", false);
//        excelWriter.setStyle(cellStyle,10,5);
            ExcelUtils.setCellValue(excelWriter, 5, 5, "收货人", false);
//        excelWriter.setStyle(headCellStyle,0,6);
            ExcelUtils.setCellValue(excelWriter, 0, 6, "序号", true);
//        excelWriter.setStyle(headCellStyle,1,6);
            ExcelUtils.setCellValue(excelWriter, 1, 6, "产品编号", true);
//        excelWriter.setStyle(headCellStyle,2,6);
            ExcelUtils.setCellValue(excelWriter, 2, 6, "产品名称", true);
//        excelWriter.setStyle(headCellStyle,3,6);
            ExcelUtils.setCellValue(excelWriter, 3, 6, "资产编码", true);
////        excelWriter.setStyle(headCellStyle,4,6);
            ExcelUtils.setCellValue(excelWriter, 4, 6, "资产名称", true);
////        excelWriter.setStyle(headCellStyle,5,6);
//            ExcelUtils.setCellValue(excelWriter, 5, 6, "物料分类名称", true);
//        excelWriter.setStyle(headCellStyle,6,6);
            ExcelUtils.setCellValue(excelWriter, 5, 6, "品牌", true);
//        excelWriter.setStyle(headCellStyle,7,6);
            ExcelUtils.setCellValue(excelWriter, 6, 6, "规格", true);
//        excelWriter.setStyle(headCellStyle,8,6);
            ExcelUtils.setCellValue(excelWriter, 7, 6, "型号", true);
//        excelWriter.setStyle(headCellStyle,9,6);
            ExcelUtils.setCellValue(excelWriter, 8, 6, "计量单位", true);
//        excelWriter.setStyle(headCellStyle,10,6);
            ExcelUtils.setCellValue(excelWriter, 9, 6, "单价", true);
//        excelWriter.setStyle(headCellStyle,11,6);
            ExcelUtils.setCellValue(excelWriter, 10, 6, "服务区数量", true);
//        excelWriter.setStyle(headCellStyle,12,6);
            ExcelUtils.setCellValue(excelWriter, 11, 6, "玩具区数量", true);
//        excelWriter.setStyle(headCellStyle,13,6);
            ExcelUtils.setCellValue(excelWriter, 12, 6, "用品区数量", true);
//        excelWriter.setStyle(headCellStyle,14,6);
            ExcelUtils.setCellValue(excelWriter, 13, 6, "快消区数量", true);
//        excelWriter.setStyle(headCellStyle,15,6);
            ExcelUtils.setCellValue(excelWriter, 14, 6, "纺织区数量", true);
//        excelWriter.setStyle(headCellStyle,16,6);
            ExcelUtils.setCellValue(excelWriter, 15, 6, "核减数量", true);
//        excelWriter.setStyle(headCellStyle,17,6);
            ExcelUtils.setCellValue(excelWriter, 16, 6, "小计", true);
//        excelWriter.setStyle(headCellStyle,18,6);
            ExcelUtils.setCellValue(excelWriter, 17, 6, "总价", true);
//        excelWriter.setStyle(headCellStyle,19,6);
            ExcelUtils.setCellValue(excelWriter, 18, 6, "备注", true);
//        ArrayList<String> detailHeadRow = CollUtil.newArrayList("序号", "产品编号", "产品名称", "资产编码", "资产名称", "物料分类名称", "品牌", "规格", "型号", "计量单位", "单价", "服务区数量", "玩具区数量", "用品区数量", "快消区数量", "纺织区数量", "核减数量", "小计", "总价", "备注");
//        cell0.setCellValue("门店名称");
            for (OrderNodeInfoDto nodeInfoDto : list) {
                if ("con-0090912".equals(nodeInfoDto.getNodeCode())) {
                    //门店名称
                    excelWriter.merge(0, 0, 1, 3, nodeInfoDto.getRemark(), false);
//                resultMap.put("B1",nodeInfoDto.getRemark());
                } else if ("con-0090913".equals(nodeInfoDto.getNodeCode())) {
                    //项目名称
                    String remark = nodeInfoDto.getRemark();
                    if (ObjectUtils.isEmpty(remark)) {
                        remark = "日常";
                    }
                    excelWriter.merge(1, 1, 1, 3, remark, false);
//                excelWriter.writeCellValue(1,1,nodeInfoDto.getRemark());
//                resultMap.put("B2",nodeInfoDto.getRemark());
                } else if ("con-0090915".equals(nodeInfoDto.getNodeCode())) {
                    //费用类型
                    DictDetail label = Optional.ofNullable(dictDetailRepository.findDictDetailByValue(nodeInfoDto.getRemark())).orElseGet(DictDetail::new);
                    excelWriter.merge(3, 3, 1, 3, label.getLabel(), false);
//                excelWriter.writeCellValue(3,1,nodeInfoDto.getRemark());
//                resultMap.put("B4",nodeInfoDto.getRemark());
                } else if ("con-0090921".equals(nodeInfoDto.getNodeCode())) {
                    //工程经理
                    if (ObjectUtils.isNotEmpty(nodeInfoDto.getRemark())) {
                        User one = Optional.ofNullable(userRepository.getOne(Long.valueOf(nodeInfoDto.getRemark()))).orElseGet(User::new);
                        excelWriter.merge(5, 5, 1, 3, one.getUsername() + "-" + one.getNickName(), false);
//                    resultMap.put("B6",one.getUsername()+one.getNickName());
//                    excelWriter.writeCellValue(5,1,nodeInfoDto.getRemark());
                    }
                } else if ("con-0090902".equals(nodeInfoDto.getNodeCode())) {
                    LambdaQueryWrapper supMaterelQuery = Wrappers.lambdaQuery(SupplierMaterielInfo.class)
                            .eq(SupplierMaterielInfo::getIsDelete, 0);
                    List<SupplierMaterielInfo> supList = supplierMaterielInfoService.list(supMaterelQuery);
                    Map<String, SupplierMaterielInfo> smMap = new HashMap<>();
                    for (SupplierMaterielInfo sm : supList) {
                        smMap.put(sm.getSupplierId() + sm.getMaterielCode(), sm);
                    }
                    //订单详情
                    List<OrderDetailDto> orderDetails = nodeInfoDto.getOrderDetails();
                    if (CollectionUtils.isNotEmpty(orderDetails)) {
                        BigDecimal total = new BigDecimal(0);
                        OrderDetailDto detailDto = orderDetails.get(0);
                        excelWriter.merge(2, 2, 1, 3, detailDto.getSupNameCn(), false);
                        int index = 0;
                        for (int i = 0; i < orderDetails.size(); i++) {
                            OrderDetailDto detail = orderDetails.get(i);
                            if (detail.getTotalPrice().compareTo(new BigDecimal(0)) == 0) {
                                continue;
                            }
                            excelWriter.writeCellValue(0, 7 + index, index + 1);
                            excelWriter.writeCellValue(1, 7 + index, detail.getMaterielCode());
                            excelWriter.writeCellValue(2, 7 + index, detail.getProductName());
                            excelWriter.writeCellValue(3, 7 + index, detail.getFinanceCode());
                            SupplierMaterielInfo supplierMaterielInfo = Optional.ofNullable(smMap.get(detail.getSupplierId() + detail.getMaterielCode())).orElseGet(SupplierMaterielInfo::new);
                            excelWriter.writeCellValue(4, 7 + index, supplierMaterielInfo.getProductName());
//                            excelWriter.writeCellValue(5, 7 + i, detail.getSecondClass());
                            excelWriter.writeCellValue(5, 7 + index, detail.getBrand());
                            excelWriter.writeCellValue(6, 7 + index, detail.getSpec());
                            excelWriter.writeCellValue(7, 7 + index, detail.getModel());
                            excelWriter.writeCellValue(8, 7 + index, detail.getUnit());
                            excelWriter.writeCellValue(9, 7 + index, detail.getUnitPrice());
                            excelWriter.writeCellValue(10, 7 + index, detail.getServiceNum());
                            excelWriter.writeCellValue(11, 7 + index, detail.getToyNum());
                            excelWriter.writeCellValue(12, 7 + index, detail.getDailyNum());
                            excelWriter.writeCellValue(13, 7 + index, detail.getFastNum());
                            excelWriter.writeCellValue(14, 7 + index, detail.getSpinNum());
                            excelWriter.writeCellValue(15, 7 + index, detail.getAccountAdjustNum());
                            excelWriter.writeCellValue(16, 7 + index, detail.getSubtotal());
                            excelWriter.writeCellValue(17, 7 + index, detail.getTotalPrice());
                            excelWriter.writeCellValue(18, 7 + index, detail.getRemark());
                            index++;
                            total = total.add(detail.getTotalPrice());
                        }
                        excelWriter.merge(0, 0, 6, 7, total, false);
                    }
                } else if ("con-0090909".equals(nodeInfoDto.getNodeCode())) {
                    //原订单金额
                    excelWriter.merge(1, 1, 6, 7, nodeInfoDto.getRemark(), false);
                } else if ("con-0090910".equals(nodeInfoDto.getNodeCode())) {
                    //送审总金额
                    excelWriter.merge(2, 2, 6, 7, nodeInfoDto.getRemark(), false);
                } else if ("con-0090951".equals(nodeInfoDto.getNodeCode())) {
                    //结算总金额
                    excelWriter.merge(3, 3, 6, 7, nodeInfoDto.getRemark(), false);
                } else if ("con-0090918".equals(nodeInfoDto.getNodeCode())) {
                    //订单创建人
                    excelWriter.merge(4, 4, 6, 7, nodeInfoDto.getRemark(), false);
                } else if ("con-0090945".equals(nodeInfoDto.getNodeCode())) {
                    //收货人
                    excelWriter.merge(5, 5, 6, 7, nodeInfoDto.getRemark(), false);
                }
            }
        } finally {
            if (excelWriter != null) {
                excelWriter.flush(outputStream, true);
                excelWriter.close();
                IoUtil.close(outputStream);
            }
        }
    }

    @Override
    public void downloadOrderData(HttpServletResponse response, OrderNodeInfoQueryCriteria criteria) throws IOException {
        String fileName = "订单信息.xlsx";

        OutputStream outputStream = response.getOutputStream();
        String fileNameURL = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileNameURL + ";" + "filename*=utf-8''" + fileNameURL);
        response.setContentType("application/octet-stream");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        response.setCharacterEncoding("UTF-8");

        String nodeCode = criteria.getNodeCode();
        Long orderId = criteria.getOrderId();
        Boolean isMobile = criteria.getIsMobile();
        ExcelWriter excelWriter = null;
        try {
//        String useCase = criteria.getUseCase();
            OrderNodeInfoDto orderNodeInfoDto = new OrderNodeInfoDto();
            if (ObjectUtils.isNotEmpty(nodeCode) && ObjectUtils.isNotEmpty(orderId)) {
                orderNodeInfoDto = this.getOrderNodeByNodeCode(orderId, nodeCode, isMobile);
            }
//        Integer sheetStart = KidsSystemEnum.TemplateConfigType.ORDER001.getSheetStart();
//        String configType = KidsSystemEnum.TemplateConfigType.ORDER001.getValue();
//        TemplateConfig byConfigType = templateConfigService.findByConfigType(configType);
//        if (byConfigType == null) {
//            throw new BadRequestException("请先配置导入模板");
//        }
//        Timestamp actualEndDate = orderNodeInfoDto.getActualEndDate();
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//        if (ObjectUtils.isEmpty(actualEndDate)){
//            throw new BadRequestException("请先完成节点");
//        }
//        Map<String,String> resultMap = new HashMap<>();
//        resultMap.put("A1","门店名称");
//        resultMap.put("A2","项目名称");
//        resultMap.put("A3","供应商名称");
//        resultMap.put("A4","费用类型");
//        resultMap.put("A5","结算时间");
//        resultMap.put("A6","工程经理");
//        resultMap.put("B5",dateFormat.format(actualEndDate));
//        resultMap.put("K2","原订单金额(元)");
//        resultMap.put("K3","原订单金额(元)");
            excelWriter = ExcelUtil.getBigWriter();
            List<OrderNodeInfoDto> list = orderNodeInfoDto.getList();
            excelWriter.setColumnWidth(0, 13);
            excelWriter.setColumnWidth(1, 13);
            excelWriter.setColumnWidth(2, 13);
            excelWriter.setColumnWidth(3, 13);
            excelWriter.setColumnWidth(4, 13);
            excelWriter.setColumnWidth(5, 13);
            excelWriter.setColumnWidth(6, 13);
            excelWriter.setColumnWidth(7, 13);
            excelWriter.setColumnWidth(8, 13);
            excelWriter.setColumnWidth(9, 13);
            excelWriter.setColumnWidth(10, 13);
            excelWriter.setColumnWidth(11, 13);
            excelWriter.setColumnWidth(12, 13);
            excelWriter.setColumnWidth(13, 13);
            excelWriter.setColumnWidth(14, 13);
            excelWriter.setColumnWidth(15, 13);
            excelWriter.setColumnWidth(16, 13);
            excelWriter.setColumnWidth(17, 13);
            excelWriter.setColumnWidth(18, 13);
            excelWriter.setColumnWidth(19, 13);
            excelWriter.setRowHeight(0, 21);
            excelWriter.setRowHeight(1, 21);
            excelWriter.setRowHeight(2, 21);
            excelWriter.setRowHeight(3, 21);
            excelWriter.setRowHeight(4, 21);
            excelWriter.setRowHeight(5, 21);
            excelWriter.setRowHeight(6, 21);
//        CellStyle cellStyle = excelWriter.getCellStyle();
//        cellStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
//        Font font = excelWriter.createFont();
//        font.setBold(true);
//        CellStyle headCellStyle = excelWriter.getCellStyle();
//        headCellStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
//        headCellStyle.setFont(font);
//        excelWriter.setStyle(cellStyle,0,0);
            ExcelUtils.setCellValue(excelWriter, 0, 0, "门店名称", false);
//        excelWriter.setStyle(cellStyle,0,1);
            ExcelUtils.setCellValue(excelWriter, 0, 1, "项目名称", false);
//        excelWriter.setStyle(cellStyle,0,2);
            ExcelUtils.setCellValue(excelWriter, 0, 2, "费用类型", false);
//        excelWriter.setStyle(cellStyle,0,3);
            ExcelUtils.setCellValue(excelWriter, 0, 3, "订单创建人", false);
//        excelWriter.setStyle(cellStyle,0,4);
            ExcelUtils.setCellValue(excelWriter, 0, 4, "订单时间", false);
//        excelWriter.setStyle(cellStyle,10,1);
//        ExcelUtils.setCellValue(excelWriter,4,1,"原订单金额",false);
//        excelWriter.setStyle(cellStyle,10,2);
            ExcelUtils.setCellValue(excelWriter, 5, 1, "供应商名称", false);
//        excelWriter.setStyle(cellStyle,10,3);
            ExcelUtils.setCellValue(excelWriter, 5, 2, "订单金额", false);
//        excelWriter.setStyle(cellStyle,10,4);
            ExcelUtils.setCellValue(excelWriter, 5, 3, "工程经理", false);
//        excelWriter.setStyle(cellStyle,10,5);
//        ExcelUtils.setCellValue(excelWriter,4,5,"收货人",false);
//        excelWriter.setStyle(headCellStyle,0,6);
            ExcelUtils.setCellValue(excelWriter, 0, 6, "序号", true);
//        excelWriter.setStyle(headCellStyle,1,6);
            ExcelUtils.setCellValue(excelWriter, 1, 6, "产品编号", true);
//        excelWriter.setStyle(headCellStyle,2,6);
            ExcelUtils.setCellValue(excelWriter, 2, 6, "产品名称", true);
//        excelWriter.setStyle(headCellStyle,3,6);
            ExcelUtils.setCellValue(excelWriter, 3, 6, "资产编码", true);
////        excelWriter.setStyle(headCellStyle,4,6);
            ExcelUtils.setCellValue(excelWriter, 4, 6, "资产名称", true);
////        excelWriter.setStyle(headCellStyle,5,6);
//            ExcelUtils.setCellValue(excelWriter, 5, 6, "物料分类名称", true);
//        excelWriter.setStyle(headCellStyle,6,6);
            ExcelUtils.setCellValue(excelWriter, 5, 6, "品牌", true);
//        excelWriter.setStyle(headCellStyle,7,6);
            ExcelUtils.setCellValue(excelWriter, 6, 6, "规格", true);
//        excelWriter.setStyle(headCellStyle,8,6);
            ExcelUtils.setCellValue(excelWriter, 7, 6, "型号", true);
//        excelWriter.setStyle(headCellStyle,9,6);
            ExcelUtils.setCellValue(excelWriter, 8, 6, "计量单位", true);
//        excelWriter.setStyle(headCellStyle,10,6);
            ExcelUtils.setCellValue(excelWriter, 9, 6, "单价", true);
//        excelWriter.setStyle(headCellStyle,11,6);
            ExcelUtils.setCellValue(excelWriter, 10, 6, "服务区数量", true);
//        excelWriter.setStyle(headCellStyle,12,6);
            ExcelUtils.setCellValue(excelWriter, 11, 6, "玩具区数量", true);
//        excelWriter.setStyle(headCellStyle,13,6);
            ExcelUtils.setCellValue(excelWriter, 12, 6, "用品区数量", true);
//        excelWriter.setStyle(headCellStyle,14,6);
            ExcelUtils.setCellValue(excelWriter, 13, 6, "快消区数量", true);
//        excelWriter.setStyle(headCellStyle,15,6);
            ExcelUtils.setCellValue(excelWriter, 14, 6, "纺织区数量", true);
//        excelWriter.setStyle(headCellStyle,16,6);
            ExcelUtils.setCellValue(excelWriter, 15, 6, "核减数量", true);
//        excelWriter.setStyle(headCellStyle,17,6);
            ExcelUtils.setCellValue(excelWriter, 16, 6, "小计", true);
//        excelWriter.setStyle(headCellStyle,18,6);
            ExcelUtils.setCellValue(excelWriter, 17, 6, "总价", true);
//        excelWriter.setStyle(headCellStyle,19,6);
            ExcelUtils.setCellValue(excelWriter, 18, 6, "备注", true);
//        ArrayList<String> detailHeadRow = CollUtil.newArrayList("序号", "产品编号", "产品名称", "资产编码", "资产名称", "物料分类名称", "品牌", "规格", "型号", "计量单位", "单价", "服务区数量", "玩具区数量", "用品区数量", "快消区数量", "纺织区数量", "核减数量", "小计", "总价", "备注");
//        cell0.setCellValue("门店名称");
            BigDecimal settleMoney = new BigDecimal(0);
            for (OrderNodeInfoDto nodeInfoDto : list) {
                if ("con-0090104".equals(nodeInfoDto.getNodeCode())) {
                    //门店名称
                    excelWriter.merge(0, 0, 1, 3, nodeInfoDto.getRemark(), false);
//                resultMap.put("B1",nodeInfoDto.getRemark());
                } else if ("con-0090105".equals(nodeInfoDto.getNodeCode())) {
                    //项目名称
                    String remark = nodeInfoDto.getRemark();
                    if (ObjectUtils.isEmpty(remark)) {
                        remark = "日常";
                    }
                    excelWriter.merge(1, 1, 1, 3, remark, false);
//                excelWriter.writeCellValue(1,1,nodeInfoDto.getRemark());
//                resultMap.put("B2",nodeInfoDto.getRemark());
                } else if ("con-0090107".equals(nodeInfoDto.getNodeCode())) {
                    //费用类型
                    DictDetail label = Optional.ofNullable(dictDetailRepository.findDictDetailByValue(nodeInfoDto.getRemark())).orElseGet(DictDetail::new);
                    excelWriter.merge(2, 2, 1, 3, label.getLabel(), false);
//                excelWriter.writeCellValue(3,1,nodeInfoDto.getRemark());
//                resultMap.put("B4",nodeInfoDto.getRemark());
                } else if ("con-0090113".equals(nodeInfoDto.getNodeCode())) {
                    //工程经理
                    if (ObjectUtils.isNotEmpty(nodeInfoDto.getRemark())) {
                        User one = Optional.ofNullable(userRepository.getOne(Long.valueOf(nodeInfoDto.getRemark()))).orElseGet(User::new);
                        excelWriter.merge(3, 3, 6, 7, one.getUsername() + "-" + one.getNickName(), false);
//                    resultMap.put("B6",one.getUsername()+one.getNickName());
//                    excelWriter.writeCellValue(5,1,nodeInfoDto.getRemark());
                    }
                } else if ("con-0090102".equals(nodeInfoDto.getNodeCode())) {
                    LambdaQueryWrapper supMaterelQuery = Wrappers.lambdaQuery(SupplierMaterielInfo.class)
                            .eq(SupplierMaterielInfo::getIsDelete, 0);
                    List<SupplierMaterielInfo> supList = supplierMaterielInfoService.list(supMaterelQuery);
                    Map<String, SupplierMaterielInfo> smMap = new HashMap<>();
                    for (SupplierMaterielInfo sm : supList) {
                        smMap.put(sm.getSupplierId() + sm.getMaterielCode(), sm);
                    }
                    //订单详情
                    List<OrderDetailDto> orderDetails = nodeInfoDto.getOrderDetails();
                    if (CollectionUtils.isNotEmpty(orderDetails)) {
                        BigDecimal total = new BigDecimal(0);
                        OrderDetailDto detailDto = orderDetails.get(0);
                        //供应商名称
                        excelWriter.merge(1, 1, 6, 7, detailDto.getSupNameCn(), false);
                        int index = 0;
                        for (int i = 0; i < orderDetails.size(); i++) {
                            OrderDetailDto detail = orderDetails.get(i);
                            if (detail.getTotalPrice().compareTo(new BigDecimal(0)) == 0) {
                                continue;
                            }
                            excelWriter.writeCellValue(0, 7 + index, index + 1);
                            excelWriter.writeCellValue(1, 7 + index, detail.getMaterielCode());
                            excelWriter.writeCellValue(2, 7 + index, detail.getProductName());
                            excelWriter.writeCellValue(3, 7 + index, detail.getFinanceCode());
                            SupplierMaterielInfo supplierMaterielInfo = Optional.ofNullable(smMap.get(detail.getSupplierId() + detail.getMaterielCode())).orElseGet(SupplierMaterielInfo::new);
                            excelWriter.writeCellValue(4, 7 + index, supplierMaterielInfo.getProductName());
//                            excelWriter.writeCellValue(5, 7 + i, detail.getSecondClass());
                            excelWriter.writeCellValue(5, 7 + index, detail.getBrand());
                            excelWriter.writeCellValue(6, 7 + index, detail.getSpec());
                            excelWriter.writeCellValue(7, 7 + index, detail.getModel());
                            excelWriter.writeCellValue(8, 7 + index, detail.getUnit());
                            excelWriter.writeCellValue(9, 7 + index, detail.getUnitPrice());
                            excelWriter.writeCellValue(10, 7 + index, detail.getServiceNum());
                            excelWriter.writeCellValue(11, 7 + index, detail.getToyNum());
                            excelWriter.writeCellValue(12, 7 + index, detail.getDailyNum());
                            excelWriter.writeCellValue(13, 7 + index, detail.getFastNum());
                            excelWriter.writeCellValue(14, 7 + index, detail.getSpinNum());
                            excelWriter.writeCellValue(15, 7 + index, detail.getAccountAdjustNum());
                            excelWriter.writeCellValue(16, 7 + index, detail.getSubtotal());
                            excelWriter.writeCellValue(17, 7 + index, detail.getTotalPrice());
                            excelWriter.writeCellValue(18, 7 + index, detail.getRemark());
                            index++;
                            total = total.add(detail.getTotalPrice());
                        }
                        excelWriter.merge(2, 2, 6, 7, total, false);
                    }
                } else if ("con-0090110".equals(nodeInfoDto.getNodeCode())) {
                    //订单创建人
                    excelWriter.merge(3, 3, 1, 3, nodeInfoDto.getRemark(), false);
                } else if ("con-0090111".equals(nodeInfoDto.getNodeCode())) {
                    //订单时间
                    excelWriter.merge(4, 4, 1, 3, nodeInfoDto.getRemark(), false);
                }
            }
        } finally {
            if (excelWriter != null) {
                excelWriter.flush(outputStream, true);
                excelWriter.close();
                IoUtil.close(outputStream);
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<OrderNodeInfoDto> updateData(List<OrderNodeInfoDto> list, Boolean isCommit) {
        //判断是否存在数据
        Boolean isUpdateNodeStatus = true;
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, String> statusMap = list.stream().filter(o -> "订单状态".equals(o.getNodeName())).collect(Collectors.toMap(OrderNodeInfoDto::getNodeName, OrderNodeInfoDto::getRemark));

            for (OrderNodeInfoDto orderNodeInfo : list) {
                    if (ObjectUtils.isNotEmpty(orderNodeInfo.getNodeCode()) && JhSystemEnum.NodeCodeEnum.NODE_90102.getKey().equals(orderNodeInfo.getNodeCode())) {
                    if (ObjectUtils.isNotEmpty(orderNodeInfo.getOrderDetails()) && orderNodeInfo.getOrderDetails().size() > 0) {
                        if (KidsSystemEnum.OrderStatus.ORDER_RETURN.getValue().equals(statusMap.get("订单状态"))) {
                            operateOrderReturn(orderNodeInfo.getOrderDetails(),StringUtils.isEmpty(orderNodeInfo.getProjectId())?null:Long.valueOf(orderNodeInfo.getProjectId()),Long.valueOf(orderNodeInfo.getOrderId()));
                        }

                        orderDetailService.saveOrUpdateBatch(orderDetailMapper.toEntity(orderNodeInfo.getOrderDetails()));
                    }
                }

                if (ObjectUtils.isNotEmpty(orderNodeInfo.getNodeCode()) && JhSystemEnum.NodeCodeEnum.NODE_90902.getKey().equals(orderNodeInfo.getNodeCode())) {
                    if (ObjectUtils.isNotEmpty(orderNodeInfo.getOrderDetails()) && orderNodeInfo.getOrderDetails().size() > 0) {
                        orderDetailService.saveOrUpdateBatch(orderDetailMapper.toEntity(orderNodeInfo.getOrderDetails()));
                    }
                }

                if (JhSystemEnum.NodeCodeEnum.NODE_90904.getKey().equals(orderNodeInfo.getNodeCode())) {
                    if (ObjectUtils.isNotEmpty(orderNodeInfo.getSpecialProjects()) && orderNodeInfo.getSpecialProjects().size() > 0) {
                        List<SpecialProjectDto> specialProjects = orderNodeInfo.getSpecialProjects();
                        for (SpecialProjectDto p : specialProjects) {
                            p.setOrderId(orderNodeInfo.getOrderId());
                        }
                        specialProjectService.saveOrUpdateBatch(specialProjectMapper.toEntity(orderNodeInfo.getSpecialProjects()));
                    }
                }


                //判断节点等级，若等级为1,2 则不可以修改 跳过
                if (null != orderNodeInfo.getNodeLevel() && orderNodeInfo.getNodeLevel() > 2) {
                    if (JhSystemEnum.NodeType.TEXTAREA.getValue().equals(orderNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.FORM_SELECT.getValue().equals(orderNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.FORM_DATE.getValue().equals(orderNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.INPUT_SHOW_BLOCK.getValue().equals(orderNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.RADIO_BUTTON.getValue().equals(orderNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.SINGLE.getValue().equals(orderNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.DIGITAL_SHOW_BLOCK.getValue().equals(orderNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.TABLE_VALUE.getValue().equals(orderNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.FUZZY_SEARCH.getValue().equals(orderNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.MULTIPLE.getValue().equals(orderNodeInfo.getNodeType()) ||
                            JhSystemEnum.NodeType.FUZZY_MULTI_INTER.getValue().equals(orderNodeInfo.getNodeType())) {
                        //
                        LambdaUpdateWrapper<OrderNodeInfo> orderNodeInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(OrderNodeInfo.class).eq(OrderNodeInfo::getNodeId, orderNodeInfo.getNodeId()).set(OrderNodeInfo::getRemark, orderNodeInfo.getRemark());
                        update(orderNodeInfoLambdaUpdateWrapper);
                        if (StringUtils.isNotEmpty(orderNodeInfo.getRelationCode()) && "1".equals(orderNodeInfo.getIsEdit())) {
                            LambdaUpdateWrapper<OrderNodeInfo> relationLambdaUpdateWrapper = Wrappers.lambdaUpdate(OrderNodeInfo.class).eq(OrderNodeInfo::getOrderId, orderNodeInfo.getOrderId()).eq(OrderNodeInfo::getNodeCode, orderNodeInfo.getRelationCode()).set(OrderNodeInfo::getRemark, orderNodeInfo.getRemark());
                            update(relationLambdaUpdateWrapper);
                        }
                    } else if (JhSystemEnum.NodeType.FILE_UPLOAD.getValue().equals(orderNodeInfo.getNodeType())) {
                        //文件
                        continue;
                    }


                    if (null != orderNodeInfo.getNodeIsfin() && isUpdateNodeStatus) {
                        //判断任意一个三级节点状态不为null，即可更新 二级节点 状态
                        LambdaQueryWrapper<OrderNodeInfo> order = Wrappers.lambdaQuery(OrderNodeInfo.class).eq(null != orderNodeInfo.getProjectId(), OrderNodeInfo::getOrderId, orderNodeInfo.getOrderId()).eq(OrderNodeInfo::getTemplateId, orderNodeInfo.getParentId());
                        OrderNodeInfoDto parent = orderNodeInfoMapper.toDto(getOne(order));
                        if (null != parent) {
                            LambdaUpdateWrapper<OrderNodeInfo> orderNodeInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(OrderNodeInfo.class).eq(OrderNodeInfo::getNodeId, parent.getNodeId()).set(OrderNodeInfo::getNodeIsfin, orderNodeInfo.getNodeIsfin());
                            update(orderNodeInfoLambdaUpdateWrapper);
                            //更新一次后即可
                            isUpdateNodeStatus = false;
                        }
                    }
                }
            }
        } else {
            throw new BadRequestException("无修改的数据！");
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<OrderDetailDto> operateOrderReturn(List<OrderDetailDto> orderDetailList,Long projectId,Long orderId){
        List<OrderDetailDto> orderDetailDtos = orderDetailService.checkOrderDetail(orderDetailList, projectId, orderId);

        return orderDetailDtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderNodeInfoDto submit(OrderNodeInfoDto orderNodeInfoD) {
        LambdaQueryWrapper<ProjectGroup> gLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class).eq(null != orderNodeInfoD.getProjectGroupId(), ProjectGroup::getProjectGroupId, orderNodeInfoD.getProjectGroupId());
        ProjectGroupDto projectGroupDto = projectGroupMapper.toDto(projectGroupRepository.selectOne(gLambdaQueryWrapper));
        if (null == projectGroupDto || null == projectGroupDto.getNodeLevel() || projectGroupDto.getNodeLevel() != 2) {
            throw new BadRequestException("传递的nodeId 节点参数值 不正确，不是2级节点");
        }

        //如果是订单创建提交，需要面积录入已审批通过
//        if (JhSystemEnum.NodeCodeEnum.NODE_901.getKey().equals(orderNodeInfoD.getNodeCode()) && ObjectUtils.isNotEmpty(orderNodeInfoD.getProjectId())) {
//            Boolean isPass = projectApproveService.isApprovePass(JhSystemEnum.NodeCodeSEEnum.NODE_705.getKey(), Long.parseLong(orderNodeInfoD.getProjectId()));
//            if (!isPass) {
//                throw new BadRequestException("请先审批当前项目面积录入");
//            }
//        }

        Long orderId = Long.parseLong(orderNodeInfoD.getOrderId());
        if (orderNodeInfoD.getList() != null && orderNodeInfoD.getList().size() != 0) {
            List<OrderNodeInfoDto> list = orderNodeInfoD.getList();
            updateData(list, Boolean.TRUE);
        }
        //获取订单表格详情数据  订单详情表格删除问题 2023/08/04 陆
        //2023 0816暂时影响流程报错，注释，等陆琦改 shi
        if (CollUtil.isNotEmpty(orderNodeInfoD.getList())){
            List<OrderNodeInfoDto> orderListUses = orderNodeInfoD.getList();
            List<OrderNodeInfoDto> orderListFilter = orderListUses.stream().filter(orderNodeInfoDto ->
                    KidsSystemEnum.OrderDetailTableEnum.CREATE_ORDER_DETAIL.getLabel().equals(orderNodeInfoDto.getNodeCode())
                            ||KidsSystemEnum.OrderDetailTableEnum.SUPPLIER_ORDER_DETAIL.getLabel().equals(orderNodeInfoDto.getNodeCode())
                            ||KidsSystemEnum.OrderDetailTableEnum.ORDER_ACCEPTANCE_ORDER_DETAIL.getLabel().equals(orderNodeInfoDto.getNodeCode())
                            ||KidsSystemEnum.OrderDetailTableEnum.ORDER_SETTLEMENT_ORDER_DETAIL.getLabel().equals(orderNodeInfoDto.getNodeCode())
                            ||KidsSystemEnum.OrderDetailTableEnum.ORDER_PAY_ORDER_DETAIL.getLabel().equals(orderNodeInfoDto.getNodeCode())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(orderListFilter)){
                OrderNodeInfoDto orderNodeInfoDto = orderListFilter.get(0);
                //页面订单表格详情
                List<OrderDetailDto> orderDetails = orderNodeInfoDto.getOrderDetails();
                //库里订单表格详情
                List<OrderDetailDto> orderDetailSql = Lists.newArrayList();
                if (CollUtil.isNotEmpty(orderDetails)){
                    String orderIdUse = orderDetails.stream().map(OrderDetailDto::getOrderId).collect(Collectors.toList()).get(0);
                    LambdaQueryWrapper<OrderDetail> orderLambdaQueryWrapper = Wrappers.lambdaQuery(OrderDetail.class).eq(OrderDetail::getOrderId, orderIdUse);
                    List<OrderDetail> orderDetailLists = orderDetailService.list(orderLambdaQueryWrapper);
                    if (CollUtil.isNotEmpty(orderDetailLists)){
                        orderDetailLists.stream().forEach(orderDetail -> {
                            OrderDetailDto orderDetailDtoUse=new OrderDetailDto();
                            OrderDetailDto orderDetailDto1 = orderDetailMapper.toDto(orderDetail);
                            BeanUtils.copyProperties(orderDetailDto1,orderDetailDtoUse);
                            orderDetailSql.add(orderDetailDtoUse);
                        });
                    }
                    if (CollUtil.isNotEmpty(orderDetailSql)){
                        //筛选出被删除的数据
                        List<OrderDetailDto> orderDetailDtoLists = orderDetailSql.stream()
                                .filter(obituaryInfoVO -> !orderDetails.stream()
                                        .map(digitalAssetVO -> digitalAssetVO.getDetailId())
                                        .collect(Collectors.toList())
                                        .contains(obituaryInfoVO.getDetailId()))
                                .collect(Collectors.toList());

                        logger.info("订单表格详情数据 已删除", orderDetailDtoLists);
                        if (CollUtil.isNotEmpty(orderDetailDtoLists)){
                            List<String> keyIdS = orderDetailDtoLists.stream().map(OrderDetailDto::getDetailId).collect(Collectors.toList());
                            orderDetailRepository.deleteBatchIds(keyIdS);
                        }

                    }
                }
//            orderDetails.stream().forEach(orderDetailDto -> {
//                orderDetailService.findById(orderDetailDto.getOrderId())
//            });
            }
        }

        //创建审批
        //查询是否有关联的审批节点
        Boolean hasApprove = projectAppTemplateService.orderHasApprove(orderNodeInfoD);
        if (hasApprove) {
//            判断审批
            boolean estimate = projectApproveService.estimate(orderNodeInfoD);
            if (estimate) {
                throw new BadRequestException("当前节点审批中，请核实后重新提交");
            }

            projectApproveService.createApprove(projectGroupDto);
            //新增审批数据
            logger.info(projectGroupDto.getNodeName() + "节点有审批");

            OrderInfo orderInfoOrigin = Optional.ofNullable(orderInfoRepository.selectById(orderId)).orElseGet(OrderInfo::new);
            if (KidsSystemEnum.OrderType.ADVANCE_ORDER.getValue().equals(orderInfoOrigin.getOrderType())) {
                //修改orderInfo中的order_status为审批中
                OrderInfo orderInfo = new OrderInfo();
                orderInfo.setOrderId(orderInfoOrigin.getOrderId());
                orderInfo.setOrderStatus(KidsSystemEnum.AdvanceOrderStatusFlow.getOrderStatus(JhSystemEnum.NodeCodeEnum.NODE_127.getKey(), "submit").getValue());
                orderInfoRepository.updateById(orderInfo);
            } else if (KidsSystemEnum.OrderType.PURCHASE_ORDER.getValue().equals(orderInfoOrigin.getOrderType())) {
                //如果是订单的节点，修改订单状态
                NodeInfoDto nodeInfoDto = new NodeInfoDto();
                nodeInfoDto.setOrderId(orderInfoOrigin.getOrderId());
                nodeInfoDto.setNodeCode(orderNodeInfoD.getNodeCode());
                nodeInfoDto.setType(KidsSystemEnum.OperationType.SUBMIT.getValue());
                orderInfoService.updateStatus(nodeInfoDto);
            }

        } else {
            NodeInfoDto nodeInfoDto = new NodeInfoDto();
            nodeInfoDto.setOrderId(Long.parseLong(orderNodeInfoD.getOrderId()));
            nodeInfoDto.setNodeCode(orderNodeInfoD.getNodeCode());
            nodeInfoDto.setType(KidsSystemEnum.OperationType.FINISH.getValue());
//            if (orderNodeInfoD.getNodeCode().equals("con-00903")){
//                nodeInfoDto.setType(KidsSystemEnum.OperationType.CANEL.getValue());
//            }
            orderInfoService.updateStatus(nodeInfoDto);
            updateOrderGroupStatusNext(projectGroupMapper.toEntity(projectGroupDto));
        }
        //是否逾期
        Boolean isOverDue = Boolean.FALSE;
       /* if (projectNodeInfoDto.getNodeIsfin()==null|| (!projectNodeInfoDto.getNodeIsfin())) {
            throw new BadRequestException("传递的nodeId 节点参数值 对应节点状态不正确，请先保存完成状态");
        }*/
        Boolean isUpdateNodeStatus = true;
        if (isUpdateNodeStatus) {
            //更新已完成字段
            //判断紧前任务是否都已完成
            String frontWbsConfig = projectGroupDto.getFrontWbsConfig();
            if (frontWbsConfig != null) {
                List<FrontWbsConfigDto> frontList = new ArrayList<>();
                JSONArray jsonArray = new JSONArray(frontWbsConfig);
                Boolean statusFlag = Boolean.TRUE;
                String unFinish = "";
                for (int i = 0; i < jsonArray.size(); i++) {
                    FrontWbsConfigDto frontDto = new FrontWbsConfigDto();
                    JSONObject object = jsonArray.getJSONObject(i);
                    String type = object.getStr("type");
                    String wbs = object.getStr("wbs");
                    if ("FS".equals(type)) {
                        //查找当前节点的紧前是否已完成
                        LambdaQueryWrapper<ProjectGroup> lastLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class).eq(ProjectGroup::getOrderId, orderId).eq(ProjectGroup::getNodeCode, wbs);
                        ProjectGroup one = projectGroupService.getOne(lastLambdaQueryWrapper);
                        if (one == null || (!JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(one.getNodeStatus()))) {
                            statusFlag = Boolean.FALSE;
                            unFinish = unFinish + one.getNodeName() + ",";

                        }

                    }

                }
                if (!statusFlag) {
                    unFinish = unFinish.substring(0, unFinish.length() - 1);
                    throw new BadRequestException(unFinish + "节点尚未提交，请先处理前置节点");
                }
            }

            if (null != projectGroupDto) {
                LambdaUpdateWrapper<ProjectGroup> projectNodeInfoLambdaUpdateWrapper = Wrappers.lambdaUpdate(ProjectGroup.class)
                        .eq(ProjectGroup::getProjectGroupId, projectGroupDto.getProjectGroupId())
                        .set(ProjectGroup::getNodeIsfin, Boolean.TRUE)
                        .set(ProjectGroup::getIsSubmit, Boolean.TRUE);
                if (hasApprove) {
                    projectNodeInfoLambdaUpdateWrapper.set(ProjectGroup::getActualEndDate, null);
                    projectNodeInfoLambdaUpdateWrapper.set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS2.getKey());
                }
                projectGroupService.update(projectNodeInfoLambdaUpdateWrapper);
                //更新一次后即可
                isUpdateNodeStatus = false;
            }
        }

        //改代办为已办
        ProjectGroup projectGroup = projectGroupMapper.toEntity(projectGroupDto);
        projectTaskService.finishTask(projectGroup);

        return orderNodeInfoD;
    }

    public OrderNodeInfoDto getOrderNodeByNodeCode(Long orderId, String nodeCode, Boolean isMobile) {
        //查询当前人的角色
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        List<Long> roleIds = roleRepository.findRoleIdsByUserId(currentUserId);
        //查找当前订单所对应的project
        LambdaQueryWrapper<OrderInfo> orderInfoLambdaQueryWrapper = Wrappers.lambdaQuery(OrderInfo.class)
                .eq(OrderInfo::getOrderId, orderId);
        OrderInfo orderInfo = orderInfoRepository.selectOne(orderInfoLambdaQueryWrapper);
        Long projectId = orderInfo.getProjectId();

        //根据角色出二级页面权限
        // LambdaQueryWrapper<ProjectNodeInfo> project = Wrappers.lambdaQuery(ProjectNodeInfo.class).eq(null != projectId, ProjectNodeInfo::getProjectId, projectId).eq(ProjectNodeInfo::getNodeCode, nodeCode);
        OrderNodeInfoDto parent = orderNodeInfoRepository.getLevelTwoNodeInfo(orderId, nodeCode, roleIds);
        //判断是否显示
        if (parent.getIsWrite() != null && parent.getIsWrite() && parent.getIsOpen() != null && parent.getIsOpen()) {
            parent.setIsWrite(Boolean.TRUE);
        } else {
            parent.setIsWrite(Boolean.FALSE);
        }
        //临时将groupid 给nodeId
        parent.setNodeId(parent.getProjectGroupId());
        LambdaQueryWrapper<OrderNodeInfo> query = Wrappers.lambdaQuery(OrderNodeInfo.class).eq(null != orderId, OrderNodeInfo::getOrderId, orderId);

        if (null != isMobile) {
            query.eq(OrderNodeInfo::getIsMobile, isMobile);
        }
        List<OrderNodeInfoDto> dtos = orderNodeInfoMapper.toDto(list(query));

        if (null == parent || CollectionUtils.isEmpty(dtos)) {
            throw new BadRequestException("参数 projectId,nodeCode 错误，无法获取数据！");
        }

        Map<String, OrderNodeInfoDto> orderNodeInfoMap = new HashMap<>();
        for (OrderNodeInfoDto dto : dtos) {
            if (ObjectUtils.isNotEmpty(dto.getNodeCode()) && JhSystemEnum.NodeCodeEnum.NODE_90102.getKey().equals(dto.getNodeCode()) || JhSystemEnum.NodeCodeEnum.NODE_12702.getKey().equals(dto.getNodeCode())) {
                LambdaQueryWrapper orderDetailQueryWrapper = Wrappers.lambdaQuery(OrderDetail.class)
                        .eq(OrderDetail::getOrderId, dto.getOrderId());
                List<OrderDetail> orderDetailList = orderDetailRepository.selectList(orderDetailQueryWrapper);
                dto.setOrderDetails(orderDetailMapper.toDto(orderDetailList));
            }

            if (JhSystemEnum.NodeCodeEnum.NODE_90904.getKey().equals(dto.getNodeCode())) {
                LambdaQueryWrapper specialProjectQueryWrapper = Wrappers.lambdaQuery(SpecialProject.class)
                        .eq(SpecialProject::getOrderId, dto.getOrderId())
                        .eq(SpecialProject::getIsDelete, false);
                List<SpecialProject> specialProjectList = specialProjectRepository.selectList(specialProjectQueryWrapper);
                dto.setSpecialProjects(specialProjectMapper.toDto(specialProjectList));
            }

            orderNodeInfoMap.put(dto.getNodeCode(), dto);
        }

        LambdaQueryWrapper<ProjectStakeholders> atdLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class);
        atdLambdaQueryWrapper.eq(ProjectStakeholders::getProjectId, projectId);
        List<ProjectStakeholders> approveTemplateDetails = projectStakeholdersService.list(atdLambdaQueryWrapper);
        Map<String, Long> userMap = new HashMap<>();
        for (ProjectStakeholders sta : approveTemplateDetails) {
            userMap.put(sta.getRoleCode(), sta.getUserId());
        }


        for (OrderNodeInfoDto dto : dtos) {
            //关联查询出干系人和其他页面数据
            if (StringUtils.isNotEmpty(dto.getRelationType())) {
                if (JhSystemEnum.RelationTypeEnum.STA.getKey().equals(dto.getRelationType()) && (!JhSystemEnum.JobEnum.ZBHTZRR.getKey().equals(dto.getJobCode()))) {
                    //干系人
                    Long userId = userMap.get(dto.getJobCode());
                    if (null != userId) {
                        dto.setRemark(String.valueOf(userId));
                    }
                }
            }
            if (StringUtils.isNotEmpty(dto.getRelationCode())) {
                OrderNodeInfoDto temp = orderNodeInfoMap.get(dto.getRelationCode());
                if (JhSystemEnum.NodeType.FILE_UPLOAD.getValue().equals(dto.getNodeType()) && (null != temp)) {
                    //如果是附件类型的关联
                    dto.setStartSign(String.valueOf(temp.getNodeId()));
                } else if (null != temp) {
                    dto.setRemark(temp.getRemark());
                }

                if (JhSystemEnum.NodeCodeEnum.NODE_90102.getKey().equals(dto.getRelationCode())) {
                    dto.setOrderDetails(orderNodeInfoMap.get(dto.getRelationCode()).getOrderDetails());
                }

                if (JhSystemEnum.NodeCodeEnum.NODE_90904.getKey().equals(dto.getRelationCode())) {
                    dto.setSpecialProjects(orderNodeInfoMap.get(dto.getRelationCode()).getSpecialProjects());
                }

            }
        }

        parent.setList(dtos);
        return parent;
    }

    @Override
    public OrderNodeInfoDto orderTreeByNodeCode(Long orderId, String nodeCode, String useCase, Boolean isMobile) {
        //查询当前人的角色
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        List<Long> roleIds = roleRepository.findRoleIdsByUserId(currentUserId);
        //查找当前订单所对应的project
        LambdaQueryWrapper<OrderInfo> orderInfoLambdaQueryWrapper = Wrappers.lambdaQuery(OrderInfo.class)
                .eq(OrderInfo::getOrderId, orderId);
        OrderInfo orderInfo = orderInfoRepository.selectOne(orderInfoLambdaQueryWrapper);
        Long projectId = orderInfo.getProjectId();

        //根据角色出二级页面权限
        // LambdaQueryWrapper<ProjectNodeInfo> project = Wrappers.lambdaQuery(ProjectNodeInfo.class).eq(null != projectId, ProjectNodeInfo::getProjectId, projectId).eq(ProjectNodeInfo::getNodeCode, nodeCode);
        OrderNodeInfoDto parent = orderNodeInfoRepository.getLevelTwoNodeInfo(orderId, nodeCode, roleIds);
        //判断是否显示
        if (parent.getIsWrite() != null && parent.getIsWrite() && parent.getIsOpen() != null && parent.getIsOpen()) {
            parent.setIsWrite(Boolean.TRUE);
        } else {
            parent.setIsWrite(Boolean.FALSE);
        }
        //临时将groupid 给nodeId
        parent.setNodeId(parent.getProjectGroupId());
        LambdaQueryWrapper<OrderNodeInfo> query = Wrappers.lambdaQuery(OrderNodeInfo.class).eq(null != orderId, OrderNodeInfo::getOrderId, orderId);

        if (null != isMobile) {
            query.eq(OrderNodeInfo::getIsMobile, isMobile);
        }
        List<OrderNodeInfoDto> dtos = orderNodeInfoMapper.toDto(list(query));

        if (null == parent || CollectionUtils.isEmpty(dtos)) {
            throw new BadRequestException("参数 projectId,nodeCode 错误，无法获取数据！");
        }

        Map<String, OrderNodeInfoDto> orderNodeInfoMap = new HashMap<>();
        for (OrderNodeInfoDto dto : dtos) {
            if (ObjectUtils.isNotEmpty(dto.getNodeCode()) && JhSystemEnum.NodeCodeEnum.NODE_90102.getKey().equals(dto.getNodeCode()) || JhSystemEnum.NodeCodeEnum.NODE_12702.getKey().equals(dto.getNodeCode())) {
                LambdaQueryWrapper orderDetailQueryWrapper = Wrappers.lambdaQuery(OrderDetail.class)
                        .eq(OrderDetail::getOrderId, dto.getOrderId());
                List<OrderDetail> orderDetailList = orderDetailRepository.selectList(orderDetailQueryWrapper);
                for (OrderDetail od : orderDetailList){
                    if (ObjectUtils.isNotEmpty(od.getSubtotal())){
                        od.setApprovedNum(od.getSubtotal());
                    }
                }
                dto.setOrderDetails(orderDetailMapper.toDto(orderDetailList));
            }

            if (JhSystemEnum.NodeCodeEnum.NODE_90904.getKey().equals(dto.getNodeCode())) {
                LambdaQueryWrapper specialProjectQueryWrapper = Wrappers.lambdaQuery(SpecialProject.class)
                        .eq(SpecialProject::getOrderId, dto.getOrderId())
                        .eq(SpecialProject::getIsDelete, false);
                List<SpecialProject> specialProjectList = specialProjectRepository.selectList(specialProjectQueryWrapper);
                for (SpecialProject sp : specialProjectList){
                    if (ObjectUtils.isNotEmpty(sp.getNum())) {
                        sp.setApprovedNum(sp.getNum());
                    }
                }
                dto.setSpecialProjects(specialProjectMapper.toDto(specialProjectList));
            }

            orderNodeInfoMap.put(dto.getNodeCode(), dto);
        }

        LambdaQueryWrapper<ProjectStakeholders> atdLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class);
        atdLambdaQueryWrapper.eq(ProjectStakeholders::getProjectId, projectId);
        List<ProjectStakeholders> approveTemplateDetails = projectStakeholdersService.list(atdLambdaQueryWrapper);
        Map<String, Long> userMap = new HashMap<>();
        for (ProjectStakeholders sta : approveTemplateDetails) {
            userMap.put(sta.getRoleCode(), sta.getUserId());
        }


        for (OrderNodeInfoDto dto : dtos) {
            //关联查询出干系人和其他页面数据
            if (StringUtils.isNotEmpty(dto.getRelationType())) {
                if (JhSystemEnum.RelationTypeEnum.STA.getKey().equals(dto.getRelationType()) && (!JhSystemEnum.JobEnum.ZBHTZRR.getKey().equals(dto.getJobCode()))) {
                    //干系人
                    Long userId = userMap.get(dto.getJobCode());
                    if (null != userId) {
                        dto.setRemark(String.valueOf(userId));
                    }
                }
            }
            if (StringUtils.isNotEmpty(dto.getRelationCode())) {
                OrderNodeInfoDto temp = orderNodeInfoMap.get(dto.getRelationCode());
                if (JhSystemEnum.NodeType.FILE_UPLOAD.getValue().equals(dto.getNodeType()) && (null != temp)) {
                    //如果是附件类型的关联
                    dto.setStartSign(String.valueOf(temp.getNodeId()));
                } else if (null != temp) {
                    dto.setRemark(temp.getRemark());
                }

                if (JhSystemEnum.NodeCodeEnum.NODE_90102.getKey().equals(dto.getRelationCode())) {
                    dto.setOrderDetails(orderNodeInfoMap.get(dto.getRelationCode()).getOrderDetails());
                }

                if (JhSystemEnum.NodeCodeEnum.NODE_90904.getKey().equals(dto.getRelationCode())) {
                    dto.setSpecialProjects(orderNodeInfoMap.get(dto.getRelationCode()).getSpecialProjects());
                }

            }


        }
        //任务负责人转值
        /*if(StringUtils.isNoneEmpty(parent.getRemark())){
            UserQueryCriteria userQueryCriteria = new UserQueryCriteria();
            List<Long> list = Lists.newArrayList();
            Arrays.stream(parent.getRemark().split(",")).forEach(s->{
                list.add(Long.parseLong(s));
            });
            userQueryCriteria.setUserId(list);
            List<UserDto> userDtos = userService.queryAll(userQueryCriteria);
//                        dto.setRemark(String.valueOf(userId));
            parent.setRemark(userDtos.stream().map(UserDto::getUsername).collect(Collectors.joining(",")));


        }*/
        if (null != parent.getNodeLevel() && parent.getNodeLevel() == 2) {

            List<ProjectTemplateApproveRelationDto> appRelations = projectTemplateApproveRelationService.getAppRelation(Long.parseLong(parent.getTemplateId()), Long.parseLong(parent.getTemplateGroupId()));
            //若存在审批节点，则给值
            if (ObjectUtils.isNotNull(appRelations) && appRelations.size() > 0) {
                parent.setApproveMatrixId(appRelations.get(0).getApproveMatrixId());
            }
            //改为只从干系人中取值 2022 0608
            if (parent.getRoleCode() != null) {
                List<String> list = Lists.newArrayList();
                Arrays.stream(parent.getRoleCode().split(",")).forEach(s -> {
                    list.add(s);
                });
                String userName = "";
                for (String roleCode : list) {
                    LambdaQueryWrapper<ProjectStakeholders> lambdaQueryWrapper = Wrappers.lambdaQuery(ProjectStakeholders.class);
                    lambdaQueryWrapper.eq(ProjectStakeholders::getRoleCode, roleCode)
                            .eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey())
                            .eq(ProjectStakeholders::getProjectId, projectId);
                    List<ProjectStakeholders> stakeholderslist = projectStakeholdersService.list(lambdaQueryWrapper);
                    if (stakeholderslist.size() > 0) {
                        for (ProjectStakeholders one : stakeholderslist) {
                            if (one != null && null != one.getUserId()) {
                               /* UserDto UserDto = userService.findById(one.getUserId());
                                if(null!=UserDto){
                                    if(userName==""){
                                        userName= changeUserName(UserDto,UserDto.getUsername());
                                    }else{
                                        userName=userName+","+changeUserName(UserDto,UserDto.getUsername());
                                    }

                                }*/
                            }
                            parent.setRemark(userName);
                        }
                    }

                }
            }


        }

        //转换treeNode
        List<TreeNode<String>> treeNodes = dtos.stream().map(dto -> {
            Class<? extends OrderNodeInfoDto> templateClass = dto.getClass();
            Field[] fields = templateClass.getDeclaredFields();
            HashMap<String, Object> map = new HashMap<>(8);
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(dto));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                field.setAccessible(false);
            }
            return new TreeNode<String>().setId(dto.getTemplateId())
                    .setName(dto.getNodeName())
                    .setParentId(dto.getParentId())
                    .setWeight(dto.getNodeIndex())
                    .setExtra(map);
        }).collect(Collectors.toList());
        //设置treeNode
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setDeep(4);
        //使用Hutools TreeUtil转换树结构
        List<Tree<String>> allTree = TreeUtil.build(treeNodes, parent.getTemplateId(), treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    Map<String, Object> extra = treeNode.getExtra();
                    Set<Map.Entry<String, Object>> entries = extra.entrySet();
                    for (Map.Entry<String, Object> entry : entries) {
                        tree.putExtra(entry.getKey(), entry.getValue());
                    }
                });
        parent.setChildren(allTree);
        return parent;
    }

    @Override
    public List<Tree<String>> orderTree(Long orderId, String useCase, Boolean isMobile, int deep) {
        LambdaQueryWrapper<ProjectGroup> project = Wrappers.lambdaQuery(ProjectGroup.class).eq(null != orderId, ProjectGroup::getOrderId, orderId)
                .eq(ProjectGroup::getTemplateCode,"order")
                .isNull(ProjectGroup::getParentId);
        ProjectGroupDto parent = projectGroupMapper.toDto(projectGroupRepository.selectOne(project));
        LambdaQueryWrapper<OrderNodeInfo> query = Wrappers.lambdaQuery(OrderNodeInfo.class).eq(null != orderId, OrderNodeInfo::getOrderId, orderId);

        if (StringUtils.isNotEmpty(useCase)) {
            query.eq(OrderNodeInfo::getUseCase, useCase);
        }

        if (null != isMobile) {
            query.eq(OrderNodeInfo::getIsMobile, isMobile);
        }

        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null || currentUserId == 0) {
            throw new BadRequestException("登录用户为空，请登录后再请求");
        }
        List<Long> roleIds = roleRepository.findRoleIdsByUserId(currentUserId);
        List<Role> rolesByUserId = roleRepository.findRolesByUserId(currentUserId);
        // List<ProjectNodeInfoDto> dtos = projectNodeInfoMapper.toDto(list(query));
        List<OrderNodeInfoDto> dtos = orderNodeInfoRepository.getAllNodeInfo(orderId, useCase, isMobile, roleIds);
        if (null == parent || CollectionUtils.isEmpty(dtos)) {
            throw new BadRequestException("参数 projectId 错误，无法获取数据！");
        }

        Map<String, OrderNodeInfoDto> projectNodeInfoMap = new HashMap<>();
        for (OrderNodeInfoDto dto : dtos) {
            projectNodeInfoMap.put(dto.getNodeCode(), dto);
            if (dto.getIsWrite() != null && dto.getIsWrite() && dto.getIsOpen() != null && dto.getIsOpen()) {
                dto.setIsWrite(Boolean.TRUE);
            } else {
                dto.setIsWrite(Boolean.FALSE);
            }
            //临时将groupid 给nodeId
            dto.setNodeId(dto.getProjectGroupId());
        }


        Map<String, Long> nodeCodeMap = new HashMap<>();
        for (int i = 0; i < dtos.size(); i++) {
            if (null != dtos.get(i).getNodeLevel() && dtos.get(i).getNodeLevel() == 2) {
                //查找二三级数据
                Long templateQueueId = Long.parseLong(dtos.get(i).getTemplateQueueId());
                LambdaQueryWrapper<OrderNodeInfo> twoQuery = Wrappers.lambdaQuery(OrderNodeInfo.class).eq(OrderNodeInfo::getOrderId, orderId).eq(OrderNodeInfo::getTemplateQueueId, templateQueueId);
                OrderNodeInfo orderNodeInfo = orderNodeInfoRepository.selectOne(twoQuery);
                LambdaQueryWrapper<OrderNodeInfo> thirdQuery = Wrappers.lambdaQuery(OrderNodeInfo.class).eq(OrderNodeInfo::getOrderId, orderId).eq(OrderNodeInfo::getParentId, orderNodeInfo.getTemplateId());
                List<OrderNodeInfo> projectNodeInfos = orderNodeInfoRepository.selectList(thirdQuery);

                for (OrderNodeInfo projectNodeInfo : projectNodeInfos) {
                    if (ObjectUtils.isNotEmpty(projectNodeInfo.getNodeCode()) && JhSystemEnum.NodeCodeEnum.NODE_90102.getKey().equals(projectNodeInfo.getNodeCode()) || JhSystemEnum.NodeCodeEnum.NODE_12702.getKey().equals(projectNodeInfo.getNodeCode())) {
                        LambdaQueryWrapper orderDetailQueryWrapper = Wrappers.lambdaQuery(OrderDetail.class)
                                .eq(OrderDetail::getOrderId, projectNodeInfo.getOrderId());
                        List<OrderDetail> orderDetailList = orderDetailRepository.selectList(orderDetailQueryWrapper);
                        for (OrderDetail od : orderDetailList){
                            if (ObjectUtils.isNotEmpty(od.getSubtotal())){
                                od.setApprovedNum(od.getSubtotal());
                            }
                        }
                        projectNodeInfo.setOrderDetails(orderDetailList);
                        nodeCodeMap.put(projectNodeInfo.getNodeCode(), projectNodeInfo.getOrderId());
                    }
                    //特殊项目
                    if (JhSystemEnum.NodeCodeEnum.NODE_90904.getKey().equals(projectNodeInfo.getNodeCode())) {
                        LambdaQueryWrapper specialProjectQueryWrapper = Wrappers.lambdaQuery(SpecialProject.class)
                                .eq(SpecialProject::getOrderId, projectNodeInfo.getOrderId())
                                .eq(SpecialProject::getIsDelete, false);
                        List<SpecialProject> specialProjectList = specialProjectRepository.selectList(specialProjectQueryWrapper);
                        for (SpecialProject sp : specialProjectList){
                            if (ObjectUtils.isNotEmpty(sp.getNum())) {
                                sp.setApprovedNum(sp.getNum());
                            }
                        }
                        projectNodeInfo.setSpecialProjects(specialProjectList);
                        nodeCodeMap.put(projectNodeInfo.getNodeCode(), projectNodeInfo.getOrderId());
                    }

                }
                for (OrderNodeInfo nodeInfo : projectNodeInfos) {
                    if (ObjectUtils.isNotEmpty(nodeInfo.getRelationCode())) {
                        if (JhSystemEnum.NodeCodeEnum.NODE_90102.getKey().equals(nodeInfo.getRelationCode())) {
                            Long orderIdOrigin = nodeCodeMap.get(nodeInfo.getRelationCode());
                            if (ObjectUtils.isNotEmpty(orderIdOrigin)) {
                                LambdaQueryWrapper orderDetailQueryWrapper = Wrappers.lambdaQuery(OrderDetail.class)
                                        .eq(OrderDetail::getOrderId, orderIdOrigin);
                                List<OrderDetail> orderDetailList = orderDetailRepository.selectList(orderDetailQueryWrapper);
                                for (OrderDetail od : orderDetailList){
                                    if (ObjectUtils.isNotEmpty(od.getSubtotal())){
                                        od.setApprovedNum(od.getSubtotal());
                                    }
                                }
                                nodeInfo.setOrderDetails(orderDetailList);
                            }
                        }

                        if (JhSystemEnum.NodeCodeEnum.NODE_90904.getKey().equals(nodeInfo.getRelationCode())) {
                            Long orderIdOrigin = nodeCodeMap.get(nodeInfo.getRelationCode());
                            if (ObjectUtils.isNotEmpty(orderIdOrigin)) {
                                LambdaQueryWrapper specialProjectQueryWrapper = Wrappers.lambdaQuery(SpecialProject.class)
                                        .eq(SpecialProject::getOrderId, orderIdOrigin)
                                        .eq(SpecialProject::getIsDelete, false);
                                List<SpecialProject> specialProjectList = specialProjectRepository.selectList(specialProjectQueryWrapper);
                                for (SpecialProject sp : specialProjectList){
                                    if (ObjectUtils.isNotEmpty(sp.getNum())) {
                                        sp.setApprovedNum(sp.getNum());
                                    }
                                }
                                nodeInfo.setSpecialProjects(specialProjectList);
                            }

                            //特殊项目在订单结算的时候，由甲供材助理可编辑
                            if (JhSystemEnum.NodeCodeEnum.NODE_00909.getKey().equals(orderNodeInfo.getNodeCode())) {
                                rolesByUserId.forEach(r -> {
                                    if (JhSystemEnum.JobEnum.JGCZL.getKey().equals(r.getRoleCode())) {
                                        nodeInfo.setIsEdit("1");
                                    }
                                });
                            }
                        }
                    }

                    projectNodeInfoMap.put(nodeInfo.getNodeCode(), orderNodeInfoMapper.toDto(nodeInfo));
                }

                dtos.addAll(orderNodeInfoMapper.toDto(projectNodeInfos));
            } else {
                continue;
            }
        }

        for (OrderNodeInfoDto dto : dtos) {
            if (null != dto.getNodeLevel() && dto.getNodeLevel() == 1) {
                if (JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(dto.getNodeStatus())) {
                    dto.setPhaseStatus("已完成");
                } else {
                    dto.setPhaseStatus("未完成");

                }
                if (isMobile != null && isMobile) {
                    String nodeCode = dto.getNodeCode();
                    String simple = JhSystemEnum.TaskCodeEnum.bt(nodeCode);
                    dto.setSimpleName(simple);
                }

            }
            if (null != dto.getNodeLevel() && dto.getNodeLevel() == 2) {
                //查找二三级数据


                if (JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(dto.getNodeStatus())) {
                    dto.setPhaseStatus("已完成");
                } else {
                    dto.setPhaseStatus("未完成");

                }

                List<ProjectTemplateApproveRelationDto> appRelations = projectTemplateApproveRelationService.getAppRelation(Long.parseLong(dto.getTemplateId()), Long.parseLong(dto.getTemplateGroupId()));
                //若存在审批节点，则给值
                if (ObjectUtils.isNotNull(appRelations) && appRelations.size() > 0) {
                    dto.setApproveMatrixId(appRelations.get(0).getApproveMatrixId());
                }

            }
            //关联查询出干系人和其他页面数据
          /*  if (StringUtils.isNotEmpty(dto.getRelationType())) {
                if (JhSystemEnum.RelationTypeEnum.STA.getKey().equals(dto.getRelationType())) {
                    //干系人
                    Long userId = userMap.get(dto.getJobCode());
                    if(null!=userId){
                        dto.setRemark(String.valueOf(userId));
                    }
                }
            }*/
            if (StringUtils.isNotEmpty(dto.getRelationCode())) {
                OrderNodeInfoDto temp = projectNodeInfoMap.get(dto.getRelationCode());
                if (JhSystemEnum.NodeType.FILE_UPLOAD.getValue().equals(dto.getNodeType()) && (null != temp)) {
                    //如果是附件类型的关联
                    dto.setStartSign(String.valueOf(temp.getNodeId()));
                } else if (null != temp) {
                    dto.setRemark(temp.getRemark());
                }

             /*  if (JhSystemEnum.NodeCodeEnum.NODE_90102.getKey().equals(dto.getRelationCode())) {
                   dto.setOrderDetails(projectNodeInfoMap.get(dto.getRelationCode()).getOrderDetails());
               }

               if (JhSystemEnum.NodeCodeEnum.NODE_90904.getKey().equals(dto.getRelationCode())){
                   dto.setSpecialProjects(projectNodeInfoMap.get(dto.getRelationCode()).getSpecialProjects());
               }*/

            }


        }

        //转换treeNode
        List<TreeNode<String>> treeNodes = dtos.stream().map(dto -> {
            Class<? extends OrderNodeInfoDto> templateClass = dto.getClass();
            Field[] fields = templateClass.getDeclaredFields();
            HashMap<String, Object> map = new HashMap<>(8);
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(dto));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
                field.setAccessible(false);
            }
            return new TreeNode<String>().setId(dto.getTemplateId())
                    .setName(dto.getNodeName())
                    .setParentId(dto.getParentId())
                    .setWeight(dto.getNodeIndex())
                    .setExtra(map);
        }).collect(Collectors.toList());
        //设置treeNode
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setDeep(deep);
        //使用Hutools TreeUtil转换树结构
        return TreeUtil.build(treeNodes, parent.getTemplateId(), treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    Map<String, Object> extra = treeNode.getExtra();
                    Set<Map.Entry<String, Object>> entries = extra.entrySet();
                    for (Map.Entry<String, Object> entry : entries) {
                        tree.putExtra(entry.getKey(), entry.getValue());
                    }
                });

    }

    @Override
    public void updateOrderGroupStatusNext(ProjectGroup projectGroup) {
        Long orderId = projectGroup.getOrderId();
        Timestamp d = new Timestamp(System.currentTimeMillis());

        //更新此二级节点 实际结束时间
        LambdaUpdateWrapper<ProjectGroup> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjectGroup::getProjectGroupId, projectGroup.getProjectGroupId())
                .set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey())
                .set(ProjectGroup::getActualEndDate, d);


        if (projectGroupService.update(updateWrapper)) {
            //改待办为已办
            projectTaskService.finishTask(projectGroup);

            //查找当前项目所对应的紧前
            //查找所有节点
            List<ProjectGroup> wbsConfigs = new ArrayList<>();
            if (redisUtils.hasKey("wbs" + orderId)) {
                wbsConfigs = (List<ProjectGroup>) redisUtils.get("wbs" + orderId);
            } else {
                LambdaQueryWrapper<ProjectGroup> groupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class).eq(ProjectGroup::getOrderId, orderId).eq(ProjectGroup::getNodeLevel, 2);
                List<ProjectGroup> projectGroups = projectGroupRepository.selectList(groupLambdaQueryWrapper);
                for (ProjectGroup group : projectGroups) {
                    ProjectGroup wbs = new ProjectGroup();
                    wbs.setNodeCode(group.getNodeCode());
                    wbs.setProjectId(group.getProjectId());
                    wbs.setNodeName(group.getNodeName());
                    wbs.setFrontWbsConfig(group.getFrontWbsConfig());
                    wbs.setProjectGroupId(group.getProjectGroupId());
                    wbs.setRoleCode(group.getRoleCode());
                    wbs.setOrderId(group.getOrderId());
                    wbsConfigs.add(wbs);
                }
                redisUtils.setIfAbsent("wbs" + orderId, wbsConfigs, 86400);
            }
            String nodeCode = projectGroup.getNodeCode();
            for (ProjectGroup group : wbsConfigs) {
                FrontWbsConfigDto frontWbsConfigDto = new FrontWbsConfigDto();
                String frontWbsConfig = group.getFrontWbsConfig();
                String nodeCodeForOpen = group.getNodeCode();
                Long projectGroupId = group.getProjectGroupId();
                if (ObjectUtils.isNotEmpty(frontWbsConfig)) {
                    List<FrontWbsConfigDto> frontList = new ArrayList<>();
                    JSONArray jsonArray = new JSONArray(frontWbsConfig);
                    for (int i = 0; i < jsonArray.size(); i++) {
                        FrontWbsConfigDto frontDto = new FrontWbsConfigDto();
                        JSONObject object = jsonArray.getJSONObject(i);
                        String type = object.getStr("type");
                        String wbs = object.getStr("wbs");
                        if ("FS".equals(type) && nodeCode.equals(wbs)) {
                            LambdaUpdateWrapper<ProjectGroup> openLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                            openLambdaUpdateWrapper.eq(ProjectGroup::getProjectGroupId, projectGroupId).and(o -> o.eq(ProjectGroup::getIsOpen, Boolean.FALSE).or().isNull(ProjectGroup::getIsOpen))
                                    .set(ProjectGroup::getIsOpen, Boolean.TRUE)
                                    .set(ProjectGroup::getNodeStatus, JhSystemEnum.NodeStatusEnum.NODE_STATUS6.getKey());
                            projectGroupService.update(openLambdaUpdateWrapper);
                            if (ObjectUtils.isNotEmpty(group.getOrderId())){
                                group.setTemplateCode(projectGroup.getTemplateCode());
                            }
                            projectTaskService.generateTodoTask(group);
                        }

                    }

                }

            }

            //预约订单节点
            //修改预约订单状态为完成
            if (JhSystemEnum.NodeCodeEnum.NODE_127.getKey().equals(nodeCode)) {
                updateAdvanceStatus(orderId, "finish");
            }

            //订单创建节点
            //将订单总金额/施工面积存入订单创建的预估单坪效
            if (JhSystemEnum.NodeCodeEnum.NODE_901.getKey().equals(nodeCode)) {
                savePredictAverage(projectGroup.getProjectId(), orderId);
            }

            //订单付款节点
            //订单付款
            if (JhSystemEnum.NodeCodeEnum.NODE_00121.getKey().equals(nodeCode)){
//                saveOrderPay(projectGroup.getProjectId(),orderId);
                projectToMasterService.updateMasterPayInfoByOrder(projectGroup);
                enterMasterService.enterMasterEnergyConsume(projectGroup);
                enterMasterService.enterMasterOrderData(projectGroup);
            }

            // 调用eam
            if (JhSystemEnum.NodeCodeEnum.NODE_00909.getKey().equals(nodeCode)) {
                // 调用eam
                IeamOrderSyncRequest ieamOrderSyncRequest = new IeamOrderSyncRequest();
                ieamOrderSyncRequest.setOrderId(orderId);
                ieamHttpService.syncOrderToIeam(ieamOrderSyncRequest);

                //计算结算金额
//                LambdaQueryWrapper detailQuery = Wrappers.lambdaQuery(OrderDetail.class)
//                        .eq(OrderDetail::getOrderId,projectGroup.getOrderId())
//                        .eq(OrderDetail::getIsDelete,false);
//                List<OrderDetail> orderDetails = orderDetailService.list(detailQuery);
//                LambdaQueryWrapper specialQuery = Wrappers.lambdaQuery(SpecialProject.class)
//                        .eq(SpecialProject::getOrderId,projectGroup.getOrderId())
//                        .eq(SpecialProject::getIsDelete,false);
//                List<SpecialProject> specialProjects = specialProjectService.list(specialQuery);
//                BigDecimal totalPrice = new BigDecimal(0);
//                BigDecimal specialTotal = new BigDecimal(0);
//                orderDetails.forEach(o -> totalPrice.add(o.getTotalPrice()));
//                specialProjects.forEach(s -> specialTotal.add(s.getTotalPrice()));
//                LambdaUpdateWrapper orderApproveUpdate = Wrappers.lambdaUpdate(OrderNodeInfo.class)
//                        .eq(OrderNodeInfo::getOrderId,projectGroup.getOrderId())
//                        .eq(OrderNodeInfo::getNodeCode,"con-0090906")
//                        .eq(OrderNodeInfo::getRemark,totalPrice);

            }
        }
    }

    @Override
    public void updateOrderGroupStatusBack(ProjectGroup projectGroup) {
        projectGroup.setIsSubmit(Boolean.FALSE);
        projectGroup.setNodeStatus(JhSystemEnum.NodeStatusEnum.NODE_STATUS0.getKey());
        projectGroup.setNodeIsfin(false);
        projectGroupService.update(projectGroup);
        projectTaskService.generateTodoTask(projectGroup);
    }

    /**
     * 更新预约订单的库存
     *
     * @param nodeCode
     * @param orderId
     */
    @Override
    public void updateAdvanceStock(String nodeCode, Long orderId) {
        LambdaQueryWrapper orderInfoQueryWrapper = Wrappers.lambdaQuery(OrderInfo.class)
                .eq(OrderInfo::getOrderId, orderId);
        OrderInfo orderInfo = orderInfoRepository.selectOne(orderInfoQueryWrapper);
        if (ObjectUtils.isNotEmpty(orderInfo)) {

            LambdaQueryWrapper orderDetailQueryWrapper = Wrappers.lambdaQuery(OrderDetail.class)
                    .eq(OrderDetail::getOrderId, orderId);
            List<OrderDetail> orderDetailList = orderDetailRepository.selectList(orderDetailQueryWrapper);
            if (ObjectUtils.isNotEmpty(orderDetailList) && orderDetailList.size() > 0) {
                for (OrderDetail orderDetail : orderDetailList) {
                    LambdaQueryWrapper supplierMaterielQueryWrapper = Wrappers.lambdaQuery(SupplierMaterielInfo.class)
                            .eq(SupplierMaterielInfo::getMaterielId, orderDetail.getMaterielId())
                            .eq(SupplierMaterielInfo::getIsDelete, false);
                    SupplierMaterielInfo supplierMaterielInfo = Optional.ofNullable(supplierMaterielInfoRepository.selectOne(supplierMaterielQueryWrapper)).orElseGet(SupplierMaterielInfo::new);
                    LambdaQueryWrapper supplierInfoQueryWrapper = Wrappers.lambdaQuery(SupplierInfo.class)
                            .eq(SupplierInfo::getId, supplierMaterielInfo.getSupplierId())
                            .eq(SupplierInfo::getIsDelete, false);
                    SupplierInfo supplierInfo = Optional.ofNullable(supplierInfoRepository.selectOne(supplierInfoQueryWrapper)).orElseGet(SupplierInfo::new);
                    orderStockService.incrStock(supplierMaterielInfo.getMaterielCode(), supplierInfo.getSupplierNum(), new BigDecimal(String.valueOf(orderDetail.getSubtotal())));
                }
            }
        }
    }

    /**
     * 修改预约订单状态为完成
     *
     * @param orderId
     */
    @Override
    public void updateAdvanceStatus(Long orderId, String statusType) {
        //修改订单状态
        OrderInfo updateOrderInfo = new OrderInfo();
        updateOrderInfo.setOrderId(orderId);
        updateOrderInfo.setOrderStatus(KidsSystemEnum.AdvanceOrderStatusFlow.getOrderStatus(JhSystemEnum.NodeCodeEnum.NODE_127.getKey(), statusType).getValue());
        orderInfoRepository.updateById(updateOrderInfo);

        LambdaQueryWrapper orderNodeInfoQuery = Wrappers.lambdaQuery(OrderNodeInfo.class)
                .eq(OrderNodeInfo::getOrderId, orderId)
                .eq(OrderNodeInfo::getNodeCode, JhSystemEnum.NodeCodeEnum.NODE_12708.getKey());
        OrderNodeInfo orderNodeInfoOrign = Optional.ofNullable(orderNodeInfoRepository.selectOne(orderNodeInfoQuery)).orElseGet(OrderNodeInfo::new);
        OrderNodeInfo orderNodeInfo = new OrderNodeInfo();
        orderNodeInfo.setNodeId(orderNodeInfoOrign.getNodeId());
        orderNodeInfo.setRemark(KidsSystemEnum.AdvanceOrderStatusFlow.getOrderStatus(JhSystemEnum.NodeCodeEnum.NODE_127.getKey(), statusType).getValue());
        orderNodeInfoRepository.updateById(orderNodeInfo);
    }

    /**
     * 将审批通过后的面积录入，用来计算订单创建的预估单坪效
     *
     * @param projectId
     * @param orderId
     */
    @Override
    public void savePredictAverage(Long projectId, Long orderId) {
        if (ObjectUtils.isEmpty(projectId)) {
            return;
        }
        //创建订单节点
//        Boolean isPass = projectApproveService.isApprovePass(JhSystemEnum.NodeCodeSEEnum.NODE_705.getKey(), projectId);
//        if (isPass) {
//
//        }
        ProjectInfo project = Optional.ofNullable(projectInfoService.getById(projectId)).orElseGet(ProjectInfo::new);
        BigDecimal buildArea;
        if (ObjectUtils.isNotEmpty(project.getDecorateArea())) {
            //施工面积
            buildArea = project.getDecorateArea();

            //查询存入订单中的订单总金额
            OrderInfo orderInfo = Optional.ofNullable(orderInfoRepository.selectById(orderId)).orElseGet(OrderInfo::new);
            if (ObjectUtils.isNotEmpty(orderInfo.getOrderAmount())) {
                //将订单总金额/施工面积=预估单坪效
                LambdaUpdateWrapper orderUpdate = Wrappers.lambdaUpdate(OrderNodeInfo.class)
                        .eq(OrderNodeInfo::getOrderId, orderId)
                        .eq(OrderNodeInfo::getNodeCode, "con-0090120")
                        .set(OrderNodeInfo::getRemark, String.valueOf(orderInfo.getOrderAmount().multiply(buildArea)));
                update(orderUpdate);
            }
        }
    }

    /**
     * 自动生成订单付款的订购单号
     * @param projectId
     * @param orderId
     */
    public void saveOrderPay(Long projectId,Long orderId){
        LambdaUpdateWrapper orderNodeUpdate = Wrappers.lambdaUpdate(OrderNodeInfo.class)
                .eq(OrderNodeInfo::getOrderId,orderId)
                .eq(OrderNodeInfo::getNodeCode,"con-0012124")
                .set(OrderNodeInfo::getRemark,getOrderNo("DG"));
        this.update(orderNodeUpdate);
    }

    /**
     * 创建信息入node表
     *
     * @param orderNodeInfo
     * @param orderInfoDto
     * @return
     */
    private OrderNodeInfo copyOrderNodeInfo(OrderNodeInfo orderNodeInfo, OrderInfoDto orderInfoDto) {
        if (orderNodeInfo.getNodeCode() != null) {
            //所属门店
            if ("con-0090104".equals(orderNodeInfo.getNodeCode())) {
                orderNodeInfo.setRemark(orderInfoDto.getStoreName());
            } else if ("con-0090105".equals(orderNodeInfo.getNodeCode())) {
                //所属项目
                orderNodeInfo.setRemark(orderInfoDto.getProjectName());
            } else if ("con-0090106".equals(orderNodeInfo.getNodeCode())) {
                //门店类型
                orderNodeInfo.setRemark(orderInfoDto.getStoreType());
            } else if ("con-0090107".equals(orderNodeInfo.getNodeCode())) {
                //费用类型
                orderNodeInfo.setRemark(orderInfoDto.getCostType());
            } else if ("con-0090108".equals(orderNodeInfo.getNodeCode())) {
                //订单状态
                orderNodeInfo.setRemark(orderInfoDto.getOrderStatus());
            } else if ("con-0090109".equals(orderNodeInfo.getNodeCode())) {
                //采购专项说明
                orderNodeInfo.setRemark(orderInfoDto.getPurchaseDesc());
            } else if ("con-0090110".equals(orderNodeInfo.getNodeCode())) {
                //订单创建人
                orderNodeInfo.setRemark(orderInfoDto.getAddUser());
            } else if ("con-0090111".equals(orderNodeInfo.getNodeCode())) {
                //订单创建时间 时间转换
                orderNodeInfo.setRemark(orderInfoDto.getAddTime());
            } else if ("con-0090112".equals(orderNodeInfo.getNodeCode())) {
                //联系电话
                orderNodeInfo.setRemark(orderInfoDto.getAddPhone());
            } else if ("con-0090113".equals(orderNodeInfo.getNodeCode())) {
                //工程经理
                orderNodeInfo.setRemark(orderInfoDto.getReceiver());
            } else if ("con-0090114".equals(orderNodeInfo.getNodeCode())) {
                //工程经理电话
                orderNodeInfo.setRemark(orderInfoDto.getReceivePhone());
            } else if ("con-0090116".equals(orderNodeInfo.getNodeCode())) {
                //要求到货时间
                orderNodeInfo.setRemark(orderInfoDto.getArrivalTime());
            } else if ("con-0090117".equals(orderNodeInfo.getNodeCode())) {
                //要求开始安装时间
                orderNodeInfo.setRemark(orderInfoDto.getStartInstallTime());
            } else if ("con-0090118".equals(orderNodeInfo.getNodeCode())) {
                //要求结束安装时间
                orderNodeInfo.setRemark(orderInfoDto.getEndInstallTime());
            } else if ("con-0090124".equals(orderNodeInfo.getNodeCode())) {
                //备注
                orderNodeInfo.setRemark(orderInfoDto.getRemark());
            } else if ("con-0090909".equals(orderNodeInfo.getNodeCode())) {
                orderNodeInfo.setRemark(String.valueOf(orderInfoDto.getTotalPrice()));
            }
        }
        return orderNodeInfo;
    }

    @Override
    public void updateOrderCancelStatus(OrderInfoDto resources) {
        Long orderId = resources.getOrderId();
        LambdaUpdateWrapper<ProjectTaskInfo> projectTaskInfoUpdate = Wrappers.lambdaUpdate(ProjectTaskInfo.class)
                .eq(ProjectTaskInfo::getOrderId,orderId)
                .eq(ProjectTaskInfo::getTaskStatus,"task_unfin")
                .set(ProjectTaskInfo::getTaskStatus,"task_fin");
        projectTaskInfoService.update(projectTaskInfoUpdate);
        String approveName = null;
        Boolean flag = Boolean.FALSE;
        LambdaQueryWrapper<ProjectGroup> groupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getOrderId,orderId)
                .eq(ProjectGroup::getNodeCode,"con-00901");
        List<ProjectGroup> groups = Optional.ofNullable(projectGroupService.list(groupLambdaQueryWrapper)).orElseGet(LinkedList::new);
        if (!ObjectUtils.isNull(groups) && groups.size() > 0) {
            for (ProjectGroup projectGroup : groups) {
                Long projectGroupId = projectGroup.getProjectGroupId();
                LambdaQueryWrapper<ProjectApprove> projectApproveLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApprove.class)
                        .eq(ProjectApprove::getOrderId, orderId)
                        .eq(ProjectApprove::getNodeId, projectGroupId)
                        .orderByDesc(ProjectApprove::getApproveStart);
                List<ProjectApprove> projectApproves = projectApproveRepository.selectList(projectApproveLambdaQueryWrapper);
                ProjectApprove projectApprove = projectApproves.get(0);
                Long approveId = projectApprove.getApproveId();
                LambdaQueryWrapper<ProjectApproveDetail> projectApproveDetailLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectApproveDetail.class)
                        .eq(ProjectApproveDetail::getApproveId, approveId);
                List<ProjectApproveDetail> projectApproveDetails = projectApproveDetailRepository.selectList(projectApproveDetailLambdaQueryWrapper);
                for (ProjectApproveDetail projectApproveDetail : projectApproveDetails) {
                    Role role = roleRepository.findRoleByRoleId(Long.parseLong(projectApproveDetail.getApproveRole()));
                    if (("under_approve".equals(projectApproveDetail.getApproveResult())
                            || "approve_pass".equals(projectApproveDetail.getApproveResult())
                            || "approve_refuse".equals(projectApproveDetail.getApproveResult()))&&"sjjl".equals(role.getRoleCode())) {
                        flag = Boolean.TRUE;
                        approveName = KidsSystemEnum.OrderCancelApproveCodeEnum.SJJL.getValue();
                    } else if ("approve_pass".equals(projectApproveDetail.getApproveResult())&&"jgcjl".equals(role.getRoleCode())) {
                        flag = Boolean.TRUE;
                        approveName = KidsSystemEnum.OrderCancelApproveCodeEnum.JGCJL.getValue();
                    } else if ("approve_pass".equals(projectApproveDetail.getApproveResult())&&"jgccsgly".equals(role.getRoleCode())) {
                        flag = Boolean.TRUE;
                        approveName = KidsSystemEnum.OrderCancelApproveCodeEnum.CSQR.getValue();
                    }
                }
            }
        }


        LambdaQueryWrapper<ProjectGroup> projectGroupLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectGroup.class)
                .eq(ProjectGroup::getOrderId,orderId)
                .eq(ProjectGroup::getTemplateCode,"order_cancel")
                .eq(ProjectGroup::getNodeLevel,2);
        List<ProjectGroup> list = Optional.ofNullable(projectGroupService.list(projectGroupLambdaQueryWrapper)).orElseGet(LinkedList::new);
        if (!ObjectUtils.isNull(list) && list.size() > 0){
            ProjectGroup group = list.get(0);
            if (group.getNodeLevel() == 2){
                LambdaQueryWrapper<ProjectAppTemplate> appTemplateLambdaQueryWrapper = Wrappers.lambdaQuery(ProjectAppTemplate.class)
                        .eq(ProjectAppTemplate::getOrderId,orderId)
                        .eq(ProjectAppTemplate::getNodeCode,"con-00903");
                List<ProjectAppTemplate> appTemplates = Optional.ofNullable(projectAppTemplateService.list(appTemplateLambdaQueryWrapper)).orElseGet(LinkedList::new);
                if (ObjectUtils.isNull(appTemplates)) {
                    if (flag) {
                        // 审批模板入库
                        List<ProjectTemplateApproveRelationDto> appRelations = projectTemplateApproveRelationService.getAppRelation(group.getTemplateId(), group.getTemplateGroupId());
                        //若存在审批节点，则复制当前节点数据
                        if (ObjectUtils.isNotNull(appRelations)) {
                            for (ProjectTemplateApproveRelationDto appRelation : appRelations) {
                                ApproveTemplate approveTemplate = approveTemplateRepository.selectById(appRelation.getApproveMatrixId());
                                if (approveName.equals(approveTemplate.getAppTempleteCode())) {
                                    projectAppTemplateService.copyApproveTemplate(group, appRelation);
                                }
                            }
                        }
                    }
                }
                //查看当前二级节点有没有联合关系数据，存在就把 联合关系模版入库
                projectJointTaskOnfigurationService.upJointTask(group.getNodeCode(),group.getProjectId());
            }
        }
    }

    /**
     * 生成订单付款的订购单号
     *
     * @return
     */
    private String getOrderNo(String topStr) {
        String current = cn.hutool.core.date.DateUtil.format(new Date(), "yyyyMMdd");
        String key = ORDER_NO_PREFIX + topStr + current;
        Integer value = (Integer) redisUtils.get(key);
        String num = "";
        String prefix = topStr;
        if (null == value) {
            redisUtils.set(key, 1, 86400, TimeUnit.SECONDS);
            num = "001";
        } else {
            Long increment = redisUtils.increment(key);
            StringBuilder s = new StringBuilder(increment.toString());
            for (int i = s.length(); i < 3; i++) {
                s.insert(0, "0");
            }
            num = s.toString();
        }


        String code = prefix + current + num;
        return code;
    }
}