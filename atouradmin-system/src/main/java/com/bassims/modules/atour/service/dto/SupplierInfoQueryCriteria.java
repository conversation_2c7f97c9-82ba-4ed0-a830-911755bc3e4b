/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bassims.annotation.QueryPlus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-09-15
**/
@Data
public class SupplierInfoQueryCriteria{

    /**
     * 厂商中文名称
     */
    @QueryPlus(type = QueryPlus.Type.INNER_LIKE)
    private String supNameCn;

    @QueryPlus(type = QueryPlus.Type.EQUAL)
    private Integer isDelete=0;
    
    @QueryPlus(type = QueryPlus.Type.NOT_NULL)
    private Long userId;
    /**
     * 厂商状态
     */
    @QueryPlus(type = QueryPlus.Type.IN)
    private List<String> supStatus;
    /**
     * 是否甲供材单位
     */
    @QueryPlus(type = QueryPlus.Type.EQUAL)
    private Boolean isFirstMaterialSupplier;
    /**
     * 厂商状态
     */
    @QueryPlus(type = QueryPlus.Type.BETWEEN)
    private List<Timestamp> serviceStartDate;


    /** 城市id */
    @QueryPlus(type = QueryPlus.Type.EQUAL)
    private Long city;

    /**
     * 服务范围  空调	air
     *        消防	fire
     *        弱电	weak_current
     *       样板间装配板	template_room_assembly_plate
     *      样板间家具	showroom_furniture
     */
    @QueryPlus(type = QueryPlus.Type.EQUAL)
    private String serviceScope;


    /**
     * 供应商类型（码值）
     */
   // @QueryPlus(type = QueryPlus.Type.EQUAL)
    private String supplierType;

    /**
     * 供应商类型（码值）
     */
    private String supplierType1;

    /**
     * 承接品牌（码值）
     */
    @QueryPlus(type = QueryPlus.Type.EQUAL)
    private String productCode;

    /**是否平台供应商（码值platform_provider_not）*/
    @QueryPlus(type = QueryPlus.Type.EQUAL)
    private String platformProviderNot;


    /**税号*/
    @QueryPlus(type = QueryPlus.Type.INNER_LIKE)
    private String taxNumber;



    /**项目ID*/
    private String projectId;
    @QueryPlus(type = QueryPlus.Type.EQUAL)
    private String status;

}