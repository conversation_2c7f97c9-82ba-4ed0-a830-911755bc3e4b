package com.bassims.modules.atour.domain;

import com.bassims.modules.atour.service.dto.ProjectGroupDto;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ProjectNodeForProgress implements Serializable {
    private String projectId;
    private Date projectCreateDate;
    private String taskPhase;
    private List<ProjectGroupDto> projectNodeInfoList;
}
