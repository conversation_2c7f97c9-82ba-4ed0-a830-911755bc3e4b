package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 *计划事件表
 */
@Data
@TableName(value="t_plan_event_info")
public class TPlanEventInfo implements Serializable {

    @TableId(value = "plan_event_id",type = IdType.AUTO)
    @ApiModelProperty(value = "计划事件主键")
    private Long planEventId;

    @TableField(value = "plan_event_name")
    @ApiModelProperty(value = "计划事件名称",required = true)
    private String planEventName;

    @TableField(value = "group_id")
    @ApiModelProperty(value = "所有模板的集合表t_template_collection的group_id外键",required = true)
    private Long groupId;

    @TableField(value = "plan_event_belong")
    @ApiModelProperty(value = "计划时间所属(0:项目启动1：项目设计2：项目施工)",required = true)
    private String planEventBelong;

    @TableField(value = "plan_event_duration")
    @ApiModelProperty(value = "计划工期",required = true)
    private Integer planEventDuration;

    @TableField(value = "all_duration")
    @ApiModelProperty(value = "总工期",required = true)
    private Integer allDuration;

    @TableField(value = "del_flag")
    @ApiModelProperty(value = "删除标记（0：正常 2：删除）")
    private String delFlag;

    @TableField(value = "sort")
    @ApiModelProperty(value = "排序码")
    private String sort;

    @TableField(exist = false)
    private String nodeCode;

    @TableField(exist = false)
    private String nodeName;

}
