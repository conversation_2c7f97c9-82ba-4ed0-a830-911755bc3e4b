/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.Owner;
import com.bassims.modules.atour.domain.SupplierInfo;
import com.bassims.modules.atourWithout.domain.AtosSubjectRequest;
import com.bassims.modules.atourWithout.domain.AtosUserRequest;
import com.bassims.modules.system.domain.User;
import com.bassims.modules.system.service.UserService;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.OwnerRepository;
import com.bassims.modules.atour.service.OwnerService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.OwnerDto;
import com.bassims.modules.atour.service.dto.OwnerQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.OwnerMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2023-10-10
 **/
@Service
public class OwnerServiceImpl extends BaseServiceImpl<OwnerRepository, Owner> implements OwnerService {

    private static final Logger logger = LoggerFactory.getLogger(OwnerServiceImpl.class);

    @Autowired
    private OwnerRepository ownerRepository;
    @Autowired
    private OwnerMapper ownerMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public Map<String, Object> queryAll(OwnerQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<Owner> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(Owner.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", ownerMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<OwnerDto> queryAll(OwnerQueryCriteria criteria) {
        return ownerMapper.toDto(list(QueryHelpPlus.getPredicate(Owner.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OwnerDto findById(Long ownerId) {
        Owner owner = Optional.ofNullable(getById(ownerId)).orElseGet(Owner::new);
        ValidationUtil.isNull(owner.getOwnerId(), getEntityClass().getSimpleName(), "ownerId", ownerId);
        return ownerMapper.toDto(owner);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OwnerDto create(Owner resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setOwnerId(snowflake.nextId());
        save(resources);
        return findById(resources.getOwnerId());
    }

    @Override
    public OwnerDto saveOwner(Owner resources) {
        Owner owner = ownerRepository.getOwner(resources);
        if (ObjectUtil.isNotEmpty(owner)) {
            throw new BadRequestException("当前业主信息已存在！！！");
        }
        return this.create(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Owner resources) {
        Owner owner = Optional.ofNullable(getById(resources.getOwnerId())).orElseGet(Owner::new);
        ValidationUtil.isNull(owner.getOwnerId(), "Owner", "id", resources.getOwnerId());
        owner.copy(resources);
        updateById(owner);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long ownerId : ids) {
            ownerRepository.deleteById(ownerId);
        }
    }

    @Override
    public void download(List<OwnerDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (OwnerDto owner : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("业主名称", owner.getOwnerName());
            map.put("业主手机", owner.getOwnerPhone());
            map.put("业主邮箱", owner.getOwnerEmail());
            map.put("业主code", owner.getOwnerCode());
            map.put("创建人", owner.getCreateUser());
            map.put("创建时间", owner.getCreateTime());
            map.put("更新人", owner.getUpdateUser());
            map.put("更新时间", owner.getUpdateTime());
            map.put("是否已删除：0 正常，1 已删除", owner.getIsDelete());
            map.put("状态", owner.getStatus());
            map.put("业主NO", owner.getOwnerNum());
            map.put("供应商编码", owner.getSupplierCode());
            map.put("业主简称", owner.getOwnerShortName());
            map.put("公司注册日期", owner.getCompRegDate());
            map.put("公司注册地址", owner.getCompRegAddr());
            map.put("营业执照有效期到期日", owner.getBusinessExpDate());
            map.put("进入亚朵开始时间", owner.getServiceStartDate());
            map.put("业主类型（码值）", owner.getOwnerType());
            map.put("推荐人", owner.getReferrer());
            map.put("联系方式", owner.getContactWay());
            map.put("是否平台供应商（码值platform_provider_not）", owner.getPlatformProviderNot());
            map.put("开户名字(中文)", owner.getBankAccountName());
            map.put("银行名字(中文，银行全称)", owner.getBankNameCn());
            map.put("银行名字(英文)", owner.getBankNameEn());
            map.put("银行支行名字(中文)", owner.getBankBranchCn());
            map.put("银行支行城市(中文)", owner.getBankBranchCityCn());
            map.put("账户号", owner.getBankAccountNumber());
            map.put("税号", owner.getTaxNumber());
            map.put("税率(服务)", owner.getTaxServiceRate());
            map.put("服务区域", owner.getServiceAreas());
            map.put("atos主键id", owner.getTkId());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void saveOrUpdateFromAtos(AtosSubjectRequest atosSubjectRequest) {
        //查询主体是否存在
        String tk = atosSubjectRequest.getTk();
        String tk_name = atosSubjectRequest.getTk_name();
        AtosUserRequest adminInfo = atosSubjectRequest.getAdminInfo();
        LambdaQueryWrapper<Owner> queryWrapper = Wrappers.lambdaQuery(Owner.class)
                .eq(Owner::getTkId, tk).or().eq(Owner::getOwnerName, tk_name);
        Owner owner = ownerRepository.selectOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(owner)) {
            //更新业主
            owner.setOwnerName(tk_name);
            ownerRepository.updateById(owner);
        } else {
            //新增业主
            Snowflake snowflake = IdUtil.getSnowflake(1, 1);
            long ownerId = snowflake.nextId();
            Snowflake adminInfoId = IdUtil.getSnowflake(1, 1);
            Long adminId = adminInfoId.nextId();

            owner.setOwnerName(tk_name);
            owner.setTkId(tk);
            owner.setOwnerId(ownerId);
            owner.setIsDelete(Boolean.FALSE);
            //新增admin用户
            User user = new User();
            user.setId(adminId);
            user.setUsername(adminInfo.getMobile());
            user.setPhone(adminInfo.getMobile());
            user.setEmail(adminInfo.getEmail());
            user.setOpenId(adminInfo.getUk());
            user.setPassword(passwordEncoder.encode("yaduo201203.acms"));

            userService.create(user);
            ownerRepository.insert(owner);

        }
    }

    @Override
    public void deleteFromAtos(AtosSubjectRequest atosSubjectRequest) {
        //查询主体是否存在
        String tk = atosSubjectRequest.getTk();
        String tk_name = atosSubjectRequest.getTk_name();
        AtosUserRequest adminInfo = atosSubjectRequest.getAdminInfo();
        LambdaQueryWrapper<Owner> queryWrapper = Wrappers.lambdaQuery(Owner.class)
                .eq(Owner::getTkId, tk).or().eq(Owner::getOwnerName, tk_name);
        Owner owner = ownerRepository.selectOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(owner)) {
            //更新业主
            owner.setIsDelete(Boolean.TRUE);
            ownerRepository.updateById(owner);
        }
    }
}