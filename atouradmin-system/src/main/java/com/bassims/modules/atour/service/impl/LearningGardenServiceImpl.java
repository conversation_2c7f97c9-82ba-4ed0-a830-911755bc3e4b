/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bassims.base.BaseServiceImpl;
import com.bassims.domain.LocalStorage;
import com.bassims.modules.atour.domain.LearningGarden;
import com.bassims.modules.atour.domain.PackageInfo;
import com.bassims.modules.atour.repository.LearningGardenRepository;
import com.bassims.modules.atour.repository.PackageInfoRepository;
import com.bassims.modules.atour.service.LearningGardenService;
import com.bassims.modules.atour.service.dto.DeleteLearningGardenParam;
import com.bassims.modules.atour.service.dto.LearningGardenDirectoryParam;
import com.bassims.modules.atour.service.dto.LearningGardenDto;
import com.bassims.modules.atour.service.dto.LearningGardenQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.LearningGardenMapper;
import com.bassims.repository.LocalStorageRepository;
import com.bassims.service.LocalStorageService;
import com.bassims.service.dto.LocalStorageDto;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.utils.ValidationUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.log4j.Log4j2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.sql.Timestamp;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-04-03
**/
@Service
@Log4j2
public class LearningGardenServiceImpl extends BaseServiceImpl<LearningGardenRepository,LearningGarden> implements LearningGardenService {

    private static final byte[] buf = new byte[1024];

    private static final Logger logger = LoggerFactory.getLogger(LearningGardenServiceImpl.class);

    @Autowired
    private LearningGardenRepository learningGardenRepository;
    @Autowired
    private LearningGardenService learningGardenService;
    @Autowired
    private LearningGardenMapper learningGardenMapper;
    @Autowired
    private LocalStorageRepository localStorageRepository;
    @Autowired
    private LocalStorageService localStorageService;
    @Autowired
    private PackageInfoRepository packageInfoRepository;

    @Override
    public Map<String,Object> queryAll(LearningGardenQueryCriteria criteria, Pageable pageable){

        PageInfo<LearningGarden> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(LearningGarden.class, criteria)));
        List<LearningGarden> learningGardenslist = page.getList();
        if (CollectionUtil.isNotEmpty(learningGardenslist)){
            learningGardenslist.forEach(learningGarden -> {
                LocalStorageDto localStorageDto = localStorageService.findById(learningGarden.getToolStorageId());
                if (Objects.nonNull(localStorageDto)){
                    learningGarden.setLocalStorageDto(localStorageDto);
                    String name = localStorageDto.getName();
                    Long packageId = learningGarden.getPackageId();
                    PackageInfo packageInfo = packageInfoRepository.findSpackageInfo(name, packageId);
                    if (Objects.nonNull(packageInfo)){
                        learningGarden.setPackageInfoUse(packageInfo);
                    }
                }
            });
        }
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", learningGardenMapper.toDto(learningGardenslist));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<LearningGardenDto> queryAll(LearningGardenQueryCriteria criteria){
        return learningGardenMapper.toDto(list(QueryHelpPlus.getPredicate(LearningGarden.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LearningGardenDto findById(Long learningGardenId) {
        LearningGarden learningGarden = Optional.ofNullable(getById(learningGardenId)).orElseGet(LearningGarden::new);
        ValidationUtil.isNull(learningGarden.getLearningGardenId(),getEntityClass().getSimpleName(),"learningGardenId",learningGardenId);
        return learningGardenMapper.toDto(learningGarden);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LearningGardenDto create(LearningGarden resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setLearningGardenId(snowflake.nextId());
        save(resources);
        return findById(resources.getLearningGardenId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(LearningGarden resources) {
        LearningGarden learningGarden = Optional.ofNullable(getById(resources.getLearningGardenId())).orElseGet(LearningGarden::new);
        ValidationUtil.isNull( learningGarden.getLearningGardenId(),"LearningGarden","id",resources.getLearningGardenId());
        learningGarden.copy(resources);
        updateById(learningGarden);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(Long[] ids) {
        for (Long learningGardenId : ids) {
            learningGardenRepository.deleteById(learningGardenId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDirectory(DeleteLearningGardenParam deleteLearningGardenParam) {
        Long[] ids = deleteLearningGardenParam.getIds();
        String path = deleteLearningGardenParam.getPath();
        for (Long learningGardenId : ids) {
            LearningGarden learningGarden = learningGardenRepository.selectById(learningGardenId);
            if (Objects.nonNull(learningGarden)){
//                packageInfoRepository.deleteById(learningGarden.getPackageId());
                LocalStorageDto localStorageDto = localStorageService.findById(learningGarden.getToolStorageId());
                if (Objects.nonNull(localStorageDto)){
                    PackageInfo packageInfo = packageInfoRepository.findSpackageInfo(localStorageDto.getName(), learningGarden.getPackageId());
                    if (Objects.nonNull(packageInfo)){
                        packageInfoRepository.deleteById(packageInfo.getPackageId());
                    }
                    List<Long> spackageInfoDeletesIdS = packageInfoRepository.findSpackageInfoDeletes(localStorageDto.getName());
                    if (CollectionUtil.isNotEmpty(spackageInfoDeletesIdS)){
                        for (Long id : spackageInfoDeletesIdS) {
                            localStorageRepository.deleteById(id);
                        }
                    }
                }
            }
            learningGardenRepository.deleteById(learningGardenId);
        }
        File file=new File(path);
        deleteDir(file);
    }

    @Override
    public void download(List<LearningGardenDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (LearningGardenDto learningGarden : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put(" toolStorageId",  learningGarden.getToolStorageId());
            map.put(" source",  learningGarden.getSource());
            map.put("创建者", learningGarden.getCreateBy());
            map.put("更新者", learningGarden.getUpdateBy());
            map.put("创建日期", learningGarden.getCreateTime());
            map.put("更新时间", learningGarden.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public LocalStorage createLearningGarden(Long packageId,String name, MultipartFile multipartFile, Long userId) throws IOException {
        LocalStorage localStorage = localStorageService.createLearningGarden(name, multipartFile, userId);
        LearningGarden learningGardenUse=new LearningGarden();

        if (Objects.nonNull(localStorage)){
            learningGardenUse.setLearningGardenId(IdUtil.getSnowflakeNextId());
            learningGardenUse.setToolStorageId(localStorage.getId());
            learningGardenUse.setSource("维护学习标准资料");
            learningGardenUse.setDeleted(false);
            learningGardenUse.setPackageId(packageId);
            learningGardenRepository.insert(learningGardenUse);
        }
        return  new LocalStorage() ;
    }

    @Override
    public void downloadLearningGarden(Long localId, HttpServletResponse response, HttpServletRequest request)throws IOException{
        localStorageService.downloadLearningGarden(localId,response,request);
    }

    @Override
    public void deleteLearningGarden(Long[] ids) {
        for (Long id : ids) {
            LocalStorage storage = localStorageRepository.selectById(id);
            if (Objects.nonNull(storage)){
                LearningGarden learningGarden=new LearningGarden();
                learningGarden.setDeleted(true);
                LambdaQueryWrapper<LearningGarden> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(LearningGarden::getToolStorageId, storage.getId());
                learningGardenRepository.update(learningGarden,lambdaQueryWrapper);
            }
        }
        localStorageService.deleteAll(ids);
    }

    @Override
    public void downloadScoreTemplate(HttpServletResponse response, HttpServletRequest request) throws Exception {
        List<File> fileList = new ArrayList<>();
        List<Long> ids=new ArrayList<>();
        List<LocalStorage> scoreTemplateList = localStorageRepository.findByNodeId("123458899");
        if (CollectionUtil.isNotEmpty(scoreTemplateList)){
            scoreTemplateList.stream().forEach(scoreTemplate->{
                ids.add(scoreTemplate.getId());
            });
            if (CollectionUtil.isNotEmpty(ids)){
                ids.stream().forEach(id->{
                    LocalStorage storage = localStorageRepository.selectById(id);
                    if (Objects.nonNull(storage)){
                        String path = storage.getPath();
                        if (!StringUtils.isEmpty(path)){
                            File file=new File(path);
                            fileList.add(file);
                        }
                    }
                });
            }
        }
        if (CollectionUtil.isNotEmpty(fileList)){
            localStorageService.downloadScoreTemplate(fileList,response,request);
        }else {
            throw new FileNotFoundException();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LearningGarden createLearningGardenDirectory(LearningGardenDirectoryParam learningGardenDirectoryParam, HttpServletResponse response, HttpServletRequest request) throws Exception {
        String name = learningGardenDirectoryParam.getName();
        String pathUse = learningGardenDirectoryParam.getPathUse();
        Long packageId = learningGardenDirectoryParam.getPackageId();
        LocalStorage learningGardenDirectory = localStorageService.createLearningGardenDirectory(name, pathUse, response, request);
        PackageInfo packageInfo=new PackageInfo();
        packageInfo.setPackageId(Long.parseLong(String.valueOf(IdUtil.getSnowflakeNextId()).substring(0,15)));
        packageInfo.setPackageName(name);
        packageInfo.setCreateTime(new Timestamp(System.currentTimeMillis()));
        packageInfo.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        packageInfo.setIsDelete(false);
        if (learningGardenDirectoryParam.getPackageId()!=null){
            packageInfo.setParentId(learningGardenDirectoryParam.getPackageId());
        }
        packageInfo.setIsNextLevel("1");
        packageInfoRepository.insert(packageInfo);
        LearningGarden learningGarden = new LearningGarden();
        learningGarden.setLearningGardenId(IdUtil.getSnowflakeNextId());
        learningGarden.setSource("维护学习标准资料");
        learningGarden.setPackageId(packageId);
        learningGarden.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        learningGarden.setCreateTime(new Timestamp(System.currentTimeMillis()));
        learningGarden.setToolStorageId(learningGardenDirectory.getId());
        learningGardenService.save(learningGarden);
        return learningGarden;
    }

    /**
     * 压缩成ZIP 方法1
     *
     * @param zipFileName       压缩文件夹路径
     * @param sourceFileName    要压缩的文件路径
     * @param KeepDirStructure 是否保留原来的目录结构,true:保留目录结构;
     *                         false:所有文件跑到压缩包根目录下(注意：不保留目录结构可能会出现同名文件,会压缩失败)
     * @throws RuntimeException 压缩失败会抛出运行时异常
     */
    public static Boolean toZip(String zipFileName, String sourceFileName, boolean KeepDirStructure) {
        Boolean result = true;
        long start = System.currentTimeMillis();//开始
        ZipOutputStream zos = null;
        try {
            FileOutputStream fileOutputStream = new FileOutputStream(zipFileName);
            zos = new ZipOutputStream(fileOutputStream);
            File sourceFile = new File(sourceFileName);
            compress(sourceFile, zos, sourceFile.getName(), KeepDirStructure);
            long end = System.currentTimeMillis();//结束
            System.out.println("压缩完成，耗时：" + (end - start) + " 毫秒");
        } catch (Exception e) {
            result = false;
            e.printStackTrace();
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    e.getStackTrace();
                }
            }
        }
        return result;
    }

    /**
     * 压缩成ZIP 方法2  一次性压缩多个文件
     *
     * @param srcFiles 需要压缩的文件列表
     * @param zipFileName 压缩文件输出
     * @throws RuntimeException 压缩失败会抛出运行时异常
     */
    public static void toZip(String zipFileName, List<File> srcFiles) throws Exception {
        long start = System.currentTimeMillis();
        ZipOutputStream zos = null;
        try {
            FileOutputStream fileOutputStream = new FileOutputStream(zipFileName);
            zos = new ZipOutputStream(fileOutputStream);
            for (File srcFile : srcFiles) {
                compress(srcFile, zos, srcFile.getName(), true);
            }
            long end = System.currentTimeMillis();
            System.out.println("压缩完成，耗时：" + (end - start) + " 毫秒");
        } catch (Exception e) {
            throw new RuntimeException("zip error from ZipUtils", e);
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 递归压缩方法
     *
     * @param sourceFile       源文件
     * @param zos              zip输出流
     * @param name             压缩后的名称
     * @param KeepDirStructure 是否保留原来的目录结构,true:保留目录结构;
     *                         false:所有文件跑到压缩包根目录下(注意：不保留目录结构可能会出现同名文件,会压缩失败)
     * @throws Exception
     */
    public static void compress(File sourceFile, ZipOutputStream zos, String name,
                                boolean KeepDirStructure) throws Exception {

        if (sourceFile.isFile()) {
            // 向zip输出流中添加一个zip实体，构造器中name为zip实体的文件的名字
            zos.putNextEntry(new ZipEntry(name));
            // copy文件到zip输出流中
            int len;
            FileInputStream in = new FileInputStream(sourceFile);
            while ((len = in.read(buf)) != -1) {
                zos.write(buf, 0, len);
            }
            // Complete the entry
            zos.closeEntry();
            in.close();
        } else {
            File[] listFiles = sourceFile.listFiles();
            if (listFiles == null || listFiles.length == 0) {
                // 需要保留原来的文件结构时,需要对空文件夹进行处理
                if (KeepDirStructure) {
                    // 空文件夹的处理
                    zos.putNextEntry(new ZipEntry(name + "/"));
                    // 没有文件，不需要文件的copy
                    zos.closeEntry();
                }
            } else {
                for (File file : listFiles) {
                    // 判断是否需要保留原来的文件结构
                    if (KeepDirStructure) {
                        // 注意：file.getName()前面需要带上父文件夹的名字加一斜杠,
                        // 不然最后压缩包中就不能保留原来的文件结构,即：所有文件都跑到压缩包根目录下了
                        compress(file, zos, name + "/" + file.getName(), KeepDirStructure);
                    } else {
                        compress(file, zos, file.getName(), KeepDirStructure);
                    }

                }
            }
        }
    }

    /**
     * 获取文件路径不包含文件名
     */
    private String getFilePathNotContainsName(String filePath){
        //linux 环境下
        String newFilePath="";
        if (filePath.contains("/")){
            String[] split = filePath.split("/");
            //最后一个就是文件名
            String fileName = split[split.length - 1];
            //将文件名从文件路径中用空字符串给替掉，就相当于去掉了
            newFilePath = filePath.replace(fileName, "");
        }//windows 环境下
        else if (filePath.contains("\\")){
            String[] split = filePath.split("\\\\");
            String fileName = split[split.length - 1];
            //将文件名从文件路径中用空字符串给替掉，就相当于去掉了
            newFilePath = filePath.replace(fileName, "");
        }
        return  newFilePath;
    }

    /**
     * 目录删除
     * @param directory
     */
    private  void deleteDir(File directory){
        //获取目录下所有文件和目录
        File files[] = directory.listFiles();
        for (File file : files) {
            if(file.isDirectory()){
                deleteDir(file);
            }else {
                file.delete();
            }
        }
        //最终把该目录也删除
        directory.delete();
    }
}