/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-01-06
**/
@Data
@TableName(value="t_template_cate")
public class TemplateCate implements Serializable {

    @TableId(value = "cate_template_id",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long cateTemplateId;

    @TableField(value = "cate_name")
    @ApiModelProperty(value = "证照名称")
    private String cateName;

    @TableField(value = "cate_type")
    @ApiModelProperty(value = "证照类型(码值：project_type)")
    private String cateType;

    @TableField(value = "cate_shili")
    @ApiModelProperty(value = "示例")
    private String cateShili;

    @TableField(value = "cate_stauts")
    @ApiModelProperty(value = "证照状态（码值：）")
    private String cateStauts;

    @TableField(value = "remark_one")
    @ApiModelProperty(value = "预留字段")
    private String remarkOne;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "isDelete")
    private Boolean isDelete;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "修改时间")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人")
    private String updateBy;

    public void copy(TemplateCate source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
