/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-04-18
**/
@Data
@TableName(value="t_approved_form")
public class ApprovedForm implements Serializable {

    @TableId(value = "approved_id",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private Long approvedId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @TableField(value = "unit_name")
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @TableField(value = "final_money")
    @ApiModelProperty(value = "决算金额")
    private BigDecimal finalMoney;

    @TableField(value = "approved_money")
    @ApiModelProperty(value = "审定金额")
    private BigDecimal approvedMoney;

    @TableField(value = "file_id")
    @ApiModelProperty(value = "附件")
    private String fileId;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    public void copy(ApprovedForm source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}