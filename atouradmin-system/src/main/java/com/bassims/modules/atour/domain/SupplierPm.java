/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-09-15
**/
@Data
@TableName(value="t_supplier_pm")
public class SupplierPm implements Serializable {

    @TableId(value = "id")
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "supplier_id")
    @ApiModelProperty(value = "厂商Id")
    private Long supplierId;

    @TableField(value = "pm_name")
    @ApiModelProperty(value = "项目经理名称")
    private String pmName;

    @TableField(value = "pm_grade")
    @ApiModelProperty(value = "项目经理评级")
    private String pmGrade;

    @TableField(value = "join_date")
    @ApiModelProperty(value = "参与服务日期")
    private Timestamp joinDate;

    @TableField(value = "leave_date")
    @ApiModelProperty(value = "离开日期")
    private Timestamp leaveDate;

    @TableField(value = "phone")
    @ApiModelProperty(value = "联系电话")
    private String phone;

    @TableField(value = "email")
    @ApiModelProperty(value = "邮箱")
    private String email;

    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @TableField(value = "CREATE_USER")
    @ApiModelProperty(value = "创建人")
    private Long createUser;

    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "UPDATE_USER")
    @ApiModelProperty(value = "更新人")
    private Long updateUser;

    @TableField(value = "UPDATE_TIME" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "IS_DELETE")
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    @TableField(value = "project_scope")
    @ApiModelProperty(value = "照片")
    private String projectScope;

    @TableField(value = "plan_amount")
    @ApiModelProperty(value = "从业资格文件")
    private String planAmount;

    @TableField(value = "assign_amount")
    @ApiModelProperty(value = "从业资格证书编号")
    private String assignAmount;

    @TableField(value = "available_amount")
    @ApiModelProperty(value = "从业资格起始日期-结束日期")
    private String availableAmount;

    @TableField(value = "finish_amount")
    @ApiModelProperty(value = "亚朵认证")
    private String finishAmount;

    @TableField(value = "doing_amount")
    @ApiModelProperty(value = "在建项目数")
    private Integer doingAmount;

    @TableField(value = "completion_date")
    @ApiModelProperty(value = "在建项目拟完工日")
    private Timestamp completionDate;

    @TableField(value = "current_quality_score")
    @ApiModelProperty(value = "签发日期")
    private String currentQualityScore;

    @TableField(value = "avg_quality_score")
    @ApiModelProperty(value = "有效日期")
    private String avgQualityScore;

    @TableField(value = "evaluation_score")
    @ApiModelProperty(value = "认证编号")
    private String evaluationScore;

    @TableField(value = "evaluation_start_time")
    @ApiModelProperty(value = "考评开始时间")
    private Date evaluationStartTime;

    @TableField(value = "evaluation_end_time")
    @ApiModelProperty(value = "考评结束时间")
    private Date evaluationEndTime;

    @TableField(value = "is_used")
    @ApiModelProperty(value = "是否使用")
    private Boolean isUsed;

    @TableField(value = "is_active")
    @ApiModelProperty(value = "是否激活")
    private Boolean isActive;

    @TableField(value = "fisyear")
    @ApiModelProperty(value = "财年(承接品牌)")
    private String fisyear;

    @TableField(value = "total_assign_amount")
    @ApiModelProperty(value = "本财年总派案数")
    private Integer totalAssignAmount;

    @TableField(value = "total_doing_amount")
    @ApiModelProperty(value = "本财年总在建项目数")
    private Integer totalDoingAmount;

    @TableField(value = "total_finish_amount")
    @ApiModelProperty(value = "本财年总已完成项目数")
    private Integer totalFinishAmount;

    @TableField(exist = false)
    private List<Object> evaluation;

    @TableField(value = "pargana_id")
    @ApiModelProperty(value = "大区id")
    private Integer parganaId;

    @TableField(value = "pm_status")
    @ApiModelProperty(value = "项目经理状态")
    private String pmStatus;


    @TableField(value = "supplier_personnel_role")
    @ApiModelProperty(value = "供应商人员角色(码值)")
    private String supplierPersonnelRole;

    @TableField(value = "pm_user_name")
    @ApiModelProperty(value = "用户名")
    private String pmUserName;

    @TableField(value = "pm_gender")
    @ApiModelProperty(value = "性别")
    private String pmGender;

    @TableField(value = "status")
    @ApiModelProperty(value = "审批状态")
    private String status;

    @ApiModelProperty(value = "服务区域")
    @TableField(exist = false)
    private Set<Long> pmServiceAreas;


    @ApiModelProperty(value = "供应商名称")
    @TableField(exist = false)
    private String supNameCn;

    @ApiModelProperty(value = "nodeId")
    @TableField(exist = false)
    private String nodeId;

    @ApiModelProperty(value = "availableAmountStart")
    @TableField(exist = false)
    private String availableAmountStart;

    @ApiModelProperty(value = "availableAmountEnd")
    @TableField(exist = false)
    private String availableAmountEnd;

    @TableField(exist = false)
    @ApiModelProperty(value = "供应商人员角色(码值)数组格式，方便前端转换")
    private String[] supplierPersonnelRoles;

    public void copy(SupplierPm source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}