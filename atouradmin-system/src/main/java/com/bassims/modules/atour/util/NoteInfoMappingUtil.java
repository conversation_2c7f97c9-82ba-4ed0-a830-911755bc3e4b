package com.bassims.modules.atour.util;


import cn.hutool.core.util.ObjectUtil;
import com.bassims.config.RequestDataHelper;
import com.bassims.constant.bsEnum.AtourSystemEnum;
import com.bassims.modules.atour.domain.ProjectInfo;
import com.bassims.modules.atour.domain.ProjectNodeInfo;
import com.bassims.modules.atour.domain.TestTable;
import com.bassims.modules.atour.service.ProjectInfoService;
import com.bassims.modules.atour.service.TestTableService;
import com.bassims.modules.atour.service.dto.ProjectInfoDto;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;


@Log4j2
@Service
public class NoteInfoMappingUtil {
    @Value("${portal.third-table-mapping.tablename}")

    private String tableName;
    @Value("${portal.third-table-mapping.tablenum}")
    private int tableNum;
    @Value("${portal.third-table-mapping.addbatch}")
    private int addBatch;

    @Autowired
    private TestTableService testTableService;
    @Autowired
    private ProjectInfoService projectInfoService;


    /**
     * 根據項目id得到配置表的表名
     *
     * @param projectId
     * @return
     */
    public String getNoteInfoName(Long projectId) {
        System.out.println("tableName=" + tableName + ";  tableNum=" + tableNum + ";   addBatch=" + addBatch);
        if (tableNum == 0) {
            return tableName;
        }
        StringBuffer buffer = new StringBuffer();
        buffer.append(tableName).append((projectId % tableNum + 1 + tableNum * addBatch) + "");
        return buffer.toString();
    }
    /**
     * 测试塞入表名
     *
     * @param projectId
     * @return
     */
    public void setTestTableRealTableName(Long projectId) {
        TestTable info = testTableService.getById(projectId);
        String tableName = info.getNodeTableName();
        // 传递动态表名所需参数
        RequestDataHelper.setRequestData(new HashMap<String, Object>() {{
            put("tableName", tableName);
        }});
    }


    public void setProjectTableName(Long projectId) {
        ProjectInfo info = projectInfoService.getById(projectId);
        if (ObjectUtil.isNotEmpty(info)) {
            String tableName = info.getNodeTableName();
            if (ObjectUtil.isNotEmpty(tableName)) {
                // 传递动态表名所需参数
                RequestDataHelper.setRequestData(new HashMap<String, Object>() {{
                    put("tableName", tableName);
                }});
            }
        } else {
            // 传递动态表名所需参数
            RequestDataHelper.setRequestData(new HashMap<String, Object>() {{
                put("tableName", "t_project_node_info");
            }});
        }
    }

    //保存数据
    public void initialize(Long projectId) {
//        if (AtourSystemEnum.AdditionalTemplatesEnum.ENGINEERING.getKey().equals(processCode)) {
            //如果是项目推进
            this.setProjectTableName(projectId);
//        }
    }

    public void createProjectTableName(ProjectInfoDto resource) {
        String tableName = resource.getNodeTableName();
        // 传递动态表名所需参数
        RequestDataHelper.setRequestData(new HashMap<String, Object>() {{
            put("tableName", tableName);
        }});
//        System.out.println(tableName);
    }
}
