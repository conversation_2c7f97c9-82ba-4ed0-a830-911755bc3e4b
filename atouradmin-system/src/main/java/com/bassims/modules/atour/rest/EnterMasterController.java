package com.bassims.modules.atour.rest;


import com.bassims.annotation.Log;
import com.bassims.modules.atour.service.EnterMasterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Api(tags = "数据入主档")
@RequestMapping("/api/enterMaster")
public class EnterMasterController {

    private final EnterMasterService enterMasterService;

    @Log("插入主档数据")
    @ApiOperation("插入主档数据")
    @GetMapping(value = "/storeDataInMaster")
    public ResponseEntity<Object> storeDataInMaster(){
        return new ResponseEntity<>(enterMasterService.enterMaster(), HttpStatus.OK);
    }

    @Log("能源信息入主档")
    @ApiOperation("能源信息入主档")
    @GetMapping(value = "/masterEnergyConsume")
    public ResponseEntity<Object> masterEnergyConsume(){
        return new ResponseEntity<>(enterMasterService.enterMasterEnergyConsume(), HttpStatus.OK);
    }

    @Log("其他能源信息入主档")
    @ApiOperation("其他能源信息入主档")
    @GetMapping(value = "/masterEnergyConsumeOther")
    public ResponseEntity<Object> masterEnergyConsumeOther(){
        return new ResponseEntity<>(enterMasterService.enterMasterEnergyConsumeOther(), HttpStatus.OK);
    }

    @Log("空调能源信息入主档")
    @ApiOperation("空调能源信息入主档")
    @GetMapping(value = "/masterEnergyConsumeAirCondition")
    public ResponseEntity<Object> masterEnergyConsumeAirCondition(){
        return new ResponseEntity<>(enterMasterService.enterMasterEnergyConsumeAirCondition(), HttpStatus.OK);
    }

    @Log("设备信息入主档")
    @ApiOperation("设备信息入主档")
    @GetMapping(value = "/masterDeviceInfo")
    public ResponseEntity<Object> masterDeviceInfo(){
        return new ResponseEntity<>(enterMasterService.enterMasterDeviceInfo(), HttpStatus.OK);
    }

    @Log("订单及订单详情入主档")
    @ApiOperation("订单及订单详情入主档")
    @GetMapping(value = "/masterOrderData")
    public ResponseEntity<Object> masterOrderData(){
        return new ResponseEntity<>(enterMasterService.enterMasterOrderData(), HttpStatus.OK);
    }

    @Log("图纸信息入主档")
    @ApiOperation("图纸信息入主档")
    @GetMapping(value = "/masterDrawingInfo")
    public ResponseEntity<Object> masterDrawingInfo(){
        return new ResponseEntity<>(enterMasterService.enterMasterDrawingInfo(), HttpStatus.OK);
    }

    @Log("证照信息入主档")
    @ApiOperation("证照信息入主档")
    @GetMapping(value = "/masterLicenseInfo")
    public ResponseEntity<Object> masterLicenseInfo(){
        return new ResponseEntity<>(enterMasterService.enterMasterLicenseInfo(), HttpStatus.OK);
    }
}
