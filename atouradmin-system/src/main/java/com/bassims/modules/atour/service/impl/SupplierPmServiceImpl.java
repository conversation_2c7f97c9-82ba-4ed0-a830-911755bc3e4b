/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.bsEnum.AtourSystemEnum;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.ProjectStakeholders;
import com.bassims.modules.atour.domain.SupplierInfo;
import com.bassims.modules.atour.domain.SupplierPm;
import com.bassims.modules.atour.domain.SysUsersRoles;
import com.bassims.modules.atour.repository.ProjectStakeholdersRepository;
import com.bassims.modules.atour.repository.SupplierInfoRepository;
import com.bassims.modules.atour.repository.SupplierPmRepository;
import com.bassims.modules.atour.repository.SysUsersRolesRepository;
import com.bassims.modules.atour.service.OwnerHotelService;
import com.bassims.modules.atour.service.ProjectInfoService;
import com.bassims.modules.atour.service.SupplierPmService;
import com.bassims.modules.atour.service.dto.SupplierPmDto;
import com.bassims.modules.atour.service.dto.SupplierPmQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.SupplierPmMapper;
import com.bassims.modules.atourWithout.domain.AtosUserRequest;
import com.bassims.modules.atourWithout.service.AtosSubjectInfoService;
import com.bassims.modules.system.domain.Area;
import com.bassims.modules.system.domain.Role;
import com.bassims.modules.system.domain.User;
import com.bassims.modules.system.repository.AreaRepository;
import com.bassims.modules.system.repository.RoleRepository;
import com.bassims.modules.system.repository.UserRepository;
import com.bassims.modules.system.service.DictDetailService;
import com.bassims.modules.system.service.RoleService;
import com.bassims.modules.system.service.UserService;
import com.bassims.modules.system.service.dto.DictDetailDto;
import com.bassims.modules.system.service.dto.RoleDto;
import com.bassims.utils.*;
import com.github.pagehelper.PageInfo;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-09-15
 **/
@Service
public class SupplierPmServiceImpl extends BaseServiceImpl<SupplierPmRepository, SupplierPm> implements SupplierPmService {

    private static final Logger logger = LoggerFactory.getLogger(SupplierPmServiceImpl.class);

    @Autowired
    private SupplierPmRepository supplierPmRepository;
    @Autowired
    private SupplierPmMapper supplierPmMapper;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private SupplierInfoRepository supplierInfoRepository;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private AreaRepository areaRepository;
    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private AtosSubjectInfoService atosSubjectInfoService;
    @Autowired
    private OwnerHotelService ownerHotelService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private UserService userService;
    @Autowired
    private ProjectStakeholdersRepository projectStakeholdersRepository;
    @Autowired
    private SysUsersRolesRepository sysUsersRolesRepository;
    @Autowired
    private DictDetailService dictDetailService;
    @Autowired
    private ProjectInfoService projectInfoService;

    @Override
    public Map<String, Object> queryAll(SupplierPmQueryCriteria criteria, Pageable pageable) {
        if (StringUtils.isEmpty(criteria.getStatus())) {
            criteria.setStatus(JhSystemEnum.approveResultEnum.APPROVE_PASS.getKey());
        }
        getPage(pageable);
        PageInfo<SupplierPm> page = new PageInfo<>(supplierPmRepository.getBylist(criteria));
        List<SupplierPm> supplierPmList = page.getList();
        for(SupplierPm p:supplierPmList){
            if (StringUtils.isNotEmpty(p.getAvailableAmount())) {
                String[] availableAmounts = p.getAvailableAmount().split(",");
                if (availableAmounts.length > 0)
                    p.setAvailableAmountStart(availableAmounts[0]);
                if (availableAmounts.length > 1){
                    p.setAvailableAmountEnd(availableAmounts[1]);
                }
            }
        }
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", supplierPmMapper.toDto(supplierPmList));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<SupplierPmDto> queryAllNoPage(SupplierPmQueryCriteria criteria) {
        List<SupplierPm> supplierPmList = list(QueryHelpPlus.getPredicate(SupplierPm.class, criteria));
        List<SupplierPmDto> supplierPmDtos = supplierPmMapper.toDto(supplierPmList);
        supplierPmDtos.forEach(supplierPmDto -> {
            // TODO

        });
        return supplierPmDtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierPmDto findById(Long id) {
        SupplierPm supplierPm = Optional.ofNullable(getById(id)).orElseGet(SupplierPm::new);
        ValidationUtil.isNull(supplierPm.getId(), getEntityClass().getSimpleName(), "id", id);
        LambdaQueryWrapper supplierPmWrapper = Wrappers.lambdaQuery(SupplierPm.class)
                .eq(SupplierPm::getId, id)
                .eq(SupplierPm::getIsDelete, false);
        List<SupplierPm> list = supplierPmRepository.selectList(supplierPmWrapper);
        final Set<Long> areaCodeByUserId = areaRepository.getAreaCodeByUserId(id);
        for (SupplierPm supplierPmInfo : list) {
            //BigDecimal evaluationScore = supplierPmInfo.getEvaluationScore();
            Date evaluationStartTime = supplierPmInfo.getEvaluationStartTime();
            Date evaluationEndTime = supplierPmInfo.getEvaluationEndTime();
            LinkedList<Object> evaluationList = new LinkedList<>();
            //evaluationList.add(evaluationScore);
            evaluationList.add(evaluationStartTime);
            evaluationList.add(evaluationEndTime);
            supplierPm.setEvaluation(evaluationList);
            supplierPm.setPmServiceAreas(Optional.ofNullable(areaCodeByUserId).orElse(null));
        }
        return supplierPmMapper.toDto(supplierPm);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierPmDto create(SupplierPmDto supplierPmDto, Long supplierId) {
        if (ObjectUtil.isNotEmpty(userRepository.findByUsername(supplierPmDto.getPhone()))) {
            throw new BadRequestException("当前手机号（用户名)" + supplierPmDto.getPhone() + "在用户管理中已存在，不能重复");
        }

        SupplierPm resources = supplierPmMapper.toEntity(supplierPmDto);
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null) {
            currentUserId = 1L;
        }
        resources.setSupplierId(supplierPmDto.getSupplierId());
        resources.setPmUserName(supplierPmDto.getPhone());
        resources.setCreateUser(currentUserId);
        resources.setIsDelete(0);
        resources.setPmStatus(AtourSystemEnum.PmStatusEnum.COOPERATION.getKey());
        //resources.setId(snowflake.nextId());
        //根据供应商信息新增用户
        User save = this.createUser(resources, supplierId, currentUserId);
        resources.setId(save.getId());
        save(resources);
        final LambdaQueryWrapper<SupplierInfo> wrapper = Wrappers.lambdaQuery(SupplierInfo.class)
                .eq(SupplierInfo::getId, supplierPmDto.getSupplierId());
        final SupplierInfo info = supplierInfoRepository.selectOne(wrapper);
        if ("non_platform".equals(info.getPlatformProviderNot())) {
            resources.setStatus(JhSystemEnum.approveStatusEnum.PENDING_APPROVAL.getKey());
            supplierPmRepository.updateById(resources);
            projectInfoService.createSupplierProject(JhSystemEnum.SupplierTypeEnum.SUPPLIER_PM, resources.getId());
        }
        if (ObjectUtil.isNotEmpty(supplierPmDto.getIsPullUserInfoForAtos()) && "1".equals(supplierPmDto.getIsPullUserInfoForAtos())) {
            //不需要推送到atos，直接返回信息
            return findById(resources.getId());
        }
        if (!"non_platform".equals(info.getPlatformProviderNot())) {
            //调用接口同步用户
            String tkId = info.getTkId();
            atosSubjectInfoService.pullUserInfoForAtos(save, tkId);
        }
        return findById(resources.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplierPmDto createPm(SupplierPmDto supplierPmDto, Long supplierId) {
        if (ObjectUtil.isNotEmpty(userRepository.findByUsername(supplierPmDto.getPhone()))) {
            throw new BadRequestException("当前手机号（用户名)" + supplierPmDto.getPhone() + "在用户管理中已存在，不能重复");
        }
        SupplierPm resources = supplierPmMapper.toEntity(supplierPmDto);
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (currentUserId == null) {
            currentUserId = 1L;
        }
        resources.setSupplierId(supplierPmDto.getSupplierId());
        resources.setPmUserName(supplierPmDto.getPhone());
        resources.setCreateUser(currentUserId);
        resources.setIsDelete(0);
        resources.setPmStatus(AtourSystemEnum.PmStatusEnum.COOPERATION.getKey());
        //resources.setId(snowflake.nextId());
        User save = this.createUser(resources, supplierId, currentUserId);
        resources.setId(save.getId());
        save(resources);
        final LambdaQueryWrapper<SupplierInfo> wrapper = Wrappers.lambdaQuery(SupplierInfo.class)
                .eq(SupplierInfo::getId, supplierPmDto.getSupplierId());
        final SupplierInfo info = supplierInfoRepository.selectOne(wrapper);
        if (ObjectUtil.isNotEmpty(supplierPmDto.getIsPullUserInfoForAtos()) && "1".equals(supplierPmDto.getIsPullUserInfoForAtos())) {
            //不需要推送到atos，直接返回信息
            return findById(resources.getId());
        }
        return findById(resources.getId());
    }

    @Override
    @Transactional
    public SupplierPmDto createAndGiveUserSupplier(SupplierPmDto supplierPmDto) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("phone", supplierPmDto.getPhone());
        if (count(queryWrapper) > 0) {
            throw new BadRequestException("该手机号的供应商人员已经存在");
        }
        //查询用户是否存在
        List<User> users = userRepository.selectByPhone(supplierPmDto.getPhone());
        if (users.size() == 0) { //没有用户，就新增
            return this.create(supplierPmDto, supplierPmDto.getSupplierId());
        }
        User user = users.get(0);
        SupplierPm supplierPm = getById(user.getId());
        if (supplierPm != null) {
            throw new BadRequestException("该手机号的用户ID对应的供应商的人员ID已经存在");
        }
        //获取供应商人员角色
        Role role = roleRepository.findRoleIdByCode("csxmjl");
        //判断该用户是否有供应商角色
        queryWrapper.clear();
        queryWrapper.eq("role_id", role.getId());
        queryWrapper.eq("user_id", user.getId());
        Long count = sysUsersRolesRepository.selectCount(queryWrapper);
        if (count == 0) {
            SysUsersRoles roles = new SysUsersRoles();
            roles.setUserId(user.getId());
            roles.setRoleId(role.getId());
            roles.setIsManuallyAdd("1");
            sysUsersRolesRepository.insert(roles);
        }
        //进行新增和付给用户角色
        SupplierPm resources = supplierPmMapper.toEntity(supplierPmDto);
        Long currentUserId = SecurityUtils.getCurrentUserId();
        resources.setSupplierId(supplierPmDto.getSupplierId());
        resources.setPmUserName(supplierPmDto.getPhone());
        resources.setCreateUser(currentUserId);
        resources.setIsDelete(0);
        resources.setPmStatus(AtourSystemEnum.PmStatusEnum.COOPERATION.getKey());
        resources.setId(user.getId());
        save(resources);
        return findById(resources.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SupplierPmDto supplierPmDto) {
        SupplierPm resources = supplierPmMapper.toEntity(supplierPmDto);
        SupplierPm supplierPm = Optional.ofNullable(getById(resources.getId())).orElseGet(SupplierPm::new);
        ValidationUtil.isNull(supplierPm.getId(), "SupplierPm", "id", resources.getId());
        Long currentUserId = SecurityUtils.getCurrentUserId();
        resources.setUpdateUser(currentUserId);
        supplierPm.copy(resources);
        //调用接口同步用户
        final SupplierInfo info = supplierInfoRepository.selectById(supplierPmDto.getSupplierId());
        String tkId = info.getTkId();
        Long id = supplierPm.getId();
        User user = userRepository.findById(id).orElseGet(User::new);
        if (ObjectUtil.isNotEmpty(user) && ObjectUtil.isNotEmpty(user.getId())) {
            user.setUsername(resources.getPmUserName());
            user.setNickName(resources.getPmName());
            user.setGender(resources.getPmGender());
            user.setPhone(resources.getPhone());
            user.setEmail(resources.getEmail());
            user.setAccount(resources.getPmName());
            user.setCode(resources.getPmUserName());

            userService.update(user);
            if (ObjectUtil.isNotEmpty(user.getOpenId())) {
                atosSubjectInfoService.updateUserInfoForAtos(user, tkId);
            }
        } else {
            user = userRepository.findByPhone(resources.getPhone());
            if (ObjectUtil.isNotEmpty(user)) {
                throw new BadRequestException("该手机号在用户中已存在，且与当前供应商人员没有进行关联，无法添加该用户！");
            } else {
                this.createUser(supplierPm, supplierPmDto.getSupplierId(), currentUserId);
            }
        }
        updateById(supplierPm);
    }

    @Override
    public User createUser(SupplierPm resources, Long supplierId, Long currentUserId) {
        //创建该用户，并且城市获取的是厂商的服务区域
        final User one = userRepository.getOne(currentUserId);
        User user = new User();
        user.setId(resources.getId());
        user.setUsername(resources.getPhone());
        user.setNickName(resources.getPmName());
        user.setUserStatus("1");
        user.setGender(resources.getPmGender());
        user.setPhone(resources.getPhone());
        user.setEmail(resources.getEmail());
        user.setPassword(passwordEncoder.encode("yaduo201203.acms"));
        user.setEnabled(one.getEnabled());
        user.setLoginLimit(one.getLoginLimit());
        user.setCreateBy(one.getCreateBy());
        //user.setCreateTime(new Timestamp(System.currentTimeMillis()));
        user.setAccount(resources.getPmName());
        user.setCode(resources.getPmUserName());
        user.setEnabled(Boolean.TRUE);

        List<RoleDto> roleDtos = roleService.queryAllRole();
        Set<Role> roles = new HashSet<>();
        String key = JhSystemEnum.JobEnum.CSXMJL.getKey();
        Long roleId = roleDtos.stream().filter(role -> StringUtils.isNotEmpty(role.getRoleCode()) && role.getRoleCode().equals(key)).findFirst().map(RoleDto::getId).orElse(null);
        Role role = new Role();
        role.setId(roleId);
        roles.add(role);
        user.setRoles(roles);

        User save = userRepository.save(user);
        if (user.getCitys() != null && user.getCitys().size() > 0) {
            userRepository.deleteCityRelationByUserId(user.getId());
            for (Area area : user.getCitys()) {
                userRepository.addCityRelation(save.getId(), area.getAreaCode());
            }
        }
        if (user.getRoles() != null && user.getRoles().size() > 0) {
            roleRepository.deleteRoleRelationByUserId(user.getId(), 1);
            for (Role myrole : user.getRoles()) {
                roleRepository.addRoleRelation(save.getId(), myrole.getId(), 1);
            }
        }

        Set<Long> areaCodeBySupId = areaRepository.getAreaCodeBySupId(supplierId);
        if (ObjectUtil.isNotEmpty(areaCodeBySupId)) {
            userRepository.deleteCityRelationByUserId(save.getId());
            for (Long aLong : areaCodeBySupId) {
                userRepository.addCityRelation(save.getId(), aLong);
            }
        }
        return save;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(SupplierPm resources) {
        SupplierPm supplierPm = Optional.ofNullable(getById(resources.getId())).orElseGet(SupplierPm::new);
        ValidationUtil.isNull(supplierPm.getId(), "SupplierPm", "id", resources.getId());
        Long currentUserId = SecurityUtils.getCurrentUserId();
        resources.setUpdateUser(currentUserId);
        supplierPm.copy(resources);
        //调用接口同步用户
        Long id = supplierPm.getId();
        User user = userRepository.findById(id).orElseGet(User::new);
        //供应商人员禁用
        if (resources.getPmStatus().equals(AtourSystemEnum.PmStatusEnum.NONCOOPERATION.getKey())) {
            user.setEnabled(Boolean.FALSE);
        } else {
            user.setEnabled(Boolean.TRUE);
        }
        if (user.getId() != null) {
            userService.updateStatus(user);
        }
        updateById(supplierPm);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSupplierPmStatus(List<SupplierPm> resources) {
        for (SupplierPm resource : resources) {
            //调用接口同步用户
            Long id = resource.getId();
            User user = userRepository.findById(id).orElseGet(User::new);
            //供应商人员禁用
            if (resource.getPmStatus().equals(AtourSystemEnum.PmStatusEnum.NONCOOPERATION.getKey())) {
                user.setEnabled(Boolean.FALSE);
            } else {
                user.setEnabled(Boolean.TRUE);
            }
            updateById(resource);
            userService.updateStatus(user);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSupplierPmStatus(List<SupplierPm> resources) {
        Set<Long> ids = new HashSet<>();
        for (SupplierPm resource : resources) {
            //供应商人员禁用
            updateById(resource);
            ids.add(resource.getId());
        }
        userService.delete(ids);
    }

    @Override
    public void deleteSupplierPm(SupplierPm resource) {
        Set<Long> ids = new HashSet<>();
        //判断当前用户是否已经在项目干系人中存在
        LambdaQueryWrapper<ProjectStakeholders> wrapper = Wrappers.lambdaQuery(ProjectStakeholders.class)
                .eq(ProjectStakeholders::getUserId, resource.getId())
                .eq(ProjectStakeholders::getShakeholderStatus, JhSystemEnum.StakeholderStatusEnum.STA_STATUS0.getKey());
        Long aLong = projectStakeholdersRepository.selectCount(wrapper);
        if (aLong > 0) {
            throw new BadRequestException("当前用户已经存在项目干系人中，暂不允许删除！");
        }
        //供应商人员禁用
        resource.setIsDelete(1);
        updateById(resource);
        ids.add(resource.getId());
        userService.delete(ids);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            supplierPmRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<SupplierPmDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SupplierPmDto supplierPm : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("厂商Id", supplierPm.getSupplierId());
            map.put("项目经理名称", supplierPm.getPmName());
            map.put("项目经理评级", supplierPm.getPmGrade());
            map.put("参与服务日期", supplierPm.getJoinDate());
            map.put("离开日期", supplierPm.getLeaveDate());
            map.put("联系电话", supplierPm.getPhone());
            map.put("邮箱", supplierPm.getEmail());
            map.put("备注", supplierPm.getRemark());
            map.put("创建人", supplierPm.getCreateUser());
            map.put("创建时间", supplierPm.getCreateTime());
            map.put("更新人", supplierPm.getUpdateUser());
            map.put("更新时间", supplierPm.getUpdateTime());
            map.put("是否删除", supplierPm.getIsDelete());
            map.put("项目经理工程范围", supplierPm.getProjectScope());
            map.put("可承担项目数", supplierPm.getPlanAmount());
            map.put("已派案数", supplierPm.getAssignAmount());
            map.put("尚可承担项目数", supplierPm.getAvailableAmount());
            map.put("累计已完成项目量数", supplierPm.getFinishAmount());
            map.put("在建项目数", supplierPm.getDoingAmount());
            map.put("在建项目拟完工日", supplierPm.getCompletionDate());
            map.put("本财年质量评分", supplierPm.getCurrentQualityScore());
            map.put("平均质量评分", supplierPm.getAvgQualityScore());
            map.put("是否使用", supplierPm.getIsUsed());
            map.put("是否激活", supplierPm.getIsActive());
            map.put("财年", supplierPm.getFisyear());
            map.put("本财年总派案数", supplierPm.getTotalAssignAmount());
            map.put("本财年总在建项目数", supplierPm.getTotalDoingAmount());
            map.put("本财年总已完成项目数", supplierPm.getTotalFinishAmount());
            map.put("大区id", supplierPm.getParganaId());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
//        List<Map<String, Object>> list = new ArrayList<>();
//        Map<String, Object> map = new LinkedHashMap<>();
//        map.put("手机号", "");
//        map.put("姓名", "");
//        map.put("性别", "");
//        map.put("职位", "");
//        map.put("供应商名称", "");
//        map.put("承接品牌", "");
//        map.put("从业资格证书编号", "");
//        map.put("从业资格开始日期", "");
//        map.put("从业资格结束日期", "");
//        map.put("亚朵认证", "");
//        map.put("签发日期", "");
//        map.put("有效日期", "");
//        map.put("认证编号", "");
//        list.add(map);
//
//        FileUtil.downloadExcel(list, response);
        response.setCharacterEncoding(request.getCharacterEncoding());
        response.setContentType("application/octet-stream");
        InputStream fis = null;
        try {
            fis = new ClassPathResource("excel"+File.separator+"供应商人员模板.xlsx").getInputStream();
            response.setHeader("Content-Disposition", "attachment; filename=供应商人员模板.xlsx");
            IOUtils.copy(fis, response.getOutputStream());
            response.flushBuffer();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
    }

    @Override
    public SupplierPmDto supPmInfoByName(String pmName) {
        LambdaQueryWrapper supInfoQuery = Wrappers.lambdaQuery(SupplierPm.class)
                .eq(SupplierPm::getPmName, pmName)
                .eq(SupplierPm::getIsDelete, 0);
        List<SupplierPm> pmList = supplierPmRepository.selectList(supInfoQuery);
        if (CollUtil.isNotEmpty(pmList)) {
            SupplierPm supplierPm = pmList.get(0);
            return supplierPmMapper.toDto(supplierPm);
        }
        return null;
    }

    @Override
    public List<SupplierPmDto> getSupplierPmList(SupplierPmQueryCriteria criteria) {
        //根据供应商ID和供应商人员角色查询供应商人员下拉
//        if (ObjectUtils.isEmpty(criteria.getSupplierId())) {
//            throw new BadRequestException("未拿到施工单位的数据，请确定是否已经选择了施工单位");
//        }
        SupplierPmQueryCriteria supplierPmQueryCriteria = new SupplierPmQueryCriteria();
        supplierPmQueryCriteria.setSupplierId(criteria.getSupplierId());
        supplierPmQueryCriteria.setSupplierPersonnelRole(criteria.getSupplierPersonnelRole());
        List<SupplierPmDto> supplierPmDtos = supplierPmRepository.getSupplierPmList(supplierPmQueryCriteria);
        return supplierPmDtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savOrUpdatePmFromAtos(AtosUserRequest atosUserRequest) {
        //查询主体
        String tk = atosUserRequest.getTk();
        LambdaQueryWrapper<SupplierInfo> queryWrapper = Wrappers.lambdaQuery(SupplierInfo.class)
                .eq(SupplierInfo::getTkId, tk);
        SupplierInfo supplierInfo = supplierInfoRepository.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(supplierInfo)) {
            throw new BadRequestException("供应商主体信息不存在，请先推送供应商主体信息");
        }
        //查询项目经理
        String uk = atosUserRequest.getUk();
        String mobile = atosUserRequest.getMobile();
        User userByOpenIdOrPhone = null;
        try {
            userByOpenIdOrPhone = userRepository.findUserByOpenIdOrPhone(uk, mobile);
        } catch (Exception e) {
            throw new BadRequestException("无法录入用户数据，当前手机号和uk无法对应");
        }
        if (ObjectUtil.isNotEmpty(userByOpenIdOrPhone)) {
            //更新供应商
            userByOpenIdOrPhone.setOpenId(uk);
            userByOpenIdOrPhone.setLoginLimit("password,SSO");
            userByOpenIdOrPhone.setUsername(mobile);
            userByOpenIdOrPhone.setEmail(atosUserRequest.getEmail());
            userByOpenIdOrPhone.setPhone(mobile);
            userByOpenIdOrPhone.setAccount(atosUserRequest.getUserName());

            if (ObjectUtil.isEmpty(userByOpenIdOrPhone.getNickName())) {
                userByOpenIdOrPhone.setNickName(atosUserRequest.getUserName());
            }
            userByOpenIdOrPhone.setCode(atosUserRequest.getUserName());
            userByOpenIdOrPhone.setFirstDepartName(atosUserRequest.getFirstDepartName());
            userByOpenIdOrPhone.setFirstDepartId(atosUserRequest.getFirstDepartId());
            userByOpenIdOrPhone.setTk(atosUserRequest.getTk());

            //同步用户离线在职状态   0:创建 1:更新 2:删除
            if (ObjectUtil.isNotEmpty(atosUserRequest.getMqType()) && atosUserRequest.getMqType() != 2) {
                userByOpenIdOrPhone.setEnabled(Boolean.TRUE);
            } else {
                userByOpenIdOrPhone.setEnabled(Boolean.FALSE);
            }

            if (ObjectUtil.isNotEmpty(atosUserRequest.getJobId()) && !atosUserRequest.getJobId().equals(userByOpenIdOrPhone.getJobId())) {
                List<RoleDto> roleDtos = roleService.queryAllRole();
                Set<Role> roles = new HashSet<>();
                String key = JhSystemEnum.JobEnum.CSXMJL.getKey();
                Long roleId = roleDtos.stream().filter(role -> StringUtils.isNotEmpty(role.getRoleCode()) && role.getRoleCode().equals(key)).findFirst().map(RoleDto::getId).orElse(null);
                Role role = new Role();
                role.setId(roleId);
                roles.add(role);
                userByOpenIdOrPhone.setRoles(roles);
            }

            userByOpenIdOrPhone.setJobId(atosUserRequest.getJobId());
            userByOpenIdOrPhone.setJobName(atosUserRequest.getJobName());
            userService.update(userByOpenIdOrPhone);

            LambdaQueryWrapper<SupplierPm> pmLambdaQueryWrapper = Wrappers.lambdaQuery(SupplierPm.class)
                    .eq(SupplierPm::getId, userByOpenIdOrPhone.getId());
            SupplierPm supplierPm = supplierPmRepository.selectOne(pmLambdaQueryWrapper);
            if (ObjectUtil.isEmpty(supplierPm)) {
                supplierPm = new SupplierPm();
                supplierPm.setId(userByOpenIdOrPhone.getId());
                supplierPm.setSupplierId(ObjectUtil.isNotEmpty(supplierInfo) ? supplierInfo.getId() : null);
                supplierPm.setIsDelete(0);
                supplierPm.setPmStatus(AtourSystemEnum.PmStatusEnum.COOPERATION.getKey());
                supplierPm.setEmail(atosUserRequest.getEmail());
                supplierPm.setPhone(atosUserRequest.getMobile());
                supplierPm.setPmUserName(atosUserRequest.getMobile());
                supplierPm.setPmName(atosUserRequest.getUserName());
                this.save(supplierPm);
            } else {
                supplierPm.setEmail(atosUserRequest.getEmail());
                supplierPm.setPhone(atosUserRequest.getMobile());
                supplierPm.setPmUserName(atosUserRequest.getMobile());
                supplierPm.setPmName(atosUserRequest.getUserName());
                supplierPm.setSupplierId(ObjectUtil.isNotEmpty(supplierInfo) ? supplierInfo.getId() : null);
                supplierPmRepository.updateById(supplierPm);
            }

            if (userByOpenIdOrPhone.getRoles() != null && userByOpenIdOrPhone.getRoles().size() > 0) {
                roleRepository.deleteRoleRelationByUserId(userByOpenIdOrPhone.getId(), 1);
                for (Role myrole : userByOpenIdOrPhone.getRoles()) {
                    roleRepository.addRoleRelation(userByOpenIdOrPhone.getId(), myrole.getId(), 1);
                }
            }

        } else {
            //新增供应商
            Snowflake snowflake = IdUtil.getSnowflake(1, 1);
            long supplier = snowflake.nextId();
            Snowflake adminInfoId = IdUtil.getSnowflake(1, 1);
            Long adminId = adminInfoId.nextId();
            SupplierPm supplierPm = new SupplierPm();
            supplierPm.setId(supplier);
            supplierPm.setPmUserName(atosUserRequest.getMobile());
            supplierPm.setPmName(atosUserRequest.getUserName());
            supplierPm.setPhone(atosUserRequest.getMobile());
            supplierPm.setSupplierId(ObjectUtil.isNotEmpty(supplierInfo) ? supplierInfo.getId() : null);
            supplierPm.setIsDelete(0);
            supplierPm.setPmStatus(AtourSystemEnum.PmStatusEnum.COOPERATION.getKey());
            supplierPm.setId(supplier);
            //创建该用户，并且城市获取的是厂商的服务区域
            User user = new User();
            user.setId(supplier);
            user.setUsername(mobile);
            user.setNickName(atosUserRequest.getUserName());
            user.setUserStatus("1");
            user.setGender(AtourSystemEnum.userGender.getSpec(String.valueOf(atosUserRequest.getGender())));
            user.setPhone(atosUserRequest.getMobile());
            user.setEmail(atosUserRequest.getEmail());
            user.setPassword(passwordEncoder.encode("yaduo201203.acms"));
            //user.setCreateTime(new Timestamp(System.currentTimeMillis()));
            user.setAccount(atosUserRequest.getUserName());
            user.setCode(atosUserRequest.getUserName());
            user.setEnabled(Boolean.TRUE);
            user.setOpenId(uk);
            user.setLoginLimit("password,SSO");
            user.setTk(atosUserRequest.getTk());
            user.setFirstDepartName(atosUserRequest.getFirstDepartName());
            user.setFirstDepartId(atosUserRequest.getFirstDepartId());

            //同步用户离线在职状态   0:创建 1:更新 2:删除
            if (ObjectUtil.isNotEmpty(atosUserRequest.getMqType()) && atosUserRequest.getMqType() != 2) {
                user.setEnabled(Boolean.TRUE);
            } else {
                user.setEnabled(Boolean.FALSE);
            }


            user.setJobId(atosUserRequest.getJobId());
            user.setJobName(atosUserRequest.getJobName());
            List<RoleDto> roleDtos = roleService.queryAllRole();
            Set<Role> roles = new HashSet<>();
            String key = JhSystemEnum.JobEnum.CSXMJL.getKey();
            Long roleId = roleDtos.stream().filter(role -> StringUtils.isNotEmpty(role.getRoleCode()) && role.getRoleCode().equals(key)).findFirst().map(RoleDto::getId).orElse(null);
            Role role = new Role();
            role.setId(roleId);
            roles.add(role);
            user.setRoles(roles);

            User save = userRepository.save(user);
            if (user.getCitys() != null && user.getCitys().size() > 0) {
                userRepository.deleteCityRelationByUserId(save.getId());
                for (Area area : user.getCitys()) {
                    userRepository.addCityRelation(save.getId(), area.getAreaCode());
                }
            }
            if (user.getRoles() != null && user.getRoles().size() > 0) {
                roleRepository.deleteRoleRelationByUserId(save.getId(), 1);
                for (Role myrole : user.getRoles()) {
                    roleRepository.addRoleRelation(save.getId(), myrole.getId(), 1);
                }
            }

            Long id = save.getId();
            supplierPm.setSupplierId(id);
            this.save(supplierPm);

            if (ObjectUtil.isNotEmpty(atosUserRequest.getExtend_info())) {
                ownerHotelService.insertOwnerHotel(id, atosUserRequest.getExtend_info());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePmFromAtos(AtosUserRequest atosUserRequest) {
        String tk = atosUserRequest.getTk();
        LambdaQueryWrapper<SupplierInfo> queryWrapper = Wrappers.lambdaQuery(SupplierInfo.class)
                .eq(SupplierInfo::getTkId, tk);
        SupplierInfo supplierInfo = supplierInfoRepository.selectOne(queryWrapper);
        //查询项目经理
        String uk = atosUserRequest.getUk();
        String mobile = atosUserRequest.getMobile();
        User userByOpenIdOrPhone = userRepository.findUserByOpenIdOrPhone(uk, mobile);
        if (ObjectUtil.isNotEmpty(userByOpenIdOrPhone)) {
            //更新供应商
            userByOpenIdOrPhone.setUserStatus("1");
            userRepository.updateUserStatusById(userByOpenIdOrPhone.getId(), userByOpenIdOrPhone.getUserStatus());
            LambdaQueryWrapper<SupplierPm> pmLambdaQueryWrapper = Wrappers.lambdaQuery(SupplierPm.class)
                    .eq(SupplierPm::getId, userByOpenIdOrPhone.getId());
            SupplierPm supplierPm = supplierPmRepository.selectOne(pmLambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(supplierPm)) {
                if (4 == atosUserRequest.getDataStatus()) {
                    supplierPm.setIsDelete(1);
                } else if (1 == atosUserRequest.getDataStatus()) {
                    supplierPm.setIsDelete(0);
                    supplierPm.setPmStatus(AtourSystemEnum.PmStatusEnum.NONCOOPERATION.getKey());
                } else {
                    supplierPm.setIsDelete(0);
                    supplierPm.setPmStatus(AtourSystemEnum.PmStatusEnum.COOPERATION.getKey());
                }
                saveOrUpdate(supplierPm);
            }
        }

    }

    @Override
    public Boolean supplierPmImport(MultipartFile file) throws IOException {
        List<DictDetailDto> supplierPm = dictDetailService.getDictByName("supplier_pm");
        List<DictDetailDto> executionStandards = dictDetailService.getDictByName("execution_standards");
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream(), 0);
        Map<String, String> aliasMap = new HashMap<>();
        aliasMap.put("*手机号", "phone");
        aliasMap.put("*姓名", "pmName");
        aliasMap.put("*性别", "pmGender");
        aliasMap.put("职位", "supplierPersonnelRole");
        aliasMap.put("*供应商名称", "supNameCn");
        aliasMap.put("*承接品牌", "fisyear");
        aliasMap.put("从业资格证书编号", "assignAmount");
        aliasMap.put("从业资格开始日期", "availableAmount");
        aliasMap.put("亚朵认证", "finishAmount");
        aliasMap.put("签发日期", "currentQualityScore");
        aliasMap.put("有效日期", "avgQualityScore");
        aliasMap.put("认证编号", "evaluationScore");
        reader.setHeaderAlias(aliasMap);
        List<SupplierPmDto> readMto = reader.read(1, 3, SupplierPmDto.class);
        for (SupplierPmDto ob : readMto) {
            SupplierInfo info = supplierInfoRepository.selectOne(Wrappers.lambdaQuery(SupplierInfo.class)
                    .eq(SupplierInfo::getSupNameCn, ob.getSupNameCn()).last(" limit 1"));
            if (info == null) {
                throw new BadRequestException("请填写正确的供应商");
            }
            if (StringUtils.isNotEmpty(ob.getAvailableAmount())) {
                String[] as = ob.getAvailableAmount().split(",");
                ob.setAvailableAmount(as[0]);
                if (as.length > 1) ob.setAvailableAmount(ob.getAvailableAmount().split(",")[1]);
            }
            ob.setFisyear(getDictValue(executionStandards, ob.getFisyear()));
            ob.setSupplierPersonnelRole(getDictValue(supplierPm, ob.getSupplierPersonnelRole()));
            ob.setSupplierId(info.getId());
            this.createPm(ob, info.getId());
        }
        return null;
    }

    private String getDictValue(List<DictDetailDto> dicts, String key) {
        StringBuilder builder = new StringBuilder();
        if (StringUtils.isEmpty(key)) {
            return "";
        }
        dicts.forEach(d -> {
            for (String s : key.split(",")) {
                if (s.equals(d.getLabel())) {
                    builder.append(d.getValue()).append(",");
                }
            }
        });
        String result = builder.toString();
        if (result.endsWith(",")) {
            return result.substring(0, result.length()-1);
        } else {
            return result;
        }
    }
}