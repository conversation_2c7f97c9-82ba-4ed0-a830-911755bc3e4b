/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.modules.atour.domain.TPlanEventInfo;
import com.bassims.modules.atour.repository.TemplateConditionRepository;
import com.bassims.modules.atour.service.PlanEventInfoService;
import com.bassims.modules.atour.service.dto.PaymentApplicationDto;
import com.bassims.modules.atour.service.dto.PaymentApplicationQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-02-02
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "计划事件表管理")
@RequestMapping("/api/PlanEvent")
public class PlanEventInfoController {
    private static final Logger logger = LoggerFactory.getLogger(PlanEventInfoController.class);
    @Autowired
    private PlanEventInfoService planEventInfoService;

    /**
     * @real_return {@link ResponseEntity< List < PaymentApplicationDto >>}
     */
    @RequestMapping("/groupList")
    @Log("查询方案编组")
    @ApiOperation("查询方案编组")
    @MyDataPermission(title = "任务关联配置")
    public ResponseEntity<Object> groupList(){
        return new ResponseEntity<>(planEventInfoService.groupList(),HttpStatus.OK);
    }


    @RequestMapping("/savePlanEvent")
    @Log("新增计划事件表")
    @ApiOperation("新增计划事件表")
    @MyDataPermission(title = "任务关联配置")
    public ResponseEntity<Object> savePlanEvent(@Validated @RequestBody TPlanEventInfo resources){
        return new ResponseEntity<>(planEventInfoService.savePlanEvent(resources),HttpStatus.CREATED);
    }

    @RequestMapping("/list")
    @Log("查询计划事件表")
    @ApiOperation("查询计划事件表")
    @MyDataPermission(title = "任务关联配置")
    public ResponseEntity<Object> query(@RequestParam(value ="groupId",required = true) Long groupId,@RequestParam(value ="templateId",required = true)Long templateId){
        return new ResponseEntity<>(planEventInfoService.getList(groupId,templateId),HttpStatus.OK);
    }
    @RequestMapping("/update")
    @Log("修改计划事件表")
    @ApiOperation("修改计划事件表")
    @MyDataPermission(title = "任务关联配置")
    public ResponseEntity<Object> update(@Validated @RequestBody TPlanEventInfo resources){
        planEventInfoService.updateById(resources);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @RequestMapping("/delete")
    @Log("删除计划事件表")
    @ApiOperation("删除计划事件表")
    @MyDataPermission(title = "任务关联配置")
    public ResponseEntity<Object> delete(@RequestParam(value ="planEventId",required = true) Long planEventId) {
        return new ResponseEntity<>(planEventInfoService.deletePlanEvent(planEventId),HttpStatus.OK);
    }
    @RequestMapping("/getOtherList")
    @Log("查询没有对应的节点")
    @ApiOperation("查询没有对应的节点")
//    @MyDataPermission(title = "任务关联配置")
    public ResponseEntity<Object> getOtherList(@RequestParam(value ="groupId",required = true) Long groupId){
        return new ResponseEntity<>(planEventInfoService.getOtherList(groupId),HttpStatus.OK);
    }
}