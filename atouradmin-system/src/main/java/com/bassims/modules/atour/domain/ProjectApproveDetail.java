/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-03-28
**/
@Data
@TableName(value="t_project_approve_detail")
    public class ProjectApproveDetail implements Serializable {

    @TableId(value = "approve_detail_id")
    @ApiModelProperty(value = "审批详情主键")
    private Long approveDetailId;

    @TableField(value = "approve_id")
    @ApiModelProperty(value = "审批主键")
    private Long approveId;


    @TableField(value = "approve_node_id")
    @ApiModelProperty(value = "项目审批模板主键")
    private Long approveNodeId;


    @TableField(value = "approve_group_id")
    @ApiModelProperty(value = "审批组主键")
    private Long approveGroupId;


    @TableField(value = "approve_template_detail_id")
    @ApiModelProperty(value = "审批子模板主键")
    private Long approveTemplateDetailId;

    @TableField(value = "parent_id")
    @ApiModelProperty(value = "父节点id")
    private Long parentId;

    @TableField(value = "has_child")
    @ApiModelProperty(value = "是否有子集")
    private Boolean hasChild;

    @TableField(value = "approve_role")
    @ApiModelProperty(value = "审批角色")
    private String approveRole;

    @TableField(value = "approve_role_name")
    @ApiModelProperty(value = "审批角色")
    private String approveRoleName;


    @TableField(value = "approve_user")
    @ApiModelProperty(value = "审批人id")
    private Long approveUser;

    @TableField(value = "emp_code")
    @ApiModelProperty(value = "审批人code")
    private String empCode;

    @TableField(value = "approve_status")
    @ApiModelProperty(value = "审批状态")
    private String approveStatus;

    @TableField(value = "approve_result")
    @ApiModelProperty(value = "审批结果")
    private String approveResult;

    @TableField(value = "approve_end")
    @ApiModelProperty(value = "审批时间")
    private Timestamp approveEnd;

    @TableField(value = "approve_option")
    @ApiModelProperty(value = "审批意见")
    private String approveOption;

    @TableField(value = "approve_index")
    @ApiModelProperty(value = "审批排序")
    private Integer approveIndex;

    @TableField(value = "is_modifiable")
    @ApiModelProperty(value = "该审批角色是否可以修改")
    private Integer isModifiable;

    @TableField(value = "modifiable_code")
    @ApiModelProperty(value = "可修改的字段code")
    private String modifiableCode;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "createTime")
    private Timestamp createTime;

    @TableField(value = "create_by",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "updateTime")
    private Timestamp updateTime;

    @TableField(value = "update_by",fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;


    @TableField(value = "modifiable_code_textarea")
    @ApiModelProperty(value = "可修改的字段code")
    private String modifiableCodeTextarea;


    @TableField(value = "is_show")
    @ApiModelProperty(value = "是否显示（0.显示、1.隐藏）")
    private Boolean isShow;

    @TableField(value = "is_exist_open_condition")
    @ApiModelProperty(value = "是否存在审批开启条件（0.不存在、1.存在）")
    private Boolean isExistOpenCondition;

    @TableField(value = "approval_opening_condition_node_code")
    @ApiModelProperty(value = "审批开启条件的三级code")
    private String approvalOpeningConditionNodeCode;


    @TableField(value = "approval_opening_condition_value")
    @ApiModelProperty(value = "审批开启条件的三级code值")
    private String approvalOpeningConditionValue;

    public void copy(ProjectApproveDetail source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}