/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.modules.atour.domain.TemplateTable;
import com.bassims.modules.atour.service.TemplateTableService;
import com.bassims.modules.atour.service.dto.TableConfiguringTertiaryNodesDto;
import com.bassims.modules.atour.service.dto.TemplateTableDto;
import com.bassims.modules.atour.service.dto.TemplateTableQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-10-15
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "系统列表模版管理")
@RequestMapping("/api/templateTable")
public class TemplateTableController {

    private static final Logger logger = LoggerFactory.getLogger(TemplateTableController.class);

    private final TemplateTableService templateTableService;

//    @Log("导出数据")
//    @ApiOperation("导出数据")
//    @GetMapping(value = "/download")
//    public void download(HttpServletResponse response, TemplateTableQueryCriteria criteria) throws IOException {
//        templateTableService.download(templateTableService.queryAll(criteria), response);
//    }

    /**
    * @real_return {@link ResponseEntity<List<TemplateTableDto>>}
    */
    @GetMapping("/list")
    @Log("查询系统列表模版")
    @ApiOperation("查询系统列表模版")
    @MyDataPermission(title = "物资管理,配置管理")
    public ResponseEntity<Object> query(TemplateTableQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(templateTableService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<TemplateTableDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询系统列表模版")
    @ApiOperation("查询系统列表模版")
    @MyDataPermission(title = "物资管理,配置管理")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(templateTableService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增系统列表模版")
    @ApiOperation("新增系统列表模版")
    @MyDataPermission(title = "物资管理,配置管理")
    public ResponseEntity<Object> create(@Validated @RequestBody List<TableConfiguringTertiaryNodesDto> dtos){
        return new ResponseEntity<>(templateTableService.create(dtos),HttpStatus.CREATED);
    }



    @PostMapping("/update")
    @Log("修改系统列表模版")
    @ApiOperation("修改系统列表模版")
    @MyDataPermission(title = "物资管理,配置管理")
    public ResponseEntity<Object> update(@Validated @RequestBody TemplateTable resources){
        templateTableService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除系统列表模版")
    @ApiOperation("删除系统列表模版")
    @MyDataPermission(title = "物资管理,配置管理")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        templateTableService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    @AnonymousAccess
    @GetMapping("/updateTable")
    @Log("新增系统列表模版")
    @ApiOperation("根据表头更新已经存在的json")
    public ResponseEntity<Object> updateTable(String dynamicTableType){
        return new ResponseEntity<>(templateTableService.updateTable(dynamicTableType),HttpStatus.CREATED);
    }

}