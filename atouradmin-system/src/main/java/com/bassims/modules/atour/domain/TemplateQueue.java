/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-08-25
**/
@Data
@TableName(value="t_template_queue")
public class TemplateQueue implements Serializable {

    @TableId(value = "template_queue_id",type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "二三级主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long templateQueueId;

    @TableField(value = "template_id")
    @ApiModelProperty(value = "模板主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long templateId;

    @TableField(value = "template_code")
    @ApiModelProperty(value = "templateCode")
    private String templateCode;

    @TableField(value = "parent_id")
    @ApiModelProperty(value = "父节点")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long parentId;

    @TableField(value = "node_index")
    @ApiModelProperty(value = "子节点下标")
    private double nodeIndex;

    @TableField(value = "node_level")
    @ApiModelProperty(value = "节点等级")
    private Integer nodeLevel;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

    @TableField(value = "node_name")
    @ApiModelProperty(value = "名称")
    private String nodeName;

    @TableField(value = "node_code")
    @ApiModelProperty(value = "节点编码")
    private String nodeCode;


    @TableField(value = "one_template_id")
    @ApiModelProperty(value = "关联的模版的一级template_id")
    private Long oneTemplateId;

    @TableField(value = "two_template_id")
    @ApiModelProperty(value = "关联的模版的二级template_id")
    private Long twoTemplateId;

    @TableField(value = "stencil_level")
    @ApiModelProperty(value = "模版级别,码值stencil_level")
    private String stencilLevel;

    public void copy(TemplateQueue source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}