package com.bassims.modules.atour.service.mapstruct;

import com.bassims.base.BaseMapper;
import com.bassims.modules.atour.domain.ProjectSupplierReport;
import com.bassims.modules.atour.service.dto.ProjectSupplierReportDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 工程供应商合作报表
 *
 * <AUTHOR>
 * @date 2023/03/14
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProjectSupplierReportMapper extends BaseMapper<ProjectSupplierReportDto, ProjectSupplierReport> {

}
