/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.MasterPersonInfo;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.MasterPersonInfoRepository;
import com.bassims.modules.atour.service.MasterPersonInfoService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.MasterPersonInfoDto;
import com.bassims.modules.atour.service.dto.MasterPersonInfoQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.MasterPersonInfoMapper;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-11-22
**/
@Service
public class MasterPersonInfoServiceImpl extends BaseServiceImpl<MasterPersonInfoRepository,MasterPersonInfo> implements MasterPersonInfoService {

    private static final Logger logger = LoggerFactory.getLogger(MasterPersonInfoServiceImpl.class);

    @Autowired
    private MasterPersonInfoRepository masterPersonInfoRepository;
    @Autowired
    private MasterPersonInfoMapper masterPersonInfoMapper;

    @Override
    public Map<String,Object> queryAll(MasterPersonInfoQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<MasterPersonInfo> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(MasterPersonInfo.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", masterPersonInfoMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<MasterPersonInfoDto> queryAll(MasterPersonInfoQueryCriteria criteria){
        return masterPersonInfoMapper.toDto(list(QueryHelpPlus.getPredicate(MasterPersonInfo.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MasterPersonInfoDto findById(Long stakeholderId) {
        MasterPersonInfo masterPersonInfo = Optional.ofNullable(getById(stakeholderId)).orElseGet(MasterPersonInfo::new);
        ValidationUtil.isNull(masterPersonInfo.getStakeholderId(),getEntityClass().getSimpleName(),"stakeholderId",stakeholderId);
        return masterPersonInfoMapper.toDto(masterPersonInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MasterPersonInfoDto create(MasterPersonInfo resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setStakeholderId(snowflake.nextId()); 
        save(resources);
        return findById(resources.getStakeholderId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(MasterPersonInfo resources) {
        MasterPersonInfo masterPersonInfo = Optional.ofNullable(getById(resources.getStakeholderId())).orElseGet(MasterPersonInfo::new);
        ValidationUtil.isNull( masterPersonInfo.getStakeholderId(),"MasterPersonInfo","id",resources.getStakeholderId());
        masterPersonInfo.copy(resources);
        updateById(masterPersonInfo);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long stakeholderId : ids) {
            masterPersonInfoRepository.deleteById(stakeholderId);
        }
    }

    @Override
    public void download(List<MasterPersonInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (MasterPersonInfoDto masterPersonInfo : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("门店id", masterPersonInfo.getStoreId());
            map.put("门店编号", masterPersonInfo.getStoreNo());
            map.put("项目id", masterPersonInfo.getProjectId());
            map.put("用户id", masterPersonInfo.getUserId());
            map.put("角色id", masterPersonInfo.getRoleId());
            map.put("角色名称", masterPersonInfo.getRoleName());
            map.put("加入时间", masterPersonInfo.getJoinTime());
            map.put("状态", masterPersonInfo.getShakeholderStatus());
            map.put("离开时间", masterPersonInfo.getLeaveTime());
            map.put("原因", masterPersonInfo.getReason());
            map.put("是否是审批角色", masterPersonInfo.getIsApprove());
            map.put("是否不展示", masterPersonInfo.getIsNotshow());
            map.put(" createTime",  masterPersonInfo.getCreateTime());
            map.put(" createBy",  masterPersonInfo.getCreateBy());
            map.put(" updateTime",  masterPersonInfo.getUpdateTime());
            map.put(" updateBy",  masterPersonInfo.getUpdateBy());
            map.put("是否可用", masterPersonInfo.getIsEnabled());
            map.put("是否删除", masterPersonInfo.getIsDelete());
            map.put("角色code", masterPersonInfo.getRoleCode());
            map.put("下拉列表角色名称", masterPersonInfo.getDownCode());
            map.put("订单id", masterPersonInfo.getOrderId());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}