package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.ProjectFacilityReport;
import com.bassims.modules.atour.service.dto.ProjectFacilityReportQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 工程设备报表
 *
 * <AUTHOR>
 * @date 2023/03/22
 */
public interface ProjectFacilityReportService extends BaseService<ProjectFacilityReport> {
    /**
     * 下载
     *
     * @param response 响应
     * @param criteria 标准
     */
    void download(HttpServletResponse response, ProjectFacilityReportQueryCriteria criteria) throws IOException;

    /**
     * 查询时间
     *
     * @param criteria 标准
     * @param pageable
     * @return {@link Object}
     */
    Object queryTime(ProjectFacilityReportQueryCriteria criteria, Pageable pageable);
}
