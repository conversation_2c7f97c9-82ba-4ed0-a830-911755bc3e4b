/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-10-23
**/
@Data
public class TemplateOutputRelationDto implements Serializable {

    /** 模板节点关系id */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long templateOutputRelationId;

    /** 联合关系名称 */
    private String associationName;

    /** 联合编码 */
    private String jointCode;

    /** 二级节点ID */
    private Long templateGroupId;

    /** 二级节点编码 */
    private String nodeCode;

    /** 二级节点名称 */
    private String nodeName;

    /** 关联二级节点ID */
    private Long relevanceTemplateGroupId;

    /** 关联二级节点编码 */
    private String relevanceNodeCode;

    /** 关联二级节点name */
    private String relevanceNodeName;

    private Timestamp createTime;

    private Timestamp updateTime;

    private String createBy;

    private String updateBy;

    /** 是否可用 */
    private Boolean isEnabled;

    /** 是否删除 */
    private Boolean isDelete;

    /**所属二级节点编码*/
    private String secondLevelNodeCode;

}