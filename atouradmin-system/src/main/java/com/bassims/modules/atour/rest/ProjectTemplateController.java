/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.service.*;
import com.bassims.modules.atour.service.dto.ProjectTemplateApproveRelationDto;
import com.bassims.modules.atour.service.dto.ProjectTemplateDto;
import com.bassims.modules.atour.service.dto.ProjectTemplateNoticeRelationDto;
import com.bassims.modules.atour.service.dto.ProjectTemplateQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-03-22
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "projectTemplate管理")
@RequestMapping("/api/projectTemplate")
public class ProjectTemplateController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectTemplateController.class);

    private final ProjectTemplateService projectTemplateService;

    private final ApproveTemplateService approveTemplateService;

    private final NoticeTemplateService noticeTemplateService;

    private final ProjectTemplateApproveRelationService projectTemplateApproveRelationService;

    private final ProjectTemplateNoticeRelationService projectTemplateNoticeRelationService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ProjectTemplateQueryCriteria criteria) throws IOException {
        projectTemplateService.download(projectTemplateService.queryAll(criteria), response);
    }

    /**
     * @real_return {@link ResponseEntity <List<ProjectTemplateDto>>}
     */
    @GetMapping("/list")
    @Log("查询projectTemplate")
    @ApiOperation("查询projectTemplate")
    public ResponseEntity<Object> query(ProjectTemplateQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(projectTemplateService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity <ProjectTemplateDto>}
     */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询projectTemplate")
    @ApiOperation("查询projectTemplate")
    public ResponseEntity<Object> query(@PathVariable Long id) {
        return new ResponseEntity<>(projectTemplateService.findById(id), HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增projectTemplate")
    @ApiOperation("新增projectTemplate")
    public ResponseEntity<Object> create(@Validated @RequestBody ProjectTemplate resources) {
        return new ResponseEntity<>(projectTemplateService.create(resources), HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改projectTemplate")
    @ApiOperation("修改projectTemplate")
    public ResponseEntity<Object> update(@Validated @RequestBody ProjectTemplate resources) {
        projectTemplateService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除projectTemplate")
    @ApiOperation("删除projectTemplate")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        projectTemplateService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * 批量插入
     *
     * @param template 模板
     * @return {@link ResponseEntity}<{@link Object}>
     */
    @PostMapping("/batchInsert")
    @Log("批量新增模板")
    @ApiOperation("批量新增模板")
    public ResponseEntity<Object> batchInsert(@RequestBody List<ProjectTemplate> template) {
        return new ResponseEntity<>(projectTemplateService.batchInsert(template), HttpStatus.OK);
    }

    @GetMapping("/templateTree")
    @Log("获取模板树")
    @ApiOperation("获取模板树")
    public ResponseEntity<Object> templateTree(Long templateId) {
        return new ResponseEntity<>(projectTemplateService.getTemplateTree(templateId), HttpStatus.OK);
    }

    @GetMapping("/parentList")
    @Log("获取父模板")
    @ApiOperation("获取父模板")
//    @MyDataPermission(title = "任务配置")
    public ResponseEntity<Object> getParentList(ProjectTemplateQueryCriteria criteria) {
        return new ResponseEntity<>(projectTemplateService.getParentNode(criteria), HttpStatus.OK);
    }

    @GetMapping("/parentNodeInfo")
    @Log("获取父模板信息")
    @ApiOperation("获取父模板信息")
//    @MyDataPermission(title = "任务配置")
    public ResponseEntity<Object> getParentNodeInfo(Long templateId) {
        return new ResponseEntity<>(projectTemplateService.getParentNodeInfo(templateId), HttpStatus.OK);
    }

    @GetMapping("/taskTree")
    @Log("获取任务")
    @ApiOperation("获取任务树")
    public ResponseEntity<Object> taskTree(Long templateId) {
        return new ResponseEntity<>(projectTemplateService.getTemplateTaskTreeOther(templateId), HttpStatus.OK);
    }

    @GetMapping("/taskAppRelation")
    @Log("获取关联审批任务")
    @ApiOperation("获取关联审批任务")
    public ResponseEntity<Object> taskAppRelation(Long templateId, Long templateGroupId) {
        return new ResponseEntity<>(approveTemplateService.getApproveTemplate(templateId, templateGroupId), HttpStatus.OK);
    }

    @GetMapping("/taskNotiRelation")
    @Log("获取关联消息任务")
    @ApiOperation("获取关联消息任务")
    public ResponseEntity<Object> taskNotiRelation(Long templateId) {
        return new ResponseEntity<>(projectTemplateNoticeRelationService.getNoticeTemplate(templateId), HttpStatus.OK);
    }

    @PostMapping("/updatePlanDay")
    @Log("修改projectTemplate")
    @ApiOperation("修改projectTemplate")
    public ResponseEntity<Object> updatePlanDay(@Validated @RequestBody List<ProjectTemplateDto> resources) {
        projectTemplateService.updatePlanDayByTemplateGroup(resources, "");
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/saveAppTaskRelation")
    @Log("审批任务配置")
    @ApiOperation("审批任务配置")

    public ResponseEntity<Object> saveAppTaskRelation(@Validated @RequestBody ProjectTemplateApproveRelationDto resources) {
        return new ResponseEntity<>(projectTemplateApproveRelationService.saveAppRelation(resources), HttpStatus.CREATED);
    }

    @PostMapping("/saveNotiTaskRelation")
    @Log("消息任务配置")
    @ApiOperation("消息任务配置")
    @MyDataPermission(title = "工期配置")
    public ResponseEntity<Object> saveNotiTaskRelation(@Validated @RequestBody List<ProjectTemplateNoticeRelationDto> resources) {
        return new ResponseEntity<>(projectTemplateNoticeRelationService.saveNotiRelation(resources), HttpStatus.CREATED);
    }

    @PostMapping("/deleteNotiTaskRelation")
    @Log("消息配置删除")
    @ApiOperation("消息配置删除")
    @MyDataPermission(title = "工期配置")
    public ResponseEntity<Object> deleteNotiTaskRelation(@Validated @RequestBody ProjectTemplateNoticeRelationDto resource) {
        return new ResponseEntity<>(projectTemplateNoticeRelationService.deleteNotiRelation(resource), HttpStatus.OK);
    }


    @GetMapping("/jobTemplateNode")
    @Log("获取模板权限")
    @ApiOperation("获取模板权限")
    public ResponseEntity<Object> getJobTemplateNode(Long jobId, Long templateId) {
        return new ResponseEntity<>(projectTemplateService.getJobTemplateNode(jobId, templateId), HttpStatus.OK);
    }


    @GetMapping("/templateParentList")
    @Log("查询projectTemplate")
    @ApiOperation("查询projectTemplate")
    @MyDataPermission(title = "工期配置")
    public ResponseEntity<Object> getTemplateParentList(ProjectTemplateDto projectTemplateDto) {
        return new ResponseEntity<>(projectTemplateService.getTemplateParentList(projectTemplateDto), HttpStatus.OK);
    }

    @GetMapping("/templateBySchedule")
    @Log("获取带工期模板树")
    @ApiOperation("获取带工期模板树")
    public ResponseEntity<Object> templateBySchedule(Long templateId, String scheduleType) {
        return new ResponseEntity<>(projectTemplateService.getTemplateTreeBySchedule(templateId, scheduleType), HttpStatus.OK);
    }


    @GetMapping("/selectThreeLevel")
    @Log("获取三级列表")
    @ApiOperation("获取三级列表")
    @MyDataPermission(title = "工期配置")
    public ResponseEntity<Object> selectThreeLevel(Long templateId) {
        return new ResponseEntity<>(projectTemplateService.selectThreeLevel(templateId), HttpStatus.OK);
    }


//    @PostMapping("/deleteThreeLevel")
//    @Log("删除三级列表")
//    @ApiOperation("删除三级列表")
////    @AnonymousAccess
//    public ResponseEntity<Object> deleteThreeLevel(Long templateId) {
//        return new ResponseEntity<>(projectTemplateService.deleteThreeLevel(templateId), HttpStatus.OK);
//    }

    @PostMapping("/saveTemplate")
    @Log("新增模板0级")
    @ApiOperation("新增模板0级")
    @MyDataPermission(title = "工期配置")
//    @AnonymousAccess
    public ResponseEntity<Object> saveTemplate(@RequestBody SaveTemplateReq req) {
        return new ResponseEntity<>(projectTemplateService.saveTemplate(req), HttpStatus.OK);
    }

    @GetMapping("/selectTemplateLevel")
    @Log("获取模板0级")
    @ApiOperation("获取模板0级")
//    @AnonymousAccess
    public ResponseEntity<Object> selectTemplateLevel() {
        return new ResponseEntity<>(projectTemplateService.selectTemplateLevel(), HttpStatus.OK);
    }

    @PostMapping("/updateTemplate")
    @Log("修改0级模板")
    @ApiOperation("修改0级模板")
//    @AnonymousAccess
    public ResponseEntity<Object> updateTemplate(@RequestBody UpdateTemplateReq req) {
        return new ResponseEntity<>(projectTemplateService.updateTemplate(req), HttpStatus.OK);
    }

    @PostMapping("/deleteTemplate")
    @Log("删除0级模板")
    @ApiOperation("删除0级模板")
    @MyDataPermission(title = "任务配置")
//    @AnonymousAccess
    public ResponseEntity<Object> deleteTemplate(@RequestParam String templateCode) {
        return new ResponseEntity<>(projectTemplateService.deleteTemplate(templateCode), HttpStatus.OK);
    }

    @PostMapping("/saveOneLevel")
    @Log("新增模板1级")
    @ApiOperation("新增模板1级")
//    @AnonymousAccess
    public ResponseEntity<Object> saveOneLevel(@RequestBody TemplateGroup req) {
        return new ResponseEntity<>(projectTemplateService.saveOneLevel(req), HttpStatus.OK);
    }

    @GetMapping("/selectOneLevelByTemplateCode")
    @Log("获取模板1级")
    @ApiOperation("获取模板1级")
//    @AnonymousAccess
    public ResponseEntity<Object> selectOneLevelByTemplateCode(String templateCode) {
        return new ResponseEntity<>(projectTemplateService.selectOneLevelByTemplateCode(templateCode), HttpStatus.OK);
    }


    @PostMapping("/updateOneLevel")
    @Log("修改1级模板")
    @ApiOperation("修改1级模板")
//    @AnonymousAccess
    public ResponseEntity<Object> updateOneLevel(@RequestBody TemplateGroup req) {
        return new ResponseEntity<>(projectTemplateService.updateOneLevel(req), HttpStatus.OK);
    }

    @PostMapping("/deleteOneLevel")
    @Log("删除1级模板")
    @ApiOperation("删除1级模板")
//    @AnonymousAccess
    public ResponseEntity<Object> deleteOneLevel(@RequestParam String templateCode, @RequestParam Long templateId) {
        return new ResponseEntity<>(projectTemplateService.deleteOneLevel(templateCode, templateId), HttpStatus.OK);
    }


    @PostMapping("/saveTwoLevel")
    @Log("新增模板2级")
    @ApiOperation("新增模板2级")
//    @AnonymousAccess
    public ResponseEntity<Object> saveTwoLevel(@RequestBody TemplateGroup req) {
        return new ResponseEntity<>(projectTemplateService.saveTwoLevel(req), HttpStatus.OK);
    }


    @PostMapping("/updateTwoLevel")
    @Log("修改2级模板")
    @ApiOperation("修改2级模板")
//    @AnonymousAccess
    public ResponseEntity<Object> updateTwoLevel(@RequestBody TemplateGroup req) {
        return new ResponseEntity<>(projectTemplateService.updateTwoLevel(req), HttpStatus.OK);
    }

    @PostMapping("/deleteTwoLevel")
    @Log("删除2级模板")
    @ApiOperation("删除2级模板")
//    @AnonymousAccess
    public ResponseEntity<Object> deleteTwoLevel(@RequestParam String templateCode, @RequestParam Long templateId) {
        return new ResponseEntity<>(projectTemplateService.deleteTwoLevel(templateCode, templateId), HttpStatus.OK);
    }


    @GetMapping("/selectOneAndTwoLevelByTemplateCode")
    @Log("获取模板1级和2级")
    @ApiOperation("获取模板1级和2级")
    @MyDataPermission(title = "任务配置")
//    @AnonymousAccess
    public ResponseEntity<Object> selectOneAndTwoLevelByTemplateCode(@RequestParam String templateCode) {
        return new ResponseEntity<>(projectTemplateService.selectOneAndTwoLevelByTemplateCode(templateCode), HttpStatus.OK);
    }

    @PostMapping("/saveThreeLevel")
    @Log("新增模板3级")
    @ApiOperation("新增模板3级")
//    @AnonymousAccess
    public ResponseEntity<Object> selectThreeLevel(@RequestBody TemplateQueueReq req) {
        return new ResponseEntity<>(projectTemplateService.saveThreeLevel(req), HttpStatus.OK);
    }

    @GetMapping("/selectThreeLevelByTemplateCode")
    @Log("获取模板3级")
    @ApiOperation("获取模板3级")
//    @AnonymousAccess
    public ResponseEntity<Object> selectThreeLevelByTemplateCode(@RequestParam String templateCode, @RequestParam Long templateId) {
        return new ResponseEntity<>(projectTemplateService.selectThreeLevelByTemplateCode(templateCode, templateId), HttpStatus.OK);
    }

    @PostMapping("/updateThreeLevel")
    @Log("修改3级模板")
    @ApiOperation("修改3级模板")
//    @AnonymousAccess
    public ResponseEntity<Object> updateThreeLevel(@RequestBody TemplateQueueReq req) {
        return new ResponseEntity<>(projectTemplateService.updateThreeLevel(req), HttpStatus.OK);
    }

    @PostMapping("/deleteThreeLevel")
    @Log("删除3级模板")
    @ApiOperation("删除3级模板")
//    @AnonymousAccess
    public ResponseEntity<Object> deleteThreeLevel(@RequestParam String templateCode, @RequestParam Long templateId) {
        return new ResponseEntity<>(projectTemplateService.deleteThreeLevel(templateCode, templateId), HttpStatus.OK);
    }


    @GetMapping("/selectTwoLevelByTemplateCode")
    @Log("获取模板2级")
    @ApiOperation("获取模板2级")
//    @AnonymousAccess
    public ResponseEntity<Object> selectTwoLevelByTemplateCode(@RequestParam(required = false) String templateCode, String nodeCode) {
        return new ResponseEntity<>(projectTemplateService.selectTwoLevelByTemplateCode(templateCode, nodeCode), HttpStatus.OK);
    }

    @GetMapping("/selectTwoByTemplateCode")
    @Log("获取模板2级")
    @ApiOperation("获取模板123级")
//    @AnonymousAccess
    public ResponseEntity<Object> selectTwoByTemplateCode(@RequestParam(required = false) String templateCode, String nodeCode) {
        return new ResponseEntity<>(projectTemplateService.selectTwoByTemplateCode(templateCode, nodeCode), HttpStatus.OK);
    }

    @GetMapping("/selectThreeLevelUniteByTemplateCode")
    @Log("获取模板3级")
    @ApiOperation("获取模板3级（返回是否已经存在联合任务）")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "templateCode", value = "templateCode"),
            @ApiImplicitParam(paramType = "query", dataType = "Long", name = "templateId", value = "templateId"),
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "jointCode", value = "联合code（二级与联合二级用逗号拼接）")
    })
    public ResponseEntity<Object> selectThreeLevelUniteByTemplateCode(@RequestParam String templateCode, @RequestParam Long templateId, @RequestParam String jointCode) {
        return new ResponseEntity<>(projectTemplateService.selectThreeLevelUniteByTemplateCode(templateCode, templateId, jointCode), HttpStatus.OK);
    }

    @GetMapping("/getThreeLevelByNodeCode")
    @Log("根据三级code查询三级字段")
    @ApiOperation("根据三级code查询三级字段")
    public ResponseEntity<Object> getThreeLevelByNodeCode(@RequestParam String nodeCode) {
        return new ResponseEntity<>(projectTemplateService.getThreeLevelByNodeCode(nodeCode), HttpStatus.OK);
    }

    @PostMapping("/saveSecondaryTemplate")
    @Log("根据页面勾选的二级任务生成一套次级模板")
    @ApiOperation("根据页面勾选的二级任务生成一套次级模板")
    public ResponseEntity<Object> saveSecondaryTemplate(@RequestBody List<TemplateGroup> templateGroup) {
        return new ResponseEntity<>(projectTemplateService.saveSecondaryTemplate(templateGroup), HttpStatus.OK);
    }


    @GetMapping("/selectTwo")
    @Log("获取模板2级模板")
    @ApiOperation("获取2级模板")
//    @AnonymousAccess
    public ResponseEntity<Object> selectTwo(@RequestParam(required = false) String templateCode, String nodeCode) {
        return new ResponseEntity<>(projectTemplateService.selectTwo(templateCode, nodeCode), HttpStatus.OK);
    }
}