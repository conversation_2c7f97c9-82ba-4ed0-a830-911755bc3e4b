/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.modules.atour.domain.SupplierInfo;
import com.bassims.modules.atour.domain.vo.SupplierManagerParam;
import com.bassims.modules.atour.service.SupplierInfoService;
import com.bassims.modules.atour.service.dto.SupplierInfoDto;
import com.bassims.modules.atour.service.dto.SupplierInfoQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
* 厂商管理
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-09-15
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_supplier_info管理")
@RequestMapping("/api/supplierInfo")
public class SupplierInfoController {

    private static final Logger logger = LoggerFactory.getLogger(SupplierInfoController.class);

    private final SupplierInfoService supplierInfoService;

    @AnonymousAccess
    @PostMapping("/saveSupplierInfo")
    @Log("新增t_supplier_info")
    @ApiOperation("新增t_supplier_info")
    public ResponseEntity<Object> saveSupplierInfo(@Validated @RequestBody SupplierInfo resources){
        return new ResponseEntity<>(supplierInfoService.create(resources),HttpStatus.CREATED);
    }

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, SupplierInfoQueryCriteria criteria) throws IOException {
        supplierInfoService.downloadNew(supplierInfoService.queryAll(criteria), response);
    }

    @Log("导出模板")
    @ApiOperation("导出模板")
    @GetMapping(value = "/downloadTemplate")
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response, SupplierInfoQueryCriteria criteria) throws IOException {
        supplierInfoService.downloadTemplate(request, response);
    }

    @Log("供应商导入")
    @ApiOperation("供应商导入")
    @PostMapping(value = "/supplierImport")
    public ResponseEntity<Object> supplierImport(MultipartFile file) throws IOException {
        return new ResponseEntity<>(supplierInfoService.supplierImport(file), HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<List<SupplierInfoDto>>}
    */
    @GetMapping("/list")
    @Log("查询t_supplier_info")
    @ApiOperation("查询t_supplier_info")
    @MyDataPermission(title = "供应商信息,人员管理,供应商清单,供应商人员清单,营建对接启动,项目推进")
    public ResponseEntity<Object> query(SupplierInfoQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(supplierInfoService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @GetMapping("/caseList")
    @Log("查询案例")
    @ApiOperation("查询案例")
//    @MyDataPermission(title = "供应商信息")
    public ResponseEntity<Object> caseList(Long supplierId){
        return new ResponseEntity<>(supplierInfoService.caseList(supplierId),HttpStatus.OK);
    }


    /**
     * 不分页查询
     * @param criteria
     * @return
     */
    @GetMapping("/noPageList")
    @Log("查询t_supplier_info")
    @ApiOperation("查询t_supplier_info")
    public ResponseEntity<Object> noPageList(SupplierInfoQueryCriteria criteria){
        return new ResponseEntity<>(supplierInfoService.queryAllNeedData(criteria),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<SupplierInfoDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("厂商详情")
    @ApiOperation("厂商详情")
    @MyDataPermission(title = "供应商信息,人员管理,供应商清单,供应商人员清单,营建对接启动,项目推进")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(supplierInfoService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("[供应商]新建")
    @ApiOperation("[供应商]新建")
    public ResponseEntity<Object> create(@Validated @RequestBody SupplierInfo resources){
        return new ResponseEntity<>(supplierInfoService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("[供应商]编辑")
    @ApiOperation("[供应商]编辑")
    public ResponseEntity<Object> update(@Validated @RequestBody SupplierInfo resources){
        supplierInfoService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/updateStatus")
    @Log("[供应商]变更状态")
    @ApiOperation("[供应商]变更状态")
    public ResponseEntity<Object> updateStatus(@Validated @RequestBody SupplierInfo resources){
        supplierInfoService.updateStatus(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/deleteStatus")
    @Log("[供应商]删除")
    @ApiOperation("[供应商]删除")
    public ResponseEntity<Object> deleteStatus(@Validated @RequestBody SupplierInfo resources){
        supplierInfoService.deleteStatus(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }


    @PostMapping("/delete")
    @Log("删除t_supplier_info")
    @ApiOperation("删除t_supplier_info")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        supplierInfoService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity<List<SupplierInfoDto>>}
     */
    @GetMapping("/search")
    @Log("根据厂商名查询信息")
    @ApiOperation("根据厂商名查询信息")
    public ResponseEntity<Object> search(String supplierName){
        return new ResponseEntity<>(supplierInfoService.supplierByName(supplierName),HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity<List<SupplierInfoDto>>}
     */
    @GetMapping("/relateSupInfo")
    @Log("查询厂商信息")
    @ApiOperation("查询厂商信息")
    public ResponseEntity<Object> relateSupInfo(){
        return new ResponseEntity<>(supplierInfoService.getSupplierInfo(),HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity<List<SupplierInfoDto>>}
     */
    @GetMapping("/relate")
    @Log("查询厂商管理员信息")
    @ApiOperation("查询厂商管理员信息")
    public ResponseEntity<Object> supplierManagerRelate(){
        return new ResponseEntity<>(supplierInfoService.supplierManagerRelate(),HttpStatus.OK);
    }


    @PostMapping("/relateSave")
    @Log("保存厂商关联信息")
    @ApiOperation("保存厂商关联信息")
    public ResponseEntity<Object> relateSave(@RequestBody SupplierManagerParam supplierManagerParam){
        supplierInfoService.relateSave(supplierManagerParam);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity<List<SupplierInfoDto>>}
     */
    @GetMapping("/searchFirst")
    @Log("根据厂商名查询信息")
    @ApiOperation("根据厂商名查询信息")
    public ResponseEntity<Object> searchFirst(String supplierName){
        return new ResponseEntity<>(supplierInfoService.supplierByName(supplierName),HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity<SupplierInfoDto>}
     */
    @GetMapping("/searchOne")
    @Log("根据厂商名查询信息")
    @ApiOperation("根据厂商名查询信息")
    public ResponseEntity<Object> searchOne(String supplierName){
        return new ResponseEntity<>(supplierInfoService.supplierByName(supplierName),HttpStatus.OK);
    }


    /**
     * 选择供应商下拉数据
     * @param criteria
     * @return
     */
    @GetMapping("/getSupplierList")
    @Log("选择下拉数据")
    @ApiOperation("选择下拉数据")
    public ResponseEntity<Object> getSupplierList(SupplierInfoQueryCriteria criteria){
        return new ResponseEntity<>(supplierInfoService.getSupplierList(criteria),HttpStatus.OK);
    }

    @GetMapping("/getByName")
    @Log("根据供应商名称查询")
    @ApiOperation("根据供应商名称查询")
    public ResponseEntity<Object> getByName(String name){
        return new ResponseEntity<>(supplierInfoService.getByName(name), HttpStatus.CREATED);
    }
}