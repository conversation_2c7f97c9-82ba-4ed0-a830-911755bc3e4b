/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.modules.atour.domain.TemplateJointTaskOnfiguration;
import com.bassims.modules.atour.service.TemplateJointTaskOnfigurationService;
import com.bassims.modules.atour.service.dto.TemplateJointTaskOnfigurationDto;
import com.bassims.modules.atour.service.dto.TemplateJointTaskOnfigurationQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-10-08
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "二级与二级的联合关系表管理")
@RequestMapping("/api/templateJointTaskOnfiguration")
public class TemplateJointTaskOnfigurationController {

    private static final Logger logger = LoggerFactory.getLogger(TemplateJointTaskOnfigurationController.class);

    private final TemplateJointTaskOnfigurationService templateJointTaskOnfigurationService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, TemplateJointTaskOnfigurationQueryCriteria criteria) throws IOException {
        templateJointTaskOnfigurationService.download(templateJointTaskOnfigurationService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity<List<TemplateJointTaskOnfigurationDto>>}
    */
    @GetMapping("/list")
    @Log("查询二级与二级的联合关系表")
    @ApiOperation("查询二级与二级的联合关系表")
    public ResponseEntity<Object> query(TemplateJointTaskOnfigurationQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(templateJointTaskOnfigurationService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<TemplateJointTaskOnfigurationDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询二级与二级的联合关系表")
    @ApiOperation("查询二级与二级的联合关系表")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(templateJointTaskOnfigurationService.findById(id),HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增二级与二级的联合关系表")
    @ApiOperation("新增二级与二级的联合关系表")
    public ResponseEntity<Object> create(@Validated @RequestBody TemplateJointTaskOnfiguration resources){
        return new ResponseEntity<>(templateJointTaskOnfigurationService.create(resources),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改二级与二级的联合关系表")
    @ApiOperation("修改二级与二级的联合关系表")
    public ResponseEntity<Object> update(@Validated @RequestBody TemplateJointTaskOnfiguration resources){
        templateJointTaskOnfigurationService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/deleteByJointCode")
    @Log("根据联合编码删除联合关系/条件表")
    @ApiOperation("根据联合编码删除联合关系/条件表")
    public ResponseEntity<Object> deleteByJointCode(@RequestBody String jointCode) {
        templateJointTaskOnfigurationService.deleteByJointCode(jointCode);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/delete")
    @Log("删除二级与二级的联合关系表")
    @ApiOperation("删除二级与二级的联合关系表")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        templateJointTaskOnfigurationService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}