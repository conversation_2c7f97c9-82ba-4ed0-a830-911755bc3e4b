/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-10-26
**/
@Data
public class TemplateTableRelevanceExpansionDto implements Serializable {

    /** id */
    private Long expansionId;

    /** 模版表格分组id */
    private Long relevanceId;

    /** 是否必采（0非必采、1必采） */
    private String isMustMined;

    /** 安全文明施工类型（0办公室管理、1临时水电、2工人住宿、3安全管理）*/
    private String constructionType;



    /*竣工验收总项*/
    private String totalItem;
}