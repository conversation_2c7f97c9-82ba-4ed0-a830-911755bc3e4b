/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.exception.BadRequestException;
import com.bassims.modules.atour.domain.ProjectStakeholders;
import com.bassims.modules.atour.domain.vo.ProjectStakeholdersVO;
import com.bassims.modules.atour.service.ProjectStakeholdersService;
import com.bassims.modules.atour.service.dto.ProjectStakeholdersQueryCriteria;
import com.bassims.modules.atour.service.dto.ProjectStakeholdersUpdateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-03-29
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_project_stakeholders管理")
@RequestMapping("/api/projectStakeholders")
public class ProjectStakeholdersController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectStakeholdersController.class);

    private final ProjectStakeholdersService projectStakeholdersService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ProjectStakeholdersQueryCriteria criteria) throws IOException {
        projectStakeholdersService.download(projectStakeholdersService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity <List<ProjectStakeholdersDto>>}
    */
    @GetMapping("/page")
    @Log("查询t_project_stakeholders")
    @ApiOperation("查询t_project_stakeholders")
    public ResponseEntity<Object> page(ProjectStakeholdersQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(projectStakeholdersService.queryAll(criteria,pageable), HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity <List<ProjectStakeholdersDto>>}
     */
    @GetMapping("/getPage")
    @Log("查询t_project_stakeholders")
    @ApiOperation("查询t_project_stakeholders")
    public ResponseEntity<Object> getPage(ProjectStakeholdersQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(projectStakeholdersService.queryList(criteria,pageable), HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity <List<ProjectStakeholdersDto>>}
     */
    @GetMapping("/list")
    @Log("查询t_project_stakeholders")
    @ApiOperation("查询t_project_stakeholders")
    @MyDataPermission(title = "营建对接启动,项目推进,数据处理,批量变更干系人,供应商清单,供应商人员清单")
    public ResponseEntity<Object> list(ProjectStakeholdersQueryCriteria criteria){
        return new ResponseEntity<>(projectStakeholdersService.queryAll(criteria), HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity <ProjectStakeholdersDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询t_project_stakeholders")
    @ApiOperation("查询t_project_stakeholders")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(projectStakeholdersService.findById(id), HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增t_project_stakeholders")
    @ApiOperation("新增t_project_stakeholders")
    public ResponseEntity<Object> create(@Validated @RequestBody ProjectStakeholders resources){
        return new ResponseEntity<>(projectStakeholdersService.create(resources), HttpStatus.CREATED);
    }


    @PostMapping("/createStakeholders")
    @Log("重新加载项目干系人")
    @ApiOperation("重新加载项目干系人")
    public ResponseEntity<Object> createStakeholders(@Validated @RequestBody ProjectStakeholdersVO resources){
        projectStakeholdersService.createStakeholders(resources);
        return new ResponseEntity<>(projectStakeholdersService.createStakeholders(resources),HttpStatus.OK);
    }



    @PostMapping("/update")
    @Log("修改t_project_stakeholders")
    @ApiOperation("修改t_project_stakeholders")
    public ResponseEntity<Object> update(@Validated @RequestBody ProjectStakeholders resources){
        projectStakeholdersService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }



    @PostMapping("/updateStakeholders")
    @Log("修改t_project_stakeholders")
    @ApiOperation("修改t_project_stakeholders")
    public ResponseEntity<Object> updateStakeholders(@Validated @RequestBody ProjectStakeholders resources){
        //校验时间
        if (resources.getChangeStartTime() != null && resources.getChangeEndTime() != null){
            if (resources.getChangeStartTime().after(resources.getChangeEndTime())){
                throw new BadRequestException("开始时间请小于结束时间");
            }
            String strTime = DateUtil.format(resources.getChangeStartTime(), DatePattern.NORM_DATE_PATTERN);
            Date now = new Date();
            String nowStr = DateUtil.format(now, DatePattern.NORM_DATE_PATTERN);
            if (!nowStr.equals(strTime) && resources.getChangeStartTime().before(now)){
                throw new BadRequestException("开始时间请大于等于当前时间");
            }
        }
        projectStakeholdersService.updateStakeholdersInTime(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/batchUpdate")
    @Log("批量变更干系人")
    @ApiOperation("批量变更干系人")
    public ResponseEntity<Object> batchUpdate(@Validated @RequestBody List<ProjectStakeholders> list){
        projectStakeholdersService.batchUpdate(list);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/batchUpdateStakeholders")
    @Log("批量变更干系人")
    @ApiOperation("批量变更干系人")
    public ResponseEntity<Object> batchUpdateStakeholders(@Validated @RequestBody ProjectStakeholdersUpdateDto resources){
        projectStakeholdersService.batchUpdateStakeholders(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_project_stakeholders")
    @ApiOperation("删除t_project_stakeholders")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        projectStakeholdersService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/selectRolesByUserId")
    public ResponseEntity<Object> selectRolesByUserId(Long userId){
        List<String> roles = projectStakeholdersService.selectRolesByUserId(userId);
        return new ResponseEntity<>(roles,HttpStatus.OK);
    }

    @GetMapping("/changeRollBack")
    public ResponseEntity<Object> changeRollBack(Long stakeholderId){
        projectStakeholdersService.changeRollBack(stakeholderId);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}