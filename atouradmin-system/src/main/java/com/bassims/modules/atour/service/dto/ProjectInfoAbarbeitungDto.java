/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-11-28
**/
@Data
public class ProjectInfoAbarbeitungDto implements Serializable {

    /** 项目整改ID */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long abarbeitungId;

    /** 项目id */
    private Long projectId;

    /** 问题类型 */
    private String abarbeitungType;

    /** 项目备注 */
    private String remark;

    /** 创建时间 */
    private Timestamp createTime;

    /** 创建人 */
    private String createBy;

    /** 更新时间 */
    private Timestamp updateTime;

    /** 更新人 */
    private String updateBy;

    /** 是否可用 */
    private Boolean isEnabled;

    /** 是否删除 */
    private Boolean isDelete;

    /** 三级nodeCode */
    private String nodeCode;

    /** 文件的nodeId */
    private Long fileNodeId;

    /** 描述 */
    private String problemDescription;

    /** 解决方案 */
    private String solution;

    /** 状态 */
    private String status;

    /** 标题 */
    private String title;


}