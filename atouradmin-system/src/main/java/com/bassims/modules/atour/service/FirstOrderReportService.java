package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.FirstOrderReport;
import com.bassims.modules.atour.service.dto.FirstOrderReportQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 甲供材订单报表
 *
 * <AUTHOR>
 * @date 2023/03/13
 */
public interface FirstOrderReportService extends BaseService<FirstOrderReport> {
    /**
     * 下载
     *
     * @param response 响应
     * @param criteria 标准
     */
    void download(HttpServletResponse response, FirstOrderReportQueryCriteria criteria) throws IOException;

    /**
     * 查询时间
     *
     * @param criteria 标准
     * @param pageable
     * @return {@link Object}
     */
    Object queryTime(FirstOrderReportQueryCriteria criteria, Pageable pageable);
}
