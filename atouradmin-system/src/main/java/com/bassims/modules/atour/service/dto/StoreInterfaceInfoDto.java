/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.math.BigDecimal;
import java.io.Serializable;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-11-23
**/
@Data
public class StoreInterfaceInfoDto implements Serializable {

    /** 门店ID */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long storeId;

    /** 门店名称 */
    private String storeName;

    /** 门店全称 */
    private String storeDesc;

    /** 门店编号 */
    private String storeCode;

    /** 区域 */
    private String area;

    /** 分部 */
    private String provinceId;

    /** 分部名称 */
    private String provinceName;

    /** 门店省份国标 */
    private Long province;

    /** 门店省份名称 */
    private String addressProvince;

    /** 门店城市国标 */
    private Long city;

    /** 门店城市名称 */
    private String addressCity;

    /** 城市拼音 */
    private String spell;

    /** 门店区国标 */
    private Long district;

    /** 门店区名称 */
    private String addressDistrict;

    /** 门店地址 */
    private String addressStreet;

    /** 是否新店（1-是；0-否） */
    private Boolean newStore;

    /** 开业时间（时间戳） */
    private String openTime;

    /** 开业进度(码值) */
    private String openProgress;

    /** 门店类型(码值) */
    private String feature;

    /** 门店链接 */
    private String link;

    /** 是否店外配送（1-是；2-否） */
    private Boolean isDelivery;

    /** 是否店内自取（1-是；2-否） */
    private Boolean isFetch;

    /** 是否店内配送（1-是；2-否） */
    private Boolean isDeliveryInside;

    /** 是否支持店外自取（1-是；2-否） */
    private Boolean isFetchOutside;

    /** POS开单是否检查库存（1为否，2为是） */
    private Boolean isCheck;

    /** 联系电话1 */
    private String contactPhone01;

    /** 联系电话2 */
    private String contactPhone02;

    /** 营业开始时间 */
    private String startTime;

    /** 营业结束时间 */
    private String endTime;

    /** 门店照片 */
    private String photo;

    /** 营业执照 */
    private String license;

    /** 距离 */
    private BigDecimal distance;

    /** 状态：有效1 无效0 */
    private Boolean yn;

    /** 支持荣誉会员标志（0：不支持；1：支持） */
    private Boolean supportHmFlag;

    /** 实体 */
    private Long entity;

    /** 部门 */
    private Long departmentNo;

    /** 支持扫码购物标志（0：不支持；1：支持） */
    private Boolean supportScanCode;

    /** 是否直辖市（1-是；0-否） */
    private Boolean isMunicipality;

    /** 运费单位分 */
    private Long freight;

    /** 运费门槛单位分 */
    private Long freightThreshold;

    /**  是否支持在线客服（0：不支持；1：支持） */
    private Boolean supportOnlineService;

    /** 门店销售等级(码值) */
    private String saleslevel;

    /** 设施 */
    private String facilitys;

    private Timestamp createTime;

    private String createBy;

    private Timestamp updateTime;

    private String updateBy;

    /** 是否可用 */
    private Boolean isEnabled;

    /** 是否删除 */
    private Boolean isDelete;

    private String noAndName;
}