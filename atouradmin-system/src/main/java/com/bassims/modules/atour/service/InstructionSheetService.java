/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.InstructionSheet;
import com.bassims.modules.atour.service.dto.InstructionSheetDto;
import com.bassims.modules.atour.service.dto.InstructionSheetQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2022-09-26
**/
public interface InstructionSheetService extends BaseService<InstructionSheet> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(InstructionSheetQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<InstructionSheetDto>
    */
    List<InstructionSheetDto> queryAll(InstructionSheetQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return InstructionSheetDto
     */
    InstructionSheetDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return InstructionSheetDto
    */
    InstructionSheetDto create(InstructionSheet resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(InstructionSheet resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(String[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<InstructionSheetDto> all, HttpServletResponse response) throws IOException;

    /**
     * 查询数据分页
     * @param instructionSheetDto 条件
     * @param pageable 分页参数
     * @return Map<String,Object>
     */
    Map<String,Object> queryApprovePassInfo(InstructionSheetDto instructionSheetDto,Pageable pageable);

    /**
     * 判断是否需要录入干系人
     * @param instructionSheetDto 条件
     * @return Map<String,String>
     */
    Map<String,String> isNeedSta(InstructionSheetDto instructionSheetDto);

    /**
     * 创建干系人
     * @param orderId 条件
     * @param roleName
     * @param userId
     */
    void createStakeholders(Long orderId,String roleName,Long userId);

    /**
     * 提交审批
     * @param instructionSheetDto 条件
     * @return InstructionSheetDto
     */
    InstructionSheetDto submit(InstructionSheetDto instructionSheetDto);

    /**
     * 导出excel模板
     * @param sheetId
     * @param response
     * @throws IOException
     */
    void downloadExcel (Long sheetId,HttpServletResponse response)throws IOException;
}