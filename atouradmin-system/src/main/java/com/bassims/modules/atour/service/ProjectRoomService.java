/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service;

import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.ProjectRoom;
import com.bassims.modules.atour.service.dto.ProjectRoomDto;
import com.bassims.modules.atour.service.dto.ProjectRoomQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务接口
 * @date 2023-11-06
 **/
public interface ProjectRoomService extends BaseService<ProjectRoom> {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(ProjectRoomQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<ProjectRoomDto>
     */
    List<ProjectRoomDto> queryAll(ProjectRoomQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return ProjectRoomDto
     */
    ProjectRoomDto findById(Long id);

    /**
     * 创建
     *
     * @param resources /
     * @return ProjectRoomDto
     */
    ProjectRoomDto create(ProjectRoom resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(ProjectRoom resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Integer[] ids);

    /**
     * 导出数据
     *
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<ProjectRoomDto> all, HttpServletResponse response) throws IOException;

    /**
     * 读取Excel数据插入t_project_room
     *
     * @param file
     * @param projectId
     * @return
     */
    Map<String, Object> importProjectRoom(MultipartFile file, Long projectId);

    /**
     * 获取t_project_room的工程房号总和
     *
     * @param projectId
     * @return
     */
    String getRoomNumSum(Long projectId);

    /**
     * 获取房间总数、未验收的房间号、验收房间数
     *
     * @param projectId
     * @return
     */
    Map<String, Object> isAccept(Long projectId);

    List<ProjectRoom> updateRoomBySupplier(Long projectId,String supplierId, List<ProjectRoom> resources);

    List<ProjectRoom> getModelRoom(Long projectId);

    Map<String,Object> updateAccept(Long projectId, List<ProjectRoom> resources);

    List<ProjectRoom> getList(Long projectId);

    List<ProjectRoom> listByIsUsed(Long projectId, String isUsed,String roomType);

    Boolean removeRoom(ProjectRoomQueryCriteria criteria);

    List<ProjectRoom> listByWeakCurrent(Long projectId);

    List<ProjectRoom> listByComplete(Long projectId);
}