/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service;

import cn.hutool.core.lang.tree.Tree;
import com.bassims.base.BaseService;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.service.dto.ProjectTemplateDto;
import com.bassims.modules.atour.service.dto.ProjectTemplateQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2022-03-22
**/
public interface ProjectTemplateService extends BaseService<ProjectTemplate> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(ProjectTemplateQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<ProjectTemplateDto>
    */
    List<ProjectTemplateDto> queryAll(ProjectTemplateQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param templateId ID
     * @return ProjectTemplateDto
     */
    ProjectTemplateDto findById(Long templateId);

    /**
    * 创建
    * @param resources /
    * @return ProjectTemplateDto
    */
    ProjectTemplateDto create(ProjectTemplate resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(ProjectTemplate resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<ProjectTemplateDto> all, HttpServletResponse response) throws IOException;

    /**
     * 批量插入
     *
     * @param template 模板
     * @return boolean
     */
    boolean batchInsert(List<ProjectTemplate> template);

    /**
     * 得到模板树
     *
     * @param templateId 模板id
     * @return {@link List}<{@link ProjectTemplateDto}>
     */
    List<Tree<String>> getTemplateTree(Long templateId);

    /**
     * 查询所有父模板
     * @return
     */
    List<ProjectTemplateDto> getParentNode(ProjectTemplateQueryCriteria criteria);

    /**
     * 子页面查询当前模板相关信息
     * @param templateId
     * @return
     */
    ProjectTemplateDto getParentNodeInfo(Long templateId);

    /**
     * 任务管理
     *
     * @param templateId 模板id
     * @return {@link List}<{@link ProjectTemplateDto}>
     */
    List<Tree<String>> getTemplateTaskTree(Long templateId);

    /**
     * 任务管理(审批节点变二级节点)
     *
     * @param templateId 模板id
     * @return {@link List}<{@link ProjectTemplateDto}>
     */
    List<Tree<String>> getTemplateTaskTreeOther(Long templateId);

    /**
     * 编辑
     * @param resources /
     */
    void updatePlanDay(List<ProjectTemplateDto> resources);

    /**
     * 获取权限管理中节点勾选
     * @param jobId
     * @param templateId
     * @return
     */
    List<Tree<String>> getJobTemplateNode(Long jobId, Long templateId);

    List<ProjectTemplateDto> getTemplateParentList(ProjectTemplateDto projectTemplateDto);

    List<Tree<String>> getTemplateTreeBySchedule(Long templateId, String scheduleType);

    String updatePlanDayByTemplateGroup(List<ProjectTemplateDto> resources,String front);

    boolean batchInsertBySchedule(List<ProjectTemplateDto> template);

    Object selectThreeLevel(Long templateId);

    Object saveThreeLevel(TemplateQueueReq req);

    Object updateThreeLevel(TemplateQueueReq projectTemplate);

    Object deleteThreeLevel(String templateCode, Long templateId);

    Object saveTemplate(SaveTemplateReq req);


    Object updateTemplate(UpdateTemplateReq req);

    Boolean saveOneLevel(TemplateGroup req);

    Object selectTemplateLevel();


    Object selectOneLevelByTemplateCode(String templateCode);

    Object saveTwoLevel(TemplateGroup req);

    Object selectOneAndTwoLevelByTemplateCode(String templateCode);

    Object selectThreeLevelByTemplateCode(String templateCode, Long templateId);

    Object updateOneLevel(TemplateGroup req);

    Object updateTwoLevel(TemplateGroup req);

    Object deleteTemplate(String templateCode);

    Object deleteOneLevel(String templateCode, Long templateId);

    Object deleteTwoLevel(String templateCode, Long templateId);
    TemplateGroup selectTwo(String templateCode, String nodeCode);
    List<TemplateGroupOneAndTwo> selectTwoLevelByTemplateCode(String templateCode, String nodeCode);

    List<TemplateGroupOneAndTwo> selectTwoByTemplateCode(String templateCode, String nodeCode);

    Object selectThreeLevelUniteByTemplateCode(String templateCode, Long templateId,String jointCode);

    Object getThreeLevelByNodeCode(String nodeCode);


    List<ProjectTemplate> saveSecondaryTemplate(List<TemplateGroup> templateGroup);
}