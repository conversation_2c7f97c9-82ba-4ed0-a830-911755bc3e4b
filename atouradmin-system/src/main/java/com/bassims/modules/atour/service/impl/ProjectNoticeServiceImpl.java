/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.base.BaseServiceImpl;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.modules.atour.domain.*;
import com.bassims.modules.atour.repository.NoticeTemplateRepository;
import com.bassims.modules.atour.repository.ProjectNoticeRepository;
import com.bassims.modules.atour.repository.ProjectStakeholdersRepository;
import com.bassims.modules.atour.service.ProjectInfoService;
import com.bassims.modules.atour.service.ProjectNoticeService;
import com.bassims.modules.atour.service.dto.ProjectNoticeDto;
import com.bassims.modules.atour.service.dto.ProjectNoticeQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.ProjectNoticeMapper;
import com.bassims.modules.atour.util.NoteInfoMappingUtil;
import com.bassims.modules.system.domain.Role;
import com.bassims.modules.system.domain.User;
import com.bassims.modules.system.repository.RoleRepository;
import com.bassims.modules.system.repository.UserRepository;
import com.bassims.utils.*;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-05-17
 **/
@Service
public class ProjectNoticeServiceImpl extends BaseServiceImpl<ProjectNoticeRepository, ProjectNotice> implements ProjectNoticeService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectNoticeServiceImpl.class);

    @Autowired
    private ProjectNoticeRepository projectNoticeRepository;
    @Autowired
    private ProjectNoticeMapper projectNoticeMapper;
    @Autowired
    private NoticeTemplateRepository noticeTemplateRepository;
    @Autowired
    private ProjectInfoService projectInfoService;
    @Autowired
    private NoteInfoMappingUtil util;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private ProjectStakeholdersRepository projectStakeholdersRepository;

    @Override
    public Map<String, Object> queryAll(ProjectNoticeQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        List<ProjectNoticeDto> list = projectNoticeRepository.getListQueryAll(criteria);
        PageInfo<ProjectNoticeDto> page = new PageInfo<>(list);
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", page.getList());
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<ProjectNoticeDto> queryAll(ProjectNoticeQueryCriteria criteria) {
        return  projectNoticeRepository.getList(criteria);

//        return projectNoticeMapper.toDto(list(QueryHelpPlus.getPredicate(ProjectNotice.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectNoticeDto findById(Long projectNoticeId) {
        ProjectNotice projectNotice = Optional.ofNullable(getById(projectNoticeId)).orElseGet(ProjectNotice::new);
        ValidationUtil.isNull(projectNotice.getProjectNoticeId(), getEntityClass().getSimpleName(), "projectNoticeId", projectNoticeId);
        return projectNoticeMapper.toDto(projectNotice);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectNoticeDto create(ProjectNotice resources) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        resources.setProjectNoticeId(snowflake.nextId());
        save(resources);
        return findById(resources.getProjectNoticeId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectNotice resources) {
        ProjectNotice projectNotice = Optional.ofNullable(getById(resources.getProjectNoticeId())).orElseGet(ProjectNotice::new);
        ValidationUtil.isNull(projectNotice.getProjectNoticeId(), "ProjectNotice", "id", resources.getProjectNoticeId());
        projectNotice.copy(resources);
        updateById(projectNotice);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long projectNoticeId : ids) {
            projectNoticeRepository.deleteById(projectNoticeId);
        }
    }

    @Override
    public void download(List<ProjectNoticeDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectNoticeDto projectNotice : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("项目id", projectNotice.getProjectId());
            map.put("节点id", projectNotice.getNodeId());
            map.put("节点编码", projectNotice.getNodeCode());
            map.put("项目名称", projectNotice.getProjectName());
            map.put("门店类型", projectNotice.getStoreType());
            map.put("节点名称", projectNotice.getNodeName());
            map.put("角色id", projectNotice.getRoleId());
            map.put("用户名", projectNotice.getUserId());
            map.put("角色名称", projectNotice.getRoleName());
            map.put("消息状态", projectNotice.getNoticeStatus());
            map.put("消息id", projectNotice.getNoticeId());
            map.put("消息名称", projectNotice.getNoticeName());
            map.put("消息类型", projectNotice.getNoticeType());
            map.put("消息内容", projectNotice.getNoticeContent());
            map.put("消息编码", projectNotice.getNoticeCode());
            map.put("计划结束时间", projectNotice.getPlanEndDate());
            map.put("消息返回", projectNotice.getMsg());
            map.put("是否发送", projectNotice.getIsSend());
            map.put(" isDelete", projectNotice.getIsDelete());
            map.put("创建时间", projectNotice.getCreateTime());
            map.put(" createBy", projectNotice.getCreateBy());
            map.put("修改时间", projectNotice.getUpdateTime());
            map.put("修改人", projectNotice.getUpdateBy());
            map.put("是否可用", projectNotice.getIsEnabled());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public Object queryCount(ProjectNoticeQueryCriteria criteria) {
        return count(QueryHelpPlus.getPredicate(ProjectNotice.class, criteria));
    }

    @Override
    public void generateNotice(Long nodeId, ProjectGroup projectGroup, JhSystemEnum.MessageTemplate template, Long sendUserId) {
        ProjectInfo projectInfo = projectInfoService.getById(projectGroup.getProjectId());
        if (ObjectUtil.isNotEmpty(projectInfo)) {
            LambdaQueryWrapper<NoticeTemplate> noticeTemplateWrapper = Wrappers.lambdaQuery(NoticeTemplate.class)
                    .eq(NoticeTemplate::getNoticeCode, template.getNoticeCode()).last(" limit 1");
            NoticeTemplate noticeTemplate = noticeTemplateRepository.selectOne(noticeTemplateWrapper);
            //查询当前项目的干系人，如果存在抄送角色的话，查询当前项目的抄送角色的干系人
            if (ObjectUtil.isEmpty(projectGroup.getIsCarbonCopy()) || projectGroup.getIsCarbonCopy() != 1) {
                projectGroup.setCarbonCopyRoleCode(null);
            }
            List<ProjectStakeholders> projectStakeholdersList = projectStakeholdersRepository.selectListByProjectId(projectGroup.getProjectId(),projectGroup.getCarbonCopyRoleCode(),template.getRoleCode());
            List<ProjectNotice> projectNoticeList = new ArrayList<>();
            User sendUser = userRepository.getOne(sendUserId);
            //获取管理员信息;给干系人发送消息通知的同时给管理员发送消息
            User userAdm = userRepository.getOne(1L);
            Role roleAdm = roleRepository.findRoleByRoleId(1L);
            final ProjectStakeholders admin = new ProjectStakeholders();
            admin.setUserId(userAdm.getId());
            admin.setRoleId(roleAdm.getId());
            projectStakeholdersList.add(admin);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String now = sdf.format(new Date());
            for (ProjectStakeholders stakeholders : projectStakeholdersList) {
//                User user = userRepository.getOne(stakeholders.getUserId());
//                Role role = roleRepository.findRoleByRoleId(stakeholders.getRoleId());
                ProjectNotice notice = new ProjectNotice();
                Snowflake snowflake = IdUtil.getSnowflake(1, 1);
                Long projectNoticeId = snowflake.nextId();
                notice.setProjectNoticeId(projectNoticeId);
                notice.setNoticeId(noticeTemplate.getNoticeId());
                notice.setProjectId(projectGroup.getProjectId());
                notice.setNodeId(nodeId);
                notice.setNodeCode(projectGroup.getNodeCode());
                notice.setProjectName(projectInfo.getProjectName());
                notice.setRoleName(stakeholders.getRoleName());
                notice.setRoleId(stakeholders.getRoleId());
                notice.setUserId(stakeholders.getUserId());
                notice.setStoreType(projectInfo.getProjectType());
                notice.setNoticeName(noticeTemplate.getNoticeName());
                notice.setNoticeType(noticeTemplate.getNoticeType());
                notice.setNodeName(projectGroup.getNodeName());
                notice.setNoticeStatus(JhSystemEnum.NoticeStatus.SENT.getStatus());
                notice.setNoticeType(noticeTemplate.getNoticeType());
                notice.setIsSend(Boolean.TRUE);
                notice.setIsEnabled(Boolean.TRUE);
                notice.setIsDelete(Boolean.FALSE);
                notice.setCreateTime(new Timestamp(System.currentTimeMillis()));
                notice.setCreateBy(stakeholders.getUsername());

                String templateContent = noticeTemplate.getNoticeContent();
                templateContent = templateContent.replace("{projectName}", projectInfo.getProjectName())
                        .replace("{taskName}", projectGroup.getNodeName())
                        .replace("{userName}", sendUser.getNickName())
                        .replace("{submitDate}", ObjectUtil.isNotEmpty(now)?now:"")
                        .replace("{fieldNodeName}", ObjectUtil.isNotEmpty(projectGroup.getFieldNodeName())?projectGroup.getFieldNodeName():"");
                notice.setAddress(noticeTemplate.getAddress());
                notice.setNoticeContent(templateContent);
                notice.setNoticeCode(noticeTemplate.getNoticeCode());
                projectNoticeList.add(notice);
            }
            this.saveBatch(projectNoticeList);
        }
    }

    @Override
    public boolean sendNotice(Long templateId, Map<String, String> params, Long... userIds) {
        NoticeTemplate noticeTemplate = noticeTemplateRepository.selectById(templateId);
        String templateContent = noticeTemplate.getNoticeContent();
        //参数替换
        int countParams = StringUtils.countChar("{", templateContent);
        if (countParams != params.size()) {
            logger.info("消息参数错误！");
            return Boolean.FALSE;
        }
        Set<String> keys = params.keySet();
        keys.forEach(k -> {
            templateContent.replace("{" + k + "}", params.get(k));
        });
        List<ProjectNotice> projectNoticeList = new ArrayList<>();
        for (Long userId : userIds) {
            User user = userRepository.getOne(userId);
            ProjectNotice notice = new ProjectNotice();
            Snowflake snowflake = IdUtil.getSnowflake(1, 1);
            Long projectNoticeId = snowflake.nextId();
            notice.setProjectNoticeId(projectNoticeId);
            notice.setNoticeId(noticeTemplate.getNoticeId());
            notice.setUserId(userId);
            notice.setNoticeName(noticeTemplate.getNoticeName());
            notice.setNoticeType(noticeTemplate.getNoticeType());
            notice.setAddress(noticeTemplate.getAddress());
            notice.setNoticeType(noticeTemplate.getNoticeType());
            notice.setNoticeStatus(JhSystemEnum.NoticeStatus.SENT.getStatus());
            notice.setIsSend(Boolean.TRUE);
            notice.setIsEnabled(Boolean.TRUE);
            notice.setIsDelete(Boolean.FALSE);
            notice.setCreateTime(new Timestamp(System.currentTimeMillis()));
            notice.setCreateBy(user.getUsername());
            notice.setNoticeContent(templateContent);
            notice.setNoticeCode(noticeTemplate.getNoticeCode());
            projectNoticeList.add(notice);
        }
        return this.saveBatch(projectNoticeList);
    }

    @Override
    public boolean noticeRead(Long projectNoticeId) {
        LambdaUpdateWrapper<ProjectNotice> noticeWrapper = Wrappers.lambdaUpdate(ProjectNotice.class)
                .eq(ProjectNotice::getProjectNoticeId, projectNoticeId)
                .set(ProjectNotice::getNoticeStatus, JhSystemEnum.NoticeStatus.READ.getStatus());
        return this.update(noticeWrapper);

    }

    @Override
    public Map<String, Object> getSystemLog(ProjectNoticeQueryCriteria criteria, Pageable pageable) {
        final LambdaQueryWrapper<ProjectNotice> lambdaQuery = Wrappers.lambdaQuery(ProjectNotice.class);
        final List<ProjectNotice> projectNotices = projectNoticeRepository.selectList(lambdaQuery);
        final List<ProjectNoticeDto> list = new ArrayList<>();
        projectNotices.stream().map(ProjectNotice::getNoticeContent).distinct().forEach(p -> {
            getNoticeContent(list, p, "关于项目【(.*?)】-任务【(.*?)】，由【(.*?)】于【(.*?)】执行完毕！", "执行完毕");
            getNoticeContent(list, p, "项目【(.*?)】-任务【(.*?)】，【(.*?)】于【(.*?)】提交审批申请！", "提交审批申请");
            getNoticeContent(list, p, "项目【(.*?)】-任务【(.*?)】，【(.*?)】于【(.*?)】审批拒绝！", "审批拒绝");
            getNoticeContent(list, p, "项目【(.*?)】-任务【(.*?)】，【(.*?)】于【(.*?)】审批通过！", "审批通过");
        });
        //getPage(pageable);
        final List toPage = PageUtil.toPage(pageable.getPageNumber() - 1, pageable.getPageSize(), list);
        PageInfo<ProjectNoticeDto> page = new PageInfo<>(list);
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", toPage);
        map.put("totalElements", page.getTotal());
        return map;
    }

    private void getNoticeContent(List<ProjectNoticeDto> list, String noticeContent, String content, String event) {
        Pattern pattern = Pattern.compile(content);
        Matcher matcher = pattern.matcher(noticeContent);
        if (matcher.find()) {
            final ProjectNoticeDto dto = new ProjectNoticeDto();
            dto.setProjectName(matcher.group(1));
            dto.setTaskName(matcher.group(2));
            dto.setUserName(matcher.group(3));
            dto.setSubmitDate(matcher.group(4));
            dto.setOperationalEvents(event);
            list.add(dto);
        }
    }
}