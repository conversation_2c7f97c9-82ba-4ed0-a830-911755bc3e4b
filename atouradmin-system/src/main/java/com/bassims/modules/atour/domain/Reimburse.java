/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-12-21
**/
@Data
@TableName(value="s_reimburse")
public class Reimburse implements Serializable {

    @TableId(value = "reimburse_id")
    @ApiModelProperty(value = "主键id")
    private Long reimburseId;

    @TableField(value = "payment_application_id")
    @ApiModelProperty(value = "付款申请id")
    private Long paymentApplicationId;

    @TableField(value = "reimburse_total")
    @ApiModelProperty(value = "报销合计")
    private BigDecimal reimburseTotal;

    @TableField(value = "tax_total")
    @ApiModelProperty(value = "税额合计")
    private BigDecimal taxTotal;

    @TableField(value = "reimburse_to_tax_total")
    @ApiModelProperty(value = "不含税金额合计")
    private BigDecimal reimburseToTaxTotal;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "create_by",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_by",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableLogic
    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    public void copy(Reimburse source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}