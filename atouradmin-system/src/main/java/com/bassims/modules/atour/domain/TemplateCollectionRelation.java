package com.bassims.modules.atour.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * group和条件的关联
 */
@Data
@TableName(value="t_template_collection_relation")
public class TemplateCollectionRelation implements Serializable {

    @TableId(value = "template_collection_relation_id")
    @ApiModelProperty(value = "主键id")
    private Long templateCollectionRelationId;

    @TableField(value = "group_id")
    @ApiModelProperty(value = "组id")
    private Long groupId;

    @TableField(value = "condition_code")
    @ApiModelProperty(value = "条件code")
    private String conditionCode;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "createBy")
    private String createBy;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

    @TableField(value = "remark")
    @ApiModelProperty(value = "remark")
    private String remark;

    public void copy(TemplateCollectionRelation source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
