/*
 *  Copyright 2019-2020 KB
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.modules.atour.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description / 整改照片列表
 * @date 2023-10-30
 **/
@Data
public class ProjectInfoPushRectifyImgListDTO implements Serializable {


    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "上传后返回的文件唯一标示ossKey ")
    private String fileUrl;

    @ApiModelProperty(value = "新文件标识：true是 false否 null否 ")
    private Boolean isNew;

}