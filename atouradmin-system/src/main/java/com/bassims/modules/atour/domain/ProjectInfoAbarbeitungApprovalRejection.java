/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import java.sql.Timestamp;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-11-29
**/
@Data
@TableName(value="t_project_info_abarbeitung_approval_rejection")
public class ProjectInfoAbarbeitungApprovalRejection implements Serializable {

    @TableId(value = "abarbeitung_id")
    @ApiModelProperty(value = "项目整改ID") /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long abarbeitungId;

    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id") /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long projectId;

    @TableField(value = "node_code")
    @ApiModelProperty(value = "三级nodeCode")
    private String nodeCode;

    @TableField(value = "file_node_id")
    @ApiModelProperty(value = "文件的nodeId") /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long fileNodeId;

    @TableField(value = "abarbeitung_type")
    @ApiModelProperty(value = "问题类型")
    private String abarbeitungType;

    @TableField(value = "problem_description")
    @ApiModelProperty(value = "描述")
    private String problemDescription;

    @TableField(value = "solution")
    @ApiModelProperty(value = "解决方案")
    private String solution;

    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @TableField(value = "status")
    @ApiModelProperty(value = "状态")
    private String status;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(value = "update_time" ,fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField(value = "is_enabled")
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnabled;

    @TableField(value = "is_delete")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @TableField(value = "title")
    @ApiModelProperty(value = "标题")
    private String title;

    public void copy(ProjectInfoAbarbeitungApprovalRejection source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}