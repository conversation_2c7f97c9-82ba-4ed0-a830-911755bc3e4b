/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bassims.modules.atour.domain.SupplierCase;
import com.bassims.modules.atour.domain.SupplierInfo;
import com.bassims.modules.atour.domain.vo.SupplierManagerVo;
import com.bassims.modules.atour.service.dto.SupplierInfoDto;
import com.bassims.modules.atour.service.dto.SupplierInfoQueryCriteria;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-09-15
**/
@Repository
public interface SupplierInfoRepository extends BaseMapper<SupplierInfo> {

    SupplierInfoDto getSupplierInfo(SupplierInfo resources);

    List<SupplierManagerVo> supplierManagerRelate();
    void insertSupplierCity(@Param("citys") Set<Long> citys,@Param("supplierId") Long supplierId);

    int getMaxNumFromSupplierInfo();

    SupplierInfo getSupInfoBySupNameAndRoleCode(@Param("supName") String supName,@Param("roleCode") String roleCode);

    SupplierManagerVo getSupManagerInfoBySupCn(@Param("supName") String supName);

    List<SupplierInfoDto> getSupplierList(SupplierInfoQueryCriteria criteria);

    List<SupplierCase> getSupplierCaseList(@Param("supplierId")Long supplierId);

    SupplierInfo getSupplierInfoBySupplierPm(@Param("supplierPmName")String[] supplierPmName);
    List<Long> getSupplierCity(@Param("supplierId")Long supplierId);
}