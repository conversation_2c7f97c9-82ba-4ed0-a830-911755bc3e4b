package com.bassims.modules.atour.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @BelongsProject: atouradmin
 * @BelongsPackage: com.bassims.modules.kids.service.dto
 * @Author: xiong
 * @CreateTime: 2022-12-01  21:52
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class MeasureDto implements Serializable {

    /**
     * 商户编号
     */
    private String storeNo;

    /**
     * 商户名称
     */
    private String storeName;

    /**
     * 分摊详情
     */
    private List<MeasureInfoDto> measureInfoDtoList;





}
