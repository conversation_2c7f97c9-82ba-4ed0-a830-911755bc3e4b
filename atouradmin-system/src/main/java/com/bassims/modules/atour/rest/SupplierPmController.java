/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.AnonymousAccess;
import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.modules.atour.domain.SupplierInfo;
import com.bassims.modules.atour.domain.SupplierPm;
import com.bassims.modules.atour.service.SupplierPmService;
import com.bassims.modules.atour.service.dto.SupplierPmDto;
import com.bassims.modules.atour.service.dto.SupplierPmQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.SupplierPmMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
* 项目经理
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-09-15
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "t_supplier_pm管理")
@RequestMapping("/api/supplierPm")
public class SupplierPmController {

    private static final Logger logger = LoggerFactory.getLogger(SupplierPmController.class);

    private final SupplierPmService supplierPmService;

    @Autowired
    private SupplierPmMapper supplierPmMapper;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, SupplierPmQueryCriteria criteria) throws IOException {
        supplierPmService.download(supplierPmService.queryAllNoPage(criteria), response);
    }

    @Log("导出模板")
    @ApiOperation("导出模板")
    @GetMapping(value = "/downloadTemplate")
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response, SupplierPmQueryCriteria criteria) throws IOException {
        supplierPmService.downloadTemplate(request, response);
    }

    @Log("供应商人员信息导入")
    @ApiOperation("供应商人员信息导入")
    @PostMapping(value = "/supplierPmImport")
    public ResponseEntity<Object> supplierPmImport(MultipartFile file) throws IOException {
        return new ResponseEntity<>(supplierPmService.supplierPmImport(file), HttpStatus.OK);
    }

    /**
    * 分页查询
    * @real_return {@link ResponseEntity<List<SupplierPmDto>>}
    */
    @GetMapping("/list")
//    @Log("查询t_supplier_pm")
    @ApiOperation("查询t_supplier_pm")
    @MyDataPermission(title = "供应商信息,人员管理,供应商清单,供应商人员清单,营建对接启动,项目推进")
    public ResponseEntity<Object> query(SupplierPmQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(supplierPmService.queryAll(criteria,pageable),HttpStatus.OK);
    }


    /**
     * 不分页查询
     * @param criteria
     * @real_return {@link ResponseEntity<List<SupplierPmDto>>}
     */
    @GetMapping("/noPageList")
//    @Log("查询t_supplier_pm")
    @ApiOperation("查询t_supplier_pm")
    public ResponseEntity<Object> noPageList(SupplierPmQueryCriteria criteria){
        return new ResponseEntity<>(supplierPmService.queryAllNoPage(criteria),HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity<SupplierPmDto>}
    */
    @GetMapping(value = "/{id}")
//    @Log("通过Id查询t_supplier_pm")
    @ApiOperation("查询t_supplier_pm")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(supplierPmService.findById(id),HttpStatus.OK);
    }

//    @PostMapping("/save")
//    @Log("[供应商]-供应商人员新建")
//    @ApiOperation("[供应商]-供应商人员新建")
//    public ResponseEntity<Object> create(@Validated @RequestBody SupplierPmDto resources,Long supplierId){
//        return new ResponseEntity<>(supplierPmService.create(resources,supplierId),HttpStatus.CREATED);
//    }

    @PostMapping("/save")
    @Log("[供应商]-供应商人员新建添加原有用户为供应商人员角色")
    @ApiOperation("[供应商]-供应商人员新建添加原有用户为供应商人员角色")
    public ResponseEntity<Object> createAndGiveUserSupplier(@Validated @RequestBody SupplierPmDto resources){
        return new ResponseEntity<>(supplierPmService.createAndGiveUserSupplier(resources),HttpStatus.CREATED);
    }

    @PostMapping("/saveNoAuthorization")
    @Log("[供应商]-供应商人员新建")
    @ApiOperation("[供应商]-供应商人员新建")
    @AnonymousAccess
    public ResponseEntity<Object> saveNoAuthorization(@Validated @RequestBody SupplierPmDto resources,Long supplierId){
        return new ResponseEntity<>(supplierPmService.create(resources,supplierId),HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("[供应商]-供应商人员编辑")
    @ApiOperation("[供应商]-供应商人员编辑")
    public ResponseEntity<Object> update(@Validated @RequestBody SupplierPmDto resources){
        supplierPmService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/updateStatus")
    @Log("[供应商]-供应商人员变更状态")
    @ApiOperation("[供应商]-供应商人员变更状态")
    public ResponseEntity<Object> updateStatus(@Validated @RequestBody SupplierPm resources){
        supplierPmService.updateStatus(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/deleteStatus")
    @Log("[供应商]-供应商人员删除")
    @ApiOperation("[供应商]-供应商人员删除")
    public ResponseEntity<Object> deleteStatus(@Validated @RequestBody SupplierPm resources){
        supplierPmService.deleteSupplierPm(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除t_supplier_pm")
    @ApiOperation("删除t_supplier_pm")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        supplierPmService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity<SupplierPmDto>}
     */
    @GetMapping(value = "/supPmInfoByName")
//    @Log("根据名称查询项目经理信息")
    @ApiOperation("根据名称查询项目经理信息")
    public ResponseEntity<Object> supPmInfoByName(String pmName){
        return new ResponseEntity<>(supplierPmService.supPmInfoByName(pmName),HttpStatus.OK);
    }

    /**
     * @real_return {@link ResponseEntity<SupplierPmDto>}
     * 根据供应商和供应商角色查询项目经理信息
     */
    @GetMapping(value = "/getSupplierPmList")
//    @Log("查询项目经理信息")
    @ApiOperation("查询项目经理信息")
    public ResponseEntity<Object> getSupplierPmList(SupplierPmQueryCriteria criteria){
        return new ResponseEntity<>(supplierPmService.getSupplierPmList(criteria),HttpStatus.OK);
    }

}