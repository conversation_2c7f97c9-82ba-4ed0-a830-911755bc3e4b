/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.rest;

import com.bassims.annotation.Log;
import com.bassims.annotation.MyDataPermission;
import com.bassims.modules.atour.domain.ApproveTemplateDetail;
import com.bassims.modules.atour.service.ApproveTemplateDetailService;
import com.bassims.modules.atour.service.dto.ApproveTemplateDetailQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-03-24
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "审批模版详情 管理")
@RequestMapping("/api/approveTemplateDetail")
public class ApproveTemplateDetailController {

    private static final Logger logger = LoggerFactory.getLogger(ApproveTemplateDetailController.class);

    private final ApproveTemplateDetailService approveTemplateDetailService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @MyDataPermission(title = "系统管理")
    public void download(HttpServletResponse response, ApproveTemplateDetailQueryCriteria criteria) throws IOException {
        approveTemplateDetailService.download(approveTemplateDetailService.queryAll(criteria), response);
    }

    /**
    * @real_return {@link ResponseEntity <List<ApproveTemplateDetailDto>>}
    */
    @GetMapping("/list")
    @Log("查询审批模版详情")
    @ApiOperation("查询审批模版详情")
    @MyDataPermission(title = "系统管理")
    public ResponseEntity<Object> query(ApproveTemplateDetailQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(approveTemplateDetailService.queryAll(criteria,pageable), HttpStatus.OK);
    }

    /**
    * @real_return {@link ResponseEntity <ApproveTemplateDetailDto>}
    */
    @GetMapping(value = "/{id}")
    @Log("通过Id查询审批模版详情")
    @ApiOperation("查询审批模版详情")
    @MyDataPermission(title = "系统管理")
    public ResponseEntity<Object> query(@PathVariable Long id){
        return new ResponseEntity<>(approveTemplateDetailService.findById(id), HttpStatus.OK);
    }

    @PostMapping("/save")
    @Log("新增审批模版详情")
    @ApiOperation("新增审批模版详情")
    public ResponseEntity<Object> create(@Validated @RequestBody ApproveTemplateDetail resources){
        return new ResponseEntity<>(approveTemplateDetailService.create(resources), HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @Log("修改审批模版详情")
    @ApiOperation("修改审批模版详情")
    @MyDataPermission(title = "系统管理")
    public ResponseEntity<Object> update(@Validated @RequestBody ApproveTemplateDetail resources){
        approveTemplateDetailService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/delete")
    @Log("删除审批模版详情")
    @ApiOperation("删除审批模版详情")
    @MyDataPermission(title = "系统管理")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        approveTemplateDetailService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}