/*
*  Copyright 2019-2020 KB
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.bassims.modules.atour.service.impl;

import com.bassims.modules.atour.domain.TemplateGroupExpand;
import com.bassims.utils.ValidationUtil;
import com.bassims.utils.FileUtil;
import com.bassims.utils.QueryHelpPlus;
import com.bassims.modules.atour.repository.TemplateGroupExpandRepository;
import com.bassims.modules.atour.service.TemplateGroupExpandService;
import com.bassims.base.BaseServiceImpl;
import com.bassims.modules.atour.service.dto.TemplateGroupExpandDto;
import com.bassims.modules.atour.service.dto.TemplateGroupExpandQueryCriteria;
import com.bassims.modules.atour.service.mapstruct.TemplateGroupExpandMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-12-27
**/
@Service
public class TemplateGroupExpandServiceImpl extends BaseServiceImpl<TemplateGroupExpandRepository,TemplateGroupExpand> implements TemplateGroupExpandService {

    private static final Logger logger = LoggerFactory.getLogger(TemplateGroupExpandServiceImpl.class);

    @Autowired
    private TemplateGroupExpandRepository templateGroupExpandRepository;
    @Autowired
    private TemplateGroupExpandMapper templateGroupExpandMapper;

    @Override
    public Map<String,Object> queryAll(TemplateGroupExpandQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<TemplateGroupExpand> page = new PageInfo<>(list(QueryHelpPlus.getPredicate(TemplateGroupExpand.class, criteria)));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", templateGroupExpandMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<TemplateGroupExpandDto> queryAll(TemplateGroupExpandQueryCriteria criteria){
        return templateGroupExpandMapper.toDto(list(QueryHelpPlus.getPredicate(TemplateGroupExpand.class, criteria)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateGroupExpandDto findById(Long expandId) {
        TemplateGroupExpand templateGroupExpand = Optional.ofNullable(getById(expandId)).orElseGet(TemplateGroupExpand::new);
        ValidationUtil.isNull(templateGroupExpand.getExpandId(),getEntityClass().getSimpleName(),"expandId",expandId);
        return templateGroupExpandMapper.toDto(templateGroupExpand);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateGroupExpandDto create(TemplateGroupExpand resources) {
        save(resources);
        return findById(resources.getExpandId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TemplateGroupExpand resources) {
        TemplateGroupExpand templateGroupExpand = Optional.ofNullable(getById(resources.getExpandId())).orElseGet(TemplateGroupExpand::new);
        ValidationUtil.isNull( templateGroupExpand.getExpandId(),"TemplateGroupExpand","id",resources.getExpandId());
        templateGroupExpand.copy(resources);
        updateById(templateGroupExpand);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long expandId : ids) {
            templateGroupExpandRepository.deleteById(expandId);
        }
    }

    @Override
    public void download(List<TemplateGroupExpandDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TemplateGroupExpandDto templateGroupExpand : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("审图分类（reclassify）", templateGroupExpand.getReclassify());
            map.put("审图属性（深化、设计）", templateGroupExpand.getReviewAttribute());
            map.put("文件标题", templateGroupExpand.getFileHeader());
            map.put("二级nodeCode", templateGroupExpand.getGroupNodeCode());
            map.put("三级nodeCode", templateGroupExpand.getNodeCode());
            map.put(" templateCode",  templateGroupExpand.getTemplateCode());
            map.put("是否可用", templateGroupExpand.getIsEnabled());
            map.put("是否删除", templateGroupExpand.getIsDelete());
            map.put("创建时间", templateGroupExpand.getCreateTime());
            map.put("更新时间", templateGroupExpand.getUpdateTime());
            map.put(" createBy",  templateGroupExpand.getCreateBy());
            map.put(" updateBy",  templateGroupExpand.getUpdateBy());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}