package com.bassims.controller;

import com.bassims.annotation.rest.AnonymousGetMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 用于Eureka服务注册中心监控服务状态
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "健康检查")
@RestController
public class HealthController {

    @ApiOperation("健康检查")
    @AnonymousGetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        result.put("service", "atouradmin-system");
        result.put("version", "2.6");
        return result;
    }

    @ApiOperation("服务信息")
    @AnonymousGetMapping("/info")
    public Map<String, Object> info() {
        Map<String, Object> result = new HashMap<>();
        result.put("app", "atouradmin-system");
        result.put("description", "ATOUR-ADMIN 后台管理系统");
        result.put("version", "2.6");
        result.put("spring-boot-version", "2.1.0.RELEASE");
        result.put("spring-cloud-version", "Greenwich.SR6");
        result.put("build-time", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        return result;
    }
}
