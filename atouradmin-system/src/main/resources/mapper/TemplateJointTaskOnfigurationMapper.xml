<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.TemplateJointTaskOnfigurationRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.TemplateJointTaskOnfiguration">
        <id column="template_joint_task_id" property="templateJointTaskId"/>
        <result column="joint_code" property="jointCode"/>
        <result column="association_name" property="associationName"/>
        <result column="template_group_id" property="templateGroupId"/>
        <result column="node_code" property="nodeCode"/>
        <result column="node_name" property="nodeName"/>
        <result column="relevance_template_group_id" property="relevanceTemplateGroupId"/>
        <result column="relevance_node_code" property="relevanceNodeCode"/>
        <result column="relevance_node_name" property="relevanceNodeName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        template_joint_task_id,joint_code,association_name,template_group_id,node_code,node_name,relevance_template_group_id,relevance_node_code,relevance_node_name,create_time,update_time,create_by,update_by,is_enabled,is_delete
    </sql>


    <select id="getJointTaskByNodeCode" resultType="com.bassims.modules.atour.domain.TemplateJointTaskOnfiguration">
        SELECT
            t_template_joint_task_onfiguration.* ,
            t_template_joint_condition_onfiguration.field_requirement,
            t_template_joint_condition_onfiguration.field_requirement_node_code,
            t_template_joint_condition_onfiguration.conditional_demand
        FROM
            t_template_joint_task_onfiguration  t_template_joint_task_onfiguration
                left join t_template_joint_condition_onfiguration t_template_joint_condition_onfiguration on t_template_joint_condition_onfiguration.template_joint_task_id =t_template_joint_task_onfiguration.template_joint_task_id
        WHERE
            t_template_joint_task_onfiguration.node_code = #{nodeCode}
    </select>

    <select id="getJointTaskConfigurationList" resultType="com.bassims.modules.atour.domain.ProjectJointTaskOnfiguration">
        SELECT
            t_template_joint_task_onfiguration.* ,
            t_template_joint_condition_onfiguration.field_requirement,
            t_template_joint_condition_onfiguration.field_requirement_node_code,
            t_template_joint_condition_onfiguration.conditional_demand
        FROM
            t_template_joint_task_onfiguration  t_template_joint_task_onfiguration
                left join t_template_joint_condition_onfiguration t_template_joint_condition_onfiguration on t_template_joint_condition_onfiguration.template_joint_task_id =t_template_joint_task_onfiguration.template_joint_task_id
        WHERE
            t_template_joint_task_onfiguration.node_code = #{nodeCode}
    </select>

</mapper>
