<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.SupplierInfoRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.SupplierInfo">
        <id column="id" property="id"/>
        <result column="supplier_num" property="supplierNum"/>
        <result column="supplier_version_id" property="supplierVersionId"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="sup_name_cn" property="supNameCn"/>
        <result column="sup_name_en" property="supNameEn"/>
        <result column="sup_short_name" property="supShortName"/>
        <result column="sup_province" property="supProvince"/>
        <result column="sup_city" property="supCity"/>
        <result column="sup_post_code" property="supPostCode"/>
        <result column="comp_reg_date" property="compRegDate"/>
        <result column="comp_reg_addr" property="compRegAddr"/>
        <result column="effect_status" property="effectStatus"/>
        <result column="version_status" property="versionStatus"/>
        <result column="sup_status" property="supStatus"/>
        <result column="is_disposable" property="isDisposable"/>
        <result column="contact" property="contact"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="fax" property="fax"/>
        <result column="bizlic_exp_date" property="bizlicExpDate"/>
        <result column="spp_exp_date" property="sppExpDate"/>
        <result column="tax_reg_attr" property="taxRegAttr"/>
        <result column="tax_sale_rate" property="taxSaleRate"/>
        <result column="tax_service_rate" property="taxServiceRate"/>
        <result column="trc_exp_date" property="trcExpDate"/>
        <result column="logistics_terms" property="logisticsTerms"/>
        <result column="parent_id" property="parentId"/>
        <result column="project_id" property="projectId"/>
        <result column="is_active" property="isActive"/>
        <result column="CREATE_USER" property="createUser"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_USER" property="updateUser"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="IS_DELETE" property="isDelete"/>
        <result column="business_exp_date" property="businessExpDate"/>
        <result column="service_start_date" property="serviceStartDate"/>
        <result column="service_scope" property="serviceScope"/>
        <!--                    <result column="service_area" property="serviceArea" />-->
        <result column="supplier_starbucks_rating" property="supplierStarbucksRating"/>
        <result column="avg_quality_score" property="avgQualityScore"/>
        <result column="bank_account_name" property="bankAccountName"/>
        <result column="bank_name_cn" property="bankNameCn"/>
        <result column="bank_name_en" property="bankNameEn"/>
        <result column="bank_branch_cn" property="bankBranchCn"/>
        <result column="bank_branch_city_cn" property="bankBranchCityCn"/>
        <result column="bank_account_number" property="bankAccountNumber"/>
        <result column="tax_number" property="taxNumber"/>
        <result column="market_type" property="marketType"/>
        <result column="referrer" property="referrer"/>
        <result column="contact_way" property="contactWay"/>
        <result column="pay_retention_money" property="payRetentionMoney"/>
        <result column="refund_warranty_deposit" property="refundWarrantyDeposit"/>
        <result column="is_first_material_supplier" property="isFirstMaterialSupplier"/>
        <result column="is_test" property="isTest"/>
        <result column="legal_entity" property="legalEntity"/>
        <result column="project_convenor" property="projectConvenor"/>
        <result column="project_convenor_phone" property="projectConvenorPhone"/>
        <result column="project_convenor_email" property="projectConvenorEmail"/>
        <result column="decoration_qualification" property="decorationQualification"/>
        <result column="decoration_exp_date" property="decorationExpDate"/>
        <result column="electromechanical_qualification" property="electromechanicalQualification"/>
        <result column="electromechanical_qualification_type" property="electromechanicalQualificationType"/>
        <result column="electromechanical_exp_date" property="electromechanicalExpDate"/>
        <result column="spl_exp_date" property="splExpDate"/>
        <result column="tral_exp_date" property="tralExpDate"/>
        <result column="service_content" property="serviceContent"/>
        <result column="project_manager_amount" property="projectManagerAmount"/>
        <result column="is_used" property="isUsed"/>
        <result column="project_convenor_fax" property="projectConvenorFax"/>
        <result column="other_licence_exp_date" property="otherLicenceExpDate"/>
        <result column="change_desc" property="changeDesc"/>
        <result column="postal_code" property="postalCode"/>
        <result column="pay_term" property="payTerm"/>
        <result column="supplier_address" property="supplierAddress"/>
        <result column="user_id" property="userId"/>
        <result column="file_id" property="fileId"/>
        <result column="platform_provider_not" property="platformProviderNot"/>
        <result column="supplier_type" property="supplierType"/>
        <result column="tk_id" property="tkId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,supplier_num,supplier_version_id,supplier_code,sup_name_cn,sup_name_en,sup_short_name,sup_province,sup_city,sup_post_code,comp_reg_date,comp_reg_addr,effect_status,version_status,sup_status,is_disposable,contact,phone,email,fax,bizlic_exp_date,spp_exp_date,tax_reg_attr,tax_sale_rate,tax_service_rate,trc_exp_date,logistics_terms,parent_id,project_id,is_active,CREATE_USER,CREATE_TIME,UPDATE_USER,UPDATE_TIME,IS_DELETE,business_exp_date,service_start_date,service_scope,service_area,supplier_starbucks_rating,avg_quality_score,bank_account_name,bank_name_cn,bank_name_en,bank_branch_cn,bank_branch_city_cn,bank_account_number,tax_number,market_type,referrer,contact_way,pay_retention_money,refund_warranty_deposit,is_first_material_supplier,is_test,legal_entity,project_convenor,project_convenor_phone,project_convenor_email,decoration_qualification,decoration_exp_date,electromechanical_qualification,electromechanical_qualification_type,electromechanical_exp_date,spl_exp_date,tral_exp_date,service_content,project_manager_amount,is_used,project_convenor_fax,other_licence_exp_date,change_desc,postal_code,pay_term,supplier_address,user_id,file_id,platform_provider_not,supplier_type,tk_id
    </sql>

    <select id="getMaxNumFromSupplierInfo" resultType="Integer">
        select IF(max(SUBSTR(supplier_num,5)+0) IS NULL, 1, max(SUBSTR(supplier_num,5)+0)) AS result  from t_supplier_info
    </select>
    <select id="supplierManagerRelate" resultType="com.bassims.modules.atour.domain.vo.SupplierManagerVo">
        select s.user_id,
               s.dept_id,
               s.username,
               s.nick_name,
               s.gender,
               s.phone,
               s.email,
               s.avatar_name,
               s.avatar_path,
               s.password,
               s.is_admin,
               s.open_id,
               s.enabled,
               s.login_limit,
               s.create_by,
               s.update_by,
               s.pwd_reset_time,
               s.create_time,
               s.update_time,
               t.sup_name_cn,
               t.id   supplier_id,
               r.name role_name,
               t.tk_id
        from s_user_info s
                 left join s_sys_users_roles u on s.user_id = u.user_id
                 left join s_sys_role r on u.role_id = r.role_id
                 left join t_supplier_info t on find_in_set(cast(s.user_id as char), t.user_id)
        where r.role_code = 'csgly'
           or r.role_code = 'jgccsgly'
    </select>

    <insert id="insertSupplierCity">
        insert into s_sys_supplier_city (area_code,supplier_id) values
        <foreach collection="citys" item="item" separator=",">
            (#{item},#{supplierId})
        </foreach>
    </insert>

    <select id="getSupInfoBySupNameAndRoleCode" resultType="com.bassims.modules.atour.domain.SupplierInfo">
        SELECT t.*,
               f.name role_name
        FROM t_supplier_info t
                 LEFT JOIN s_user_info r ON r.user_id = t.user_id
                 LEFT JOIN s_sys_users_roles o ON r.user_id = o.user_id
                 LEFT JOIN s_sys_role f ON o.role_id = f.role_id
        where t.sup_name_cn = #{supName}
          and f.role_code = #{roleCode}
    </select>

    <select id="getSupManagerInfoBySupCn" parameterType="String"
            resultType="com.bassims.modules.atour.domain.vo.SupplierManagerVo">
        select s.user_id,
        s.dept_id,
        s.username,
        s.nick_name,
        s.gender,
        s.phone,
        s.email,
        s.avatar_name,
        s.avatar_path,
        s.password,
        s.is_admin,
        s.open_id,
        s.enabled,
        s.login_limit,
        s.create_by,
        s.update_by,
        s.pwd_reset_time,
        s.create_time,
        s.update_time,
        t.sup_name_cn,
        t.id supplier_id,
        r.name role_name
        from s_user_info s left join s_sys_users_roles u on s.user_id = u.user_id
        left join s_sys_role r on u.role_id = r.role_id
        left join t_supplier_info t on find_in_set(cast(s.user_id as char),t.user_id)
        where (r.role_code = 'csgly' or r.role_code = 'jgccsgly')
        <if test="supName != null and supName != ''">
            and t.sup_name_cn = #{supName}
        </if>
    </select>

    <select id="getSupplierList" resultType="com.bassims.modules.atour.service.dto.SupplierInfoDto">
        SELECT distinct t_supplier_info.*
        FROM t_supplier_info t_supplier_info
        LEFT JOIN s_sys_supplier_city s_sys_supplier_city ON s_sys_supplier_city.supplier_id = t_supplier_info.id
        where t_supplier_info.IS_DELETE = 0 and t_supplier_info.sup_status  = 'cooperation' and status = 'approve_pass'
        <if test="null != supNameCn and '' != supNameCn">
            and t_supplier_info.sup_name_cn like concat('%', #{supNameCn}, '%')
        </if>
        <if test="null != projectId and '' != projectId">
            and (
            s_sys_supplier_city.area_code = (select city from t_project_info where project_id =#{projectId})
            )
        </if>
        <if test="null != city and '' != city">
            and (
            s_sys_supplier_city.area_code = #{city}
            or t_supplier_info.service_area = '全国'
            )
        </if>
        <if test="null != serviceScope and '' != serviceScope ">
            and FIND_IN_SET(
                (select `value` from s_sys_dict_detail where dict_id
                  =(select dict_id from s_sys_dict where `name` = 'service_scope' limit 1)
                and `value` = #{serviceScope} limit 1)
                ,t_supplier_info.service_scope)
        </if>
        <if test="null != supplierType and '' != supplierType">
            <foreach collection="supplierType.split(',')" item="supplierTypeItem" index="index" open="and (" close=")" separator="or">
                FIND_IN_SET(#{supplierTypeItem},t_supplier_info.supplier_type)
            </foreach>
        </if>

<!--        <if test="null != supplierType and '' != supplierType  and (null == supplierType1 or '' == supplierType)">-->
<!--            and FIND_IN_SET(-->
<!--                (select `value` from s_sys_dict_detail where-->
<!--                    dict_id	=(select dict_id from s_sys_dict where `name` = 'supplier_type' limit 1)-->
<!--                and `value` = #{supplierType} limit 1)-->
<!--                ,t_supplier_info.supplier_type)-->
<!--        </if>-->
<!--        <if test="null != supplierType and '' != supplierType and null != supplierType1 and '' != supplierType1 ">-->
<!--            and FIND_IN_SET(t_supplier_info.supplier_type ,-->
<!--                (select GROUP_CONCAT(`value` ,',') from s_sys_dict_detail-->
<!--                where dict_id	=(select dict_id from s_sys_dict where `name` = 'supplier_type' limit 1)-->
<!--                and (`value` = #{supplierType} or `value` = #{supplierType1} ) ))-->
<!--        </if>-->
        <if test="null != productCode and '' != productCode ">
            and FIND_IN_SET((select `value` from s_sys_dict_detail where dict_id	=(select dict_id from s_sys_dict where `name` = 'execution_standards' limit 1) and `value` = #{productCode} limit 1),t_supplier_info.referrer)
        </if>
        group by t_supplier_info.id
    </select>

    <select id="getSupplierInfo" resultType="com.bassims.modules.atour.service.dto.SupplierInfoDto">
        select *
        from t_supplier_info
        where (sup_name_cn like concat('%', #{supNameCn}, '%') or email like concat('%', #{email}, '%')
            or referrer like concat('%', #{referrer}, '%') or contact_way like concat('%', #{contactWay}, '%')
            or bank_account_number = #{bankAccountNumber} or tax_number = #{taxNumber})
          and IS_DELETE = 0 limit 1

    </select>
    <select id="getSupplierCaseList" resultType="com.bassims.modules.atour.domain.SupplierCase">
        select * from t_supplier_case where supplier_id = #{supplierId}
    </select>
    <select id="getSupplierInfoBySupplierPm" resultType="com.bassims.modules.atour.domain.SupplierInfo">
        select i.* from t_supplier_info i left join `atour-con`.t_supplier_pm tsp on i.id = tsp.supplier_id
            where tsp.pm_name in
            <foreach collection="supplierPmName" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
            limit 1
    </select>
    <select id="getSupplierCity" resultType="java.lang.Long">
        select area_code from s_sys_supplier_city where supplier_id = #{supplierId}
    </select>
</mapper>
