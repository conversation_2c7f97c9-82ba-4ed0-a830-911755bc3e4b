<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.MaterialManagementExpandRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.MaterialManagementExpand">
                    <id column="management_expand_id" property="managementExpandId" />
                    <result column="management_id" property="managementId" />
                    <result column="price_year" property="priceYear" />
                    <result column="unit_price" property="unitPrice" />
                    <result column="create_time" property="createTime" />
                    <result column="create_user" property="createUser" />
                    <result column="create_dept" property="createDept" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_user" property="updateUser" />
                    <result column="is_delete" property="isDelete" />
                    <result column="tenant_id" property="tenantId" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            management_expand_id,management_id,price_year,unit_price,create_time,create_user,create_dept,update_time,update_user,is_delete,tenant_id
        </sql>

</mapper>
