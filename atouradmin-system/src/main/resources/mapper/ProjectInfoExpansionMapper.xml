<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ProjectInfoExpansionRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ProjectInfoExpansion">
        <id column="project_expansion_id" property="projectExpansionId"/>
        <result column="project_id" property="projectId"/>
        <result column="hotel_id" property="hotelId"/>
        <result column="project_name" property="projectName"/>
        <result column="brand" property="brand"/>
        <result column="brand_name" property="brandName"/>
        <result column="product" property="product"/>
        <result column="product_name" property="productName"/>
        <result column="city" property="city"/>
        <result column="city_ad_code" property="cityAdCode"/>
        <result column="is_flip_card" property="isFlipCard"/>
        <result column="operate_area" property="operateArea"/>
        <result column="effective_name" property="effectiveName"/>
        <result column="legal_agreement_content" property="legalAgreementContent"/>
        <result column="construct_special_remark" property="constructSpecialRemark"/>
        <result column="rooms_number" property="roomsNumber"/>
        <result column="red_line_description" property="redLineDescription"/>
        <result column="project_version" property="projectVersion"/>
        <result column="construct_partition" property="constructPartition"/>
        <result column="construct_partition_desc" property="constructPartitionDesc"/>
        <result column="project_nature" property="projectNature"/>
        <result column="effective_date" property="effectiveDate"/>

        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>


        <result column="owner_project_manager" property="ownerProjectManager"/>
        <result column="red_line_image" property="redLineImage"/>
        <result column="memorandum" property="memorandum"/>
        <result column="concession" property="concession"/>
        <result column="notice_preparation" property="noticePreparation"/>
        <result column="decision_report" property="decisionReport"/>
        <result column="renovation_list" property="renovationList"/>
        <result column="meeting_state" property="meetingState"/>
        <result column="meeting_state_desc" property="meetingStateDesc"/>

        <result column="construction_area_leader" property="constructionAreaLeader"/>
        <result column="security_project" property="securityProject"/>
        <result column="develope_manager_id" property="developeManagerId"/>
        <result column="number_floor_levels" property="numberFloorLevels"/>
        <result column="project_start_time" property="projectStartTime"/>


        <result column="project_type" property="projectType"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        project_expansion_id,project_id, hotel_id, project_name, brand, brand_name, product, product_name,
        city, city_ad_code, is_flip_card, operate_area, effective_name, legal_agreement_content,
        construct_special_remark, rooms_number, red_line_description, project_version, construct_partition,
        construct_partition_desc, project_nature, effective_date, create_time, create_by, update_time,
        update_by,owner_project_manager, red_line_image, memorandum, concession, notice_preparation, decision_report,
        renovation_list, meeting_state,
        meeting_state_desc,construction_area_leader,security_project,develope_manager_id,number_floor_levels,project_start_time,project_type
    </sql>


    <select id="selectCountByProjectId" resultType="java.lang.Long">
        SELECT count(*)
        FROM t_project_info_expansion t_project_info_expansion
        WHERE (t_project_info_expansion.hotel_id = #{hotelId} or t_project_info_expansion.project_id = #{projectId})
    </select>


    <select id="getProjectIdByHotelId" resultType="java.lang.Long">
        SELECT t_project_info.project_id
        FROM t_project_info_expansion t_project_info_expansion
                 LEFT JOIN t_project_info t_project_info
                           on t_project_info.project_hlm_id = t_project_info_expansion.project_id
        WHERE t_project_info_expansion.hotel_id = #{hotelId}
    </select>


    <select id="syncProjectExpansion1" resultType="com.bassims.modules.atour.domain.ProjectInfoExpansion">
        SELECT
        t_project_info_expansion.*
        FROM
        t_project_info_expansion t_project_info_expansion
        WHERE 1=1
        <if test="null != ownerPmList and ownerPmList.size > 0 ">
            AND t_project_info_expansion.project_id IN
            <foreach collection="ownerPmList" item="ownerPmList1" open="(" separator="," close=")">
                #{ownerPmList1.projectId}
            </foreach>
        </if>
    </select>

    <select id="queryConstructionDockingStarted"
            resultType="com.bassims.modules.atour.service.dto.ProjectInfoExpansionDto">
        select t_project_info_expansion.*,
                   t_hlm_project_info_owner.username AS ownerUsername,
               t_hlm_project_info_owner.mobile AS ownerMobile,
               t_hlm_project_info_owner.email AS ownerEmail
        from t_project_info_expansion t_project_info_expansion
                 left join t_hlm_project_info_owner t_hlm_project_info_owner
                           on t_hlm_project_info_owner.project_id = t_project_info_expansion.project_id
        WHERE t_project_info_expansion.project_id = #{projectHlmId}
        limit 1
    </select>

    <select id="queryHotelId"
            resultType="com.bassims.modules.atour.service.dto.ProjectInfoExpansionDto">
        select t_project_info_expansion.*
        from t_project_info_expansion t_project_info_expansion
        WHERE t_project_info_expansion.hotel_id = #{hotelId}
    </select>


</mapper>
