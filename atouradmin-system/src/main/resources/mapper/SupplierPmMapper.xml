<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.SupplierPmRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.SupplierPm">
        <id column="id" property="id"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="pm_name" property="pmName"/>
        <result column="pm_grade" property="pmGrade"/>
        <result column="join_date" property="joinDate"/>
        <result column="leave_date" property="leaveDate"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="remark" property="remark"/>
        <result column="CREATE_USER" property="createUser"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_USER" property="updateUser"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="IS_DELETE" property="isDelete"/>
        <result column="project_scope" property="projectScope"/>
        <result column="plan_amount" property="planAmount"/>
        <result column="assign_amount" property="assignAmount"/>
        <result column="available_amount" property="availableAmount"/>
        <result column="finish_amount" property="finishAmount"/>
        <result column="doing_amount" property="doingAmount"/>
        <result column="completion_date" property="completionDate"/>
        <result column="current_quality_score" property="currentQualityScore"/>
        <result column="avg_quality_score" property="avgQualityScore"/>
        <result column="evaluation_score" property="evaluationScore"/>
        <result column="evaluation_start_time" property="evaluationStartTime"/>
        <result column="evaluation_end_time" property="evaluationEndTime"/>
        <result column="is_used" property="isUsed"/>
        <result column="is_active" property="isActive"/>
        <result column="fisyear" property="fisyear"/>
        <result column="total_assign_amount" property="totalAssignAmount"/>
        <result column="total_doing_amount" property="totalDoingAmount"/>
        <result column="total_finish_amount" property="totalFinishAmount"/>
        <result column="pargana_id" property="parganaId"/>
        <result column="pm_status" property="pmStatus"/>
        <result column="supplier_personnel_role" property="supplierPersonnelRole"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,supplier_id,pm_name,pm_grade,join_date,leave_date,phone,email,remark,CREATE_USER,CREATE_TIME,UPDATE_USER,UPDATE_TIME,IS_DELETE,project_scope,plan_amount,assign_amount,available_amount,finish_amount,doing_amount,completion_date,current_quality_score,avg_quality_score,evaluation_score,evaluation_start_time,evaluation_end_time,is_used,is_active,fisyear,total_assign_amount,total_doing_amount,total_finish_amount,pargana_id,pm_status,supplier_personnel_role
    </sql>

    <select id="getSupplierPmList" resultType="com.bassims.modules.atour.service.dto.SupplierPmDto">
        select * from t_supplier_pm
        where IS_DELETE = 0 and pm_status = 'cooperation' and status = 'approve_pass'
            <if test="null != supplierId and '' != supplierId">
                and supplier_id =#{supplierId}
            </if>
            <if test="null != productCode and '' != productCode ">
                and FIND_IN_SET((select `value` from s_sys_dict_detail where dict_id	=(select dict_id from s_sys_dict where `name` = 'execution_standards' limit 1) and `value` = #{productCode} limit 1),fisyear)
            </if>
            <if test="null != supplierPersonnelRole and '' != supplierPersonnelRole">
                and supplier_personnel_role =#{supplierPersonnelRole}
            </if>
    </select>



    <select id="getBylist" resultType="com.bassims.modules.atour.domain.SupplierPm">
        select t_supplier_pm.*,t_supplier_info.sup_name_cn AS supNameCn
        <if test="null != status and 'pending_approval' == status and null == supplierId">, a.c</if>
        from t_supplier_pm t_supplier_pm
        left join t_supplier_info t_supplier_info on t_supplier_info.id=t_supplier_pm.supplier_id
        <if test="null != status and 'pending_approval' == status and null == supplierId">
            LEFT JOIN (SELECT
            count(tpni.project_id) AS c,
            t_supplier_pm.id
            FROM
            t_project_node_info AS tpni
            LEFT JOIN t_supplier_pm ON t_supplier_pm.id = tpni.project_id

            GROUP BY
            t_supplier_pm.id
            ) AS a ON a.id = t_supplier_pm.id
        </if>
        where t_supplier_pm.IS_DELETE = 0
        <if test="null != supNameCn and '' != supNameCn">
            and t_supplier_info.sup_name_cn like concat('%',#{supNameCn},'%')
        </if>
        <if test="null != phone and '' != phone">
            and t_supplier_pm.phone like concat('%',#{phone},'%')
        </if>
        <if test="null != supplierId and '' != supplierId">
            and t_supplier_pm.supplier_id =#{supplierId}
        </if>
        <if test="null != fisyear and '' != fisyear ">
            and FIND_IN_SET((select `value` from s_sys_dict_detail where dict_id	=(select dict_id from s_sys_dict where `name` = 'execution_standards' limit 1) and `value` = #{fisyear} limit 1),fisyear)
        </if>
        <if test="null != supplierPersonnelRole and '' != supplierPersonnelRole">
            and t_supplier_pm.supplier_personnel_role =#{supplierPersonnelRole}
        </if>

        <if test="null != pmName and '' != pmName">
            and t_supplier_pm.pm_name like concat('%',#{pmName},'%')
        </if>

        <if test="null != finishAmount and '' != finishAmount">
            and t_supplier_pm.finish_amount =#{finishAmount}
        </if>

        <if test="null != status and '' != status">
            and t_supplier_pm.status =#{status}
        </if>
        <if test="null != status and 'pending_approval' == status and null == supplierId ">
            and c > 0
        </if>
    </select>
    <select id="getUserListBySupplierId" resultType="com.bassims.modules.system.domain.User">
        SELECT
            *
        FROM
            s_user_info
        WHERE
            user_id IN (
                SELECT
                    id
                FROM
                    t_supplier_pm
                WHERE
                    supplier_id = #{supplierId}
            )
    </select>
</mapper>
