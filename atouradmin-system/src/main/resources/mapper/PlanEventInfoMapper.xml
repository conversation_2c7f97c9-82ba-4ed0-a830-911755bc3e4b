<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.TPlanEventInfoRepository">

        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.TPlanEventInfo">
                    <id column="plan_event_id" property="planEventId" />
                    <result column="plan_event_name" property="planEventName" />
                    <result column="group_id" property="groupId" />
                    <result column="plan_event_belong" property="planEventBelong" />
                    <result column="plan_event_duration" property="planEventDuration" />
                    <result column="all_duration" property="allDuration" />
                    <result column="del_flag" property="delFlag" />
                    <result column="sort" property="sort" />
                    <result column="node_code" property="nodeCode" />
                    <result column="node_name" property="nodeName" />
    </resultMap>

    <select id="queryByGroupId" resultMap="BaseResultMap">
        select t.node_code,t.node_name,a.* from
        (select * from t_template_group where 1=1
        <if test="null != templateId ">
            and parent_id = #{templateId}
        </if>
        ) t left join t_planevent_nodecode_relation b on t.template_id=b.template_id left join (select * from t_plan_event_info where group_id = #{groupId} and del_flag='0') a on b.plan_event_id= a.plan_event_id
    </select>

    <select id="getOtherList" resultType="java.util.HashMap">
        select node_name as nodeName,node_code as nodeCode from t_template_group where  parent_id in(select template_id from t_template_collection where group_id=#{groupId}) and node_level = 1 and (template_code = 'engineering' or template_code = 'design' or template_code = 'public_area_design') and node_code not in (select a.node_code from t_planevent_nodecode_relation a left join  t_plan_event_info b on a.plan_event_id = b.plan_event_id where b.group_id=#{groupId}) and is_delete = '0'
    </select>


<!--        select DISTINCT t.template_code as templateCode,c.group_id as groupId,CONCAT(t.node_name,'(',dic.`label`,')') as nodeName,-->
<!--                        t.template_id as templateId  from t_template_collection c-->
<!--                            left join (select t.*, s.store_type from t_template_group t LEFT JOIN t_store_template_relation s on t.template_id = s.template_id-->
<!--                            where  t.node_level=0 and t.is_planned_event=1 ) t on c.template_id =t.template_id-->
<!--                            left join (select * from s_sys_dict_detail-->
<!--                            where dict_id = (select dict_id from s_sys_dict where `name`='brand')) dic on t.store_type = dic.`value`-->
<!--        where t.template_id is not null and t.store_type is not null-->
    <select id="getGroupList" resultType="java.util.HashMap">
        SELECT
            t_template_group.template_id AS templateId,
            t_template_collection.template_code AS templateCode,
            t_template_collection.group_id AS groupId,
            CONCAT( t_template_group.node_name, '(', GROUP_CONCAT( DISTINCT t_conditions_relation1.condition_type_group_name ), '-', GROUP_CONCAT( DISTINCT t_conditions_relation.condition_type_group_name ), ')' ) AS nodeName
        FROM
            t_template_group t_template_group
                LEFT JOIN t_template_collection t_template_collection ON t_template_group.template_id = t_template_collection.template_id
                LEFT JOIN t_template_collection_relation t_template_collection_relation ON t_template_collection.group_id = t_template_collection_relation.group_id
                LEFT JOIN ( SELECT * FROM t_conditions_relation WHERE condition_type = 'brand' ORDER BY condition_code ASC ) t_conditions_relation ON FIND_IN_SET( BINARY t_conditions_relation.condition_code, t_template_collection_relation.condition_code ) > 0
                LEFT JOIN ( SELECT * FROM t_conditions_relation WHERE condition_type = 'project_type' ORDER BY condition_code ASC ) t_conditions_relation1 ON FIND_IN_SET( BINARY t_conditions_relation1.condition_code, t_template_collection_relation.condition_code ) > 0
        WHERE
            t_template_group.parent_id IS NULL
          AND t_template_group.is_planned_event = 1
        GROUP BY
            t_template_group.template_id,
            t_template_collection.group_id,
            t_template_collection.template_code,
            t_template_group.node_name
        ORDER BY
            t_template_collection.group_id
    </select>
    <select id="getPlanEventByRelation" resultType="com.bassims.modules.atour.domain.TPlanEventInfo">
        SELECT
            *
        FROM
            `t_plan_event_info` pei
                LEFT JOIN t_planevent_nodecode_relation pnr ON pei.plan_event_id = pnr.plan_event_id
    </select>
    <select id="getTemplateId" resultType="java.lang.Long">
        select template_id from t_template_group where node_code =#{nodeCode} and parent_id in (select template_id from t_template_collection where group_id = #{groupId})
    </select>
</mapper>
