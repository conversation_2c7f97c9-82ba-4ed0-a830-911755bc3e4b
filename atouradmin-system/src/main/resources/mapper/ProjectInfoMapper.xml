<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ProjectInfoRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ProjectInfo">
        <id column="project_id" property="projectId"/>
        <result column="brand_id" property="brandId"/>
        <result column="template_id" property="templateId"/>
        <result column="store_id" property="storeId"/>
        <result column="project_no" property="projectNo"/>
        <result column="project_version" property="projectVersion"/>
        <result column="project_name" property="projectName"/>
        <result column="store_name" property="storeName"/>
        <result column="store_no" property="storeNo"/>
        <result column="business_nature" property="businessNature"/>
        <result column="store_type" property="storeType"/>
        <result column="design_image" property="designImage"/>
        <result column="region" property="region"/>
        <result column="city_company" property="cityCompany"/>
        <result column="province" property="province"/>
        <result column="province_name" property="provinceName"/>
        <result column="city" property="city"/>
        <result column="city_name" property="cityName"/>
        <result column="county" property="county"/>
        <result column="county_name" property="countyName"/>
        <result column="project_address" property="projectAddress"/>
        <result column="trade_id" property="tradeId"/>
        <result column="establish_time" property="establishTime"/>
        <result column="region_net_manager" property="regionNetManager"/>
        <result column="construction_manager" property="constructionManager"/>
        <result column="engineer_director" property="engineerDirector"/>
        <result column="designer" property="designer"/>
        <result column="general_network" property="generalNetwork"/>
        <result column="regiona_finance_manager" property="regionaFinanceManager"/>
        <result column="project_plan_start" property="projectPlanStart"/>
        <result column="project_plan_end" property="projectPlanEnd"/>
        <result column="project_actual_end" property="projectActualEnd"/>
        <result column="plan_open_date" property="planOpenDate"/>
        <result column="plan_approach_date" property="planApproachDate"/>
        <result column="project_create_date" property="projectCreateDate"/>
        <result column="actual_open_date" property="actualOpenDate"/>
        <result column="is_open" property="isOpen"/>
        <result column="is_create" property="isCreate"/>
        <result column="total_area" property="totalArea"/>
        <result column="used_area" property="usedArea"/>
        <result column="decorate_area" property="decorateArea"/>
        <result column="project_type" property="projectType"/>
        <result column="project_status" property="projectStatus"/>
        <result column="task_phase" property="taskPhase"/>
        <result column="is_overdue" property="isOverdue"/>
        <result column="account_phase" property="accountPhase"/>
        <result column="account_overdue" property="accountOverdue"/>
        <result column="remark" property="remark"/>
        <result column="is_active" property="isActive"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="is_sheet" property="isSheet"/>
        <result column="sheet_id" property="sheetId"/>
        <result column="decorate_grade" property="decorateGrade"/>
        <result column="floor" property="floor"/>
        <result column="design_position" property="designPosition"/>
        <result column="final_settlement_expenses" property="finalSettlementExpenses"/>
        <result column="flag" property="flag"/>
        <result column="note1" property="note1"/>
        <result column="note2" property="note2"/>
        <result column="note3" property="note3"/>
        <result column="note4" property="note4"/>
        <result column="note5" property="note5"/>
        <result column="product_code" property="productCode"/>
        <result column="brand_code" property="brandCode"/>
        <result column="node_table_name" property="nodeTableName"/>
        <result column="operations_theater_leader" property="operationsTheaterLeader"/>
        <result column="opening_manager" property="openingManager"/>
        <result column="operations_manager" property="operationsManager"/>
        <result column="franchise_manager" property="franchiseManager"/>
        <result column="development_theater_leader" property="developmentTheaterLeader"/>
        <result column="development_zone_leader" property="developmentZoneLeader"/>
        <result column="development_manager" property="developmentManager"/>
        <result column="construction_area_leader" property="constructionAreaLeader"/>
        <result column="project_manager" property="projectManager"/>
        <result column="designer" property="designer"/>
        <result column="mechanical_and_electrical_engineer" property="mechanicalAndElectricalEngineer"/>
        <result column="weak_current_engineer" property="weakCurrentEngineer"/>
        <result column="design_co_management_personnel" property="designCoManagementPersonnel"/>
        <result column="weak_current_acceptance_personnel" property="weakCurrentAcceptancePersonnel"/>
        <result column="mechanical_and_electrical_acceptance_personnel"
                property="mechanicalAndElectricalAcceptancePersonnel"/>
        <result column="flight_quality_inspection_personnel" property="flightQualityInspectionPersonnel"/>
        <result column="completion_acceptance_personnel" property="completionAcceptancePersonnel"/>
        <result column="delivery_center_shared_support" property="deliveryCenterSharedSupport"/>
        <result column="owner" property="owner"/>
        <result column="owner_project_manager" property="ownerProjectManager"/>
        <result column="design_unit" property="designUnit"/>
        <result column="procurement_marketing" property="procurementMarketing"/>
        <result column="delivery_center_engineering_leader" property="deliveryCenterEngineeringLeader"/>
        <result column="delivery_center_technical_leader" property="deliveryCenterTechnicalLeader"/>
        <result column="delivery_center_department_head" property="deliveryCenterDepartmentHead"/>
        <result column="decoration_designer" property="decorationDesigner"/>
        <result column="mechanical_and_electrical_designer" property="mechanicalAndElectricalDesigner"/>
        <result column="soft_decoration_design" property="softDecorationDesign"/>
        <result column="allocation_status" property="allocationStatus"/>
        <result column="effective_date" property="effectiveDate"/>
        <result column="planned_start_date" property="plannedStartDate"/>
        <result column="adjusting_the_start_date" property="adjustingTheStartDate"/>
        <result column="actual_commencement_date" property="actualCommencementDate"/>
        <result column="planned_completion_date" property="plannedCompletionDate"/>
        <result column="adjust_completion_date" property="adjustCompletionDate"/>
        <result column="actual_completion_date" property="actualCompletionDate"/>
        <result column="adjusting_the_opening_date" property="adjustingTheOpeningDate"/>
        <result column="start_cycle" property="startCycle"/>
        <result column="construction_period" property="constructionPeriod"/>
        <result column="certification_cycle" property="certificationCycle"/>
        <result column="signing_status" property="signingStatus"/>
        <result column="construction_status" property="constructionStatus"/>
        <result column="opening_status" property="openingStatus"/>

        <result column="project_task_phase" property="projectTaskPhase"/>
        <result column="stair_plan_end_date" property="stairPlanEndDate"/>


        <result column="supply_chain_business_unit" property="supplyChainBusinessUnit"/>
        <result column="legal_manager" property="legalManager"/>
        <result column="shared_support_design_lead" property="sharedSupportDesignLead"/>

        <result column="head_accommodation_operations" property="headAccommodationOperations"/>
        <result column="head_operation_maintenance" property="headOperationMaintenance"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        project_id,brand_id,template_id,project_no,project_version,project_name,business_nature,store_name,store_no,store_type,design_image,region,city_company,province,province_name,city,city_name,county,county_name,project_address,trade_id,establish_time
        ,region_net_manager,construction_manager,engineer_director,designer,general_network,regiona_finance_manager,project_plan_start,project_plan_end,project_actual_end,plan_open_date,plan_approach_date,project_create_date
        ,actual_open_date,is_open,is_create,total_area,used_area,decorate_area,project_type,project_status
        ,task_phase,is_overdue,account_phase,account_overdue,remark,is_active,is_delete,create_time,update_time,create_by,update_by,is_enabled,is_sheet,
        sheet_id,decorate_grade,floor,design_position,final_settlement_expenses,flag,note1,note2,note3,note4,note5,project_hlm_id,product_code,project_task_phase
        ,brand_code
        ,node_table_name
        ,operations_theater_leader
        ,opening_manager
        ,operations_manager
        ,franchise_manager
        ,development_theater_leader
        ,development_zone_leader
        ,development_manager
        ,construction_area_leader
        ,project_manager
        ,designer
        ,mechanical_and_electrical_engineer
        ,weak_current_engineer
        ,design_co_management_personnel
        ,weak_current_acceptance_personnel
        ,mechanical_and_electrical_acceptance_personnel
        ,flight_quality_inspection_personnel
        ,completion_acceptance_personnel
        ,delivery_center_shared_support
        ,owner
        ,owner_project_manager
        ,design_unit
        ,procurement_marketing
        ,delivery_center_engineering_leader
        ,delivery_center_technical_leader
        ,delivery_center_department_head
        ,decoration_designer
        ,mechanical_and_electrical_designer
        ,soft_decoration_design
        ,allocation_status
        ,effective_date
        ,planned_start_date
        ,adjusting_the_start_date
        ,actual_commencement_date
        ,planned_completion_date
        ,adjust_completion_date
        ,actual_completion_date
        ,adjusting_the_opening_date
        ,start_cycle
        ,construction_period
        ,certification_cycle
        ,signing_status
        ,construction_status
        ,opening_status
        ,stair_plan_end_date
        ,supply_chain_business_unit
        ,legal_manager
        ,shared_support_design_lead
        ,head_accommodation_operations
        ,head_operation_maintenance
    </sql>

    <sql id="P_Column_List">
        <!--@mbg.generated-->
        p.project_id,p.brand_id,p.template_id,p.project_no,p.project_version,p.project_name,p.business_nature,p.store_type,p.store_no,p.design_image,p.region,p.city_company,p.province,p.province_name,p.city,p.city_name,p.county,p.county_name,p.project_address,p.trade_id,p.establish_time
        ,p.region_net_manager,p.construction_manager,p.engineer_director,p.designer,p.general_network,p.regiona_finance_manager,p.project_plan_start,p.project_plan_end,p.project_actual_end,p.plan_open_date,p.plan_approach_date,p.project_create_date
        ,p.actual_open_date,p.is_open,p.is_create,p.total_area,p.used_area,p.decorate_area,p.project_type,p.project_status
        ,p.task_phase,p.is_overdue,p.account_phase,p.account_overdue,p.remark,p.is_active,p.is_delete,p.create_time,p.update_time,p.create_by,p.update_by,p.is_enabled,p.is_sheet,p.sheet_id,p.decorate_grade,p.floor,p.design_position,p.final_settlement_expenses,p.flag,p.note1,p.note2,p.note3,p.note4,p.note5,p.project_hlm_id,product_code
        ,brand_code
        ,node_table_name
        ,operations_theater_leader
        ,opening_manager
        ,operations_manager
        ,franchise_manager
        ,development_theater_leader
        ,development_zone_leader
        ,development_manager
        ,construction_area_leader
        ,project_manager
        ,designer
        ,mechanical_and_electrical_engineer
        ,weak_current_engineer
        ,design_co_management_personnel
        ,weak_current_acceptance_personnel
        ,mechanical_and_electrical_acceptance_personnel
        ,flight_quality_inspection_personnel
        ,completion_acceptance_personnel
        ,delivery_center_shared_support
        ,owner
        ,owner_project_manager
        ,design_unit
        ,procurement_marketing
        ,delivery_center_engineering_leader
        ,delivery_center_technical_leader
        ,delivery_center_department_head
        ,decoration_designer
        ,mechanical_and_electrical_designer
        ,soft_decoration_design
        ,allocation_status
        ,effective_date
        ,planned_start_date
        ,adjusting_the_start_date
        ,actual_commencement_date
        ,planned_completion_date
        ,adjust_completion_date
        ,actual_completion_date
        ,adjusting_the_opening_date
        ,start_cycle
        ,construction_period
        ,certification_cycle
        ,signing_status
        ,construction_status
        ,opening_status
        ,supply_chain_business_unit
        ,legal_manager
        ,shared_support_design_lead
        ,head_accommodation_operations
        ,head_operation_maintenance
    </sql>
    <select id="getPermissionmode" resultType="com.bassims.modules.system.domain.Role">
        SELECT r.*
        FROM s_user_info u
                 LEFT JOIN s_sys_users_roles ur on u.user_id = ur.user_id
                 LEFT JOIN s_sys_role r ON ur.role_id = r.role_id
        WHERE u.user_id = #{userId}
    </select>
    <select id="getPrjectNameAndNodeNameRolCode" resultMap="BaseResultMap">
        select distinct a.project_name,b.node_name as store_name,b.role_code as province_name,project_no ,
                        b.project_group_id AS remark
        from t_project_info a
            left join t_project_group b on a.project_id= b.project_id
        where a.is_delete = '0'  and
        a.project_id = #{projectId} and b.node_code = #{nodeCode} and b.project_group_id = #{projectGroupId}
        group by b.project_id,b.node_code;
    </select>

    <select id="getProjectIdsByCity" resultType="java.lang.Long">
        SELECT
        DISTINCT t_project_info.project_id
        FROM
        t_project_info t_project_info
        left join (
            SELECT
                t_project_group1.project_id,
                t_project_group1.node_code,
                t_project_group1.node_name,
                CASE WHEN t_project_group1.node_code LIKE 'eng%' THEN 'engineering' ELSE 'design'  END stageNodeName
            FROM  t_project_group t_project_group1
            WHERE  t_project_group1.node_level = 1
                    AND t_project_group1.template_id IN (
                    SELECT
                    parent_id
                    FROM
                    t_project_group
                    WHERE  node_status IN ( 'task_uncommitted', 'task_submit' )
                    AND node_level = 2
                    AND ( node_code LIKE 'eng%' OR node_code LIKE 'des%' OR node_code LIKE 'pad%' )
                    AND project_id = t_project_group1.project_id
                    ORDER BY node_index
            )
        )  t_project_group on t_project_group.project_id = t_project_info.project_id
        WHERE
        t_project_info.city IN (
        SELECT DISTINCT
        c.area_code
        FROM
        s_user_info u
        LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id where u.user_id =#{userId})
        <if test="null != projectTaskPhase and  '' != projectTaskPhase ">
            AND t_project_group.node_code like concat(#{projectTaskPhase}, '%')
        </if>
        <if test="null != taskPhase and  '' != taskPhase ">
            AND t_project_group.stageNodeName = #{taskPhase}
        </if>

        <if test="null != delete and delete != '' ">
            AND t_project_info.is_delete=#{delete}
        </if>
        <if test="null != preparationDocking and preparationDocking != 1 ">
            AND t_project_info.node_table_name is not null
        </if>

        AND t_project_info.project_type!='design_adjust'


    </select>

    <select id="getAdjustProjectIdsByCity" resultType="java.lang.Long">
        SELECT
        project_id
        FROM
        t_project_info
        WHERE
        city IN (
        SELECT DISTINCT
        c.area_code
        FROM
        s_user_info u
        LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id where u.user_id =#{userId})
        <if test="null != delete and delete != '' ">
            AND is_delete=#{delete}
        </if>

        AND project_type='design_adjust'


    </select>

    <select id="getDistinctInfoByStoreNoAndName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_project_info where is_delete = '0'
        <if test="storeId !=null">
            and store_id =#{storeId}
        </if>
    </select>

    <select id="getProjectIdsByStakeholders" resultType="java.lang.Long">
        SELECT
        DISTINCT p.project_id
        FROM
        t_project_info p
        LEFT JOIN t_project_stakeholders s ON p.project_id=s.project_id
        left join (
        SELECT
            t_project_group1.project_id,
            t_project_group1.node_code,
            t_project_group1.node_name,
        CASE WHEN t_project_group1.node_code LIKE 'eng%' THEN 'engineering' ELSE 'design' END stageNodeName
        FROM  t_project_group t_project_group1
        WHERE  node_level = 1
            AND t_project_group1.template_id IN (
                SELECT parent_id  FROM t_project_group t_project_group
                WHERE  node_status IN ( 'task_uncommitted', 'task_submit' )
                AND node_level = 2
                AND ( node_code LIKE 'eng%' OR node_code LIKE 'des%' OR node_code LIKE 'pad%' )
                AND project_id = t_project_group1.project_id
                ORDER BY node_index
            )
        )  t_project_group on t_project_group.project_id = p.project_id
        WHERE
        s.user_id=#{userId} and (s.shakeholder_status='in_term' or s.shakeholder_status='temporarily_off_term')
        and s.order_id is null
        <if test="null != projectTaskPhase and  '' != projectTaskPhase ">
            AND t_project_group.node_code = #{projectTaskPhase}
        </if>
        <if test="null != taskPhase and  '' != taskPhase ">
            AND t_project_group.stageNodeName = #{taskPhase}
        </if>

        <if test="null != delete and delete != '' ">
            AND p.is_delete=#{delete}
        </if>
        <if test="null != preparationDocking and preparationDocking != 1 ">
            AND p.node_table_name is not null
        </if>
        AND p.project_type!='design_adjust'


    </select>



    <select id="getByStakeholders" resultType="com.bassims.modules.atour.service.dto.ProjectInfoDto">
        SELECT
            p.*,
            GROUP_CONCAT( DISTINCT s.role_name ) roleName
        FROM
            t_project_info p
                LEFT JOIN t_project_stakeholders s ON p.project_id = s.project_id
                LEFT JOIN t_project_task_info t ON p.project_id = t.project_id
        WHERE
            p.is_delete = 0
          AND t.task_status = 'task_unfin'
          AND p.city IN ( SELECT DISTINCT c.area_code FROM s_user_info u LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id WHERE u.user_id = #{userId} )
          AND s.user_id = #{leavingUser} AND s.shakeholder_status = 'in_term'
          AND p.project_type != 'design_adjust'
        GROUP BY   p.project_id;
    </select>

    <select id="getAdjustProjectIdsByStakeholders" resultType="java.lang.Long">
        SELECT
        DISTINCT p.project_id
        FROM
        t_project_info p
        LEFT JOIN t_project_stakeholders s ON p.project_id=s.project_id
        WHERE
        s.user_id=#{userId} and   (s.shakeholder_status='in_term' or s.shakeholder_status='temporarily_off_term')
        and s.order_id is null
        <if test="null != delete and delete != '' ">
            AND p.is_delete=#{delete}
        </if>

        AND p.project_type='design_adjust'

    </select>

    <select id="getProjectIdsByUser" resultType="long">
        select
        distinct p.project_id
        from t_project_info p
        left join t_project_task_info t on p.project_id = t.project_id
        where t.user_id = #{userId}
        and t.order_id is null
        and t.is_delete = 0
        <if test="delete != null and delete != ''">
            and p.is_delete = #{delete}
        </if>
        and t.task_status != 'task_fin'
    </select>

    <select id="getOutPersonAccessIds" parameterType="long" resultType="long">
        SELECT DISTINCT a.project_id
        FROM t_project_approve a
                 LEFT JOIN
             (SELECT de.approve_id,
                     de.approve_result,
                     r.approve_index
              FROM t_project_approve_detail de
                       LEFT JOIN (
                  SELECT approve_id,
                         approve_index
                  FROM t_project_approve_detail
                  WHERE approve_user = #{userId}
                    AND approve_result IN ('approve_pass', 'under_approve')) r ON de.approve_id = r.approve_id
              WHERE r.approve_index is not null
                and ((de.approve_index = r.approve_index - 1
                  AND de.approve_result IN ('approve_pass'))
-- 	or (de.approve_index = 1 and de.approve_result in ('approve_pass','under_approve'))
                  )
             ) d ON a.approve_id = d.approve_id
        WHERE a.approve_result IN ('approve_pass', 'under_approve')
          AND a.approve_id = d.approve_id
    </select>

    <select id="getJobByJobCode" resultType="com.bassims.modules.system.domain.Job">
        select j.*
        from s_sys_job j
        where job_code = #{jobCode};
    </select>

    <select id="getProjectTaskUserByNodeCode" resultType="com.bassims.modules.atour.domain.ProjectInfo">
        select distinct
        p.project_id,p.store_type,p.project_type,p.project_version,p.decorate_grade,CONCAT(s.short_name,'分区')
        province_name,p.project_name,p.task_phase,n.node_code,n.node_name,p.decorate_area,p.establish_time,p.plan_open_date,p.project_plan_start,
        p.project_plan_end,p.construction_manager,p.project_address
        from t_project_info p
        left join t_project_task_info t on p.project_id = t.project_id
        left join t_project_group n on t.node_id = n.project_group_id
        left join s_sys_area s on p.province = s.area_code
        where
        t.task_status!='task_fin'
        <if test="projectIds != null and projectIds.size > 0 ">
            and p.project_id in
            <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="taskType != null and taskType != ''">
            and t.task_type = #{taskType}
        </if>
        <if test="nodeCode != null and nodeCode != ''">
            and n.node_code = #{nodeCode}
        </if>
        and t.user_id = #{userId}
        and t.order_id is null
        and n.order_id is null
        and p.is_delete = 0
        and t.is_delete = 0
        and n.is_delete = 0
    </select>

    <select id="getProjectTaskCityByNodeCode" resultType="com.bassims.modules.atour.domain.ProjectInfo">
        select distinct
        p.project_id,p.store_type,p.project_type,p.project_version,p.decorate_grade,CONCAT(s.short_name,'分区')
        province_name,p.project_name,p.task_phase,n.node_code,n.node_name,p.decorate_area,p.establish_time,
        p.plan_open_date,p.project_plan_start,p.project_plan_end,p.construction_manager,p.project_address
        from t_project_info p
        left join t_project_task_info t on p.project_id = t.project_id
        left join t_project_group n on t.node_id = n.project_group_id
        left join s_sys_area s on p.province = s.area_code
        where
        t.task_status!='task_fin'
        <if test="projectIds != null and projectIds.size > 0">
            and p.project_id in
            <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="taskType != null and taskType != ''">
            and t.task_type = #{taskType}
        </if>
        <if test="nodeCode != null and nodeCode != ''">
            and n.node_code = #{nodeCode}
        </if>
        and t.order_id is null
        and n.order_id is null
        and p.is_delete = 0
        and t.is_delete = 0
        and n.is_delete = 0
        and p.city IN (
        SELECT DISTINCT
        c.area_code
        FROM
        s_user_info u
        LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id where u.user_id =#{userId})
    </select>

    <select id="getProjectTaskStaByNodeCode" resultType="com.bassims.modules.atour.domain.ProjectInfo">
        select distinct
        p.project_id,p.store_type,p.project_type,p.project_version,p.decorate_grade,CONCAT(s.short_name,'分区')
        province_name,p.project_name,p.task_phase,n.node_code,n.node_name,p.decorate_area,p.establish_time,
        p.plan_open_date,p.project_plan_start,p.project_plan_end,p.construction_manager,p.project_address
        from t_project_info p
        left join t_project_stakeholders s on p.project_id = s.project_id
        left join t_project_task_info t on s.project_id = t.project_id
        left join t_project_group n on t.node_id = n.project_group_id
        left join s_sys_area s on p.province = s.area_code
        where
        t.task_status!='task_fin'
        <if test="projectIds != null and projectIds.size > 0">
            and p.project_id in
            <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="taskType != null and taskType != ''">
            and t.task_type = #{taskType}
        </if>
        <if test="nodeCode != null and nodeCode != ''">
            and n.node_code = #{nodeCode}
        </if>
        and t.order_id is null
        and n.order_id is null
        and p.is_delete = 0
        and t.is_delete = 0
        and n.is_delete = 0
        and s.user_id=#{userId}
        and s.shakeholder_status='in_term'
        and s.order_id is null
        and s.is_delete=0
    </select>

    <select id="taskQueryByProjectId" resultType="com.bassims.modules.atour.domain.ProjectInfo">
        select p.project_id,
               p.project_type,
               p.project_version,
               p.decorate_grade,
               CONCAT(s.short_name, '分部') province_name,
               p.project_name,
               p.task_phase,
               n.node_code,
               n.node_name,
               p.decorate_area,
               p.establish_time,
               p.plan_open_date,
               p.project_plan_start,
               p.project_plan_end,
               p.construction_manager,
               p.project_address
        from t_project_info p
                 left join t_project_task_info t on p.project_id = t.project_id
                 left join t_project_group n on t.node_id = n.project_group_id
                 left join s_sys_area s on p.province = s.area_code
        where p.project_id = #{projectId}
    </select>

    <select id="getTotalProject" resultType="java.lang.Integer">
        SELECT count(project_id) project_total
        FROM t_project_info
        WHERE city IN (
            SELECT DISTINCT c.area_code
            FROM s_user_info u
                     LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id
            where u.user_id = #{userId})
          AND is_delete = 0
    </select>
    <select id="getBuildProject" resultType="java.lang.Integer">
        SELECT count(p.project_id) project_total
        FROM t_project_info p
                 LEFT JOIN t_project_node_info n
                           on p.project_id = n.project_id and (n.node_code = 'con-0067' or n.node_code = 'con-00607')
        WHERE p.city IN (
            SELECT DISTINCT c.area_code
            FROM s_user_info u
                     LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id
            where u.user_id = #{userId})
          AND p.is_delete = 0
          and (n.node_status = 'task_unfin' or n.node_status is null);

    </select>
    <select id="getThisYearProject" resultType="java.lang.Integer">
        SELECT count(project_id) project_total
        FROM t_project_info
        WHERE city IN (
            SELECT DISTINCT c.area_code
            FROM s_user_info u
                     LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id
            where u.user_id = #{userId})
                  AND is_delete = 0 AND YEAR (create_time)= YEAR (NOW());
    </select>
    <select id="getFinishProject" resultType="java.lang.Integer">
        SELECT count(p.project_id) project_total
        FROM t_project_info p
                 LEFT JOIN t_project_node_info n
                           on p.project_id = n.project_id and (n.node_code = 'con-0067' or n.node_code = 'con-00607')
        WHERE p.city IN (
            SELECT DISTINCT c.area_code
            FROM s_user_info u
                     LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id
            where u.user_id = #{userId})
          AND p.is_delete = 0
          and n.node_status = 'task_fin';
    </select>
    <select id="getOverDueProject" resultType="java.lang.Integer">
        SELECT count(DISTINCT p.project_id) project_total
        FROM t_project_info p
                 LEFT JOIN t_project_node_info n on p.project_id = n.project_id
                 LEFT JOIN t_project_node_info ni
                           on p.project_id = ni.project_id and (ni.node_code = 'con-0067' or ni.node_code = 'con-00607')
        WHERE p.city IN (
            SELECT DISTINCT c.area_code
            FROM s_user_info u
                     LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id
            where u.user_id = #{userId})
          AND p.is_delete = 0
          and n.node_level = 2
          AND n.delay_day > 0
          AND (ni.node_status = 'task_unfin' or ni.node_status is null)
    </select>

    <select id="getOverDuePercentForCity" resultType="java.lang.Long">
        select DISTINCT project_id from t_project_info
        <where>is_overdue=true
            and city IN (
            SELECT DISTINCT
            c.area_code
            FROM
            s_user_info u
            LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id where u.user_id =#{userId})
            AND is_delete=0
            <if test="null != querydate and querydate != '' ">
                AND create_time &lt;= #{querydate}
            </if>
            <if test="null == querydate or querydate == '' ">
                AND YEAR(create_time)=YEAR(NOW())
            </if>
        </where>
    </select>
    <select id="getPhasePercentForCity" resultType="com.bassims.modules.atour.domain.ProjectInfo">
        select
        <include refid="Base_Column_List"/>
        from t_project_info
        <where>(is_open =false or is_open is null)
            and city IN (
            SELECT DISTINCT
            c.area_code
            FROM
            s_user_info u
            LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id where u.user_id =#{userId})
            AND is_delete=0
            <if test="null != querydate and querydate != '' ">
                AND create_time &lt;= #{querydate}
            </if>
            <if test="null == querydate or querydate == '' ">
                AND YEAR(create_time)=YEAR(NOW())
            </if>
        </where>
    </select>

    <select id="getPercentForCity" resultType="com.bassims.modules.atour.domain.ProjectInfo">
        select
        <include refid="Base_Column_List"/>
        from t_project_info
        <where>(is_open =false or is_open is null)
            and city IN (
            SELECT DISTINCT
            c.area_code
            FROM
            s_user_info u
            LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id where u.user_id =#{userId})
            AND is_delete=0
        </where>
    </select>

    <select id="getCityLongsByCity" resultType="com.bassims.modules.atour.service.dto.ProjectInfoDto">
        select
        <include refid="P_Column_List"/>
        from t_project_info p
        where p.city IN (
        SELECT DISTINCT
        c.area_code
        FROM
        s_user_info u
        LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id where u.user_id =#{userId})
        AND p.is_delete=0

    </select>


    <select id="getRoleByRoleCode" resultType="com.bassims.modules.system.domain.Role">
        select r.*, r.role_id id
        from s_sys_role r
        where role_code = #{roleCode};
    </select>
    <select id="getRoleByRoleCodeList" resultType="com.bassims.modules.system.domain.Role">
        select r.*, r.role_id id
        from s_sys_role r
        where role_code in
        <foreach collection="roleCodes" item="roleCode" open="(" separator="," close=")">
            #{roleCode}
        </foreach>
    </select>


    <select id="getTotalProjectForSta" resultType="java.lang.Integer">

    </select>
    <select id="getBuildProjectForSta" resultType="java.lang.Integer">
        SELECT count(project_id) project_total
        FROM t_project_info
        WHERE city IN (
            SELECT DISTINCT c.area_code
            FROM s_user_info u
                     LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id
            where u.user_id = #{userId})
          AND is_delete = 0

    </select>
    <select id="getThisYearProjectForSta" resultType="java.lang.Integer"></select>
    <select id="getFinishProjectForSta" resultType="java.lang.Integer"></select>
    <select id="getOverDueProjectForSta" resultType="java.lang.Integer"></select>
    <select id="getAllProjectByCity" resultType="com.bassims.modules.atour.service.dto.ProjectInfoDto">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        t_project_info
        WHERE
        city IN (
        SELECT DISTINCT
        c.area_code
        FROM
        s_user_info u
        LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id where u.user_id =#{userId}) AND is_delete=0
    </select>
    <select id="getAllProjectBySta" resultType="com.bassims.modules.atour.service.dto.ProjectInfoDto">


        SELECT
        <include refid="Base_Column_List"/>
        FROM
        t_project_info
        WHERE
        project_id IN (
        SELECT DISTINCT
        project_id
        FROM
        t_project_stakeholders
        WHERE
        user_id = #{userId}
        AND shakeholder_status = 'in_term') AND is_delete=0


    </select>
    <select id="getOverDuePercentForSta" resultType="java.lang.Long">
        select DISTINCT p.project_id from t_project_info p
        <where>p.is_overdue=true
            and p.project_id IN (
            SELECT DISTINCT
            project_id
            FROM
            t_project_stakeholders
            WHERE
            user_id = #{userId}
            AND shakeholder_status = 'in_term')
            AND p.is_delete=0
            <if test="null != querydate and querydate != '' ">
                AND p.project_create_date &lt;= #{querydate}
            </if>
            <if test="null == querydate or querydate == '' ">
                AND YEAR(p.project_create_date)=YEAR(NOW())
            </if>
        </where>
    </select>
    <select id="getPhasePercentForSta" resultType="com.bassims.modules.atour.domain.ProjectInfo">
        select
        <include refid="P_Column_List"/>
        from t_project_info p
        <where>(p.is_open =false or p.is_open is null)
            and p.project_id IN (
            SELECT DISTINCT
            project_id
            FROM
            t_project_stakeholders
            WHERE
            user_id = #{userId}
            AND shakeholder_status = 'in_term')
            AND p.is_delete=0
            <if test="null != querydate and querydate != '' ">
                AND p.project_create_date &lt;= #{querydate}
            </if>
            <if test="null == querydate or querydate == '' ">
                AND YEAR(p.project_create_date)=YEAR(NOW())
            </if>
        </where>
    </select>

    <select id="getPercentForSta" resultType="com.bassims.modules.atour.domain.ProjectInfo">
        select
        <include refid="P_Column_List"/>
        from t_project_info p
        <where>(p.is_open =false or p.is_open is null)
            and p.project_id IN (
            SELECT DISTINCT
            project_id
            FROM
            t_project_stakeholders
            WHERE
            user_id = #{userId}
            AND shakeholder_status = 'in_term')
            AND p.is_delete=0
        </where>
    </select>

    <select id="getCityLongsBySta" resultType="com.bassims.modules.atour.service.dto.ProjectInfoDto">
        select
        <include refid="P_Column_List"/>
        from t_project_info p
        where p.project_id IN (
        SELECT DISTINCT
        project_id
        FROM
        t_project_stakeholders
        WHERE
        user_id = #{userId}
        AND shakeholder_status = 'in_term')
        AND p.is_delete=0
    </select>


    <select id="getAccountOverDuePercentForCity" resultType="java.lang.Long">
        select DISTINCT project_id from t_project_info
        <where>(task_phase='settlement_phase' or project_status='project_finish' or account_phase !='--') and
            account_overdue=true
            and city IN (
            SELECT DISTINCT
            c.area_code
            FROM
            s_user_info u
            LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id where u.user_id =#{userId})
            AND is_delete=0
            <if test="null != querydate and querydate != '' ">
                AND create_time &lt;= #{querydate}
            </if>
            <if test="null == querydate or querydate == '' ">
                AND YEAR(create_time)=YEAR(NOW())
            </if>
        </where>
    </select>
    <select id="getAccountOverDuePercentForSta" resultType="java.lang.Long">
        select DISTINCT p.project_id from t_project_info p
        <where>(task_phase='settlement_phase' or project_status='project_finish' or account_phase !='--') and
            account_overdue=true
            and p.project_id IN (
            SELECT DISTINCT
            project_id
            FROM
            t_project_stakeholders
            WHERE
            user_id = #{userId}
            AND shakeholder_status = 'in_term')
            AND p.is_delete=0
            <if test="null != querydate and querydate != '' ">
                AND p.project_create_date &lt;= #{querydate}
            </if>
            <if test="null == querydate or querydate == '' ">
                AND YEAR(p.project_create_date)=YEAR(NOW())
            </if>
        </where>
    </select>
    <select id="getAccountPhasePercentForCity" resultType="com.bassims.modules.atour.domain.ProjectInfo">
        select
        <include refid="Base_Column_List"/>
        from t_project_info
        <where>(task_phase='settlement_phase' or project_status='project_finish' or account_phase !='--') and
            project_status !='project_account'
            and city IN (
            SELECT DISTINCT
            c.area_code
            FROM
            s_user_info u
            LEFT JOIN s_sys_users_city c ON u.user_id = c.user_id where u.user_id =#{userId})
            AND is_delete=0
            <if test="null != querydate and querydate != '' ">
                AND create_time &lt;= #{querydate}
            </if>
            <if test="null == querydate or querydate == '' ">
                AND YEAR(create_time)=YEAR(NOW())
            </if>
        </where>
    </select>
    <select id="getAccountPhasePercentForSta" resultType="com.bassims.modules.atour.domain.ProjectInfo">
        select
        <include refid="P_Column_List"/>
        from t_project_info p
        <where>(task_phase='settlement_phase' or project_status='project_finish' or account_phase !='--') and
            project_status !='project_account'
            and p.project_id IN (
            SELECT DISTINCT
            project_id
            FROM
            t_project_stakeholders
            WHERE
            user_id = #{userId}
            AND shakeholder_status = 'in_term')
            AND p.is_delete=0
            <if test="null != querydate and querydate != '' ">
                AND p.project_create_date &lt;= #{querydate}
            </if>
            <if test="null == querydate or querydate == '' ">
                AND YEAR(p.project_create_date)=YEAR(NOW())
            </if>
        </where>
    </select>

    <select id="getUserIdByRegion" resultType="Long">
        SELECT user_id
        FROM s_sys_area_stakeholders
        WHERE region = #{region}
          AND role_code = #{roleCode}
    </select>

    <select id="getOldProjectData" resultMap="BaseResultMap">
        --         select p.project_id `project_id`,p.brand_id `brand_id`,m.store_master_id `store_id`,null `template_id`,p.project_no `project_no`,p.project_version `project_version`,p.project_name `project_name`,p.business_nature `business_nature`,p.store_name `store_name`,p.store_no `store_no`,p.store_type `store_type`,p.design_image `design_image`,p.region `region`,p.province `province`,p.province_name `province_name`,p.city `city`,p.city_name `city_name`,p.county `county`,p.county_name `county_name`,p.project_address `project_address`,p.trade_id `trade_id`,p.city_company `city_company`,p.project_plan_start `project_plan_start`,p.project_plan_end `project_plan_end`,p.project_actual_end `project_actual_end`,p.establish_time `establish_time`,p.region_net_manager `region_net_manager`,p.construction_manager `construction_manager`,p.engineer_director `engineer_director`,p.designer `designer`,p.general_network `general_network`,p.regiona_finance_manager `regiona_finance_manager`,p.plan_open_date `plan_open_date`,p.plan_approach_date `plan_approach_date`,p.project_create_date `project_create_date`,p.actual_open_date `actual_open_date`,p.is_open `is_open`,p.is_create `is_create`,p.task_phase `task_phase`,p.account_phase `account_phase`,p.total_area `total_area`,p.used_area `used_area`,p.decorate_area `decorate_area`,p.project_type `project_type`,p.project_status `project_status`,p.is_overdue `is_overdue`,p.account_overdue `account_overdue`,p.remark `remark`,p.is_active `is_active`,p.create_time `create_time`,p.create_by `create_by`,p.update_time `update_time`,p.update_by `update_by`,p.is_enabled `is_enabled`,p.is_delete `is_delete`,p.is_sheet `is_sheet`,p.sheet_id `sheet_id`,p.decorate_grade `decorate_grade`,p.floor `floor`,p.design_position `design_position`,p.final_Settlement_Expenses `final_Settlement_Expenses`,concat('oldData',CURDATE()) `flag`,null `note1`,null `note2`,null `note3`,null `note4`,null `note5`
--         from t_temp_confirm_project_info_param p
--         left join t_store_master_info m on p.store_no = m.store_no
--         where p.is_confirm_sta = b'0'
--         and p.construction_manager is not null
--         and p.region_net_manager is not null
--         and p.engineer_director is not null
        select p.project_id                 `project_id`,
               p.brand_id                   `brand_id`,
               m.store_master_id            `store_id`,
               null                         `template_id`,
               p.project_no                 `project_no`,
               p.project_version            `project_version`,
               p.project_name               `project_name`,
               p.business_nature            `business_nature`,
               p.store_name                 `store_name`,
               p.store_no                   `store_no`,
               p.store_type                 `store_type`,
               p.design_image               `design_image`,
               p.region                     `region`,
               p.province                   `province`,
               p.province_name              `province_name`,
               p.city                       `city`,
               p.city_name                  `city_name`,
               p.county                     `county`,
               p.county_name                `county_name`,
               p.project_address            `project_address`,
               p.trade_id                   `trade_id`,
               p.city_company               `city_company`,
               p.project_plan_start         `project_plan_start`,
               p.project_plan_end           `project_plan_end`,
               p.project_actual_end         `project_actual_end`,
               p.establish_time             `establish_time`,
               p.region_net_manager         `region_net_manager`,
               p.construction_manager       `construction_manager`,
               p.engineer_director          `engineer_director`,
               p.designer                   `designer`,
               p.general_network            `general_network`,
               p.regiona_finance_manager    `regiona_finance_manager`,
               p.plan_open_date             `plan_open_date`,
               p.plan_approach_date         `plan_approach_date`,
               p.project_create_date        `project_create_date`,
               p.actual_open_date           `actual_open_date`,
               p.is_open                    `is_open`,
               p.is_create                  `is_create`,
               p.task_phase                 `task_phase`,
               p.account_phase              `account_phase`,
               p.total_area                 `total_area`,
               p.used_area                  `used_area`,
               p.decorate_area              `decorate_area`,
               p.project_type               `project_type`,
               p.project_status             `project_status`,
               p.is_overdue                 `is_overdue`,
               p.account_overdue            `account_overdue`,
               p.remark                     `remark`,
               p.is_active                  `is_active`,
               p.create_time                `create_time`,
               p.create_by                  `create_by`,
               p.update_time                `update_time`,
               p.update_by                  `update_by`,
               p.is_enabled                 `is_enabled`,
               p.is_delete                  `is_delete`,
               p.is_sheet                   `is_sheet`,
               p.sheet_id                   `sheet_id`,
               p.decorate_grade             `decorate_grade`,
               p.floor                      `floor`,
               p.design_position            `design_position`,
               p.final_Settlement_Expenses  `final_Settlement_Expenses`,
               concat('oldData', CURDATE()) `flag`,
               null                         `note1`,
               null                         `note2`,
               null                         `note3`,
               null                         `note4`,
               null                         `note5`
        from t_temp_confirm_project_info_param p
                 left join t_store_master_info m on p.store_no = m.store_no
        where project_id in (
--         1425655739746426882,
                             1436219329171759105,
--         1451363845784543234,
                             1467765501879197698)
    </select>

    <select id="getTeamplateQueueTwoThreeByTemplateCode" resultType="com.bassims.modules.atour.domain.TemplateQueue">
        SELECT
        tq.*
        FROM
        t_template_queue tq
        LEFT JOIN t_project_template pt ON tq.parent_id = pt.template_id and tq.template_id=pt.template_id
        OR ( tq.parent_id = NULL AND tq.template_id = pt.template_id )
        WHERE
        tq.template_code
        IN
        <foreach collection="templateCodes" item="templateCode" open="(" separator="," close=")">
            #{templateCode}
        </foreach>
        ORDER BY
        tq.node_code;
    </select>

    <select id="getGroupByTemplateCode" resultType="com.bassims.modules.atour.domain.TemplateGroup">
        SELECT
        tg.*
        FROM
        t_template_group tg
        LEFT JOIN t_template_queue tq ON tq.template_queue_id = tg.template_queue_id
        WHERE
        tq.template_code
        IN
        <foreach collection="templateCodes" item="templateCode" open="(" separator="," close=")">
            #{templateCode}
        </foreach>
        ORDER BY
        tg.node_wbs;
    </select>

    <select id="getFindById" resultType="com.bassims.modules.atour.service.dto.ProjectInfoDto">
        select t_project_info.*,
               t_project_info_expansion.security_project
        from t_project_info t_project_info
                 left join t_project_info_expansion t_project_info_expansion
                           on t_project_info.project_hlm_id = t_project_info_expansion.project_id
        where t_project_info.project_id = #{projectId}
    </select>


    <select id="getListByCriteria" resultType="com.bassims.modules.atour.service.dto.ProjectInfoDto">
        select t_project_info.*,
               t_project_info_expansion.security_project
        from t_project_info t_project_info
                 left join t_project_info_expansion t_project_info_expansion
                           on t_project_info.project_hlm_id = t_project_info_expansion.project_id
        where t_project_info.project_id = #{projectId}
    </select>

    <select id="downloadMyFile" resultType="com.bassims.modules.atour.domain.vo.ReceiptInfo">
        SELECT distinct
            a.project_no AS projectId,
            a.project_name AS projectName,
            a.project_address AS projectAddress,

            max(b.installation_standards) AS type1,
            max(b.installation_standards_remark) AS type1Remark,
            max(b.linkage_test) AS type2,
            max(b.linkage_test_remark) AS type2Remark,
            max(b.fire_broadcast) AS type3,
            max(b.fire_broadcast_remark) AS type3Remark,
            max(b.fire_water) AS type4,
            max(b.fire_water_remark) AS type4Remark,
            max(b.fire_evacuation) AS type5,
            max(b.fire_evacuation_remark) AS type5Remark,
            max(b.three_calling) AS type6,
            max(b.three_calling_remark) AS type6Remark,
            max(b.ladder_control) AS type7,
            max(b.ladder_control_remark) AS type7Remark,
            max(b.certificate) AS type8,
            max(b.certificate_remark) AS type8Remark,
            max(b.access_control) AS type9,
            max(b.access_control_remark) AS type9Remark,

            max(c.property_division) AS propertyManage,
            max(c.network) AS networkExperience,
            max(c.sound_insulation) AS soundExperience,
            max(c.peculiar_smell) AS tasteExperience,
            max(c.air_conditioner) AS airconditioningExperience,
            max(c.hot_water) AS hotwaterExperience

        FROM
            t_project_info a
                left join  t_project_completion_receipt_summary b on a.project_id = b.project_id
                left join  t_project_completion_receipt_description c on a.project_id = c.project_id
        where a.project_id = #{projectId}
        group by a.project_no,a.project_name,a.project_address
    </select>
    <select id="getUserNameByUserId" resultType="java.lang.String">
        select nick_name from s_user_info where user_id = #{userId}
    </select>
    <select id="getDownloadData" resultType="com.bassims.modules.atour.service.dto.ProjectInfoDto">
        SELECT a.project_no as projectNo,
               a.project_name as projectName,
               (select `label`from s_sys_dict_detail where dict_id=(select dict_id from s_sys_dict where `name`='brand') and `value`=a.brand_id) as brandName,
               (select `label`from s_sys_dict_detail where dict_id=(select dict_id from s_sys_dict where `name`='execution_standards') and `value`=a.product_code) as productName,
               a.city_name as cityName,a.province_name as provinceName,a.effective_date as effectiveDate,case when project_status='project_conimplement' then '施工中' when project_status='project_settlement' then '结算中' when project_status='project_finish' then '已完成' when project_status='project_delete' then '已删除' else '筹备中' end as projectStatus,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'zxdw-xmjl') group by is_delete) aa) as constructionManagerName,
               (select sup_name_cn from t_supplier_info where id in(select supplier_id from t_supplier_pm where id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'zxdw-xmjl')) limit 1) as construction,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'sjdwsjs') group by is_delete) aa) as decorationDesignerName,
               (select sup_name_cn from t_supplier_info where id in(select supplier_id from t_supplier_pm where id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'sjdwsjs')) limit 1) as designUnitName,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'gqzssjs') group by is_delete) aa) as areaDesigner,
               (select sup_name_cn from t_supplier_info where id in(select supplier_id from t_supplier_pm where id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'gqzssjs')) limit 1) as areaDesignUnit,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'jdsjs') group by is_delete) aa) as mechanicalAndElectricalDesignerName,
               (select sup_name_cn from t_supplier_info where id in(select supplier_id from t_supplier_pm where id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'jdsjs')) limit 1) as mechanicalAndElectricalDesignerUnit,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'gqjdsjs') group by is_delete) aa) as electricalDesignerName,
               (select sup_name_cn from t_supplier_info where id in(select supplier_id from t_supplier_pm where id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'gqjdsjs')) limit 1) as electricalDesignerUnit,

               (select `label`from s_sys_dict_detail where dict_id=(select dict_id from s_sys_dict where `name`='region') and `value`=a.region) as developmentTheaterName,
               ((select `label`from s_sys_dict_detail where dict_id=(select dict_id from s_sys_dict where `name`='operational_theater') and `value`=(select operational_theater from s_sys_area where area_code=a.city))) as operationsTheaterName,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'yyzqfzr') group by is_delete) aa) as operationsTheaterLeaderName,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'kyjl') group by is_delete) aa) as openingManagerName,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'yyjl') group by is_delete) aa) as operationsManagerName,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'txjl') group by is_delete) aa) as franchiseManagerName,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'kfzqfzr') group by is_delete) aa) as developmentTheaterLeaderName,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'kffqfzr') group by is_delete) aa) as developmentZoneLeaderName,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info  where  FIND_IN_SET(user_id,a.development_manager) group by is_delete) aa) as developmentManager,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'zbzxfzr') group by is_delete) aa) as constructionAreaLeaderName,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'cgxs') group by is_delete) aa) as purchase,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'gcjl') group by is_delete) aa) as projectManagerName,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'sjfzr') group by is_delete) aa) as designerName,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'rdysry') group by is_delete) aa) as weakCurrentEngineerName,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'jdgcs') group by is_delete) aa) as mechanicalAndElectricalEngineerName,
               (select aa.nick_name from (select is_delete,GROUP_CONCAT(nick_name) as nick_name from s_user_info where user_id in (SELECT user_id from t_project_stakeholders where  project_id=a.project_id and shakeholder_status='in_term' and is_delete='0' and role_code = 'rzsj') group by is_delete) aa) as softDesigner,
               b.rooms_number as roomsNumber,
               (SELECT GROUP_CONCAT(DISTINCT  CONCAT(parent.node_name,'-',g.node_name,'【', CASE g.node_status
                                                                                               WHEN 'task_fin' THEN '已完成' WHEN 'task_submit' THEN '审批中'  ELSE '未完成'  END,'】')
                                    SEPARATOR ';')
                FROM t_project_group g
                         LEFT JOIN t_project_group parent
                                   ON parent.project_id = g.project_id
                                       AND parent.template_id = g.parent_id
                WHERE g.project_id = a.project_id
                  AND g.template_code = 'engineering'
                  AND g.node_level = 2 and g.node_status='task_fin'
                  AND g.node_code != 'des-00127') AS 'engineeringNodeTask',
               (SELECT GROUP_CONCAT(DISTINCT  CONCAT(parent.node_name,'-',g.node_name,'【', CASE g.node_status
                                                                                               WHEN 'task_fin' THEN '已完成' WHEN 'task_submit' THEN '审批中'  ELSE '未完成'  END,'】')
                                    SEPARATOR ';')
                FROM t_project_group g
                         LEFT JOIN t_project_group parent
                                   ON parent.project_id = g.project_id
                                       AND parent.template_id = g.parent_id
                WHERE g.project_id = a.project_id
                  AND g.template_code = 'deepening_plan'
                  AND g.node_level = 2 and g.node_status='task_fin') AS 'deepenNodeTask',
               (SELECT GROUP_CONCAT(DISTINCT  CONCAT(parent.node_name,'-',g.node_name,'【', CASE g.node_status
                                                                                               WHEN 'task_fin' THEN '已完成' WHEN 'task_submit' THEN '审批中'  ELSE '未完成'  END,'】')
                                    SEPARATOR ';')
                FROM t_project_group g
                         LEFT JOIN t_project_group parent
                                   ON parent.project_id = g.project_id
                                       AND parent.template_id = g.parent_id
                WHERE g.project_id = a.project_id
                  AND (g.template_code = 'design' or g.template_code = 'public_area_design')
                  AND g.node_level = 2 and g.node_status='task_fin') AS 'designNodeTask',
               (select max(plan_end_date) from t_project_group where node_code ='eng-00107' and project_id=a.project_id ) as 'projectPlanStart',
               (select max(actual_end_date) from t_project_group where node_code ='eng-00107' and project_id=a.project_id) as 'projectActualStart',
               (select max(plan_end_date) from t_project_group where node_code ='eng-00135' and project_id=a.project_id) as 'projectPlanEnd',
               (select max(actual_end_date) from t_project_group where node_code ='eng-00135' and project_id=a.project_id) as 'projectActualEnd',
               a.used_area usedArea,
               a.decorate_area decorateArea,
               CASE a.project_type WHEN 'new' THEN '新开店'  ELSE '其他'  END as projectType,
               a.project_status projectStatus,
               a.task_phase taskPhase,
               CASE a.is_overdue WHEN 'TRUE' THEN '是'  ELSE '否'  END as  overdue,
               a.account_phase accountPhase,
               a.account_overdue accountOverdue,
               a.remark ,
               a.is_active isActive,
               a.is_enabled isEnabled,
               a.floor

        from t_project_info a LEFT JOIN t_project_info_expansion b ON a.project_hlm_id = b.project_id where a.node_table_name is not null;

    </select>

</mapper>
