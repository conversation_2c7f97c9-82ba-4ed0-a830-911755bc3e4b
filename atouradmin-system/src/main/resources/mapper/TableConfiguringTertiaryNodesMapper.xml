<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.TableConfiguringTertiaryNodesRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.TableConfiguringTertiaryNodes">
        <id column="id" property="id"/>
        <result column="tertiary_key" property="tertiaryKey"/>
        <result column="tertiary_value" property="tertiaryValue"/>
        <result column="tertiary_type" property="tertiaryType"/>
        <result column="start_sign" property="startSign"/>
        <result column="relation_code" property="relationCode"/>
        <result column="relation_name" property="relationName"/>
        <result column="project_version" property="projectVersion"/>
        <result column="is_hide" property="isHide"/>
        <result column="create_time" property="createTime"/>

        <result column="dynamic_table_type" property="dynamicTableType"/>
        <result column="table_type" property="tableType"/>
        <result column="is_delete" property="isDelete"/>
        <result column="is_compile" property="isCompile"/>
        <result column="is_audit_display" property="isAuditDisplay"/>

        <result column="is_merge_cells" property="isMergeCells"/>
        <result column="is_must" property="isMust"/>
        <result column="is_modifiable" property="isModifiable"/>
        <result column="modifiable_code" property="modifiableCode"/>
        <result column="width_and_height" property="widthAndHeight"/>


    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,tertiary_key,tertiary_value,tertiary_type,start_sign,relation_code,relation_name,project_version,
        is_hide,create_time,dynamic_table_type,table_type,is_delete,is_compile,is_audit_display,is_merge_cells,is_must,is_modifiable,modifiable_code,width_and_height
    </sql>

    <select id="getNodeByStartSignList"
            resultType="com.bassims.modules.atour.service.dto.TableConfiguringTertiaryNodesDto">
        select *
        from t_table_configuring_tertiary_nodes
        where dynamic_table_type = #{dynamicTableType}
          and is_delete = 0
    </select>

    <select id="getNodeByStartSign" resultType="com.bassims.modules.atour.service.dto.TableConfiguringTertiaryNodesVO">
        select *
        from t_table_configuring_tertiary_nodes
        where dynamic_table_type = #{dynamicTableType}
          and is_delete = 0
    </select>

    <select id="getList" resultType="com.bassims.modules.atour.service.dto.TableConfiguringTertiaryNodesDto">
        select id,
               tertiary_key,
               tertiary_value AS label,
               tertiary_value,
               tertiary_type,
               start_sign,
               relation_code,
               dynamic_table_type,
               table_type,
               is_compile,
               is_merge_cells,
               width_and_height
        from t_table_configuring_tertiary_nodes
        where dynamic_table_type = #{dynamicTableType}
          and is_delete = 0
    </select>

</mapper>
