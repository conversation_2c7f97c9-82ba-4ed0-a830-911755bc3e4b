<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.OwnerUserRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.OwnerUser">
                    <id column="owner_user_id" property="ownerUserId" />
                    <result column="owner_id" property="ownerId" />
                    <result column="owner_code" property="ownerCode" />
                    <result column="user_id" property="userId" />
                    <result column="user_role" property="userRole" />
                    <result column="create_user" property="createUser" />
                    <result column="create_time" property="createTime" />
                    <result column="update_user" property="updateUser" />
                    <result column="update_time" property="updateTime" />
                    <result column="is_delete" property="isDelete" />
                    <result column="status" property="status" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            owner_user_id,owner_id,owner_code,user_id,user_role,create_user,create_time,update_user,update_time,is_delete,status
        </sql>

    <select id="getListByOwnerCode" resultType="com.bassims.modules.atour.service.dto.OwnerUserDto">
        SELECT
            t_owner_user.user_id,
            s_user_info.username,
            s_user_info.nick_name
        FROM
            t_owner_user t_owner_user
                LEFT JOIN (
                SELECT
                    s_user_info.user_id,
                    s_user_info.username,
                    s_user_info.nick_name,
                    s_sys_role.role_code AS roleCode,
                    s_sys_role.`name`
                FROM
                    s_user_info s_user_info
                        LEFT JOIN s_sys_users_roles s_sys_users_roles ON s_sys_users_roles.user_id = s_user_info.user_id
                        LEFT JOIN s_sys_role s_sys_role ON s_sys_role.role_id = s_sys_users_roles.role_id
            ) s_user_info ON s_user_info.user_id = t_owner_user.user_id
        WHERE
            owner_code = #{ownerCode}
          AND s_user_info.roleCode = #{roleCode}
    </select>
</mapper>
