<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.MasterLicenseInfoRepository">

        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.MasterLicenseInfo">
                    <id column="license_id" property="licenseId" />
                    <result column="license_name" property="licenseName" />
                    <result column="license_type" property="licenseType" />
                    <result column="store_id" property="storeId" />
                    <result column="store_no" property="storeNo" />
                    <result column="node_id" property="nodeId" />
                    <result column="actual_start" property="actualStart" />
                    <result column="actual_end" property="actualEnd" />
                    <result column="uploader" property="uploader" />
                    <result column="upload_time" property="uploadTime" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            license_id,license_name,license_type,store_id,store_no,node_id,actual_start,actual_end,uploader,upload_time,create_time,create_by,update_time,update_by,is_delete
        </sql>

</mapper>
