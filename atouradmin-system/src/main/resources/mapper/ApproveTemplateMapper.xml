<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ApproveTemplateRepository">

        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ApproveTemplate">
                    <id column="approve_template_id" property="approveTemplateId" />
                    <result column="app_templete_code" property="appTempleteCode" />
                    <result column="app_template_name" property="appTemplateName" />
                    <result column="app_template_remark" property="appTemplateRemark" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            approve_template_id,app_templete_code,app_template_name,app_template_remark,create_time,create_by,update_time,update_by,is_enabled,is_delete
        </sql>

</mapper>
