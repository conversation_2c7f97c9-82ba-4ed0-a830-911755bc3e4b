<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.OrderDetailRepository">

        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.OrderDetail">
                    <id column="detail_id" property="detailId" />
                    <result column="order_id" property="orderId" />
                    <result column="materiel_id" property="materielId" />
                    <result column="finance_code" property="financeCode" />
                    <result column="product_name" property="productName" />
                    <result column="product_code" property="productCode" />
                    <result column="materiel_code" property="materielCode" />
                    <result column="senior_class" property="seniorClass" />
                    <result column="second_class" property="secondClass" />
                    <result column="brand" property="brand" />
                    <result column="spec" property="spec" />
                    <result column="model" property="model" />
                    <result column="unit" property="unit" />
                    <result column="suggest_unit_price" property="suggestUnitPrice" />
                    <result column="supplier_id" property="supplierId" />
                    <result column="sup_name_cn" property="supNameCn" />
                    <result column="service_num" property="serviceNum" />
                    <result column="toy_num" property="toyNum" />
                    <result column="daily_num" property="dailyNum" />
                    <result column="is_match" property="isMatch" />
                    <result column="unit_price" property="unitPrice" />
                    <result column="rate" property="rate" />
                    <result column="contract_no" property="contractNo" />
                    <result column="end_time" property="endTime" />
                    <result column="fast_num" property="fastNum" />
                    <result column="spin_num" property="spinNum" />
                    <result column="account_adjust_num" property="accountAdjustNum"/>
                    <result column="approved_num" property="approvedNum"/>
                    <result column="subtotal" property="subtotal" />
                    <result column="total_price" property="totalPrice" />
                    <result column="order_stock" property="orderStock" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            detail_id,order_id,materiel_id,finance_code,product_name,product_code,materiel_code,senior_class,second_class,brand,spec,model,unit,suggest_unit_price,supplier_id,sup_name_cn,service_num,toy_num,daily_num,fast_num,spin_num,is_match,unit_price,rate,contract_no,end_time,account_adjust_num,approved_num,subtotal,total_price,order_stock,create_time,create_by,update_time,update_by,is_enabled,is_delete
        </sql>

    <select id="getOldOrderDetail" resultMap="BaseResultMap">
        select d.id `detail_id`,d.order_id `order_id`,null `materiel_id`,null `finance_code`,d.product_name `product_name`,d.materiel_no `product_code`,d.materiel_no `materiel_code`,d.middle_category_name `senior_class`,d.subcategory_name `second_class`,d.brand `brand`,d.spec `spec`,d.model `model`,d.unit `unit`,d.unit_price `suggest_unit_price`,null `order_stock`,null `supplier_id`,null `sup_name_cn`,null `service_num`,null `toy_num`,null `daily_num`,null `fast_num`,null `spin_num`,null `is_match`,d.unit_price `unit_price`,null `rate`,null `contract_no`,null `end_time`,null `account_adjust_num`,null `approved_num`,d.actual_amount `subtotal`,d.actual_total_price `total_price`,d.create_time `create_time`,d.create_user `create_by`,d.update_time `update_time`,d.update_user `update_by`,null `is_enabled`,d.is_delete `is_delete`,null `sort`,d.remark `remark`
        from t_temp_mto_order_materiel_detail d
        where d.is_delete = '0'
    </select>

    <select id="getOldOrderAmountTotal" resultType="com.bassims.modules.atour.domain.vo.OrderAmount">
        select e.detail_id,sum(case e.amount_code when 'AMOUNT_CODE_01' then e.actual_amount else 0 end) service_num,
        sum(case e.amount_code when 'AMOUNT_CODE_02' then e.actual_amount else 0 end) toy_num,
        sum(case e.amount_code when 'AMOUNT_CODE_03' then e.actual_amount else 0 end) daily_num,
        sum(case e.amount_code when 'AMOUNT_CODE_04' then e.actual_amount else 0 end) fast_num,
        sum(case e.amount_code when 'AMOUNT_CODE_05' then e.actual_amount else 0 end) spin_num,
        sum(case e.amount_code when 'AMOUNT_CODE_REDUCE' then e.actual_amount else 0 end) account_adjust_num
        from t_temp_mto_order_materiel_detail_amo e
        where e.is_delete = '0'
        group by e.detail_id
        order by e.detail_id
    </select>
</mapper>
