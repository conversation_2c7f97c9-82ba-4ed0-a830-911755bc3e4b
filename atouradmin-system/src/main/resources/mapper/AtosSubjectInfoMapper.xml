<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atourWithout.repository.AtosSubjectInfoRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atourWithout.domain.AtosSubjectInfo">
                    <id column="atos_subject_id" property="atosSubjectId" />
                    <result column="atos_user_id" property="atosUserId" />
                    <result column="company_id" property="companyId" />
                    <result column="company_name" property="companyName" />
                    <result column="company_phone" property="companyPhone" />
                    <result column="company_email" property="companyEmail" />
                    <result column="company_type" property="companyType" />
                    <result column="enabled" property="enabled" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            atos_subject_id,atos_user_id,company_id,company_name,company_phone,company_email,company_type,enabled,create_time,update_time,create_by,update_by,is_enabled,is_delete
        </sql>

</mapper>
