<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.MasterStoreOrderDataRepository">

        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.MasterStoreOrderData">
                    <id column="store_order_id" property="storeOrderId" />
                    <result column="operate" property="operate" />
                    <result column="order_no" property="orderNo" />
                    <result column="order_id" property="orderId" />
                    <result column="store_id" property="storeId" />
                    <result column="store_no" property="storeNo" />
                    <result column="order_type" property="orderType" />
                    <result column="expense_type" property="expenseType" />
                    <result column="project" property="project" />
                    <result column="order_amount" property="orderAmount" />
                    <result column="order_settlement_amount" property="orderSettlementAmount" />
                    <result column="order_status" property="orderStatus" />
                    <result column="order_time" property="orderTime" />
                    <result column="arrival_time" property="arrivalTime" />
                    <result column="supplier" property="supplier" />
                    <result column="consignee" property="consignee" />
                    <result column="special_cost" property="specialCost" />
                    <result column="original_order_amount" property="originalOrderAmount" />
                    <result column="approve_amount" property="approveAmount" />
                    <result column="store_owner" property="storeOwner" />
                    <result column="store_type" property="storeType" />
                    <result column="store" property="store" />
                    <result column="procurement_desc" property="procurementDesc" />
                    <result column="order_creator" property="orderCreator" />
                    <result column="order_creator_time" property="orderCreatorTime" />
                    <result column="order_creator_phone" property="orderCreatorPhone" />
                    <result column="engineering_manager" property="engineeringManager" />
                    <result column="engineering_manager_phone" property="engineeringManagerPhone" />
                    <result column="start_install_time" property="startInstallTime" />
                    <result column="start_end_time" property="startEndTime" />
                    <result column="order_pro_docu" property="orderProDocu" />
                    <result column="logistics_oder_no" property="logisticsOderNo" />
                    <result column="consignor" property="consignor" />
                    <result column="logistics_num" property="logisticsNum" />
                    <result column="delivery_time" property="deliveryTime" />
                    <result column="purchase_no" property="purchaseNo" />
                    <result column="po_no" property="poNo" />
                    <result column="pay_time" property="payTime" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            store_order_id,operate,order_no,order_id,store_id,store_no,order_type,expense_type,project,order_amount,order_settlement_amount,order_status,order_time,arrival_time,supplier,consignee,special_cost,original_order_amount,approve_amount,store_owner,store_type,store,procurement_desc,order_creator,order_creator_time,order_creator_phone,engineering_manager,engineering_manager_phone,start_install_time,start_end_time,order_pro_docu,logistics_oder_no,consignor,logistics_num,delivery_time,purchase_no,po_no,pay_time,create_time,create_by,update_time,update_by,is_delete
        </sql>

</mapper>
