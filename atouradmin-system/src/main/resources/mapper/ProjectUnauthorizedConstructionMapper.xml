<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ProjectUnauthorizedConstructionRepository">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ProjectUnauthorizedConstruction">
        <id column="unauthorized_construction_id" property="unauthorizedConstructionId"/>
        <result column="project_id" property="projectId"/>
        <result column="unqualified_file" property="unqualifiedFile"/>
        <result column="non_compliant_description" property="nonCompliantDescription"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="is_delete" property="isDelete"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="node_code" property="nodeCode"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        unauthorized_construction_id,project_id,unqualified_file,non_compliant_description,create_time,create_by,update_time,update_by,is_delete,is_enabled,node_code
    </sql>

    <delete id="deleteByIds">
        delete from  t_project_unauthorized_construction where unauthorized_construction_id in
        <foreach collection="collects" item="collect" open="(" separator="," close=")">
            #{collect}
        </foreach>
    </delete>

</mapper>
