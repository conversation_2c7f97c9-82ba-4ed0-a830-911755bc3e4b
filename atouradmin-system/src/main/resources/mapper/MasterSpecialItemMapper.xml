<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.MasterSpecialItemRepository">

        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.MasterSpecialItem">
                    <id column="special_item_id" property="specialItemId" />
                    <result column="order_id" property="orderId" />
                    <result column="special_item" property="specialItem" />
                    <result column="quantity" property="quantity" />
                    <result column="account_adjust_num" property="accountAdjustNum" />
                    <result column="store_id" property="storeId" />
                    <result column="store_no" property="storeNo" />
                    <result column="unit_price" property="unitPrice" />
                    <result column="subtotal" property="subtotal" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            special_item_id,order_id,special_item,quantity,account_adjust_num,store_id,store_no,unit_price,subtotal,create_time,create_by,update_time,update_by,is_delete
        </sql>

</mapper>
