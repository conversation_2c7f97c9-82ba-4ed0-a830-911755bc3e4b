<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ProjectApproveRepository">

        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ProjectApprove">
                    <id column="approve_id" property="approveId" />
                    <result column="project_id" property="projectId" />
                    <result column="order_id" property="orderId"/>
                    <result column="approve_type" property="approveType" />
                    <result column="approve_status" property="approveStatus" />
                    <result column="approve_result" property="approveResult" />
                    <result column="approve_start" property="approveStart" />
                    <result column="approve_end" property="approveEnd" />
                    <result column="approve_end" property="approveEnd" />
                    <result column="is_anew" property="isAnew" />
                    <result column="anew_reason" property="anewReason" />
                    <result column="node_id" property="nodeId" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            approve_id,project_id,order_id,approve_type,approve_status,approve_result,approve_start,approve_end,submit_user,is_anew,anew_reason,node_id,create_time,create_by,update_time,update_by,is_enabled,is_delete
        </sql>

    <select id="queryApprovePassInfo" resultType="long">
        select order_id from t_project_approve
        where order_id is not null and approve_result = 'approve_pass'
    </select>

    <select id="queryInfoByNodeId" parameterType="long" resultMap="BaseResultMap">
        select
        distinct a.*
        from t_project_approve a
        left join t_project_approve_detail d on a.approve_id = d.approve_id
        where a.node_id = #{nodeId}
        and d.emp_code is null
        order by a.create_time desc
    </select>

    <select id="queryEmpByNodeId" parameterType="long" resultMap="BaseResultMap">
        select d.* from t_project_approve a
        left join t_project_approve_detail d on a.approve_id = d.approve_id
        where d.emp_code is not null
        and a.node_id  = #{nodeId}
        order by create_time desc
    </select>

    <select id="getCountByUserAndNodeId" resultType="Integer">
        SELECT
            count( 1 )
        FROM
            t_project_approve t_project_approve
                LEFT JOIN t_project_approve_detail t_project_approve_detail ON t_project_approve_detail.approve_id = t_project_approve.approve_id
        WHERE
            t_project_approve.project_id = #{projectId}
          AND t_project_approve_detail.approve_user = #{currentUserId}
          AND t_project_approve.node_id = #{nodeId}
    </select>
    <select id="getCountByUser" resultType="Integer">
        SELECT
            count( 1 )
        FROM
            t_project_approve t_project_approve
                LEFT JOIN t_project_approve_detail t_project_approve_detail ON t_project_approve_detail.approve_id = t_project_approve.approve_id
        WHERE
            t_project_approve.project_id = #{projectId}
          AND t_project_approve_detail.approve_user = #{currentUserId}
    </select>

    <select id="getApproveDetailOpenCondition"  resultType="com.bassims.modules.atour.domain.ProjectApproveDetail">
        select d.*
        from t_project_approve a
                 left join t_project_approve_detail d on a.approve_id = d.approve_id
        where a.node_id  = #{nodeId}
          and a.project_id  = #{projectId}
          and d.is_exist_open_condition  = 1
        order by d.create_time desc
    </select>
</mapper>
