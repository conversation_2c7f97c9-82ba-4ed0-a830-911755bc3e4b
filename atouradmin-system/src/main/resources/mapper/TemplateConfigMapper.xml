<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.system.repository.TemplateConfigRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.system.domain.TemplateConfig">
                    <id column="template_config_id" property="templateConfigId" />
                    <result column="config_type" property="configType" />
                    <result column="file_path" property="filePath" />
                    <result column="content" property="content" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            template_config_id,config_type,file_path,content,create_time,update_time,create_by,update_by,is_delete
        </sql>

</mapper>
