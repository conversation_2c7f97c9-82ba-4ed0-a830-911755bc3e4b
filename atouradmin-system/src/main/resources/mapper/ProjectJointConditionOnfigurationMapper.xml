<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bassims.modules.atour.repository.ProjectJointConditionOnfigurationRepository">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.bassims.modules.atour.domain.ProjectJointConditionOnfiguration">
                    <id column="project_joint_condition_id" property="projectJointConditionId" />
                    <result column="project_id" property="projectId" />
                    <result column="project_joint_task_id" property="projectJointTaskId" />
                    <result column="joint_code" property="jointCode" />
                    <result column="field_requirement_node_code" property="fieldRequirementNodeCode" />
                    <result column="field_requirement" property="fieldRequirement" />
                    <result column="conditional_demand" property="conditionalDemand" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_by" property="updateBy" />
                    <result column="is_enabled" property="isEnabled" />
                    <result column="is_delete" property="isDelete" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            project_joint_condition_id,project_id,project_joint_task_id,joint_code,field_requirement_node_code,field_requirement,conditional_demand,create_time,update_time,create_by,update_by,is_enabled,is_delete
        </sql>

</mapper>
