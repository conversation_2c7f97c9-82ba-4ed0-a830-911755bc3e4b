<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${package}.repository.${className}Repository">

    <#if enableCache>
        <!-- 开启二级缓存 -->
        <cache type="org.mybatis.caches.ehcache.LoggingEhcache"/>

    </#if>
    <#if baseResultMap>
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="${package}.domain.${className}">
            <#list columns as column>
                <#if column.columnKey = 'PRI'><#--生成主键排在第一位-->
                    <id column="${column.columnName}" property="${column.changeColumnName}" />
                </#if>
            </#list>
            <#list columns as column>
                <#if column.columnKey != 'PRI'><#--生成普通字段-->
                    <result column="${column.columnName}" property="${column.changeColumnName}" />
                </#if>
            </#list>
        </resultMap>

    </#if>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            <!--@mbg.generated-->
            <#list columns as column><#if column.columnKey = 'PRI'>${column.columnName}</#if></#list><#list columns as column><#if column.columnKey != 'PRI'>,${column.columnName}</#if></#list>
        </sql>

</mapper>
