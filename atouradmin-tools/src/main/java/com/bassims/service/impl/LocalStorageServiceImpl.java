/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bassims.config.FileProperties;
import com.bassims.constant.jhEnum.JhSystemEnum;
import com.bassims.domain.LocalStorage;
import com.bassims.exception.BadRequestException;
import com.bassims.repository.LocalStorageRepository;
import com.bassims.service.LocalStorageService;
import com.bassims.service.dto.LocalStorageDto;
import com.bassims.service.dto.LocalStorageQueryCriteria;
import com.bassims.service.mapstruct.LocalStorageMapper;
import com.bassims.utils.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.FilenameUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR> Jie
 * @date 2019-09-05
 */
@Log4j2
@Service
@RequiredArgsConstructor
public class LocalStorageServiceImpl implements LocalStorageService {

    private static final byte[] buf = new byte[1024];
    private final LocalStorageRepository localStorageRepository;
    @Autowired
    private LocalStorageService localStorageService;
    private final LocalStorageMapper localStorageMapper;
    private final FileProperties properties;
    private final OSSClientUtil ossClientUtil;

    @Override
    public Object queryAll(LocalStorageQueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<LocalStorage> page = new PageInfo<> (localStorageRepository.selectList(Wrappers.lambdaQuery(LocalStorage.class)
                .eq(StringUtils.isNotEmpty(criteria.getNodeId()), LocalStorage::getNodeId, criteria.getNodeId())
                .eq(StringUtils.isNotEmpty(criteria.getCode()), LocalStorage::getCode, criteria.getCode())
                .eq(criteria.getId() != null, LocalStorage::getId, criteria.getId())
                .in(criteria.getIds() != null && !criteria.getIds().isEmpty(), LocalStorage::getId, criteria.getIds())));
        if (ObjectUtil.isNotEmpty(criteria.getIds()) && criteria.getIds().get(0).equals(0L)) {
            return null;
        }
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", localStorageMapper.toDto(page.getList()));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    public List<LocalStorageDto> queryAll(LocalStorageQueryCriteria criteria) {
        return localStorageMapper.toDto(localStorageRepository.selectList(Wrappers.lambdaQuery(LocalStorage.class)
                .eq(StringUtils.isNotEmpty(criteria.getNodeId()), LocalStorage::getNodeId, criteria.getNodeId())
                .eq(StringUtils.isNotEmpty(criteria.getCode()), LocalStorage::getCode, criteria.getCode())
                .eq(criteria.getId() != null, LocalStorage::getId, criteria.getId())
                .in(criteria.getIds() != null && !criteria.getIds().isEmpty(), LocalStorage::getId, criteria.getIds())));
    }

    @Override
    public LocalStorageDto findById(Long id) {
        LocalStorage localStorage = localStorageRepository.selectById(id);
        ValidationUtil.isNull(localStorage.getId(),"LocalStorage","id",id);
        return localStorageMapper.toDto(localStorage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LocalStorage create(String nodeId, String name, MultipartFile multipartFile, Long userId) throws IOException {
        if (ObjectUtil.isNotEmpty(multipartFile)) {
            FileUtil.checkSize(properties.getMaxSize(), multipartFile.getSize());
            String suffix = FileUtil.getExtensionName(multipartFile.getOriginalFilename());
            String type = FileUtil.getFileType(suffix);
            if (type.equals(FileUtil.VIDEO)) {
                throw new BadRequestException("不允许视频上传");
            }
//        if (type.equals(FileUtil.IMAGE)){
//            multipartFile = getCompressFile(multipartFile);
//        }

            if (type.equals(FileUtil.TXT) && "txt".equals(StringUtils.lowerCase(suffix))) {
                multipartFile = translateTxtToDoc(multipartFile);
            }
            Date date = new Date();
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMddhhmmssS");
            String nowStr = format.format(date);
            File file = FileUtil.upload(multipartFile, properties.getPath().getPath() + type + File.separator + userId + File.separator, nowStr);
            if (ObjectUtil.isNull(file)) {
                throw new BadRequestException("上传失败");
            }
            try {
                name = StringUtils.isBlank(name) ? FileUtil.getFileNameNoEx(multipartFile.getOriginalFilename()) : name;
                LocalStorage localStorage = new LocalStorage(
                        userId + File.separator + file.getName(),
                        name,
                        suffix,
                        file.getPath(),
                        type,
                        FileUtil.getSize(multipartFile.getSize()),
                        nodeId,
                        null
                );
                  localStorageRepository.insert(localStorage);
                return localStorage;
            } catch (Exception e) {
                FileUtil.del(file);
                throw e;
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LocalStorage createCode(String nodeId, String code, String name, MultipartFile multipartFile, Long userId) throws IOException {
        FileUtil.checkSize(properties.getMaxSize(), multipartFile.getSize());
        String suffix = FileUtil.getExtensionName(multipartFile.getOriginalFilename());
        String type = FileUtil.getFileType(suffix);
        if (type.equals(FileUtil.VIDEO)) {
            throw new BadRequestException("不允许视频上传");
        }
        /*if (type.equals(FileUtil.IMAGE)){
            multipartFile = getCompressFile(multipartFile);
        }*/

        if (type.equals(FileUtil.TXT) && "txt".equals(StringUtils.lowerCase(suffix))) {
            multipartFile = translateTxtToDoc(multipartFile);
        }
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddhhmmssS");
        String nowStr = format.format(date);
        File file = FileUtil.upload(multipartFile, properties.getPath().getPath() + type + File.separator + userId + File.separator, nowStr);
        if (ObjectUtil.isNull(file)) {
            throw new BadRequestException("上传失败");
        }
        try {
            name = StringUtils.isBlank(name) ? FileUtil.getFileNameNoEx(multipartFile.getOriginalFilename()) : name;
            LocalStorage localStorage = new LocalStorage(
                    userId + File.separator + file.getName(),
                    name,
                    suffix,
                    file.getPath(),
                    type,
                    FileUtil.getSize(multipartFile.getSize()),
                    nodeId,
                    code
            );
             localStorageRepository.insert(localStorage);
            return localStorage;
        } catch (Exception e) {
            FileUtil.del(file);
            throw e;
        }

    }

    @Override
    public LocalStorage createOss(String nodeId, String name, MultipartFile multipartFile, Long userId) throws IOException {
        //oss上传文件
        name = StringUtils.isBlank(name) ? FileUtil.getFileNameNoEx(multipartFile.getOriginalFilename()) : name;
        String suffix = FilenameUtils.getExtension(multipartFile.getOriginalFilename());
        String urlss = null;
        try {
            urlss = ossClientUtil.uploadImg2Oss(multipartFile);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String imgUrl = ossClientUtil.getImgUrl(urlss);
        String type = FileUtil.getFileType(suffix);
        //保存文件信息
        LocalStorage localStorage = new LocalStorage(
                urlss,
                name,
                suffix,
                imgUrl,
                type,
                FileUtil.getSize(multipartFile.getSize()),
                nodeId,
                null
        );
         localStorageRepository.insert(localStorage);
        return localStorage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(LocalStorage resources) {
        LocalStorage localStorage = localStorageRepository.selectById(resources.getId());
        ValidationUtil.isNull(localStorage.getId(), "LocalStorage", "id", resources.getId());
        localStorage.copy(resources);
        localStorageRepository.updateById(localStorage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            LocalStorage storage = localStorageRepository.selectById(id);
            FileUtil.del(storage.getPath());
            localStorageRepository.deleteById(storage);
        }
    }

    @Override
    public void download(List<LocalStorageDto> queryAll, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (LocalStorageDto localStorageDTO : queryAll) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("文件名", localStorageDTO.getRealName());
            map.put("备注名", localStorageDTO.getName());
            map.put("文件类型", localStorageDTO.getType());
            map.put("文件大小", localStorageDTO.getSize());
            map.put("创建者", localStorageDTO.getCreateBy());
            map.put("创建日期", localStorageDTO.getCreateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<LocalStorageDto> listById(String[] productImages) {
        List<LocalStorageDto> list = new ArrayList<>();
        for (String productImage : productImages) {
            LocalStorageDto byId = findById(Long.parseLong(productImage));
            list.add(byId);
        }
        return list;
    }

    @Override
    public void download(Long localId, HttpServletResponse response, HttpServletRequest request) throws IOException {
        if (ObjectUtil.isNotEmpty(localId)) {
            LocalStorageDto storageDto = findById(localId);
            String path = storageDto.getPath();
            File file = new File(path);
            FileUtil.downloadFile(request, response, file, false);
        }
    }

    @Override
    public void downloadOss(Long localId, HttpServletResponse response, HttpServletRequest request, String isDrawing) throws IOException {
        if (ObjectUtil.isNotEmpty(localId)) {
            LocalStorageDto fileUpload = findById(localId);
            if (ObjectUtil.isNotEmpty(fileUpload)) {
                //Object myClass = SpringContextHolder.getBean("projectNodeInfoService");
                //Method method = null;
                //Long params = localId;
                //if (org.apache.commons.lang3.StringUtils.isNotBlank(params.toString())) {
                //    try {
                //        method = myClass.getClass().getDeclaredMethod("findById", String.class);
                //    } catch (NoSuchMethodException e) {
                //        e.printStackTrace();
                //    }
                //}
                //try {
                //    final Object invoke = method.invoke(myClass, params);
                //} catch (IllegalAccessException e) {
                //    e.printStackTrace();
                //} catch (InvocationTargetException e) {
                //    e.printStackTrace();
                //}

                //当前用户为外部用户,业主项目经理和厂商项目经理，判断当前二级任务是否完成
                Long currentUserId = SecurityUtils.getCurrentUserId();
                if (ObjectUtil.isNotEmpty(currentUserId)) {
                    List<String> roleIds = localStorageRepository.findRoleCodesByUserId1(currentUserId);
                    if (ObjectUtil.isNotEmpty(roleIds) && (roleIds.contains(JhSystemEnum.externalRoleEnum.YZXMJL.getKey()) || roleIds.contains(JhSystemEnum.externalRoleEnum.CSXMJL.getKey()))) {
                        final String nodeStatus = localStorageRepository.getTwoNodeStatus(String.valueOf(localId));
                        if (ObjectUtil.isNotEmpty(nodeStatus) && !JhSystemEnum.NodeStatusEnum.NODE_STATUS1.getKey().equals(nodeStatus)
                                && "1".equals(isDrawing)) {
                            throw new BadRequestException("当前审图未通过，审图通过才可下载图纸");
                        }
                    }
                }
                ossClientUtil.downFile(fileUpload.getRealName(), fileUpload.getName(), response);
            }
        }
    }

    @Override
    public List<LocalStorage> findByNodeId(String nodeId) {
        return localStorageRepository.findByNodeId(nodeId);
    }

    @Override
    public LocalStorage createLearningGarden(String name, MultipartFile multipartFile, Long userId) throws IOException {
        FileUtil.checkSize(properties.getMaxSize(), multipartFile.getSize());
        String suffix = FileUtil.getExtensionName(multipartFile.getOriginalFilename());
        String type = FileUtil.getFileType(suffix);
        if (type.equals(FileUtil.VIDEO)) {
            throw new BadRequestException("不允许视频上传");
        }
        /*if (type.equals(FileUtil.IMAGE)){
            multipartFile = getCompressFile(multipartFile);
        }*/

        if (type.equals(FileUtil.TXT) && "txt".equals(StringUtils.lowerCase(suffix))) {
            multipartFile = translateTxtToDoc(multipartFile);
        }
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddhhmmssS");
        String nowStr = format.format(date);
        File file = FileUtil.upload(multipartFile, properties.getPath().getPath() + type + File.separator + userId + File.separator, nowStr);
        if (ObjectUtil.isNull(file)) {
            throw new BadRequestException("上传失败");
        }
        try {
            name = StringUtils.isBlank(name) ? FileUtil.getFileNameNoEx(multipartFile.getOriginalFilename()) : name;
            LocalStorage localStorage = new LocalStorage(
                    userId + File.separator + file.getName(),
                    name,
                    suffix,
                    file.getPath(),
                    type,
                    FileUtil.getSize(multipartFile.getSize())
            );
            localStorageRepository.insert(localStorage);
            List<LocalStorage> localStorages = localStorageRepository.selectList(Wrappers.lambdaQuery(LocalStorage.class));
            if (CollectionUtil.isNotEmpty(localStorages)) {
                List<LocalStorage> localStoragesUse = localStorages.stream().sorted(Comparator.comparing(LocalStorage::getCreateTime).reversed()).collect(Collectors.toList());
                return localStoragesUse.get(0);
            }
            return new LocalStorage();
        } catch (Exception e) {
            FileUtil.del(file);
            throw e;
        }
    }

    @Override
    public void downloadLearningGarden(Long localId, HttpServletResponse response, HttpServletRequest request) throws IOException {
        LocalStorageDto storageDto = findById(localId);
        String path = storageDto.getPath();
        File file = new File(path);
        FileUtil.downloadFile(request, response, file, false);
    }

    @Override
    public void downloadScoreTemplate(List<File> fileList, HttpServletResponse response, HttpServletRequest request) throws Exception {
        String title = "Supplier-Rating-Template.zip";
        toZip(properties.getPath().getPath() + title, fileList);
        downFile(response, properties.getPath().getPath(), title);
    }

    @Override
    public LocalStorage createLearningGardenDirectory(String name, String path, HttpServletResponse response, HttpServletRequest request) throws Exception {
        if (StringUtils.isNotEmpty(path)) {
//            String filePathNotContainsName = getFilePathNotContainsName(path);
            String filePathNotContainsName = getFilePathContainsName(path);
            File file = new File(filePathNotContainsName + name); //以某路径实例化一个File对象
            LocalStorage localStorage = createLocalStorage(file, filePathNotContainsName, name);
            return localStorage;
        } else {
            LocalDateTime localDateTime = LocalDateTime.now();
            String yyyyMMdd = DateUtil.localDateTimeFormat(localDateTime, "yyyyMMdd");
            String pathUse = properties.getPath().getPath() + "directoryUse" + File.separator + Long.parseLong(yyyyMMdd) + File.separator;
            if (StringUtils.isNotEmpty(name)) {
                File file = new File(pathUse + name); //以某路径实例化一个File对象
                LocalStorage localStorage = createLocalStorage(file, pathUse, name);
                return localStorage;
            }
        }

        return new LocalStorage();
    }

    private LocalStorage createLocalStorage(File file, String path, String name) {
        /**
         *         String nowStr = format.format(date);
         *         File file = FileUtil.upload(multipartFile, properties.getPath().getPath() + type +  File.separator + userId +File.separator,nowStr);
         LocalDateTime localDateTime = LocalDateTime.now();
         String yyyyMMdd = DateUtil.localDateTimeFormat(localDateTime, "yyyyMMdd");
         LocalStorage localStorage = learningGardenService.createLearningGarden(packageId,name, file, Long.parseLong(yyyyMMdd));

         */
        if (!file.exists()) {
            LocalStorage localStorage = new LocalStorage(
                    File.separator + file.getName(),
                    name,
                    "",
                    file.getPath(),
                    "directoryUse",
                    "0"
            );
            localStorage.setCreateTime(new Timestamp(System.currentTimeMillis()));
            localStorage.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            localStorageRepository.insert(localStorage);
//            LocalStorage localStorage1 = localStorageRepository.findLocalStorage(name, "directoryUse");
            boolean dr = file.mkdirs(); //创建目录
            return localStorage;
        } else {
            throw new BadRequestException("目录已存在");
        }

    }

    /**
     * 获取文件路径不包含文件名
     */
    private String getFilePathNotContainsName(String filePath) {
        //linux 环境下
        String newFilePath = "";
        if (filePath.contains("/")) {
            String[] split = filePath.split("/");
            //最后一个就是文件名
            String fileName = split[split.length - 1];
            //将文件名从文件路径中用空字符串给替掉，就相当于去掉了
            newFilePath = filePath.replace(fileName, "");
        }//windows 环境下
        else if (filePath.contains("\\")) {
            String[] split = filePath.split("\\\\");
            String fileName = split[split.length - 1];
            //将文件名从文件路径中用空字符串给替掉，就相当于去掉了
            newFilePath = filePath.replace(fileName, "");
        }
        return newFilePath;
    }


    /**
     * 获取文件路径不包含文件名
     */
    private String getFilePathContainsName(String filePath) {
        //linux 环境下
        String newFilePath = "";
        if (filePath.contains("/")) {
            String[] split = filePath.split("/");

            newFilePath = filePath + "/";
        }//windows 环境下
        else if (filePath.contains("\\")) {

            newFilePath = filePath + "\\";
        }
        return newFilePath;
    }

    private void downFile(HttpServletResponse response, String FilePath, String str) {
        Map m = new HashMap();
        try {

            String path = FilePath + "/" + str;
            File file = new File(path);
            if (file.exists()) {
                InputStream ins = new FileInputStream(path);
                BufferedInputStream bins = new BufferedInputStream(ins);// 放到缓冲流里面
                OutputStream outs = response.getOutputStream();// 获取文件输出IO流
                BufferedOutputStream bouts = new BufferedOutputStream(outs);
                response.addHeader("content-disposition", "attachment;filename="
                        + java.net.URLEncoder.encode(str, "UTF-8"));
                int bytesRead = 0;
                byte[] buffer = new byte[8192];
                // 开始向网络传输文件流
                while ((bytesRead = bins.read(buffer, 0, 8192)) != -1) {
                    bouts.write(buffer, 0, bytesRead);
                }
                bouts.flush();// 这里一定要调用flush()方法
                ins.close();
                bins.close();
                outs.close();
                bouts.close();
            }
        } catch (IOException e) {
            m.put("code", "-1");
            m.put("text", "附件下载出错：" + e.getMessage());
            e.printStackTrace();
        }
    }

    private MultipartFile getCompressFile(MultipartFile multipartFile) throws IOException {
        List fileTypeList = new ArrayList();
        fileTypeList.add("bmp");
        fileTypeList.add("dib");
        fileTypeList.add("pcp");
        fileTypeList.add("dif");
        fileTypeList.add("wmf");
        fileTypeList.add("gif");
        fileTypeList.add("jpg");
        fileTypeList.add("tif");
        fileTypeList.add("eps");
        fileTypeList.add("psd");
        fileTypeList.add("cdr");
        fileTypeList.add("iff");
        fileTypeList.add("tga");
        fileTypeList.add("pcd");
        fileTypeList.add("mpt");
        fileTypeList.add("png");
        fileTypeList.add("jpeg");
        String filePath = FileUtil.getFileAbsolutePath("doc", "jpg");
        log.info("缓存文件的绝对路径：" + filePath);
        //图片压缩
        return ImageUtil.compressFile(multipartFile, filePath, fileTypeList);
    }

    MultipartFile translateTxtToDoc(MultipartFile multipartFile) throws IOException {
        OutputStream os = null;
        try {
            byte[] bFile = multipartFile.getBytes();
            //创建word文件
            XWPFDocument doc = new XWPFDocument();
            //创建段落
            XWPFParagraph p1 = doc.createParagraph();
            //创建段落文本
            XWPFRun r1 = p1.createRun();
            //设置文本
            r1.setText(new String(bFile));
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            doc.write(out);
            out.close();
            doc.close();
            byte[] xwpfDocumentBytes = out.toByteArray();
            String path = FileUtil.getFileAbsolutePath("doc", "docx");
            log.info("文件缓存地址：{}", path);
            File tempFile = new File(path);
            if (!tempFile.exists()) {
                tempFile.createNewFile();
            }
            // 输出流
            os = new FileOutputStream(tempFile);
            os.write(xwpfDocumentBytes);

//            fileInputStream = new FileInputStream(tempFile);
//            String fileName = IdUtil.simpleUUID();
//            multipartFile = new MockMultipartFile(fileName, multipartFile.getOriginalFilename(), contentType, fileInputStream);

            MultipartFile multipartFile1 = FileUtil.fileToMultipartFile(tempFile);
            boolean success = tempFile.delete();
            log.info("删除临时file success：{}", success);
            return multipartFile1;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("txt文档转换doc失败");
        } finally {
            assert os != null;
            os.close();
        }
        return multipartFile;
    }


    /**
     * 压缩成ZIP 方法2  一次性压缩多个文件
     *
     * @param srcFiles    需要压缩的文件列表
     * @param zipFileName 压缩文件输出
     * @throws RuntimeException 压缩失败会抛出运行时异常
     */
    public static void toZip(String zipFileName, List<File> srcFiles) throws Exception {
        long start = System.currentTimeMillis();
        ZipOutputStream zos = null;
        try {
            FileOutputStream fileOutputStream = new FileOutputStream(zipFileName);
            zos = new ZipOutputStream(fileOutputStream);
            for (File srcFile : srcFiles) {
                compress(srcFile, zos, srcFile.getName(), true);
            }
            long end = System.currentTimeMillis();
            System.out.println("压缩完成，耗时：" + (end - start) + " 毫秒");
        } catch (Exception e) {
            throw new RuntimeException("zip error from ZipUtils", e);
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 递归压缩方法
     *
     * @param sourceFile       源文件
     * @param zos              zip输出流
     * @param name             压缩后的名称
     * @param KeepDirStructure 是否保留原来的目录结构,true:保留目录结构;
     *                         false:所有文件跑到压缩包根目录下(注意：不保留目录结构可能会出现同名文件,会压缩失败)
     * @throws Exception
     */
    public static void compress(File sourceFile, ZipOutputStream zos, String name,
                                boolean KeepDirStructure) throws Exception {

        if (sourceFile.isFile()) {
            // 向zip输出流中添加一个zip实体，构造器中name为zip实体的文件的名字
            zos.putNextEntry(new ZipEntry(name));
            // copy文件到zip输出流中
            int len;
            FileInputStream in = new FileInputStream(sourceFile);
            while ((len = in.read(buf)) != -1) {
                zos.write(buf, 0, len);
            }
            // Complete the entry
            zos.closeEntry();
            in.close();
        } else {
            File[] listFiles = sourceFile.listFiles();
            if (listFiles == null || listFiles.length == 0) {
                // 需要保留原来的文件结构时,需要对空文件夹进行处理
                if (KeepDirStructure) {
                    // 空文件夹的处理
                    zos.putNextEntry(new ZipEntry(name + "/"));
                    // 没有文件，不需要文件的copy
                    zos.closeEntry();
                }
            } else {
                for (File file : listFiles) {
                    // 判断是否需要保留原来的文件结构
                    if (KeepDirStructure) {
                        // 注意：file.getName()前面需要带上父文件夹的名字加一斜杠,
                        // 不然最后压缩包中就不能保留原来的文件结构,即：所有文件都跑到压缩包根目录下了
                        compress(file, zos, name + "/" + file.getName(), KeepDirStructure);
                    } else {
                        compress(file, zos, file.getName(), KeepDirStructure);
                    }

                }
            }
        }
    }

    protected void getPage(Pageable pageable) {
        String order = null;
        order = pageable.getSort().toString();
        order = order.replace(":", "");
        if ("UNSORTED".equals(order)) {
            order = "create_time desc";
        }
        PageHelper.startPage(pageable.getPageNumber(), pageable.getPageSize(), order);
    }
}
