/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.bassims.service.dto;

import com.bassims.annotation.Query;
import lombok.Data;
import java.sql.Timestamp;
import java.util.List;

/**
* <AUTHOR>
* @date 2019-09-05
*/
@Data
public class LocalStorageQueryCriteria{

    @Query(blurry = "name,suffix,type,createBy,size")
    private String blurry;

    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> createTime;
    @Query(type = Query.Type.IN, propName = "id")
    private List<Long> ids;

    @Query(type = Query.Type.EQUAL, propName = "nodeId")
    private String nodeId;

    @Query(type = Query.Type.EQUAL, propName = "id")
    private Long id;

    @Query(type = Query.Type.EQUAL, propName = "code")
    private String code;
}