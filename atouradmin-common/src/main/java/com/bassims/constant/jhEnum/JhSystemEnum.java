package com.bassims.constant.jhEnum;

//import javafx.concurrent.Task;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;

/**
 * <AUTHOR>
 */
public class JhSystemEnum {

    //    项目所属大区    1:华东开发战区,2:华北开发战区,3:华南开发战区,4:华中开发战区,5:华西开发战区,6:东北开发战区,7:轻居特战队,8:X特战队
    @Getter
    @AllArgsConstructor
    public enum regionEnum {
        /**
         * 华东开发战区
         */
        EAST_CHINA_DEVELOLMENT_ZONE("1", "华东开发战区", "jiangsuzone"),
        /**
         * 华北开发战区
         */
        NORTH_CHINA_DEVELOPMENT_THEATER("2", "华北开发战区", "jiangsuzone"),
        /**
         * 华南开发战区
         */
        SOUTH_CHINA_DEVELOPMENT_ZONE("3", "华南开发战区", "jiangsuzone"),
        /**
         * 华中开发战区
         */
        CENTRAL_CHINA_DEVELOPMENT_ZONE("4", "华中开发战区", "jiangsuzone"),
        /**
         * 华西开发战区
         */
        WEST_CHINA_DEVELOPMENT_ZONE("5", "华西开发战区", "jiangsuzone"),
        /**
         * 东北开发战区
         */
        NORTHEAST_DEVELOPMENT_ZONE("6", "东北开发战区", "jiangsuzone"),
        /**
         * 轻居特战队
         */
        LIGHT_HOUSE_SPECIAL_FORCES("7", "轻居特战队", "jiangsuzone"),
        /**
         * X特战队
         */
        X_FORCE("8", "X特战队", "jiangsuzone");


        private String code;
        private String label;
        private String value;


        public static String getConstructPartition(String constructPartition) {
            regionEnum[] values = regionEnum.values();
            for (regionEnum value : values) {
                if (value.getCode().equals(constructPartition)) {
                    return value.getValue();
                }
            }
            return "";
        }
    }

    @Getter
    @AllArgsConstructor
    public enum isAcceptRoom {
        /**
         * 未验收
         */
        NOT_ACCEPTED("未验收", "0"),
        /**
         * 已验收
         */
        ACCEPTED("已验收", "1");

        private String key;
        private String value;
    }


    @Getter
    @AllArgsConstructor
    public enum classification {
        /**
         * 启用
         */
        ONE_VOTE_VETO("一票否决", "one_vote_veto"),
        /**
         * 启用
         */
        MANDATORY_ITEMS("必备项", "mandatory_items"),
        /**
         * 启用
         */
        IMPORTANT_ITEMS("重要项", "important_items"),
        /**
         * 禁用
         */
        GENERAL_ITEMS("一般项", "general_items");

        private String label;
        private String value;
    }

    @Getter
    @AllArgsConstructor
    public enum acceptance {

        QUALIFIED("合格", "qualified"),

        UNQUALIFIED("不合格", "unqualified"),

        DEFAULT("缺省", "default");

        private String label;
        private String value;

        public static String getAcceptance(String value) {
            acceptance[] values = acceptance.values();
            for (acceptance task : values) {
                if (task.getValue().equals(value)) {
                    return task.getLabel();
                }
            }
            return "";
        }
    }

    /*是否总包*/
    @Getter
    @AllArgsConstructor
    public enum generalContractingOrNot {

        YES("是", "yes"),

        NO("否", "no");

        private String label;
        private String value;
    }


    /**
     * 商品属性
     */
    @Getter
    @AllArgsConstructor
    public enum ProductStatus {
        /**
         * 启用
         */
        PSS001("启用", "PSS001"),
        /**
         * 禁用
         */
        PSS002("禁用", "PSS002");

        private String label;
        private String value;
    }

    /**
     * 增加方向
     */
    @Getter
    @AllArgsConstructor
    public enum AddedType {
        /**
         * 增加
         */
        ADT001("增加", "ADT001"),
        /**
         * 扣减
         */
        ADT002("扣减", "ADT002");

        private String label;
        private String value;
    }

    /**
     * 邀请方式
     */
    @Getter
    @AllArgsConstructor
    public enum InviteType {
        /**
         * 链接邀请
         */
        ITT001("链接邀请", "ITT001"),
        /**
         * 小程序邀请
         */
        ITT002("小程序邀请", "ITT002");

        private String label;
        private String value;
    }

    /**
     * 支付方式
     */
    @Getter
    @AllArgsConstructor
    public enum PayType {
        /**
         * 钱包
         */
        PYT001("钱包余额支付", "PYT001"),
        /**
         * 微信
         */
        PYT002("微信支付", "PYT002"),
        /**
         * 积分支付
         */
        PYT003("积分支付", "PYT003");

        private String label;
        private String value;
    }

    /**
     * 订单状态
     */
    @Getter
    @AllArgsConstructor
    public enum OrderStatus {
        /**
         * 待付款
         */
        ODS001("待付款", "ODS001"),
        /**
         * 已完成
         */
        ODS002("已完成", "ODS002"),
        /**
         * 已取消
         */
        ODS003("已取消", "ODS003");

        private String label;
        private String value;
    }

    /**
     * 订单类型
     */
    @Getter
    @AllArgsConstructor
    public enum OrderType {
        /**
         * 购买订单
         */
        ODT001("购买订单", "ODT001"),
        /**
         * 充值订单
         */
        ODT002("充值订单", "ODT002"),
        /**
         * 兑换订单
         */
        ODT003("兑换订单", "ODT003");

        private String label;
        private String value;
    }

    /**
     * 充值类型
     */
    @Getter
    @AllArgsConstructor
    public enum RechargeType {
        /**
         * 固定金额充值
         */
        RCT001("固定金额充值", "RCT001"),
        /**
         * 自定义金额充值
         */
        RCT002("自定义金额充值", "RCT002");

        private String label;
        private String value;
    }

    /**
     * 订单来源
     */
    @Getter
    @AllArgsConstructor
    public enum ResourceType {
        /**
         * 手机应用
         */
        APP("手机应用", "APP"),
        /**
         * 微信小程序
         */
        WXMIN("微信小程序", "WXMIN"),
        /**
         * 网页
         */
        WEB("网页", "WEB");

        private String label;
        private String value;
    }

    /**
     * 商品类型
     */
    @Getter
    @AllArgsConstructor
    public enum ProductType {
        /**
         * 手机应用
         */
        PDT001("散装商品", "PDT001"),
        /**
         * 微信小程序
         */
        PDT002("桶装商品", "PDT002"),
        /**
         * 微信小程序
         */
        PDT003("优惠商品", "PDT003"),
        /**
         * 网页
         */
        PDT004("充值商品", "PDT004");

        private String label;
        private String value;
    }

    /**
     * 优惠券类型
     */
    @Getter
    @AllArgsConstructor
    public enum CouponType {
        /**
         * 抵用券
         */
        RCT001("抵用券", "CPT001"),
        /**
         * 提货券
         */
        RCT002("提货券", "CPT002"),
        /**
         * 提货券
         */
        RCT003("充值券", "CPT003");

        private String label;
        private String value;
    }

    /**
     * 0.
     * 站点状态
     */
    @Getter
    @AllArgsConstructor
    public enum StationStatus {
        /**
         * 正常
         */
        SSS001("正常", "SSS001"),
        /**
         * 禁用
         */
        SSS002("禁用", "SSS002");

        private String label;
        private String value;
    }

    /**
     * 站点状态
     */
    @Getter
    @AllArgsConstructor
    public enum AccessType {
        /**
         * 正常
         */
        ACT001("积分兑换", "ACT001"),
        /**
         * 禁用
         */
        ACT002("充值返利", "ACT002"),
        /**
         * 活动
         */
        ACT003("活动", "ACT003");

        private String label;
        private String value;
    }

    /**
     * 站点状态
     */
    @Getter
    @AllArgsConstructor
    public enum CouponStatus {
        /**
         * 正常
         */
        CPS001("未使用", "CPS001"),
        /**
         * 正常
         */
        CPS002("已使用", "CPS002"),
        /**
         * 禁用
         */
        CPS003("已过期", "CPS003");

        private String label;
        private String value;
    }

    /**
     * 站点状态
     */
    @Getter
    @AllArgsConstructor
    public enum EmissionLevel {
        /**
         * 国四
         */
        EML001("国四", "EML001"),
        /**
         * 国五
         */
        EML002("国五", "EML002"),
        /**
         * 国六
         */
        EML003("国六", "EML003");

        private String label;
        private String value;
    }

    /**
     * 项目性质: 1特许,2直营
     */
    @Getter
    @AllArgsConstructor
    public enum businessNatureType {
        /**
         * 直营店
         */
        DIRECT("直营店", "direct", 2),
        /**
         * 特许店
         */
        JOIN("特许店", "join", 1);

        private String label;
        private String value;
        private Integer projectNature;

        public static String getBusinessNature(Integer projectNature) {
            //项目性质: 1特许,2直营
            businessNatureType[] values = businessNatureType.values();
            for (businessNatureType value : values) {
                if (value.getProjectNature().equals(projectNature)) {
                    return value.getValue();
                }
            }
            return "";
        }
    }

    /**
     * 显示状态
     */
    @Getter
    @AllArgsConstructor
    public enum ShowStatus {
        /**
         * 启用
         */
        SWS001("启用", "SWS001"),
        /**
         * 禁用
         */
        SWS002("禁用", "SWS002");

        private String label;
        private String value;
    }

    /**
     * 分润类型
     */
    @Getter
    @AllArgsConstructor
    public enum ShareType {
        /**
         * 加注站分润
         */
        SAT001("加注站分润", "SAT001"),
        /**
         * 加注员分润
         */
        SAT002("加注员分润", "SAT002"),
        /**
         * 分销员分润
         */
        SAT003("分销员分润", "SAT003");

        private String label;
        private String value;
    }

    /**
     * 操作状态
     */
    @Getter
    @AllArgsConstructor
    public enum TransferStatus {
        /**
         * 未操作
         */
        TFS001("未操作", "TFS001"),
        /**
         * 已操作
         */
        TFS002("已操作", "TFS002");

        private String label;
        private String value;
    }


    /**
     * 绑定状态
     */
    @Getter
    @AllArgsConstructor
    public enum BindStatus {
        /**
         * 未绑定
         */
        BDS001("未绑定", "BDS001"),
        /**
         * 已绑定
         */
        BDS002("已绑定", "BDS002"),
        /**
         * 解除绑定
         */
        BDS003("解除绑定", "BDS003");

        private String label;
        private String value;
    }

    /**
     * 审核状态
     */
    @Getter
    @AllArgsConstructor
    public enum AuditStatus {
        /**
         * 待审核
         */
        ADS001("待审核", "ADS001"),
        /**
         * 已审核
         */
        ADS002("已审核", "ADS002");

        private String label;
        private String value;
    }

    /**
     * 审核结果
     */
    @Getter
    @AllArgsConstructor
    public enum AuditResult {
        /**
         * 审核中
         */
        ADR001("-", "ADR001"),
        /**
         * 通过
         */
        ADR002("通过", "ADR002"),
        /**
         * 未通过
         */
        ADR003("未通过", "ADR003");

        private String label;
        private String value;
    }

    /**
     * 渠道类型
     */
    @Getter
    @AllArgsConstructor
    public enum ChannelType {
        /**
         * 政府渠道
         */
        CNT001("政府渠道", "CNT001"),
        /**
         * 代工渠道
         */
        CNT002("代工渠道", "CNT002"),
        /**
         * 加注渠道
         */
        CNT003("加注渠道", "CNT003"),
        /**
         * 其他渠道
         */
        CNT004("其他渠道", "CNT004");

        private String label;
        private String value;
    }

    /**
     * 区域类型
     */
    @Getter
    @AllArgsConstructor
    public enum RegionType {
        /**
         * 启用
         */
        EC("华东", "EC"),
        /**
         * 华南
         */
        SC("华南", "SC"),
        /**
         * 华北
         */
        NC("华北", "NC"),
        /**
         * 东北
         */
        NE("东北", "NE"),
        /**
         * 西北
         */
        NW("西北", "NW"),
        /**
         * 西南
         */
        SW("西南", "SW"),


        /**
         * 北区
         */
        NORTH("北区", "north"),
        /**
         * 华东
         */
        EAST("华东", "east"),
        /**
         * 西南
         */
        SOUTHWEST("西南", "southwest"),
        /**
         * 华中
         */
        MIDDLE("华中", "middle"),
        /**
         * 西北
         */
        NORTHWEST("西北", "northwest"),
        /**
         * 华南
         */
        SOUTH("华南", "south");

        private String label;
        private String value;
    }

    /**
     * 资源类型
     */
    @Getter
    @AllArgsConstructor
    public enum SourceType {
        /**
         * 车队
         */
        SRT001("车队", "SRT001"),
        /**
         * 汽修店
         */
        SRT002("汽修店", "SRT002"),
        /**
         * 加油站
         */
        SRT003("加油站", "SRT003"),
        /**
         * 其他/无
         */
        SRT004("其他/无", "SRT004");

        private String label;
        private String value;

        public static String bt(String type) {
            String[] types = type.split(",");
            List<String> list = new ArrayList<>();
            for (String s : types) {
                list.add(SourceType.valueOf(s).getLabel());
            }
            return String.join(",", list);
        }
    }

    /**
     * 加注站服务
     */
    @Getter
    @AllArgsConstructor
    public enum StationService {
        /**
         * 洗漱
         */
        STT001("洗漱", "STT001"),
        /**
         * 汽修
         */
        STT002("汽修", "STT002"),
        /**
         * 便利店
         */
        STT003("便利店", "STT003"),
        /**
         * 收寄快递
         */
        STT004("收寄快递", "STT004"),
        /**
         * 卫生间
         */
        STT005("卫生间", "STT005"),
        /**
         * 星选尿素站
         */
        STT006("星选尿素站", "STT006"),
        /**
         * 加水
         */
        STT007("加水", "STT007"),
        /**
         * 餐饮
         */
        STT008("餐饮", "STT008");

        private String label;
        private String value;
    }

    /**
     * 加注站服务
     */
    @Getter
    @AllArgsConstructor
    public enum SysConfig {
        /**
         * 分销员分佣比例
         */
        SCF001("分销员分佣比例", "SCF001"),
        /**
         * 加注员分佣比例
         */
        SCF002("加注员分佣比例", "SCF002"),
        /**
         * 手续费比列
         */
        SCF003("手续费比列", "SCF003"),
        /**
         * 消费送积分比例
         */
        SCF004("消费送积分比例", "SCF004"),
        /**
         * 首次完善送积分
         */
        SCF005("首次完善送积分", "SCF005"),
        /**
         * 邀请信用户，邀请人获得
         */
        SCF006("邀请信用户，邀请人获得", "SCF006"),
        /**
         * 邀请用户首次完善信息，邀请人获得积分
         */
        SCF007("邀请用户首次完善信息，邀请人获得积分", "SCF007"),
        /**
         * 邀请用户首次加注，邀请人获得积分
         */
        SCF008("邀请用户首次加注，邀请人获得积分", "SCF008"),
        /**
         * 洗漱
         */
        SCF009("充值大于多少送多少积分", "SCF009"),
        /**
         * 每天最多提现申请次数
         */
        SCF010("每天最多提现申请次数", "SCF010"),
        /**
         * 每日签到送积分
         */
        SCF011("每日签到送积分", "SCF011"),
        /**
         *
         */
        SCF012("分销员绑定周期", "SCF012");

        private String label;
        private String value;
    }

    /**
     * 渠道类型
     */
    @Getter
    @AllArgsConstructor
    public enum RegisterType {
        /**
         * 政府渠道
         */
        RGT001("自主注册", "RGT001"),
        /**
         * 代工渠道
         */
        RGT002("用户推荐", "RGT002"),
        /**
         * 加注渠道
         */
        RGT003("加注员推荐", "RGT003"),
        /**
         * 其他渠道
         */
        RGT004("分销员推荐", "RGT004");

        private String label;
        private String value;
    }

    /**
     * 渠道类型
     */
    @Getter
    @AllArgsConstructor
    public enum LeaderLevel {
        /**
         * 政府渠道
         */
        A("A", "A"),
        /**
         * 代工渠道
         */
        B("B", "B"),
        /**
         * 加注渠道
         */
        C("B", "C"),
        /**
         * 其他渠道
         */
        D("D", "D");

        private String label;
        private String value;
    }

    /**
     * 渠道类型
     */
    @Getter
    @AllArgsConstructor
    public enum ApplyStatus {
        /**
         * 政府渠道
         */
        APS001("未处理", "APS001"),
        /**
         * 代工渠道
         */
        APS002("已处理", "APS002");

        private String label;
        private String value;
    }

    /**
     * 渠道类型
     */
    @Getter
    @AllArgsConstructor
    public enum WithdrawStatus {
        /**
         * 政府渠道
         */
        WDS001("待打款", "WDS001"),
        /**
         * 代工渠道
         */
        WDS002("已打款", "WDS002"),
        /**
         * 代工渠道
         */
        WDS003("打款失败", "WDS003");

        private String label;
        private String value;
    }

    /**
     * 渠道类型
     */
    @Getter
    @AllArgsConstructor
    public enum RewardType {
        /**
         * 钱包余额
         */
        RWT001("钱包余额", "RWT001"),
        /**
         * 优惠券
         */
        RWT002("优惠券", "RWT002"),
        /**
         * 积分
         */
        RWT003("积分", "RWT003");

        private String label;
        private String value;
    }

    /**
     * 活动类型
     */
    @Getter
    @AllArgsConstructor
    public enum ActivityType {
        /**
         * 政府渠道
         */
        AVT001("注册", "AVT001"),
        /**
         * 代工渠道
         */
        AVT002("消费", "AVT002"),
        /**
         * 代工渠道
         */
        AVT003("拉新", "AVT003"),
        /**
         * 代工渠道
         */
        AVT004("完善信息", "AVT004"),
        /**
         * 代工渠道
         */
        AVT005("积分兑换", "AVT005"),
        /**
         * 代工渠道
         */
        AVT006("手动发券", "AVT006");

        private String label;
        private String value;
    }

    /**
     * 活动类型
     */
    @Getter
    @AllArgsConstructor
    public enum ActivityStatus {
        /**
         * 政府渠道
         */
        AVS001("未开始", "AVS001"),
        /**
         * 代工渠道
         */
        AVS002("进行中", "AVS002"),
        /**
         * 代工渠道
         */
        AVS003("已结束", "AVS003");

        private String label;
        private String value;
    }

    /**
     * 活动类型
     */
    @Getter
    @AllArgsConstructor
    public enum ExpireType {
        /**
         * 政府渠道
         */
        EPT001("指定天数", "EPT001"),
        /**
         * 代工渠道
         */
        EPT002("指定日期", "EPT002");

        private String label;
        private String value;
    }

    /**
     * 活动类型
     */
    @Getter
    @AllArgsConstructor
    public enum IdType {
        /**
         * 政府渠道
         */
        IDT001("指定天数", "IDT001"),
        /**
         * 代工渠道
         */
        IDT002("指定日期", "IDT002"),
        /**
         * 代工渠道
         */
        IDT003("指定日期", "IDT003"),
        /**
         * 代工渠道
         */
        IDT004("指定日期", "IDT004"),
        /**
         * 代工渠道
         */
        IDT005("指定日期", "IDT004");

        private String label;
        private String value;
    }

    /**
     * 活动类型
     */
    @Getter
    @AllArgsConstructor
    public enum NodeType {
        /**
         * 展示标题
         */
        SHOW_LABEL("展示标题", "show_label"),
        /**
         * 文件
         */
        FILE_UPLOAD("文件", "file_upload"),
        /**
         * 下拉选择
         */
        FORM_SELECT("下拉选择", "form_select"),
        /**
         * 下拉附件（宣讲-资料库）  10.10
         */
        FORM_SELECT_FILE("下拉附件", "form_select_file"),
        /**
         * 附件展示（资料库）  10.18
         */
        FILE_SHOW("附件展示", "file_show"),
        /**
         * 日期
         */
        FORM_DATE("日期", "form_date"),
        /**
         * 文本框
         */
        TEXTAREA("文本框", "textarea"),
        /**
         * 普通输入框
         */
        INPUT_SHOW_BLOCK("输入框", "input_show_block"),
        /**
         * 数字输入框
         */
        DIGITAL_SHOW_BLOCK("数字输入框", "digital_show_block"),
        /**
         * 单选框
         */
        RADIO_BUTTON("单选框", "radio_button"),
        /**
         * 单选框
         */
        RADIO_BUTTON_1("单选框", "radio_button_1"),
        /**
         * 单选框
         */
        SINGLE("单选框", "single"),
        /**
         * 多选
         */
        MULTIPLE("多选", "multiple"),
        /**
         * 模糊多选
         */
        FUZZY_MULTI_INTER("模糊多选接口", "fuzzy_multi_inter"),
        /**
         * 模糊查询
         */
        FUZZY_SEARCH("模糊查询", "fuzzy_search"),
        /**
         * 表格
         */
        LIST("表格", "list"),
        /**
         * 动态表格
         */
        DYNAMIC_CONDITION_LIST("动态表格", "dynamic_condition_list"),
        /**
         * 动态表格的标题  11.02
         */
        DYNAMIC_CONDITION_LIST_TAB("动态表格的标题", "dynamic_condition_list_tab"),
        /**
         * 动态表格的表头  11.02
         */
        DYNAMIC_CONDITION_LIST_HEADER("动态表格的表头", "dynamic_condition_list_header"),
        /**
         * 动态表格的数据值  10.18
         */
        DYNAMIC_CONDITION_LIST_VALUE("动态表格的数据值 ", "dynamic_condition_list_value"),
        /**
         * 动态表单   12.23目前没做
         */
        DYNAMIC_FORM("动态表单", "dynamic_form"),
        /**
         * 动态表单的值  10.28
         */
        DYNAMIC_FORM_VALUE("动态表格的值", "dynamic_form_value"),
        /**
         * 垂直输入框
         */
        VERTICAL_INPUT_SHOW("垂直输入框", "vertical_input_show"),
        /**
         * 展示分类标题
         */
        SHOW_TAB("展示分类标题", "show_tab"),
        /**
         * 表格数据
         */
        TABLE_VALUE("表格", "table_value"),
        /**
         * 展示注释
         */
        SHOW_NOTE("展示注释", "show_note"),
        /**
         * 动态表格弹窗 11.12
         */
        DYNAMIC_TABLE_POPUP("动态表格弹窗", "dynamic_table_popup"),
        /**
         * hlm系统推送的url数据 11.21   废弃-hlm系统过来的数据，会重新保存到acms表
         */
        URL_ATTACHMENT_DISPLAY("hlm系统推送的url数据", "url_attachment_display"),
        /**
         * 蒙版弹窗材料
         */
        MASK_POPUP("蒙版弹窗（材料）", "mask_popup"),
        /**
         * 蒙版弹窗
         */
        ROOM_MASK_POPUP("蒙版弹窗(房间号)", "room_mask_popup"),
        /**
         * 附件详情
         */
        ANNEX_DETAILS("附件详情", "annex_details"),
        /**
         * 常规信息展示
         */
        GENERAL_INFORMATION_PRESENTATION("常规信息展示", "general_information_presentation"),
        /**
         * 审图类型    废弃-目前前端根据 【审图按钮】的类型来判断是否审图
         */
        FILE_UPLOAD_TYPE_REVIEW("审图类型(上传并审图)", "file_upload_type_review"),
        /**
         * 审图类型展示    废弃-目前前端根据 【审图按钮】的类型来判断是否审图
         */
        SHOW_FILE_UPLOAD_TYPE_REVIEW("审图类型展示(审图)", "show_file_upload_type_review"),
        /**
         * 审图按钮
         */
        DRAWING_REVIEW_BUTTON("审图按钮", "drawing_review_button"),
        /**
         * 图纸列表 12.27
         */
        DRAWING_LIST("图纸列表", "drawing_list"),
        /**
         * 资料管理展示
         */
        DATA_MANAGEMENT("资料管理展示", "data_management"),
        /**
         * 竣工验收验收单弹框
         */
        RECEIPT_POPUP("竣工验收验收单弹框", "receipt_popup");

        private String label;
        private String value;
    }

    @Getter
    @AllArgsConstructor
    public enum RelationTypeEnum {

        STA("STA", "干系人");

        private String key;
        private String spec;


    }


    @Getter
    @AllArgsConstructor
    public enum StakeholderStatusEnum {

        STA_STATUS0("in_term", "正常"),
        STA_STATUS1("off_term", "离项"),
        STA_STATUS2("--", "未到期"),
        STA_STATUS3("temporarily_off_term", "暂时离项");
        private String key;
        private String spec;


    }

    @Getter
    @AllArgsConstructor
    public enum NodeStatusEnum {
// 任务状态变更   已提交和不确定任务当前系统不存在
//        NODE_STATUS4("task_commit", "已提交"),
//        NODE_STATUS3("task_indeterminacy", "不确定")

        //        1. 未开始   add； 任务阶段未到达；
//        2. 未提交   add； 当前任务正在进行；
//        4. 审核中  （审批节点状态）；
//        5. 已完成     任务完成；
        NODE_STATUS5("--", "--"),
        NODE_STATUS0("task_unfin", "未开启"),
        NODE_STATUS6("task_uncommitted", "未提交"),
        NODE_STATUS2("task_submit", "审批中"),
        NODE_STATUS1("task_fin", "已完成");  //原来状态是已提交，现在区分审批中和已提交的概念


        private String key;
        private String spec;

        public static String getNodeStatus(String nodeStatus) {
            NodeStatusEnum[] values = NodeStatusEnum.values();
            for (NodeStatusEnum value : values) {
                if (value.getKey().equals(nodeStatus)) {
                    return value.getSpec();
                }
            }
            return "未开启";
        }


    }

    @Getter
    @AllArgsConstructor
    public enum ProjectStatusEnum {

        PROJECT_PREPARE("project_prepare", "筹备中", "con-001,con-002"),
        PROJECT_CONIMPLEMENT("project_conimplement", "施工中", "con-003,con-004"),
        PROJECT_SETTLEMENT("project_settlement", "结算中", "con-005"),
        PROJECT_FINISH("project_finish", "已完成", ""),
        PROJECT_DELETE("project_delete", "已删除", "");

        private String key;
        private String spec;
        private String nodeInfo;

        public static Boolean isNoFinish(String status) {
            List<String> statusStr = Arrays.asList("project_prepare", "project_conimplement", "project_settlement");
            if (statusStr.contains(status)) {
                return true;
            }
            return false;
        }

        public static String getKeyByCode(String code) {
            ProjectStatusEnum[] values = ProjectStatusEnum.values();
            for (ProjectStatusEnum value : values) {
                String[] split = value.getNodeInfo().split(",");
                List<String> strings = Arrays.asList(split);
                if (strings.contains(code)) {
                    return value.getKey();
                }

            }
            return "";
        }

    }

    @Getter
    @AllArgsConstructor
    public enum TaskPhaseEnum {
        ENGINEERING("engineering", "工程阶段", "con-001"),
        DESIGN("design", "设计阶段", "con-002"),
        DECISION_MAKING("decision_making", "决策信息模板", "dec-00"),
        CONSTRUCTION_LOG("construction_log", "筹备阶段", "con-001"),
        PUBLIC_AREA_DESIGN("public_area_design", "设计阶段", "con-002"),
        DEEPENING_PLAN("deepening_plan", "深化方案模板", "dep-00"),
        VISA_FILING("visa_filing", "签证报备模版", "con-003"),

        RENEWAL_REFURBISHMENT("renewal_refurbishment", "焕新工程", "eng-00"),


        PREPARE_PHASE("prepare_phase", "筹备阶段", "con-001"),
        DESIGN_PHASE("design_phase", "设计阶段", "con-002"),
        CONPREPARE_PHASE("conprepare_phase", "施工准备阶段", "con-003"),
        CONIMPLEMENT_PHASE("conimplement_phase", "施工阶段", "con-004"),
        SETTLEMENT_PHASE("settlement_phase", "结算阶段", "con-005");

        private String key;
        private String spec;
        private String code;

        public static String getKey(String code) {
            TaskPhaseEnum[] values = TaskPhaseEnum.values();
            for (TaskPhaseEnum value : values) {
                if (value.getCode().equals(code)) {
                    return value.getKey();
                }
            }
            return "";
        }
    }

    @Getter
    @AllArgsConstructor
    public enum TaskCodeEnum {
        CON_001("con-001", "筹备", "选址踏勘阶段"),
        CON_002("con-002", "设计", "设计阶段"),
        CON_003("con-003", "准备", "施工准备阶段"),
        CON_004("con-004", "施工", "施工阶段"),
        CON_005("con-005", "结算", "结算阶段");

        private String key;
        private String simplie;
        private String spec;

        public static String bt(String type) {
            TaskCodeEnum[] values = TaskCodeEnum.values();
            for (TaskCodeEnum task : values) {
                if (task.getKey().equals(type)) {
                    return task.getSimplie();
                }
            }
            return "";
        }


    }

    /**
     * 角色
     */
    @Getter
    @AllArgsConstructor
    public enum JobEnum {
        GCZJ("gczj", "工程总监"),
        ZBWFZG("zbwfzg", "总部网发主管"),
        ZBWF("zbwf", "总部网发"),
        CSGSWFJL("csgswfjl", "城市公司网发经理"),
        CSGSCW("csgscw", "城市公司财务"),
        SEYJJL("seyjjl", "SE营建经理"),
        SESJJL("sesjjl", "SE设计经理"),
        SELQ("selq", "SE刘群"),
        IPCCB("ipccb", "IPC成本"),
        IPCCG("ipccg", "IPC采购"),
        ZXZB("zxzb", "装修总包"),
        YWGLY("ywgly", "业务管理员"),
        ZBHTZRR("zbhtzrr", "增补合同责任人"),
        CWZG("cwzg", "财务主管"),
        CGCBZG("cgcbzg", "采购成本主管"),
        SEVP("sevp", "SE VP"),
        SEYJJLZG("seyjjlzg", "SE营建经理主管"),

        CSXMJL("csxmjl", "厂商项目经理"),
        YZXMJL("yzxmjl", "业主项目经理"),
        RZSJ("rzsj", "软装设计"),

        GCJL("gcjl", "项目经理"),
        QYJL("qyjl", "区域经理"),
        ZBJL("zbjl", "总部经理"),
        ZBZXFZR("zbzxfzr", "总部中心负责人"),
        FXGK("fxgk", "风险管控"),
        SJS("sjs", "设计师"),
        SJJL("sjjl", "设计经理"),
        SJFZR("sjfzr", "设计负责人"),
        CSGLY("csgly", "厂商管理员"),
        JGCCSGLY("jgccsgly", "甲供材厂商管理员"),
        JGCZL("jgczl", "甲供材助理"),
        JGCJL("jgcjl", "甲供材经理"),
        GCCW("gccw", "工程财务"),
        DDCJR("ddcjr", "订单创建人");

        private String key;
        private String spec;


    }


    @Getter
    @AllArgsConstructor
    public enum TaskNameEnum {
        TODO_TASK("todo_task", "待办任务"),
        REMIND_TASK("remind_task", "提醒任务"),
        OVERDUE_TASK("overdue_task", "逾期任务"),
        MESSAGE_NOTICE("message_notice", "消息通知");

        private String key;
        private String spec;


    }

    @Getter
    @AllArgsConstructor
    public enum NodeCodeSEEnum {
        NODE_101("con-00101", "创建项目", 1),
        NODE_102("con-00102", "项目勘察", 2),
        NODE_103("con-00103", "施工派案", 3),
        NODE_201("con-00201", "前置证照", 4),
        NODE_301("con-00301", "门店材料补录", 5),
        NODE_701("con-00701", "分配设计师", 6),
        NODE_702("con-00702", "租赁红线图", 7),
        NODE_703("con-00703", "平面图", 8),
        NODE_704("con-00704", "施工图", 9),
        NODE_705("con-00705", "面积录入", 10),
        NODE_801("con-00801", "联营图", 11),
        NODE_803("con-00803", "增值商户设计图", 12),
        NODE_123("con-00123", "设计图", 13),
        NODE_104("con-00104", "预验收", 14),
        NODE_105("con-00105", "工程进场", 15),
        NODE_106("con-00106", "技术交底", 16),
        NODE_401("con-00401", "预算", 17),
        NODE_402("con-00402", "发包", 18),
        NODE_403("con-00403", "装修合同", 19),
        NODE_501("con-00501", "空调合同", 20),
        NODE_601("con-00601", "消防合同", 21),
        NODE_122("con-00122", "中期验收", 22),
        NODE_107("con-00107", "材料验收", 23),
        NODE_108("con-00108", "隐蔽验收", 24),
        NODE_109("con-00109", "分部分项验收", 25),
        NODE_116("con-00116", "空调验收", 26),
        NODE_117("con-00117", "消防验收", 27),
        NODE_202("con-00202", "后置证照", 28),
        NODE_404("con-00404", "装修首付对账单", 29),
        NODE_137("con-00137", "空调首付对账单", 30),
        NODE_138("con-00138", "消防首付对账单", 31),
        NODE_111("con-00111", "竣工验收", 32),
        NODE_112("con-00112", "移交营运", 33),
        NODE_113("con-00113", "决算", 34),
        NODE_142("con-00142", "工程结算审定单", 35),
        NODE_144("con-00144", "结算审定归档", 36);
      /*
        NODE_114("con-00114","付款确认",37),
        NODE_115("con-00115","对账单汇总",38);*/

        private String key;
        private String spec;
        private int index;

        public static int getIndexByCode(String nodeCode) {
            NodeCodeSEEnum[] values = NodeCodeSEEnum.values();
            for (NodeCodeSEEnum value : values) {
                if (value.getKey().equals(nodeCode)) {
                    return value.getIndex();
                }
            }
            return 0;
        }

        public static Boolean isExists(String nodeCode) {
            NodeCodeSEEnum[] values = NodeCodeSEEnum.values();
            for (NodeCodeSEEnum value : values) {
                if (value.getKey().equals(nodeCode)) {
                    return true;
                }
            }
            return false;
        }

        public static Boolean isCreateApplyNo(String nodeCode) {
            List<String> createApplyNoList = new LinkedList<>();
            Collections.addAll(createApplyNoList, NODE_403.getKey(), NODE_601.getKey(), NODE_501.getKey(), NODE_404.getKey(), NODE_138.getKey(), NODE_137.getKey());
            return createApplyNoList.contains(nodeCode);
        }

        public static Boolean isFirstOrder(String nodeCode) {
            List<String> createApplyNoList = new LinkedList<>();
            Collections.addAll(createApplyNoList, NODE_404.getKey(), NODE_138.getKey(), NODE_137.getKey());
            return createApplyNoList.contains(nodeCode);
        }
    }

    @Getter
    @AllArgsConstructor
    public enum NodeCodeEnum {
        NODE_70103("con-0070103", "设计定位"),
        NODE_70104("con-0070104", "装修设计等级"),
        NODE_10209("con-0010209", "租赁合同面积"),
        NODE_10208("con-0010208", "楼层"),
        NODE_10108("con-0010108", "地址"),
        NODE_10202("con-0010202", "项目编号"),
        NODE_127("con-00127", "预约订单"),
        NODE_12708("con-0012708", "订单状态"),
        con_0020118("con-0020118", "软装设计经理"),
        NODE_0501("con-00501", "报建方式确定"),
        NODE_05("con-0050102", "报建类型"),
        NODE_0110("con-0010110", "立项时间"),
        NODE_0111("con-0010111", "预计进场日期"),
        NODE_0112("con-0010112", "预计开业日期"),
        NODE_0124("con-0010124", "附件"),
        NODE_901("con-00901", "订单创建"),
        NODE_00909("con-00909", "订单结算"),
        NODE_00121("con-00121", "订单付款"),
        NODE_90102("con-0090102", "订单详情(表格)"),
        NODE_90902("con-0090902", "订单结算详情(表格)"),
        NODE_90904("con-0090904", "特殊项目(表格)"),
        NODE_12702("con-0012702", "订单详情(表格)"),
        NODE_0030103("con-0030103", "门店编码"),
        NODE_0030102("con-0030102", "门店名称"),
        NODE_0030109("con-0030109", "门店全称"),
        NODE_0030105("con-0030105", "费用核算部门"),
        NODE_0040405("con-0040405", "费用核算部门"),
        NODE_0030106("con-0030106", "费用承担部门"),
        NODE_0030110("con-0030110", "开业时间"),
        NODE_0030104("con-0030104", "门店地址"),
        NODE_0010322("con-0010322", "空调二次施工方"),
        NODE_0010323("con-0010323", "消防二次施工方"),
        NODE_70607("con-0070607", "净使用面积"),
        NODE_70611("con-0070611", "招商面积"),
        NODE_40184("con-0040184", "施工面积-预算"),
        NODE_40284("con-0040284", "施工面积-发包"),
        NODE_14029("con-0014029", "施工面积-决算"),
        NODE_14355("con-0014355", "施工面积-工程结算审定单"),
        NODE_50109("con-0050109", "对方单位名称-空调合同"),
        NODE_60109("con-0060109", "对方单位名称-消防合同");
        private String key;
        private String spec;


    }

    @Getter
    @AllArgsConstructor
    public enum IsConstructionEnum {
        CON("con", "报建"),
        NON_CON("non_con", "不报建"),
        PROJECT_CON("project_con", "边报建边施工");

        private String key;
        private String spec;


    }


    @Getter
    @AllArgsConstructor
    public enum rolePermissionEnum {
        CITY_POWER("city_power", "城市权限"),
        STA_POWER("sta_power", "干系人权限");

        private String key;
        private String spec;


    }


    @Getter
    @AllArgsConstructor
    public enum externalRoleEnum {
        YZXMJL("yzxmjl", "业主项目经理"),
        CSXMJL("csxmjl", "厂商项目经理");;
        private String key;
        private String spec;
    }


    @Getter
    @AllArgsConstructor
    public enum appCodeEnum {
        APP_01("con-00202", "设计资料提报"),
        APP_02("con-00203", "工程条件确认"),
        CON_00209("con-00209", "PR审批完成"),
        CON_00402("con-00402", "增补PR完成审批"),
        con_00703("con-00703", "结算审核完成"),
        CON_0020904("con-0020904", "预计充值金额(元)"),
        CON_0020905("con-0020905", "预计PR金额(元)"),
        CON_0040204("con-0040204", "确认充值金额(元)"),
        CON_0040205("con-0040205", "确认PR金额(元)"),
        CON_0070304("con-0070304", "结算PR金额(元)"),
        CON_00601("con-00601", "总包进场"),
        CON_00704("con-00704", "增补预算完成审批"),
        CON_00705("con-00705", "增补预算充值完成"),
        CON_00706("con-00706", "增补PR完成审批");

        private String key;
        private String spec;


    }

    @Getter
    @AllArgsConstructor
    public enum approveStatusEnum {

        PENDING_APPROVAL("pending_approval", "待审批"),
        IN_APPROVE("in_approve", "审批中"),
        APPROVE_COMPLETE("approve_complete", "审批完成");

        private String key;
        private String spec;


    }

    @Getter
    @AllArgsConstructor
    public enum approveResultEnum {
        PENDING_APPROVAL("pending_approval", "待审批", ""),
        UNDER_APPROVE("under_approve", "审批中", ""),
        APPROVE_PASS("approve_pass", "通过", "approve"),
        APPROVE_REFUSE("approve_refuse", "撤回", "revoke"),
        APPROVE_REJECT("approve_reject", "驳回", "reject"),
        APPROVE_DIS("approve_dis", "不通过", "disapprove"),
        APPROVE_STOP("approve_stop", "中止", "stop");

        private String key;
        private String spec;
        private String type;

        public static approveResultEnum approveResultPhase(String type) {
            approveResultEnum phase = null;
            approveResultEnum[] values = approveResultEnum.values();
            for (approveResultEnum task : values) {
                if (task.getType().equals(type)) {
                    phase = task;
                    return phase;
                }
            }
            return UNDER_APPROVE;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum AccountCodeEnum {
        CON_00701("con-00701", "结算资料提交"),
        CON_00702("con-00702", "结算资料确认"),
        CON_00703("con-00703", "结算审核完成"),
        CON_00704("con-00704", "增补预算完成审批"),
        CON_00705("con-00705", "增补预算充值完成"),
        CON_00706("con-00706", "增补PR完成审批"),
        CON_00707("con-00707", "结算合同完成");

        private String key;
        private String spec;

        public static Boolean isAccountCode(String type) {
            Boolean flag = Boolean.FALSE;
            AccountCodeEnum[] values = AccountCodeEnum.values();
            for (AccountCodeEnum task : values) {
                if (task.getKey().equals(type)) {
                    flag = Boolean.TRUE;
                }
            }
            return flag;
        }

    }


    @Getter
    @AllArgsConstructor
    public enum AccountPhaseEnum {
        ACCOUNTSUBMIT("accountSubmit", "con-00701", "结算资料提交"),
        ACCOUNTCONFIRM("accountConfirm", "con-00702", "结算资料确认"),
        ACCOUNTFINISH("accountFinish", "con-00703", "结算审核完成"),
        BUDGETFINISH("budgetFinish", "con-00704", "增补预算完成审批"),
        BUDGETRECHARGE("budgetRecharge", "con-00705", "增补预算充值完成"),
        PRAPPROVAL("prApproval", "con-00706", "增补PR完成审批"),
        CONTRACTFINISH("contractFinish", "con-00707", "结算合同完成");

        private String key;
        private String simplie;
        private String spec;

        public static AccountPhaseEnum accountPhase(String type) {
            AccountPhaseEnum phase = null;
            AccountPhaseEnum[] values = AccountPhaseEnum.values();
            for (AccountPhaseEnum task : values) {
                if (task.getSimplie().equals(type)) {
                    phase = task;
                }
            }
            return phase;
        }

    }

    @Getter
    @AllArgsConstructor
    public enum approveBeginEnum {
        IN_ORDER("in_order", "顺序执行"),
        ALL_EXCUTE("all_excute", "同时执行");

        private String key;
        private String spec;


    }

    @Getter
    @AllArgsConstructor
    public enum approveModeEnum {
        ALL_PASS("all_pass", "全部通过"),
        ONE_PASS_PASS("one_pass_pass", "任意一个通过则通过");

        private String key;
        private String spec;


    }

    @Getter
    @AllArgsConstructor
    public enum TaskCCEnum {
        TODO_TASK("todo_task", "已开始"),
        REMIND_TASK("remind_task", "即将逾期"),
        OVERDUE_TASK("overdue_task", "已逾期"),
        MESSAGE_NOTICE("message_notice", "已完成");

        private String key;
        private String spec;

        public static TaskCCEnum getTaskCC(String type) {
            TaskCCEnum phase = null;
            TaskCCEnum[] values = TaskCCEnum.values();
            for (TaskCCEnum task : values) {
                if (task.getKey().equals(type)) {
                    phase = task;
                }
            }
            return phase;
        }

    }

    @Getter
    @AllArgsConstructor
    public enum deleteFlagEnum {
        CREATE_DELETE("create_delete", "创建删除"),
        PROJECT_DELETE("project_delete", "项目删除");

        private String key;
        private String spec;


    }

    @Getter
    @AllArgsConstructor
    public enum projectBeginEnum {
        CON_00104("con-00104", "立项通过"),
        CON_00201("con-00201", "立项信息通过输入"),
        CON_0010110("con-0010110", "立项时间");

        private String key;
        private String spec;


    }

    /**
     * 门店类型
     */
    @Getter
    @AllArgsConstructor
    public enum storeTypeEnum {
        /**
         * 经典版NH
         */
        STORE_NH("store_nh", "经典版NH"),
        /**
         * NSC
         */
        STORE_NSC("store_nsc", "NSC"),
        /**
         * NS2.0
         */
        STORE_NS("store_ns", "NS2.0"),
        /**
         * 基准版NH
         */
        STORE_NHS("store_nhs", "基准版NH"),

        /**
         * 门店
         */
        STORE("store", "门店"),
        /**
         * 大仓
         */
        WAREHOUSE("warehouse", "大仓"),
        /**
         * 办公室
         */
        OFFICE("office", "办公室");

        private String key;
        private String spec;
    }

    /**
     * 证件类型
     */
    @Getter
    @AllArgsConstructor
    public enum licenseTypeEnum {
        /**
         * 前置证照
         */
        PRE_LICENSE("前置证照", "pre_license"),
        /**
         * 后置证照
         */
        POST_LICENSE("后置证照", "post_license");

        private String key;
        private String spec;
    }

    /**
     * <AUTHOR>
     * @description: 输入框码值
     * @date: 2023/10/9
     */
    @Getter
    @AllArgsConstructor
    public enum inputBoxEnum {

        NULL("null", "等于null"),
        NOTNULL("not_null", "不为空");

        private String key;
        private String spec;

    }

    @Getter
    @AllArgsConstructor
    public enum oneNodeCodeEnum {
        NODE_ENG001("eng-001", "筹建启动会"),
        NODE_DES001("des-001", "设计勘测");

        private String key;
        private String spec;


    }

    @Getter
    @AllArgsConstructor
    public enum twoNodeCodeEnum {
        NODE_ENG103("eng-00103", "筹建启动会"),
        NODE_ENG105("eng-00105", "开工申请"),
        NODE_ENG143("eng-00143", "信息一致性"),
        NODE_ENG107("eng-00107", "正式开工"),
        NODE_ENG109("eng-00109", "样板间隐蔽验收"),
        NODE_ENG111("eng-00111", "样板间验收"),
        NODE_ENG115("eng-00115", "隐蔽验收申请"),
        NODE_DES101("des-00101", "设计勘测"),
        NODE_DES167("des-00167", "信息一致性"),
        NODE_DES103("des-00103", "装饰勘测报告"),
        NODE_DES165("des-00165", "机电勘测报告"),
        NODE_DES125("des-00125", "样板间验收"),
        NODE_ENG119("eng-00119", "封板确认"),
        NODE_DES127("des-00127", "确认设计单位");


        private String key;
        private String spec;


    }

    @Getter
    @AllArgsConstructor
    public enum threeNodeCodeEnum {
        NODE_ENG103065("eng-00103065", "筹建启动会-擅自施工"),
        NODE_ENG105041("eng-00105041", "开工申请-项目进度"),
        NODE_ENG107052("eng-00107052", "空调单位进展"),
        NODE_ENG107053("eng-00107053", "进展状态"),
        NODE_ENG107129("eng-00107129", "是否影响进度"),
        NODE_ENG107054("eng-00107054", "单位名称"),
        NODE_ENG107130("eng-00107130", "是否平台供应商"),
        NODE_ENG107055("eng-00107055", "联系人"),
        NODE_ENG107056("eng-00107056", "弱电单位进展"),
        NODE_ENG107057("eng-00107057", "进展状态"),
        NODE_ENG107131("eng-00107131", "是否影响进度"),
        NODE_ENG107058("eng-00107058", "单位名称"),
        NODE_ENG107132("eng-00107132", "是否平台供应商"),
        NODE_ENG107059("eng-00107059", "联系人"),
        NODE_ENG107060("eng-00107060", "消防单位进展"),
        NODE_ENG107061("eng-00107061", "进展状态"),
        NODE_ENG107133("eng-00107133", "是否影响进度"),
        NODE_ENG107062("eng-00107062", "单位名称"),
        NODE_ENG107134("eng-00107134", "是否平台供应商"),
        NODE_ENG107063("eng-00107063", "联系人"),
        NODE_ENG107064("eng-00107064", "样板间装配板"),
        NODE_ENG107065("eng-00107065", "进展状态"),
        NODE_ENG107135("eng-00107135", "是否影响进度"),
        NODE_ENG107066("eng-00107066", "单位名称"),
        NODE_ENG107136("eng-00107136", "是否平台供应商"),
        NODE_ENG107067("eng-00107067", "联系人"),
        NODE_ENG107068("eng-00107068", "样板间家具"),
        NODE_ENG107069("eng-00107069", "进展状态"),
        NODE_ENG107137("eng-00107137", "是否影响进度"),
        NODE_ENG107070("eng-00107070", "单位名称"),
        NODE_ENG107138("eng-00107138", "是否平台供应商"),
        NODE_ENG107071("eng-00107071", "联系人"),
        NODE_ENG107075("eng-00107075", "正式开工-擅自施工表格"),
        NODE_ENG107148("eng-00107148", "质量管理(表格)"),

        NODE_ENG109051("eng-00109051", "空调单位进展"),
        NODE_ENG109052("eng-00109052", "进展状态"),
        NODE_ENG109134("eng-00109134", "是否影响进度"),
        NODE_ENG109053("eng-00109053", "单位名称"),
        NODE_ENG109135("eng-00109135", "是否平台供应商"),
        NODE_ENG109054("eng-00109054", "联系人"),
        NODE_ENG109055("eng-00109055", "弱电单位进展"),
        NODE_ENG109056("eng-00109056", "进展状态"),
        NODE_ENG109136("eng-00109136", "是否影响进度"),
        NODE_ENG109057("eng-00109057", "单位名称"),
        NODE_ENG109137("eng-00109137", "是否平台供应商"),
        NODE_ENG109058("eng-00109058", "联系人"),
        NODE_ENG109059("eng-00109059", "消防单位进展"),
        NODE_ENG109060("eng-00109060", "进展状态"),
        NODE_ENG109138("eng-00109138", "是否影响进度"),
        NODE_ENG109061("eng-00109061", "单位名称"),
        NODE_ENG109139("eng-00109139", "是否平台供应商"),
        NODE_ENG109062("eng-00109062", "联系人"),
        NODE_ENG109063("eng-00109063", "样板间装配板"),
        NODE_ENG109064("eng-00109064", "进展状态"),
        NODE_ENG109140("eng-00109140", "是否影响进度"),
        NODE_ENG109065("eng-00109065", "单位名称"),
        NODE_ENG109141("eng-00109141", "是否平台供应商"),
        NODE_ENG109066("eng-00109066", "联系人"),
        NODE_ENG109067("eng-00109067", "样板间家具"),
        NODE_ENG109068("eng-00109068", "进展状态"),
        NODE_ENG109142("eng-00109142", "是否影响进度"),
        NODE_ENG109069("eng-00109069", "单位名称"),
        NODE_ENG109143("eng-00109143", "是否平台供应商"),
        NODE_ENG109070("eng-00109070", "联系人"),
        NODE_ENG131002("eng-00131002", "质量管理（表格）"),
        NODE_DES103074("des-00103074", "勘测报告（动态表格）"),
        NODE_DES103076("des-00103076", "勘测报告履历（表格）"),

        NODE_ENG103102("eng-00103102", "特许商营建授权人"),
        NODE_ENG103103("eng-00103103", "联系电话"),
        NODE_ENG103104("eng-00103104", "联系邮箱"),
        NODE_ENG103055("eng-00103055", "业主指定对接人员是否变更"),
        NODE_ENG133027("eng-00133027", "验收单（动态列表蒙版）"),
        NODE_ENG133041("eng-00133041", "验收单各总项汇总（动态列表）"),

        NODE_ENG103060("eng-00103060", "预计开工日期"),
        NODE_ENG129058("eng-00129058", "验收人"),
        NODE_ENG129057("eng-00129057", "计划验收日期"),
        NODE_ENG107149(" eng-00107149", "材料管理（表格）");


        private String key;
        private String spec;


    }

    @Getter
    @AllArgsConstructor
    public enum threeNodeCodeUniteSampleAcceptanceEnum {

        NODE_ENG107052("eng-00107052", "空调单位进展", "eng-00109051", "空调单位进展"),
        NODE_ENG107053("eng-00107053", "进展状态", "eng-00109052", "进展状态"),
        NODE_ENG107129("eng-00107129", "是否影响进度", "eng-00109134", "是否影响进度"),
        NODE_ENG107054("eng-00107054", "单位名称", "eng-00109053", "单位名称"),
        NODE_ENG107130("eng-00107130", "是否平台供应商", "eng-00109135", "是否平台供应商"),
        NODE_ENG107055("eng-00107055", "联系人", "eng-00109054", "联系人"),
        NODE_ENG107056("eng-00107056", "弱电单位进展", "eng-00109055", "弱电单位进展"),
        NODE_ENG107057("eng-00107057", "进展状态", "eng-00109056", "进展状态"),
        NODE_ENG107131("eng-00107131", "是否影响进度", "eng-00109136", "是否影响进度"),
        NODE_ENG107058("eng-00107058", "单位名称", "eng-00109057", "单位名称"),
        NODE_ENG107132("eng-00107132", "是否平台供应商", "eng-00109137", "是否平台供应商"),
        NODE_ENG107059("eng-00107059", "联系人", "eng-00109058", "联系人"),
        NODE_ENG107060("eng-00107060", "消防单位进展", "eng-00109059", "消防单位进展"),
        NODE_ENG107061("eng-00107061", "进展状态", "eng-00109060", "进展状态"),
        NODE_ENG107133("eng-00107133", "是否影响进度", "eng-00109138", "是否影响进度"),
        NODE_ENG107062("eng-00107062", "单位名称", "eng-00109061", "单位名称"),
        NODE_ENG107134("eng-00107134", "是否平台供应商", "eng-00109139", "是否平台供应商"),
        NODE_ENG107063("eng-00107063", "联系人", "eng-00109062", "联系人"),
        NODE_ENG107064("eng-00107064", "样板间装配板", "eng-00109063", "样板间装配板"),
        NODE_ENG107065("eng-00107065", "进展状态", "eng-00109064", "进展状态"),
        NODE_ENG107135("eng-00107135", "是否影响进度", "eng-00109140", "是否影响进度"),
        NODE_ENG107066("eng-00107066", "单位名称", "eng-00109065", "单位名称"),
        NODE_ENG107136("eng-00107136", "是否平台供应商", "eng-00109141", "是否平台供应商"),
        NODE_ENG107067("eng-00107067", "联系人", "eng-00109066", "联系人"),
        NODE_ENG107068("eng-00107068", "样板间家具", "eng-00109067", "样板间家具"),
        NODE_ENG107069("eng-00107069", "进展状态", "eng-00109068", "进展状态"),
        NODE_ENG107137("eng-00107137", "是否影响进度", "eng-00109142", "是否影响进度"),
        NODE_ENG107070("eng-00107070", "单位名称", "eng-00109069", "单位名称"),
        NODE_ENG107138("eng-00107138", "是否平台供应商", "eng-00109143", "是否平台供应商"),
        NODE_ENG107071("eng-00107071", "联系人, ", "eng-00109070", "联系人");


        private String key;
        private String spec;
        private String uniteKey;
        private String uniteSpec;

        public static JhSystemEnum.threeNodeCodeUniteSampleAcceptanceEnum getNodeCode(String key) {
            JhSystemEnum.threeNodeCodeUniteSampleAcceptanceEnum phase = null;
            JhSystemEnum.threeNodeCodeUniteSampleAcceptanceEnum[] values = JhSystemEnum.threeNodeCodeUniteSampleAcceptanceEnum.values();
            for (JhSystemEnum.threeNodeCodeUniteSampleAcceptanceEnum task : values) {
                if (task.getKey().equals(key)) {
                    phase = task;
                    return phase;
                }
            }
            return phase;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum threeNodeCodeUniteSampleRoomAcceptanceEnum {

        NODE_ENG107052("eng-00109051", "空调单位进展", "eng-00111095", "空调单位进展"),
        NODE_ENG107053("eng-00109052", "进展状态", "eng-00111096", "进展状态"),
        NODE_ENG107054("eng-00109134", "是否影响进度", "eng-00111178", "是否影响进度"),
        NODE_ENG107055("eng-00109053", "单位名称", "eng-00111097", "单位名称"),
        NODE_ENG107056("eng-00109135", "是否平台供应商", "eng-00111179", "是否平台供应商"),
        NODE_ENG107057("eng-00109054", "联系人", "eng-00111098", "联系人"),
        NODE_ENG107058("eng-00109055", "弱电单位进展", "eng-00111099", "弱电单位进展"),
        NODE_ENG107059("eng-00109056", "进展状态", "eng-00111100", "进展状态"),
        NODE_ENG107060("eng-00109136", "是否影响进度", "eng-00111180", "是否影响进度"),
        NODE_ENG107061("eng-00109057", "单位名称", "eng-00111101", "单位名称"),
        NODE_ENG107062("eng-00109137", "是否平台供应商", "eng-00111181", "是否平台供应商"),
        NODE_ENG107063("eng-00109058", "联系人", "eng-00111102", "联系人"),
        NODE_ENG107064("eng-00109059", "消防单位进展", "eng-00111182", "是否影响进度"),
        //NODE_ENG107065("eng-00109060", "进展状态", "eng-00111103", "消防单位进展"),
        NODE_ENG107066("eng-00109060", "进展状态", "eng-00111104", "进展状态"),
        NODE_ENG107067("eng-00109061", "单位名称", "eng-00111105", "单位名称"),
        //NODE_ENG107068("eng-00109139", "是否平台供应商", "eng-00111105", "单位名称"),
        NODE_ENG107069("eng-00109062", "联系人", "eng-00111106", "联系人"),
        NODE_ENG107070("eng-00109063", "样板间装配板", "eng-00111107", "样板间装配板"),
        NODE_ENG107071("eng-00109064", "进展状态", "eng-00111108", "进展状态"),
        NODE_ENG107072("eng-00109140", "是否影响进度", "eng-00111184", "是否影响进度"),
        NODE_ENG107073("eng-00109065", "单位名称", "eng-00111109", "单位名称"),
        NODE_ENG107074("eng-00109141", "是否平台供应商", "eng-00111185", "是否平台供应商"),
        NODE_ENG107075("eng-00109066", "联系人", "eng-00111110", "联系人"),
        NODE_ENG107076("eng-00109067", "样板间家具", "eng-00111111", "样板间家具"),
        NODE_ENG107077("eng-00109068", "进展状态", "eng-00111112", "进展状态"),
        NODE_ENG107078("eng-00109142", "是否影响进度", "eng-00111186", "是否影响进度"),
        NODE_ENG107079("eng-00109069", "单位名称", "eng-00111113", "单位名称"),
        NODE_ENG107080("eng-00109143", "是否平台供应商", "eng-00111187", "是否平台供应商"),
        NODE_ENG107081("eng-00109070", "联系人", "eng-00111114", "联系人");


        private String key;
        private String spec;
        private String uniteKey;
        private String uniteSpec;

        public static JhSystemEnum.threeNodeCodeUniteSampleRoomAcceptanceEnum getNodeCode(String key) {
            JhSystemEnum.threeNodeCodeUniteSampleRoomAcceptanceEnum phase = null;
            JhSystemEnum.threeNodeCodeUniteSampleRoomAcceptanceEnum[] values = JhSystemEnum.threeNodeCodeUniteSampleRoomAcceptanceEnum.values();
            for (JhSystemEnum.threeNodeCodeUniteSampleRoomAcceptanceEnum task : values) {
                if (task.getKey().equals(key)) {
                    phase = task;
                    return phase;
                }
            }
            return phase;
        }
    }

    /**
     * task_flag标识是否已经配置了输出条件output_condition
     */
    @Getter
    @AllArgsConstructor
    public enum TaskFlag {
        CONDITION_1("联合二级", "joint_level_2"),
        CONDITION_2("输出条件项", "output_condition_item");
        private String label;
        private String value;
    }

    @Getter
    @AllArgsConstructor
    public enum unauthorizedConstruction {
        NODE_ENGUC("eng-00103075,eng-00107073,eng-00109072,eng-00111116,eng-00117132,eng-00119132,eng-00123132,eng-00125132,eng-00127132", "进度描述（是否存在擅自施工）");

        private String key;
        private String spec;

        public static boolean getNodeCode(String key) {
            boolean phase = false;
            JhSystemEnum.unauthorizedConstruction[] values = JhSystemEnum.unauthorizedConstruction.values();
            for (JhSystemEnum.unauthorizedConstruction task : values) {
                if (task.getKey().contains(key)) {
                    phase = true;
                    return phase;
                }
            }
            return phase;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum unauthorizedConstructionList {
        NODE_ENGJD("eng-00103077,eng-00107075,eng-00109074,eng-00111118,eng-00117134,eng-00119134,eng-00123134,eng-00125134,eng-00127134", "进度描述（是否存在擅自施工）列表");

        private String key;
        private String spec;

        public static boolean getNodeCode(String key) {
            boolean phase = false;
            JhSystemEnum.unauthorizedConstructionList[] values = JhSystemEnum.unauthorizedConstructionList.values();
            for (JhSystemEnum.unauthorizedConstructionList task : values) {
                if (task.getKey().contains(key)) {
                    phase = true;
                    return phase;
                }
            }
            return phase;
        }
    }


    /*动态表格类型*/
    @Getter
    @AllArgsConstructor
    public enum dynamicTableType {
        MATERIAL_MANAGEMENT("material_management", "材料管理"),
        SAFE_CIVILIZED_CONSTRUCTION("safe_civilized_construction", "安全文明施工"),
        COMPLETION_ACCEPTANCE_SCORE("completion_acceptance_score", "竣工验收评分");
        private String key;
        private String spec;

    }


    @Getter
    @AllArgsConstructor
    public enum dynamicTableTypeValue {

        MUST_PAY("must_pay", "必采"),
        IS_MUST_PAY("is_must_pay", "非必采"),
        OFFICE_ADMINISTRATOR("office_administrator", "办公室管理"),
        TEMPORARY_HYDROPOWER("temporary_hydropower", "临时水电"),
        ACCOMMODATION_WORKERS("accommodation_workers", "工人住宿"),
        SAFETY_MANAGEMENT("safety_management", "安全管理");
        private String key;
        private String spec;

    }


    @Getter
    @AllArgsConstructor
    public enum completionTotalItems {
        PUBLIC_AREAS("public_areas", "公共区域「外立面、大堂区域」"),
        FUNCTIONAL_AREA("functional_area", "功能区「厨房、消毒间、布草间、仓库」"),
        GUEST_ROOM_AREA("guest_room_area", "客房区域"),
        RISK_MANAGEMENT("risk_management", "风险管理「消防、安防、监控、电梯」"),
        ELECTROMECHANICAL_FACILITIES("electromechanical_facilities", "机电设施「强电、弱电、给排水、暖通」"),
        COMPLETION_TOTAL_ITEMS("public_areas,functional_area,guest_room_area,risk_management,electromechanical_facilities", "公共区域,功能区，客房区域，风险管理");
        private String key;
        private String spec;
    }

    @Getter
    @AllArgsConstructor
    public enum areaCodeEnum {

        SH("310000", "上海市", "310100"),
        BJ("110000", "北京市", "110100"),
        TJ("120000", "天津市", "120100"),
        CQ("500000", "重庆市", "500100");

        private String key;
        private String spec;
        private String cityCode;

        public static String getCityCode(String key) {

            JhSystemEnum.areaCodeEnum[] values = JhSystemEnum.areaCodeEnum.values();
            for (JhSystemEnum.areaCodeEnum task : values) {
                if (task.getKey().contains(key)) {
                    return task.getCityCode();
                }
            }
            return key;
        }

    }


    /*元素表头*/
    @Getter
    @AllArgsConstructor
    public enum elementHeaderEnum {

        TOTAL_ITEM("total_item", "总项"),
        CONTENT("content", "内容"),
        STANDARD_SCORE("standard_score", "标准分值"),
        GET_SCORE("get_score", "得分"),
        QUALIFICATION_RATE("qualification_rate", "合格率"),
        TOTAL_SCORE_OF_SUB_ITEMS("total_score_of_sub_items", "分项总分"),
        SUB_ITEM_SCORE("sub_item_score", "分项得分"),
        PROJECT_COMPLETION_ACCEPTANCE("project_completion_acceptance", "工程竣工验收项目"),

        SUB_ITEM_CONTENT("sub_item_content", "分项"),
        SCORE("score", "分值"),
        ACCEPTANCE("acceptance", "验收"),
        CLASSIFICATION("classification", "分类"),
        GENERAL_CONTRACTIONG_OR_NOT("general_contracting_or_not", "是否总包"),
        GENERAL_CONTRACTING_STANDARD_SCORE("general_contracting_standard_score", "总包标准分值"),
        OVERALL_CONTRACTING_SCORE("overall_contracting_score", "总包得分"),
        OVERALL_CONTRACTING_SCORE_RATE("overall_contracting_score_rate", "总包得分率");

        private String key;
        private String spec;


    }


    /*竣工验收评分表-内容*/
    @Getter
    @AllArgsConstructor
    public enum completionContentEnum {

        ALLCOMPLETION("linkage_testing,fire_broadcasting,fire_water_system,access_control", "联动测试/消防联动，消防广播，消防水系统，门禁", ""),
        LINKAGE_TESTING("linkage_testing", "联动测试/消防联动", "eng-00133030"),
        FIRE_BROADCASTING("fire_broadcasting", "消防广播", "eng-00133031"),
        FIRE_WATER_SYSTEM("fire_water_system", "消防水系统", "eng-00133033"),
        ACCESS_CONTROL("access_control", "门禁", "eng-00133039"),
        INSTALLATION_STANDARDS("installation_standards", "安装标准", "eng-00133039"),
        EVACUATE("evacuate", "消防疏散", "eng-00133039");


        private String key;
        private String spec;
        private String nodeCode;

        public static completionContentEnum getCompletionContent(String key) {

            JhSystemEnum.completionContentEnum[] values = JhSystemEnum.completionContentEnum.values();
            for (JhSystemEnum.completionContentEnum contentEnum : values) {
                if (contentEnum.getKey().contains(key)) {
                    return contentEnum;
                }
            }
            return null;
        }

    }


    /*审批拒绝--根据二级查询需要保存版本的三级数据*/
    @Getter
    @AllArgsConstructor
    public enum saveVersionNodeEnum {
        ROOM_PLAN_AND_EFFECT_DRAWING("des-00107", "客房平面及效果图(客房平面汇报文件/标题、客房平面汇报文件/动态列表、审核内容/标题、审核内容/动态列表)"
                , "des-00107013,des-00107043,des-00107016,des-00107044"),
        REVIEW_WORKING_DRAWINGS_SAMPL_ROOMS("des-00111", "客房样板间施工图审核(客房样板间施工图汇报文件/标题、客房样板间施工图汇报文件/动态列表、审核内容/标题、审核内容/动态列表)",
                "des-00111017,des-00111028,des-00111020,des-00111029"),
        GUEST_ROOM_ELECTROMECHANICAL_AUDIT("des-00119", "客房机电审核(弱电图/标签、弱电图/动态列表、装饰图/标签、装饰图/动态列表、审核内容/标签、审核内容/动态列表)",
                "des-00119037,des-00119040,des-00119038,des-00119041,des-00119039,des-00119042,des-00119020,des-00119043"),
        GUEST_ROOM_DECORATION_CONSTRUCTION_DRAWING_REVIEW("des-00115", "客房装饰施工图审核（客房装饰施工图、客房装饰施工图、审核内容、审核内容）",
                "des-00115014,des-00115026,des-00115017,des-00115027"),

        PUBLIC_AREA_CONCEPT_PLAN_REVIEW("pad-00127", "公区概念方案审核（公区概念方案汇报文件,公区概念方案汇报文件,审核内容,审核内容）",
                "pad-00127017,pad-00127037,pad-00127020,pad-00127038"),
        PUBLIC_AREA_EFFECT_PLAN_REVIEW("pad-00131", "公区效果方案审核（公区效果方案汇报文件,公区效果方案汇报文件,审核内容,审核内容）",
                "pad-00131012,pad-00131038,pad-00131026,pad-00131039"),
        PUBLIC_AREA_DECORATION_CONSTRUCTION_DRAWING_REVIEW("pad-00135", "公区装饰施工图审核（公区装饰施工图汇报文件,公区装饰施工图汇报文件,审核内容,审核内容）",
                "pad-00135014,pad-00135025,pad-00135016,pad-00135026"),
        PUBLIC_ELECTROMECHANICAL_AUDIT("pad-00139", "公区机电审核（机电图,机电图,弱电图,弱电图,装饰图,装饰图,审核内容,审核内容）",
                "pad-00139038,pad-00139041,pad-00139039,pad-00139042,pad-00139040,pad-00139043,pad-00139020,pad-00139044");


        private String key;
        private String spec;
        private String nodeCode;

        public static saveVersionNodeEnum getSaveVersionNodeEnum(String key) {
            JhSystemEnum.saveVersionNodeEnum[] values = JhSystemEnum.saveVersionNodeEnum.values();
            for (JhSystemEnum.saveVersionNodeEnum versionNodeEnum : values) {
                if (versionNodeEnum.getKey().contains(key)) {
                    return versionNodeEnum;
                }
            }
            return null;
        }
    }


    /*hlm项目推送返回错误信息*/
    @Getter
    @AllArgsConstructor
    public enum projectExpansionEnum {
        DUPLICATE_DATA("项目ID或酒店ID存在重复数据！"),
        PROJECT_START_TIME("项目启动时间不允许为空！"),
        THE_CITY_CODE_CANNOT_EMPTY("城市编码不允许为空！"),
        OWNER_FRANCHISEE_CONTACT_INFORMATION_NOT_ALLOWED("业主方-特许商对接人信息不允许为空!"),
        OWNER_USERNAME_PHONE("业主方-特许商对接人用户名或手机号不允许为空!"),
        OWNER_FRANCHISEE("ACMS系统暂未录入该业主方-特许商对接人信息!"),
        DEVELOPMENT_MANAGER("开发经理信息不允许为空!"),
        HEAD_CONSTRUCTION_AREA("营建区域负责人信息不允许为空!"),
        CITY_CODING_FORMAT_ERROR("城市编码格式错误!"),
        NOT_HEAD_CONSTRUCTION_AREA("ACMS系统暂未录入该营建区域负责人信息!"),
        NOT_DEVELOPMENT_MANAGER("ACMS系统暂未录入该开发经理信息!");

        private String spec;

    }

    /*设计师三级节点的NodeCode*/
    @Getter
    @AllArgsConstructor
    public enum stylistNodeCodeEnum {


        ROOM_DECORATOR("客房装饰设计师", "eng-00103039", "客房装饰设计师", "des-00101078", "sjdwsjs"),
        ROOM_ELECTRICAL_DESIGNER("客房机电设计师", "eng-00103041", "客房机电设计师", "des-00101081", "jdsjs"),
        PUBLIC_DECORATION_DESIGNER("公区装饰设计师", "eng-00103043", "公区装饰设计师", "des-00101084", "gqzssjs"),
        PUBLIC_ELECTROMECHANICAL_DESIGNER("公区机电设计师", "eng-00103045", "公区机电设计师", "des-00101087", "gqjdsjs");


        private String nodeNameEng;
        private String nodeCodeEng;
        private String nodeNameDes;
        private String nodeCodeDes;
        private String roleCode;

    }

    /**
     * 营建-基础信息一致性
     **/
    @Getter
    @AllArgsConstructor
    public enum EngBaseInfo {
        /*品牌名称*/
        BASE_INFO7("现场核对", "eng-00103003", Arrays.asList("eng-00143001", "eng-00143002", "eng-00143003", "eng-00143004", "eng-00143033", "eng-00143034")),
        /*执行标准*/
        BASE_INFO8("现场核对", "eng-00103009", Arrays.asList("eng-00143021", "eng-00143022", "eng-00143023", "eng-00143024", "eng-00143029", "eng-00143035")),
        /*法务约定工程内容*/
        BASE_INFO9("现场核对", "eng-00103028", Arrays.asList("eng-00143005", "eng-00143006", "eng-00143007", "eng-00143008", "eng-00143032")),
        /*签约日期*/
        BASE_INFO10("现场核对", "eng-00103119", Arrays.asList("eng-00143009", "eng-00143010", "eng-00143011", "eng-00143012", "eng-00143031")),
        /*签约房量*/
        BASE_INFO11("现场核对(十间以内请选择“不变”)", "eng-00103121", Arrays.asList("eng-00143013", "eng-00143014", "eng-00143015", "eng-00143016", "eng-00143028", "eng-00143036")),
        /*经营地址*/
        BASE_INFO12("现场核对", "eng-00103123", Arrays.asList("eng-00143017", "eng-00143018", "eng-00143019", "eng-00143020", "eng-00143030"));


        private String nodeName;

        private String nodeCode;

        private List<String> relationNodeCodes;

        public static List<String> getNodeCodes() {
            EngBaseInfo[] values = EngBaseInfo.values();
            List<String> result = new ArrayList<>();
            for (EngBaseInfo baseInfo : values) {
                result.add(baseInfo.getNodeCode());
            }
            return result;
        }
    }

    /**
     * 基础信息一致性
     **/
    @Getter
    @AllArgsConstructor
    public enum DesBaseInfo {
        /*品牌名称*/
        BASE_INFO1("现场核对", "des-00101010", Arrays.asList("des-00167001", "des-00167002", "des-00167003", "des-00167004", "des-00167025", "des-00167027")),
        /*执行标准*/
        BASE_INFO2("现场核对", "des-00101016", Arrays.asList("des-00167021", "des-00167022", "des-00167023", "des-00167024", "des-00167026", "des-00167028")),
        /*法务约定工程内容*/
        BASE_INFO3("现场核对", "des-00101123", Arrays.asList("des-00167005", "des-00167006", "des-00167007", "des-00167008", "des-00167032")),
        /*签约日期*/
        BASE_INFO4("现场核对(十间以内请选择“不变”)", "des-00101031", Arrays.asList("des-00167009", "des-00167010", "des-00167011", "des-00167012", "des-00167033")),
        /*签约房量*/
        BASE_INFO5("现场核对", "des-00101028", Arrays.asList("des-00167013", "des-00167014", "des-00167015", "des-00167016", "des-00167034", "des-00167039")),
        /*经营地址*/
        BASE_INFO6("现场核对", "des-00101034", Arrays.asList("des-00167017", "des-00167018", "des-00167019", "des-00167020", "des-00167035"));


        private String nodeName;

        private String nodeCode;

        private List<String> relationNodeCodes;

        public static List<String> getNodeCodes() {
            DesBaseInfo[] values = DesBaseInfo.values();
            List<String> result = new ArrayList<>();
            for (DesBaseInfo baseInfo : values) {
                result.add(baseInfo.getNodeCode());
            }
            return result;
        }
    }


    @Getter
    @AllArgsConstructor
    public enum ConventionalInformation {
        ROOM_DESIGN_UNIT("房间设计单位", "eng-00107021"),
        ROOM_DECORATOR("客房装饰设计师", "eng-00107022"),
        ROOM_PLATFORM_UNIT("是否为平台单位", "eng-00107166"),
        ROOM_ELECTRICAL_UNIT("客房机电单位", "eng-00107141"),
        ROOM_ELECTRICAL_DESIGNER("客房机电设计师", "eng-00107142"),
        ROOM_ELECTRICAL_PLATFORM_UNIT("是否为平台单位", "eng-00107167"),
        PUBLIC_AREA_DESIGN_UNIT("公区设计单位", "eng-00107143"),
        PUBLIC_DECORATION_DESIGNER("公区装饰设计师", "eng-00107144"),
        PUBLIC_DECORATION_PLATFORM_UNIT("是否为平台单位", "eng-00107168"),
        PUBLIC_ELECTROMECHANICAL_UNIT("公区机电单位", "eng-00107145"),
        PUBLIC_ELECTROMECHANICAL_DESIGNER("公区机电设计师", "eng-00107146"),
        PUBLIC_ELECTROMECHANICAL_PLATFORM_UNIT("是否为平台单位", "eng-00107169"),
        PROJECT_MANAGER("项目经理", "eng-00107023"),
        DESIGNER("设计师", "eng-00107024"),
        OWNER("业主方", "eng-00107025"),
        DOCKING_PERSONNEL_ALTER("业主指定对接人员是否变更", "eng-00107026"),
        ALTER("变更换人员为", "eng-00107027"),
        CHANGE_LETTER("变更函件附件", "eng-00107028");

        private String name;
        private String code;

        public static List<String> getNodeCodeList() {
            JhSystemEnum.ConventionalInformation[] values = JhSystemEnum.ConventionalInformation.values();
            List<String> result = new ArrayList<>();
            for (JhSystemEnum.ConventionalInformation ci : values) {
                result.add(ci.getCode());
            }
            return result;
        }

        public static List<String> getUnitCodeList() {
            List<String> result = new ArrayList<>();
            result.add(ROOM_DESIGN_UNIT.code);
            result.add(ROOM_ELECTRICAL_UNIT.code);
            result.add(PUBLIC_AREA_DESIGN_UNIT.code);
            result.add(PUBLIC_ELECTROMECHANICAL_UNIT.code);
            return result;
        }

        public static List<String> getDesignCodeList() {
            List<String> result = new ArrayList<>();
            result.add(ROOM_DECORATOR.code);
            result.add(ROOM_ELECTRICAL_DESIGNER.code);
            result.add(PUBLIC_DECORATION_DESIGNER.code);
            result.add(PUBLIC_ELECTROMECHANICAL_DESIGNER.code);
            return result;
        }

        public static List<String> getJobCodeList() {
            List<String> result = new ArrayList<>();
            result.add(PROJECT_MANAGER.code);
            result.add(DESIGNER.code);
            return result;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum ConfirmDesignUnit {

        ROOM_DESIGN_UNIT("房间设计单位", "eng-00107021", "des-00101077", "des-00127005"),
        ROOM_DECORATOR("客房装饰设计师", "eng-00107022", "des-00101078", "des-00127006"),
        ROOM_PLATFORM_UNIT("是否为平台单位", "eng-00107166", "des-00101079", "des-00127007"),
        ROOM_ELECTRICAL_UNIT("客房机电单位", "eng-00107141", "des-00101080", "des-00127008"),
        ROOM_ELECTRICAL_DESIGNER("客房机电设计师", "eng-00107142", "des-00101081", "des-00127009"),
        ROOM_ELECTRICAL_PLATFORM_UNIT("是否为平台单位", "eng-00107167", "des-00101082", "des-00127010"),
        PUBLIC_AREA_DESIGN_UNIT("公区设计单位", "eng-00107143", "des-00101083", "des-00127011"),
        PUBLIC_DECORATION_DESIGNER("公区装饰设计师", "eng-00107144", "des-00101084", "des-00127012"),
        PUBLIC_DECORATION_PLATFORM_UNIT("是否为平台单位", "eng-00107168", "des-00101085", "des-00127013"),
        PUBLIC_ELECTROMECHANICAL_UNIT("公区机电单位", "eng-00107145", "des-00101084", "des-00127014"),
        PUBLIC_ELECTROMECHANICAL_DESIGNER("公区机电设计师", "eng-00107146", "des-00101084", "des-00127015"),
        PUBLIC_ELECTROMECHANICAL_PLATFORM_UNIT("是否为平台单位", "eng-00107169", "des-00101084", "des-00127016");

        /**
         * 名称
         **/
        private String name;
        /**
         * 正式开工nodeCode
         **/
        private String formalCode;
        /**
         * 设计勘测nodeCode
         **/
        private String designCode;
        /**
         * 确认设计单位nodeCode
         **/
        private String confirmCode;
    }

    @Getter
    @AllArgsConstructor
    public enum TitleNodeCode {

        CODE1("运营战区负责人", "yyzqfzr"),
        CODE2("开业经理", "kyjl"),
        CODE3("运营经理", "yyjl"),
        CODE4("特许经理", "txjl"),
        CODE5("开发战区负责人", "kfzqfzr"),
        CODE6("开发分区负责人", "kffqfzr"),
        //        CODE7("开发经理", "kfjl"),
        CODE8("项目经理", "gcjl"),
        CODE9("设计师", "sjfzr"),
        CODE10("机电工程师", "jdgcs"),
        CODE11("弱电工程师", "rdysry"),
        CODE12("设计共管人员", "sjggry"),
        CODE13("弱电验收人员", "rdysry"),
        CODE14("机电验收人员", "jdysry"),
        CODE15("飞行质检人员", "fxzjry"),
        CODE16("竣工验收人员", "jgysry"),
        CODE17("采购销售", "cgxs"),
        CODE18("签约日期", ""),
        CODE19("签约房量", ""),
        CODE20("楼层", ""),
        CODE21("计划开工日期", ""),
        CODE22("调整开工日期", ""),
        CODE23("世纪开工日期", ""),
        CODE24("计划完工日期", ""),
        CODE25("调整完工日期", ""),
        CODE26("实际完工日期", ""),
        CODE27("启动周期", ""),
        CODE28("施工周期", ""),
        CODE29("办证周期", ""),
        CODE30("公区装饰设计师", "gqzssjs"),
        CODE31("营建区域负责人", "zbzxfzr"),
        CODE32("公区机电设计师", "gqjdsjs");


        private String name;
        private String roleCode;

        public static List<String> getTitleNodeCode() {
            JhSystemEnum.TitleNodeCode[] values = JhSystemEnum.TitleNodeCode.values();
            List<String> result = new ArrayList<>();
            for (JhSystemEnum.TitleNodeCode dm : values) {
                if (ObjectUtil.isNotEmpty(dm.getRoleCode())) {
                    result.add(dm.getRoleCode());
                }
            }
            return result;
        }

    }

    @Getter
    @AllArgsConstructor
    public enum MessageTemplate {
        MB1000003("提交-不需要审批", "MB1000003", ""),
        MB1000004("提交-需要审批", "MB1000004", ""),
        MB1000005("审批不通过", "MB1000005", ""),
        MB1000006("审批通过", "MB1000006", ""),
        MB1000008("代办已逾期（发送给抄送人）", "MB1000008", ""),
        MB1000009("开发信息一致性（报备）消息通知 ", "MB1000009", "gcjl"),
        MB1000010("家具进场已完成，且当前项目还没有分配飞行质检人员，那么给交付中心-工程负责人发送消息提醒", "MB1000010", "");

        String name;
        private String noticeCode;
        private String roleCode;
    }

    @Getter
    @AllArgsConstructor
    public enum NoticeStatus {
        SENT("已发送", "sent"),
        READ("已读", "read");
        private String name;
        private String status;
    }

    @Getter
    @AllArgsConstructor
    public enum tertiaryKey {

        ZIJIANBIAO1("des-00107010", "自检表"),
        ZIJIANBIAO2("des-00115010", "自检表"),
        ZIJIANBIA3_1("pad-00127010", "方案汇报文本（表格）"),
        ZIJIANBIA3_2("pad-00127012", "公区平面设计质量（表格）"),
        ZIJIANBIA3_3("pad-00127014", "公区方案质量（表格）"),
        ZIJIANBIA4("pad-00135010", "公区施工图检查表（表格）"),

        AllZIJIANNODE("des-00107010,des-00115010", "自检表"),

        ZIJIANBIAO_STATUS1("des-00107052", "自检表状态"),
        ZIJIANBIAO_STATUS2("des-00115028", "自检表状态"),
        ZIJIANBIAO_STATUS3_1("pad-00127040", "自检表状态"),
        ZIJIANBIAO_STATUS3_2("pad-00127043", "自检表状态"),
        ZIJIANBIAO_STATUS3_3("pad-00127046", "自检表状态"),
        ZIJIANBIAO_STATUS4("pad-00135028", "自检表状态"),

        NO_SAME1("des-00107053", "不符合项"),
        NO_SAME2("des-00115029", "不符合项"),
        NO_SAME3_1("pad-00127041", "不符合项"),
        NO_SAME3_2("pad-00127044", "不符合项"),
        NO_SAME3_3("pad-00127047", "不符合项"),
        NO_SAME4("pad-00135029", "不符合项"),

        SAME1("des-00107054", "符合项"),
        SAME2("des-00115030", "符合项"),
        SAME3_1("pad-00127042", "符合项"),
        SAME3_2("pad-00127045", "符合项"),
        SAME3_3("pad-00127048", "符合项"),
        SAME4("pad-00135030", "符合项"),

        INSPECTION("inspection_result", "检查结果"),
        INCONFORMITY("inconformity", "不符合"),
        ACCORD_WITH("accord_with", "符合"),

        NO_SAME("des-00107053", "不符合项"),
        SAME("des-00115030", "符合项");

        private String key;
        private String name;
    }

    @Getter
    @AllArgsConstructor
    public enum cateKey {

        CATE_NEW("new", "新开店"),
        CATE_ONLINE("online", "上线"),
        CATE_DOWN("down", "下线"),

        CATE_THREE_1("eng-00107099", "证照管理（表格）"),
        CATE_THREE_2("eng-00109098", "证照管理（表格）"),
        CATE_THREE_3("eng-00111142", "证照管理（表格）"),
        CATE_THREE_4("eng-00117158", "证照管理（表格）"),
        CATE_THREE_5("eng-00119158", "证照管理（表格）"),
        CATE_THREE_6("eng-00123158", "证照管理（表格）"),
        CATE_THREE_7("eng-00125158", "证照管理（表格）"),
        CATE_THREE_8("eng-00127158", "证照管理（表格）"),

        CATE_TWO_1("eng-00107", "正式开工"),
        CATE_TWO_2("eng-00109", "样板间隐蔽工程检查"),
        CATE_TWO_3("eng-00111", "样板间验收"),
        CATE_TWO_4("eng-00117", "隐蔽验收"),
        CATE_TWO_5("eng-00119", "封板节点"),
        CATE_TWO_6("eng-00123", "家具进场"),
        CATE_TWO_7("eng-00125", "形象进度80%"),
        CATE_TWO_8("eng-00127", "竣工自检");
        private String key;
        private String name;
    }

    /**
     * 资料管理相关节点
     */
    @Getter
    @AllArgsConstructor
    public enum DocumentManage {
        FEED_WATER("给水管试压模板", "eng-00107033"),
        DRAIN_WATER("排水管道排水模板", "eng-00107035"),
        WATERPROOF("防水资料模板", "eng-00107037"),
        PRESSURE_TEST("消防管道试压模板", "eng-00107034"),
        CRACK_DOWN("消防管道打压模板", "eng-00107036"),
        CONSTRUCTION_LOG("施工日志模板", "eng-00107038");
        private String name;

        private String code;

        public static List<String> getNodeCodeList() {
            JhSystemEnum.DocumentManage[] values = JhSystemEnum.DocumentManage.values();
            List<String> result = new ArrayList<>();
            for (JhSystemEnum.DocumentManage dm : values) {
                result.add(dm.getCode());
            }
            return result;
        }
    }


    @Getter
    @AllArgsConstructor
    public enum StakeholderUnitLevelEnum {
        SECOND_LEVLE("【筹建启动会】【正式开工】【设计勘测】【确认设计单位】", "eng-00103,eng-00107,des-00101,des-00127");

        private String key;
        private String spec;

    }

    @Getter
    @AllArgsConstructor
    public enum StakeholderUnitEnum {
        //设计单位-公区机电设计师【gqjdsjs】
        SJDW_GQJDSJS("gqjdsjs", "eng-00103044,eng-00103045、eng-00107145,eng-00107146、des-00101086,des-00101087、des-00127030,des-00127031"),

        //设计单位-公区装饰设计师【gqzssjs】
        SJDW_GQZSSJS("gqzssjs", "eng-00103042,eng-00103043、eng-00107143,eng-00107144、des-00101083,des-00101084、des-00127027,des-00127028"),

        //设计单位-机电设计师【jdsjs】
        SJDW_JDSJS("jdsjs", "eng-00103040,eng-00103041、eng-00107141,eng-00107142、des-00101080,des-00101081、des-00127024,des-00127025"),

        //设计单位-装修设计师【sjdwsjs】
        SJDW_SJDWSJS("sjdwsjs", "eng-00103038,eng-00103039、eng-00107021,eng-00107022、des-00101077,des-00101078、des-00127021,des-00127022");


        private String key;
        private String spec;


        public static List<Map<String, String>> getNodeCodeList(String roleCode, String nodeCode) {
            JhSystemEnum.StakeholderUnitEnum[] values = JhSystemEnum.StakeholderUnitEnum.values();
            List<Map<String, String>> result = new ArrayList<>();
            for (JhSystemEnum.StakeholderUnitEnum stakeholderUnitEnum : values) {
                if (stakeholderUnitEnum.getKey().equals(roleCode)) {
                    String[] split = stakeholderUnitEnum.getSpec().split("、");
                    for (String spec : split) {
                        if (spec.contains(nodeCode)) {
                            Map<String, String> map = new HashMap<>();
                            map.put(spec.split(",")[0], spec.split(",")[1]);
                            result.add(map);
                        }
                    }
                }
            }
            return result;
        }

    }

    @Getter
    @AllArgsConstructor
    public enum VisaDilingEnum {
        //项目经理  施工管理
        GCJL("gcjl", "vsf9-00101,vsf10-00101,vsf11-00101,vsf12-00101,vsf13-00101,vsf14-00101,vsf15-00101,vsf16-00101,vsf17-00101,vsf18-00101,"),

        //营建区域负责人   设计管理-决策会意见无法落地
        ZBZXFZR("zbzxfzr", "vsf2-00101,"),

        //亚朵设计师    设计管理
        SJS("sjs", "vsf-00101,vsf1-00101,vsf2-00101,vsf3-00101,vsf4-00101,vsf5-00101,vsf6-00101,vsf7-00101,vsf8-00101,");

        private String key;
        private String spec;

        public static String getVisaDilingEnum(List<String> roleCodes) {
            JhSystemEnum.VisaDilingEnum[] values = JhSystemEnum.VisaDilingEnum.values();
            String result = "";
            for (JhSystemEnum.VisaDilingEnum visaDilingEnum : values) {
                for (String roleCode : roleCodes) {
                    if (visaDilingEnum.getKey().equals(roleCode)) {
                        result += visaDilingEnum.getSpec();
                    }
                }
            }
            if (ObjectUtil.isEmpty(result)) {
                result = "-1";
            }
            return result;
        }
    }


    @Getter
    @AllArgsConstructor
    public enum RoleCodeEnum {
        CJGLY("cjgly", "超级管理员"),
        YWGLY("ywgly", "业务管理员");

        private String key;
        private String spec;

    }

    //区域负责人确认
    @Getter
    @AllArgsConstructor
    public enum confirmedRegionalLeaderEnum {
        REFUSE("refuse", "拒绝"),
        REPORTING("reporting", "报备");

        private String key;
        private String spec;

    }

    @Getter
    @AllArgsConstructor
    public enum VisaDilingNodeCodeEnum {
        //更新品牌
        ENG00143033("eng-00143", "eng-00143033", "eng-00103171,eng-00103004"),
        ENG00143029("eng-00143", "eng-00143029", "eng-00103172,eng-00103010"),
        ENG00143028("eng-00143", "eng-00143028", "eng-00103173,eng-00103122"),
        ENG00143030("eng-00143", "eng-00143030", "eng-00103027,eng-00103124"),
        ENG00143032("eng-00143", "eng-00143032", "eng-00103014,eng-00103029"),

        //更新品牌
        DES00167025("des-00167", "des-00167025", "des-00101150,des-00101011"),
        //执行标准
        DES00167026("des-00167", "des-00167026", "des-00101151,des-00101126"),
        //更新房量
        DES00167034("des-00167", "des-00167034", "des-00101153,des-00101029"),
        //经营地址
        DES00167035("des-00167", "des-00167035", "eng-00103027,des-00101035"),
        //法务约定工程内容
        DES00167032("des-00167", "des-00167032", "eng-00103014,des-00101124");

        private String key;
        private String spec;
        private String value;

        public static List<String> getVisaDilingNodeCodeEnum(String nodeCode) {
            JhSystemEnum.VisaDilingNodeCodeEnum[] values = JhSystemEnum.VisaDilingNodeCodeEnum.values();
            List<String> nodeCodes = new ArrayList<>();
            for (JhSystemEnum.VisaDilingNodeCodeEnum visaDilingNodeCode : values) {
                if (visaDilingNodeCode.getKey().equals(nodeCode)) {
                    nodeCodes.add(visaDilingNodeCode.getSpec());
                }
            }
            return nodeCodes;
        }

        public static String getVisaDilingNodeCodesEnum(String nodeCodeSpec) {
            JhSystemEnum.VisaDilingNodeCodeEnum[] values = JhSystemEnum.VisaDilingNodeCodeEnum.values();
            String result = "";
            for (JhSystemEnum.VisaDilingNodeCodeEnum visaDilingNodeCode : values) {
                if (visaDilingNodeCode.getSpec().equals(nodeCodeSpec)) {
                    result += visaDilingNodeCode.getValue();
                }
            }
            return result;
        }
    }


    //看板任务阶段
    @Getter
    @AllArgsConstructor
    public enum kanbanTaskPhaseEnum {
        ENGINEERING("engineering", "工程"),
        DESIGN("design", "设计");
        private String key;
        private String spec;

    }

    @Getter
    @AllArgsConstructor
    public enum AESExample {
        AES_ECB("AES/ECB/PKCS5Padding"),
        /**
         * 加密模式之 CBC，算法/模式/补码方式
         */
        AES_CBC("AES/CBC/PKCS5Padding"),
        /**
         * 加密模式之 CFB，算法/模式/补码方式
         */
        AES_CFB("AES/CFB/PKCS5Padding"),
        KEY("0701qazsxedcrfvt");
        /**
         * AES 中的 IV 必须是 16 字节（128位）长
         */
        private String key;

    }


    @Getter
    @AllArgsConstructor
    public enum VFNodeCode {

        CODE7("开发经理", "kfjl"),
        CODE8("项目经理", "gcjl"),
        CODE9("设计师", "sjfzr"),
        CODE10("机电工程师", "jdgcs"),
        CODE11("弱电工程师", "rdysry");
        private String name;
        private String roleCode;

        public static String getVFNodeCode() {
            JhSystemEnum.VFNodeCode[] values = JhSystemEnum.VFNodeCode.values();
            String result = null;
            for (JhSystemEnum.VFNodeCode dm : values) {
                if (ObjectUtil.isNotEmpty(dm.getRoleCode())) {
                    if (ObjectUtil.isNotEmpty(result)) {
                        result = result + "," + dm.getRoleCode();
                    } else {
                        result = dm.getRoleCode();
                    }
                }
            }
            return result;
        }
    }


    @Getter
    @AllArgsConstructor
    public enum twoNodeCodeTEnum {
        NODE_ENG103("eng-00103", "筹建启动会", "", "", "eng-00103077", "", "", "", "", "", "", "", "", "", "", "", "eng-00103156"),
        NODE_ENG105("eng-00105", "开工申请", "1", "", "", "", "", "", "", "", "", "", "", "", "", "", ""),
        NODE_ENG107("eng-00107", "正式开工", "1", "eng-00107148", "eng-00107075", "eng-00107149", "eng-00107099", "", "", "", "", "", "", "",   "", "", ""),
        NODE_ENG109("eng-00109", "样板间隐蔽验收", "", "eng-00109145", "eng-00109074", "eng-00109148", "eng-00109098", "", "", "", "", "", "", "", "", "", ""),
        NODE_ENG113("eng-00113", "样板间验收申请", "1", "", "", "eng-00113005", "", "1", "", "", "", "", "", "", "", "", "1"),
        NODE_ENG111("eng-00111", "样板间验收", "", "eng-00111189", "eng-00111118", "eng-00111194", "eng-00111142", "", "", "", "", "", "", "", "", "", ""),
        NODE_ENG117("eng-00117", "隐蔽验收", "", "eng-00117213", "eng-00117134", "eng-00117220", "eng-00117158", "", "", "", "", "", "", "", "", "", ""),
        NODE_ENG119("eng-00119", "封板确认", "", "eng-00119213", "eng-00119134", "eng-00119222", "eng-00119158", "", "", "", "", "", "", "", "", "", ""),
        NODE_ENG123("eng-00123", "家具进场", "", "eng-00123213", "eng-00123134", "eng-00123224", "eng-00123158", "", "", "", "", "", "", "", "", "", ""),
        NODE_ENG125("eng-00125", "形象进度80%", "", "eng-00125213", "eng-00125134", "eng-00125226", "eng-00125158", "", "", "", "", "", "", "", "", "", ""),
        NODE_ENG137("eng-00137", "弱电验收申请", "1", "", "", "", "", "", "", "", "", "", "", "", "", "", ""),
        NODE_ENG139("eng-00139", "弱电验收", "1", "", "", "", "", "", "1", "eng-00139025", "", "", "", "", "", "", ""),
        NODE_ENG141("eng-00141", "弱电整改", "1", "", "", "", "", "", "", "", "eng-00141009", "", "", "", "", "", ""),
        NODE_ENG127("eng-00127", "竣工自检", "", "eng-00127213", "", "", "eng-00127158", "", "", "", "", "eng-00127134", "", "", "", "", ""),
        NODE_ENG129("eng-00129", "竣工验收申请", "1", "", "", "", "", "", "", "", "", "", "", "", "", "", ""),
        NODE_ENG133("eng-00133", "竣工验收", "1", "", "", "", "", "", "", "", "", "", "1", "1", "1", "", ""),
        NODE_ENG135("eng-00135", "竣工验收整改", "", "", "", "", "", "", "", "", "", "", "", "", "", "1", "");

        private String key;
        private String spec;
        private String isProjectRoom; //房间号数据
        private String nodeCodeCG; //常规检查数据
        private String nodeCodeCZ; //擅自施工
        private String nodeCodeWZ; //物资管理材料
        private String nodeCodeZZ; //证照管理集合
        private String isProjectCGZP; //施工照片
        private String isProjectRDSCSB; //弱电设施设备信息表
        private String nodeCodeRDYSD; //弱电验收验收单
        private String nodeCodeRDZGQD; //弱电整改整改清单
        private String nodeCodeJGZJ; //竣工自检 系统自检
        private String isProjectYSDSM; //竣工验收 - 验收说明
        private String isProjectYSHG; //竣工验收 - 验收合格
        private String isProjectYSD; //竣工验收 - 验收单   验收实拍照片  验收其他照片
        private String isProjectYSDZG; //竣工验收整改 - 整改清单
        private String nodeCodeTXS; //筹建启动会 特许商


        public static JhSystemEnum.twoNodeCodeTEnum getTwoNodeCodeT(String nodeCode) {
            JhSystemEnum.twoNodeCodeTEnum[] values = JhSystemEnum.twoNodeCodeTEnum.values();
            for (JhSystemEnum.twoNodeCodeTEnum dm : values) {
                if (dm.getKey().equals(nodeCode)) {
                    return dm;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum SupplierTypeEnum {
        SUPPLIER("supplier", "供应商"),
        SUPPLIER_PM("supplier_pm", "员工");

        String key;
        String value;

    }


}
