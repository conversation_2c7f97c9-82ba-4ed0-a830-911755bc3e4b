package com.bassims.constant.bsEnum;

import com.bassims.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

public class AtourSystemEnum {

    /**
     * 供应商状态
     */
    @Getter
    @AllArgsConstructor
    public enum SupStatusEnum {
        COOPERATION("cooperation", "激活", 0),
        NONCOOPERATION("noncooperation", "禁用", 1);
        private String key;
        private String spec;
        private Integer value;
    }

    /**
     * 供应商人员状态
     */
    @Getter
    @AllArgsConstructor
    public enum PmStatusEnum {
        COOPERATION("cooperation", "激活", 0),
        NONCOOPERATION("noncooperation", "禁用", 1);
        private String key;
        private String spec;
        private Integer value;
    }

    /**
     * 存放【物资管理】的三级nodeCode
     */
    @Getter
    @AllArgsConstructor
    public enum MaterialManagementCode {
        ENG00107149("eng-00107149"),
        ENG00109148("eng-00109148"),
        ENG00113005("eng-00113005"),
        ENG00111194("eng-00111194"),
        ENG00117220("eng-00117220"),
        ENG00119222("eng-00119222"),
        ENG00123224("eng-00123224"),
        ENG00125226("eng-00125226");
        private String key;
    }

    /**
     * 竣工验收-评分表
     */
    @Getter
    @AllArgsConstructor
    public enum CompletionAcceptanceScoring {
        LINKAGE_TESTING1("qualified", "合格", 1),
        LINKAGE_TESTING2("unqualified", "不合格", 2),
        LINKAGE_TESTING3("default", "缺省", 0);
        private String key;
        private String spec;
        private Integer value;

    }


    /**
     * 竣工验收整改-复核状态
     */
    @Getter
    @AllArgsConstructor
    public enum AcceptanceState {
        ADOPT("adopt", "通过", 1),
        NOT_PASSED("not_passed", "不通过", 2),
        CONDITIONAL_PASSAGE("conditional_passage", "有条件通过", 3);

        private String key;
        private String spec;
        private Integer value;

    }

    /**
     * 竣工验收整改-复核状态
     */
    @Getter
    @AllArgsConstructor
    public enum CheckTime {
        MORNING("morning", "上午", 1),
        AFTERNOON("afternoon", "下午", 2);

        private String key;
        private String spec;
        private Integer value;

        public static String getCheckTime(String key) {
              AtourSystemEnum.CheckTime[] values = AtourSystemEnum.CheckTime.values();
            for (CheckTime value : values) {
                if (value.getKey().equals(key)) {
                    return " "+value.getSpec();
                }
            }
            return "";
        }
    }

    /**
     * 验收单各总项汇总
     */
    @Getter
    @AllArgsConstructor
    public enum SummaryAcceptanceForms {
//        PUBLIC_AREAS("public_areas", "公共区域「外立面、大堂、竹居、相招、汗出、出尘、共语、公卫」", 3),
//        FUNCTIONAL_AREA("functional_area", "功能区「厨房、消毒间、布草间、仓库」", 4),
//        RISK_MANAGEMENT("risk_management", "风险管理「消防、安防、监控、电梯」", 1),
//        ELECTROMECHANICAL_FACILITIES("electromechanical_facilities", "机电设施「强电、弱电、给排水、暖通」", 2),
//        GUEST_ROOM_AREA("guest_room_area", "客房区域「客房、客房走道」", 5),
//        APPLICATION_FOR_COMPLETION_ACCEPTANCE("application_for_completion_acceptance", "竣工验收申请资料「资料不齐倒扣分」", 6),
//        ROOM_COMPLETION_RATE("room_completion_rate", "客房完工率「低于90%按比例折算验收得分」", 7),
//        SUMMARY_TOTAL_SCORE("summary_total_score", "汇总总分", 8);

        PUBLIC_AREAS("PI,PF,C", "公共区域「外立面、大堂、竹居、相招、汗出、出尘、共语、公卫」", 1),
        FUNCTIONAL_AREA("PB", "功能区「厨房、消毒间、布草间、仓库」", 2),
        RISK_MANAGEMENT("PO", "风险管理「消防、安防、监控、电梯」", 3),
        ELECTROMECHANICAL_FACILITIES("PS", "机电设施「强电、弱电、给排水、暖通」", 4),
        GUEST_ROOM_AREA("GR", "客房区域「客房、客房走道」", 5),
//        APPLICATION_FOR_COMPLETION_ACCEPTANCE("PS", "竣工验收申请资料「资料不齐倒扣分」", 6); //主系统 【消防、给排水、暖通、强弱电】
//        ROOM_COMPLETION_RATE("C", "客房完工率「低于90%按比例折算验收得分」", 7), // 核心问题
        SUMMARY_TOTAL_SCORE("summary_total_score", "汇总总分", 8);

        private String key;
        private String spec;
        private Integer value;
    }

    /**
     * 额外新增模板
     */
    @Getter
    @AllArgsConstructor
    public enum AdditionalTemplatesEnum {
        //公区设计阶段
        PUBLIC_AREA_DESIGN("public_area_design", "公区设计阶段"),

        //决策信息
        DECISION_MAKING("decision_making", "决策信息模板"),
        //施工日志
        CONSTRUCTION_LOG("construction_log", "施工日志"),
        //深化方案
        DEEPENING_PLAN("deepening_plan", "深化方案"),
        //签证报备
        VISA_FILING("visa_filing", "签证报备模版"),

        //工程整改问题
        ENGINEERING_RECTIFCATION_ISSUES("engineering_rectification_issues", "工程常规检查"),
        SYSTEM_SELF_INSPECTION("system_self_inspection", "竣工系统自检"),
        ROUTINE_SELF_INSPECTION("routine_self_inspection", "竣工常规项目自检"),
        DESIGN_SAMPLE_ROOM("design_sample_room", "设计样板间验收");
        private String key;
        private String spec;

        public static String getByKey(String pram){
            if (StringUtils.isEmpty(pram)){
                return "";
            }
            StringBuilder sb = new StringBuilder();
            for (AdditionalTemplatesEnum value : AdditionalTemplatesEnum.values()) {
                for (String s : pram.split(",")) {
                    if (value.key.equals(s)){
                        sb.append(value.spec).append(",");
                    }
                }
            }
            return sb.toString();
        }
    }


    @Getter
    @AllArgsConstructor
    public enum DeepeningPlanReclassify {

        MODEL_ROOM_DEEPENING("model_room_deepening", "样板间深化"),
        GUEST_ROOM_AREA_FURTHER_DECORATED("guest_room_area_further_decorated", "客房区装饰深化"),
        ELECTROMECHANICAL_DEEPENING_GUEST_ROOM_AREA("electromechanical_deepening_guest_room_area", "客房区机电深化"),
        PUBLIC_AREA_DECORATION_DEEPENED("public_area_decoration_deepened", "公区装饰深化"),
        PUBLIC_AREA_MECHANICAL_ELECTRICAL_DEEPENING("public_area_mechanical_electrical_deepening", "公区机电深化"),
        ALTERNATIVE_SCHEME("alternative_scheme", "其他化方案");
        private String key;
        private String spec;

    }

    /**
     * 客房平面图纸
     */
    @Getter
    @AllArgsConstructor
    public enum IsDrawingKFPMTZ {

        FILE("des-00107043", "客房平面图（CAD/PDF）");
        private String key;
        private String spec;

    }

    /**
     * 客房效果方案
     */
    @Getter
    @AllArgsConstructor
    public enum IsDrawingKFXGFA {

        FILE("des-00107043", "客房效果方案");
        private String key;
        private String spec;

    }

    /**
     * 隔墙尺寸图样板间深化图
     */
    @Getter
    @AllArgsConstructor
    public enum IsDrawingGQCCT {

        FILE("des-00111028", "平面隔墙尺寸图（CAD/PDF）");
        private String key;
        private String spec;

    }

    /**
     * 客房深化图纸
     */
    @Getter
    @AllArgsConstructor
    public enum IsDrawingKFSH {
        FILE1("dep-00201002", "样板间家具图纸"),
//        FILE2("des-00203002", "客房深化方案"),
        FILE3("des-00205002", "客房房号方案"),
        FILE4("des-00207002", "客房区家具图纸"),
        FILE5("des-00209002", "客房区装饰画（喷绘方案）"),
        FILE6("des-00211002", "客房区非标材料清单");
        private String key;
        private String spec;

    }

    /**
     * 客房机电暖通
     */
    @Getter
    @AllArgsConstructor
    public enum IsDrawingKFJDNT {
        FILE1("dep-00213002", "客房客控深化方案"),
        FILE2("dep-00215002", "客房区弱电深化方案"),
        FILE3("dep-00217002", "客房区弱电设备清单"),
        FILE4("dep-00219002", "客房区热水热源深化方案"),
//        FILE5("dep-00221002", "客房区暖通深化方案"),
        FILE7("dep-00223002", "客房区空调深化方案"),
        FILE8("dep-00225002", "客房区新风深化方案"),
        FILE9("dep-00227002", "客房区地暖深化方案"),
        FILE10("dep-00229002", "客房区给排水深化方案");
        private String key;
        private String spec;
    }

    /**
     * 公区概念方案
     */
    @Getter
    @AllArgsConstructor
    public enum IsDrawingGQGN {
        FILE1("pad-00127037", "公区平⾯图（PDF/CAD）"),
//        FILE2("dep-00215002", "公区概念方案（PPT）"),
        FILE2("pad-00127037", "公区概念方案（PPT）");
        private String key;
        private String spec;
    }

    /**
     * 公区效果方案
     */
    @Getter
    @AllArgsConstructor
    public enum IsDrawingGQXG {
        FILE1("dep-00233002", "室外VI方案"),
        FILE2("pad-00131038", "公区效果方案（PDF/PPT）");
        private String key;
        private String spec;
    }


    /**
     * 公区装饰深化
     */
    @Getter
    @AllArgsConstructor
    public enum IsDrawingGQZS {
        FILE1("dep-00233002", "室外VI方案"),
//        FILE2("dep-00235002", "店招小样"),
        FILE3("dep-00237002", "室外店招深化方案"),
        FILE4("dep-00239002", "室外门头深化方案"),
//        FILE5("dep-00241002", "店招材料清单"),
        FILE7("dep-00243002", "公区装饰深化图纸"),
        FILE8("dep-00245002", "室内全套VI方案"),
        FILE9("dep-00247002", "公区软装方案（PDF/PPT）"),
        FILE10("dep-00249002", "公区装饰画（喷绘方案）"),
        FILE11("dep-00251002", "公区固定家具深化方案"),
        FILE12("dep-00253002", "公区活动家具深化方案"),
        FILE13("dep-00255002", "公区材料列表"),
        FILE14("dep-00257002", "公区百货家具深化方案"),
        FILE15("dep-00292002", "厨房排烟深化方案"),
        FILE16("dep-00293002", "厨房设备深化方案（平面图）"),
        FILE18("dep-00294002", "厨房设备清单");
        private String key;
        private String spec;
    }

    /**
     * 公区机电暖通深化
     */
    @Getter
    @AllArgsConstructor
    public enum IsDrawingGQJDNT {
        FILE1("dep-00265002", "公区弱电深化方案"),
        FILE2("dep-00267002", "公区弱电设备清单"),
        FILE3("dep-00269002", "公区热水热源深化方案"),
        FILE4("dep-00271002", "公区暖通深化方案"),
//        FILE5("dep-00273002", "公区空调深化方案"),
//        FILE6("dep-00275002", "公区新风深化方案"),
        FILE7("dep-00277002", "公区地暖深化方案"),
        FILE8("dep-00279002", "公区给排水深化方案"),
        FILE9("dep-00281002", "公区强电深化方案"),
//        FILE10("dep-00283002", "公区灯具深化方案"),
        FILE11("dep-00285002", "公区其他深化方案");
        private String key;
        private String spec;
    }

    /**
     * 开工申请的图纸带出
     */
    @Getter
    @AllArgsConstructor
    public enum IsDrawingDes {
        FILE1("des-00107043", "客房平面图（CAD/PDF）", "eng-00105035"),
        FILE2("des-00111028", "样板间施工图（CAD/PDF）", "eng-00105036"),
        FILE3("des-00111028", "平面隔墙尺寸图（CAD/PDF）", "eng-00105037");
        private String key;
        private String spec;
        private String intoKey;
    }

    /**
     * 正式开工的图纸带出
     */
    @Getter
    @AllArgsConstructor
    public enum IsDrawingDesTwo {
        FILE1("des-00107043", "客房平面图（CAD/PDF）", "eng-00107030"),
        FILE2("des-00111028", "样板间施工图（CAD/PDF）", "eng-00107031"),
        FILE3("des-00111028", "平面隔墙尺寸图（CAD/PDF）", "eng-00107140"),
        FILE4("des-00107043", "客房平面图（CAD/PDF）", "eng-00137055");
        private String key;
        private String spec;
        private String intoKey;
    }

    /**
     * 深化部分的图纸带出
     */
    @Getter
    @AllArgsConstructor
    public enum IsDrawingDep {
//        FILE1("dep-00203002", "客房深化方案", "eng-00109029"),
        FILE2("dep-00215002", "客房区弱电深化方案", "eng-00109030"),
        FILE3("dep-00223002", "客房区空调深化方案", "eng-00109031"),
        FILE4("dep-00201002", "样板间家具图纸", "eng-00109186");
        private String key;
        private String spec;
        private String intoKey;
    }

    /**
     * 竣工验收申请的验收资料
     */
    @Getter
    @AllArgsConstructor
    public enum CompletionAcceptanceInformation {
        // 需要具体图纸
        FILE1(CompletionDraw.getFile1(), "客房区装饰施工图纸", "eng-00129090"),
        FILE2(CompletionDraw.getFile2(), "公区装饰施工图纸", "eng-00129091"),
        FILE3(CompletionDraw.getFile3(), "消防图纸", "eng-00129092"),
        FILE4(CompletionDraw.getFile4(), "客房区装饰深化方案", "eng-00129093"),
        FILE5(CompletionDraw.getFile5(), "公区装饰深化方案", "eng-00129094"),
        FILE6(CompletionDraw.getFile6(), "厨房图纸", "eng-00129095"),
        FILE7(CompletionDraw.getFile7(), "客房区机电施工图纸", "eng-00129096"),
        FILE8(CompletionDraw.getFile8(), "公区机电施工图纸", "eng-00129097"),
        FILE9(CompletionDraw.getFile9(), "客房区装饰深化方案", "eng-00129098"),
        FILE10(CompletionDraw.getFile10(), "客房区机电深化方案", "eng-00129099"),
        FILE11(CompletionDraw.getFile11(), "公区机电深化方案", "eng-00129100"),
        FILE12(CompletionDraw.getFile12(), "其他图纸", "eng-00129101");
        //需要验收的图纸
        private CompletionDraw[] key;
        private String spec;
        private String intoKey;

    }

    /**
     * 竣工验收申请的验收资料
     */
    @Getter
    @AllArgsConstructor
    public enum CompletionDraw {
        DRAW1_1("des-00107043", "客房平面方案（PPT）"),
        DRAW1_2("des-00107043", "客房平面图（CAD/PDF）"),
        DRAW1_3("des-00107043", "客房效果方案"),
//        DRAW1_4("des-00111028", "平面隔墙尺寸图（CAD/PDF）"),
        DRAW1_4("des-0011102801", "平面隔墙尺寸图（CAD/PDF）"),
        DRAW1_5("des-00115026", "客房装饰施工图"),
        //2
//        DRAW2_1("pad-00135025", "公区装饰施工图（CAD/PDF）"),
//        DRAW2_2("pad-00135025", "亚朵百货施工图纸"),
        DRAW2_1("pad-0013502502", "公区装饰施工图（CAD/PDF）"),
        DRAW2_2("pad-0013502503", "亚朵百货施工图纸"),
        //2
        DRAW3_1("pad-00147007", "消防竣工图（喷淋、报警系统、消火栓、排烟）"),
        DRAW3_2("pad-00147007", "消防点位对照表、平面图"),
        //5
//        DRAW4_1("dep-00203002", "客房深化方案"),
        DRAW4_2("dep-00205002", "客房房号方案"),
        DRAW4_3("dep-00207002", "客房区家具图纸"),
        DRAW4_4("dep-00209002", "客房区装饰画（喷绘方案）"),
        DRAW4_5("dep-00211002", "客房区非标材料清单"),
        //8
        DRAW5_1("dep-00243002", "公区装饰深化图纸"),
        DRAW5_2("dep-00245002", "室内全套VI方案"),
        DRAW5_3("dep-00247002", "公区软装方案（PDF/PPT）"),
        DRAW5_4("dep-00249002", "公区装饰画（喷绘方案）"),
        DRAW5_5("dep-00251002", "公区固定家具深化方案"),
        DRAW5_6("dep-00253002", "公区活动家具深化方案"),
        DRAW5_7("dep-00255002", "公区材料列表"),
        DRAW5_8("dep-00257002", "公区百货家具深化方案"),
        //4
        DRAW6_1("dep-00292002", "厨房排烟深化方案"),
        DRAW6_2("dep-00293002", "厨房设备深化方案（平面图）"),
        DRAW6_3("dep-00294002", "厨房设备清单"),
//        DRAW6_4("pad-00131039", "厨房施工图（PDF/CAD）"),
        //5
        DRAW7_1("des-00119040", "客房电气施工图（CAD/PDF）"),
        DRAW7_2("des-00119040", "客房暖通施工图（CAD/PDF）"),
        DRAW7_3("des-00119041", "客房弱电施工图（CAD/PDF）"),
        DRAW7_4("des-00119042", "客房给排⽔施⼯图（CAD/PDF）"),
        DRAW7_5("des-00119042", "客房综合机电管线图"),
        //5
        DRAW8_1("pad-00139041", "公区电气施工图（CAD/PDF）（含厨房）"),
        DRAW8_2("pad-00139041", "公区暖通施工图（CAD/PDF）（含厨房）"),
        DRAW8_3("pad-00139042", "公区弱电施工图（CAD/PDF）（含厨房）"),
        DRAW8_4("pad-00139043", "公区给排水施工图（CAD/PDF）（含厨房）"),
        DRAW8_5("pad-00139043", "公区给排水施工图（CAD/PDF）（含厨房）"),
        //6
//        DRAW9_1("pad-00131039  pad-00135025", "室外施工图（PDF/CAD）"),
        DRAW9_1("pad-0013502501", "室外施工图（PDF/CAD）"),
        DRAW9_2("dep-00233002", "室外VI方案"),
//        DRAW9_3("dep-00235002", "店招小样"),
        DRAW9_4("dep-00237002", "室外店招深化方案"),
        DRAW9_5("dep-00239002", "室外门头深化方案"),
//        DRAW9_6("dep-00241002", "店招材料清单"),
        //10
        DRAW10_1("dep-00213002", "客房区强电深化方案"),
        DRAW10_2("dep-00215002", "客房区弱电深化方案"),
        DRAW10_3("dep-00217002", "客房区弱电设备清单"),
        DRAW10_4("dep-00219002", "客房区热水热源深化方案"),
//        DRAW10_5("dep-00221002", "客房区暖通深化方案"),
        DRAW10_6("dep-00223002", "客房区空调深化方案"),
        DRAW10_7("dep-00225002", "客房区新风深化方案"),
        DRAW10_8("dep-00227002", "客房区地暖深化方案"),
//        DRAW10_9("dep-00229002", "客房区给排水深化方案"),
        DRAW10_10("dep-00231002", "客房其他深化方案"),
        //11
        DRAW11_1("dep-00265002", "公区弱电深化方案"),
        DRAW11_2("dep-00267002", "公区弱电设备清单"),
        DRAW11_3("dep-00269002", "公区热水热源深化方案"),
        DRAW11_4("dep-00271002", "公区暖通深化方案"),
//        DRAW11_5("dep-00273002", "公区空调深化方案"),
//        DRAW11_6("dep-00275002", "公区新风深化方案"),
        DRAW11_7("dep-00277002", "公区地暖深化方案"),
        DRAW11_8("dep-00279002", "公区给排水深化方案"),
        DRAW11_9("dep-00281002", "公区强电深化方案"),
//        DRAW11_10("dep-00283002", "公区灯具深化方案"),
        DRAW11_11("dep-00285002", "公区其他深化方案"),
        //2
        DRAW12_1("dep-00285002", "公区其他深化方案"),
        DRAW12_2("dep-00231002", "客房其他深化方案");

        private String key;
        private String spec;

        public static CompletionDraw[] getFile1() {
            final CompletionDraw[] values = CompletionDraw.values();
            final CompletionDraw[] array = Arrays.stream(values).limit(5).toArray(CompletionDraw[]::new);
            return array;
        }

        public static CompletionDraw[] getFile2() {
            final CompletionDraw[] values = CompletionDraw.values();
            final CompletionDraw[] array = Arrays.stream(values).skip(5).limit(2).toArray(CompletionDraw[]::new);
            return array;
        }

        public static CompletionDraw[] getFile3() {
            final CompletionDraw[] values = CompletionDraw.values();
            final CompletionDraw[] array = Arrays.stream(values).skip(7).limit(2).toArray(CompletionDraw[]::new);
            return array;
        }

        public static CompletionDraw[] getFile4() {
            final CompletionDraw[] values = CompletionDraw.values();
            final CompletionDraw[] array = Arrays.stream(values).skip(9).limit(5).toArray(CompletionDraw[]::new);
            return array;
        }

        public static CompletionDraw[] getFile5() {
            final CompletionDraw[] values = CompletionDraw.values();
            final CompletionDraw[] array = Arrays.stream(values).skip(14).limit(8).toArray(CompletionDraw[]::new);
            return array;
        }

        public static CompletionDraw[] getFile6() {
            final CompletionDraw[] values = CompletionDraw.values();
            final CompletionDraw[] array = Arrays.stream(values).skip(22).limit(4).toArray(CompletionDraw[]::new);
            return array;
        }

        public static CompletionDraw[] getFile7() {
            final CompletionDraw[] values = CompletionDraw.values();
            final CompletionDraw[] array = Arrays.stream(values).skip(26).limit(5).toArray(CompletionDraw[]::new);
            return array;
        }

        public static CompletionDraw[] getFile8() {
            final CompletionDraw[] values = CompletionDraw.values();
            final CompletionDraw[] array = Arrays.stream(values).skip(31).limit(5).toArray(CompletionDraw[]::new);
            return array;
        }

        public static CompletionDraw[] getFile9() {
            final CompletionDraw[] values = CompletionDraw.values();
            final CompletionDraw[] array = Arrays.stream(values).skip(36).limit(6).toArray(CompletionDraw[]::new);
            return array;
        }

        public static CompletionDraw[] getFile10() {
            final CompletionDraw[] values = CompletionDraw.values();
            final CompletionDraw[] array = Arrays.stream(values).skip(42).limit(10).toArray(CompletionDraw[]::new);
            return array;
        }

        public static CompletionDraw[] getFile11() {
            final CompletionDraw[] values = CompletionDraw.values();
            final CompletionDraw[] array = Arrays.stream(values).skip(52).limit(11).toArray(CompletionDraw[]::new);
            return array;
        }

        public static CompletionDraw[] getFile12() {
            final CompletionDraw[] values = CompletionDraw.values();
            final CompletionDraw[] array = Arrays.stream(values).skip(63).limit(2).toArray(CompletionDraw[]::new);
            return array;
        }

    }


    /**
     * 弱电验收申请的验收资料
     */
    @Getter
    @AllArgsConstructor
    public enum WeakCurrentInformation {
        FILE1(WeakCurrentDraw.getFile1(), "客房平面图", "eng-00137055"),
        FILE2(WeakCurrentDraw.getFile2(), "公区平面图", "eng-00137056"),
        FILE3(WeakCurrentDraw.getFile3(), "客房VI标识", "eng-00137057"),
        FILE4(WeakCurrentDraw.getFile4(), "弱电图纸（系统图、竣工图、施工图）", "eng-00137058"),
        FILE5(WeakCurrentDraw.getFile5(), "厨房及设备的图纸", "eng-00137059"),
        FILE6(WeakCurrentDraw.getFile6(), "户外VI标识设计图纸", "eng-00137060"),
        FILE7(WeakCurrentDraw.getFile7(), "弱电报验资料", "eng-00137061");
        //需要验收的图纸
        private WeakCurrentDraw[] key;
        private String spec;
        private String intoKey;
    }

    /**
     * 弱电验收申请的验收资料
     */
    @Getter
    @AllArgsConstructor
    public enum WeakCurrentDraw {
        DRAW1_1("des-00107043", "客房平面方案（PPT）"),
        DRAW1_2("des-00107043", "客房平面图（CAD/PDF）"),
        DRAW2_1("pad-00127037", "公区平⾯图（PDF/CAD）"),
        DRAW3_1("dep-00205002", "客房区VI方案"),
        DRAW4_1("dep-00215002", "客房区弱电深化方案"),
        DRAW4_2("dep-00217002", "客房区弱电设备清单"),
        DRAW4_3("dep-00219002", "客房区热水热源深化方案"),
        DRAW4_4("dep-00221002", "客房区暖通深化方案"),
        DRAW4_5("dep-00225002", "客房区新风深化方案"),
        DRAW4_6("dep-00227002", "客房区地暖深化方案"),
//        DRAW5_1("pad-00131039", "厨房施工图（PDF/CAD）"),
        DRAW5_2("dep-00259002", "厨房排烟深化方案"),
        DRAW5_3("dep-00261002", "厨房设备深化方案（平面图）"),
        DRAW5_4("dep-00263002", "厨房设备清单"),
        DRAW6_1("dep-00233002", "室外VI方案"),
        DRAW7_1("des-00119041", "客房弱电施工图（CAD/PDF）"),
        DRAW7_2("pad-00139042", "公区弱电施工图（CAD/PDF）");

        public static WeakCurrentDraw[] getFile1() {
            final WeakCurrentDraw[] values = WeakCurrentDraw.values();
            final WeakCurrentDraw[] array = Arrays.stream(values).limit(2).toArray(WeakCurrentDraw[]::new);
            return array;
        }

        public static WeakCurrentDraw[] getFile2() {
            final WeakCurrentDraw[] values = WeakCurrentDraw.values();
            final WeakCurrentDraw[] array = Arrays.stream(values).skip(2).limit(1).toArray(WeakCurrentDraw[]::new);
            return array;
        }

        public static WeakCurrentDraw[] getFile3() {
            final WeakCurrentDraw[] values = WeakCurrentDraw.values();
            final WeakCurrentDraw[] array = Arrays.stream(values).skip(3).limit(1).toArray(WeakCurrentDraw[]::new);
            return array;
        }

        public static WeakCurrentDraw[] getFile4() {
            final WeakCurrentDraw[] values = WeakCurrentDraw.values();
            final WeakCurrentDraw[] array = Arrays.stream(values).skip(4).limit(6).toArray(WeakCurrentDraw[]::new);
            return array;
        }

        public static WeakCurrentDraw[] getFile5() {
            final WeakCurrentDraw[] values = WeakCurrentDraw.values();
            final WeakCurrentDraw[] array = Arrays.stream(values).skip(10).limit(4).toArray(WeakCurrentDraw[]::new);
            return array;
        }

        public static WeakCurrentDraw[] getFile6() {
            final WeakCurrentDraw[] values = WeakCurrentDraw.values();
            final WeakCurrentDraw[] array = Arrays.stream(values).skip(14).limit(1).toArray(WeakCurrentDraw[]::new);
            return array;
        }

        public static WeakCurrentDraw[] getFile7() {
            final WeakCurrentDraw[] values = WeakCurrentDraw.values();
            final WeakCurrentDraw[] array = Arrays.stream(values).skip(15).limit(2).toArray(WeakCurrentDraw[]::new);
            return array;
        }

        private String key;
        private String spec;
    }

    @Getter
    @AllArgsConstructor
    public enum ReviewDrawingStatus {

        NULL("--", "--"),
        STARTING_FROM_THE_END("starting_from_the_end", "末提交"),
        IN_THE_REVIEW_OF_DRAWINGS("in_the_review_of_drawings", "审图中"),
        APPROVAL_REJECTION("approval_rejection", "审批拒绝"),
        APPROVED("approved", "审批通过"),
        NOT_APPLICABLE("not_applicable", "不适用");
        private String key;
        private String spec;

        public static String getReviewDrawingStatus(String key) {
            AtourSystemEnum.ReviewDrawingStatus[] values = AtourSystemEnum.ReviewDrawingStatus.values();
            for (AtourSystemEnum.ReviewDrawingStatus drawingStatus : values) {
                if (drawingStatus.getKey().contains(key)) {
                    return drawingStatus.getSpec();
                }
            }
            return "不适用";
        }

    }

    /**
     * 节点进展状态
     */
    @Getter
    @AllArgsConstructor

    public enum ScheduleStatus {


        UNOPENED("unopened", "未开启"),
        COMPLETED("completed", "已完成"),
        ADVANCE_COMPLETED("advance_completed", "提前已完成"),
        OVERDUE_COMPLETED("overdue_completed", "逾期已完成"),
        OVERDUE("overdue", "逾期"),
        ONGOING("ongoing", "进行中");

        private String key;
        private String spec;

    }

    /**
     * 分配状态
     */
    @Getter
    @AllArgsConstructor
    public enum AllocationStatus {

        //已分配
        ALLOCATED("allocated", "已分配"),
        //未分配
        UNALLOCATED("unallocated", "未分配");
        private String key;
        private String spec;

    }

    /**
     * 工程角色编码
     */
    @Getter
    @AllArgsConstructor
    public enum engineeringRoleCodeEnum {
        //运营战区负责人
        YYZQFZR("yyzqfzr", "运营战区负责人"),
        //开业经理
        KYJL("kyjl", "开业经理"),
        //运营经理
        YYJL("yyjl", "运营经理"),
        //特许经理
        TXJL("txjl", "特许经理"),
        //开发战区负责人
        KFZQFZR("kfzqfzr", "开发战区负责人"),
        //开发分区负责人
        KFFQFZR("kffqfzr", "开发分区负责人"),
        //开发经理
        KFJL("kfjl", "开发经理"),
        //营建区域负责人
        YJQYFZR("zbzxfzr", "营建区域负责人"),
        //项目经理
        GCJL("gcjl", "项目经理"),
        //设计师
        SJS("sjs", "设计师"),
        //机电工程师
        JDGCS("jdgcs", "机电工程师"),
        //弱电工程师
        RDGCS("rdgcs", "弱电工程师"),
        //设计共管人员
        SJGGRY("sjggry", "设计共管人员"),
        //弱电验收人员
        RDYSRY("rdysry", "弱电验收人员"),
        //机电验收人员
        JDYSRY("jdysry", "机电验收人员"),
        //飞行质检人员
        FXZJRY("fxzjry", "飞行质检人员"),
        //竣工验收人员
        JGYSRY("jgysry", "竣工验收人员"),
        //采购销售
        CGXS("cgxs", "采购销售"),

        //交付中心-共享支持
        JFZXGXZC("jfzx-gxzc", "交付中心-共享支持"),
        //交付中心-工程负责人
        JFZXGCFZR("jfzx-gcfzr", "交付中心-工程负责人"),

        //竣工验收申请-资料审核人
        ZLSH("zlsh", "竣工验收申请-资料审核人"),
        //竣工验收申请-整改审核人
        ZGSH("zgsh", "竣工验收申请-整改审核人"),



        //业主
        YZ("yz", "业主"),
        //业主项目经理  特许商营建授权人
        YZXMJL("yzxmjl", "业主项目经理"),
        //设计单位
        SJDW("sjdw", "设计单位"),
        //现长
        XZ("xz", "现长"),
        //交付中心-技术负责人
        JFZXJSFZR("jfzx-jsfzr", "交付中心-技术负责人"),
        //交付中心-部门负责人
        JFZXBMFZR("jfzx-bmfzr", "交付中心-部门负责人"),
        //装修设计师(设计单位设计师)
        ZXSJS("sjdwsjs", "装修设计师"),
        //机电设计师
        JDSJS("jdsjs", "机电设计师"),

        //装修单位-项目经理
        ZXDWXMJL("zxdw-xmjl", "装修单位-项目经理"),
        //总包单位-项目经理
        ZBDWXMJL("zbdw-xmjl", "总包单位-项目经理"),
        //空调单位-项目经理
        KTDWXMJL("ktdw-xmjl", "空调单位-项目经理"),
        //消防单位-项目经理
        XFDWXMJL("xfdw-xmjl", "消防单位-项目经理"),
        //物料单位-项目经理
        WLDWXMJL("wldw-xmjl", "物料单位-项目经理"),
        //软装设计
        RZSJ("rzsj", "软装设计"),
        //HBG负责人
        HBGFZR("hbgfzr", "HBG负责人"),
        //法务经理
        FWJL("fwjl", "法务经理"),
        //住宿运营负责人
        ZSYYFZR("zsyyfzr", "住宿运营负责人"),
        //运维负责人
        YWFZR("ywfzr", "运维负责人"),
        //共享支持设计负责人
        GXZCSJFZR("gxzcsjfzr", "共享支持设计负责人"),
        //交付中心负责人
        JFZXFZR("jfzxfzr", "交付中心负责人");
        private final String key;
        private final String spec;
    }

    /**
     * 开发系统
     */
    @Getter
    @AllArgsConstructor
    public enum DevelopmentSystem {
        ENG00101002("eng-00101002", "项目ID"),
        ENG00101003("eng-00101003", "项目名称"),
        ENG00101004("eng-00101004", "省份"),
        ENG00101005("eng-00101005", "城市"),
        ENG00101006("eng-00101006", "品牌名称"),
        ENG00101007("eng-00101007", "执行标准"),
        ENG00101008("eng-00101008", "决策意见"),
        ENG00101009("eng-00101009", "证照情况描述"),
        ENG00101010("eng-00101010", "法务约定工程内容"),
        ENG00101011("eng-00101011", "法务约定采购内容"),
        ENG00101012("eng-00101012", "房间数"),
        ENG00101013("eng-00101013", "签约时间"),
        ENG00101015("eng-00101015", "红线描述"),
        ENG00101016("eng-00101016", "营建备忘录特殊事项描述"),
        ENG00101035("eng-00101035", "酒店ID"),
        ENG00101036("eng-00101036", "项目性质"),
        ENG00101037("eng-00101037", "产品名称"),
        ENG00101038("eng-00101038", "战区"),
        ENG00101039("eng-00101039", "运营城区"),
        ENG00101040("eng-00101040", "生效定名"),
        ENG00101041("eng-00101041", "是否翻牌"),
        ENG00101042("eng-00101042", "开发经理"),
        ENG00101043("eng-00101043", "特许商对接人"),
        ENG00101044("eng-00101044", "对接人手机号"),
        ENG00101045("eng-00101045", "对接人邮箱"),
        ENG00101046("eng-00101046", "签约日期"),
        ENG00101047("eng-00101047", "签约房量"),
        ENG00101048("eng-00101048", "上会附件"),
        ENG00101049("eng-00101049", "上会结论"),
        ENG00101050("eng-00101050", "上会次数"),
        ENG00101051("eng-00101051", "决策状态"),
        ENG00101052("eng-00101052", "决策概述"),

        ENG00103051("eng-00103051", "特许商营建对接人"),
        ENG00103052("eng-00103052", "联系电话"),
        ENG00103053("eng-00103053", "联系邮箱"),
        ENG00103054("eng-00103054", "业主方"),
        ENG00103056("eng-00103056", "变更人员为"),
        ENG00103091("eng-00103091", "开发上会报告"),
        ENG00103092("eng-00103092", "筹建告知书"),
        ENG00103093("eng-00103093", "红线图"),
        ENG00103094("eng-00103094", "特许之附件"),
        ENG00103095("eng-00103095", "合同约定工程内容附件"),
        ENG00103096("eng-00103096", "合同约定采购内容附件"),
        ENG00103097("eng-00103097", "营建备忘录"),
        ENG00103098("eng-00103098", "X项目改造清单"),
        ENG00103099("eng-00103099", "版本号"),
        ENG00103169("eng-00103169", "酒店ID"),
        ENG00103170("eng-00103170", "项目ID"),
        ENG00103132("eng-00103132", "项目名称"),
        ENG00103106("eng-00103106", "项目性质"),
        ENG00103127("eng-00103127", "城市"),
        ENG00103002("eng-00103002", "品牌名称"),
        ENG00103107("eng-00103107", "执行标准"),
        ENG00103108("eng-00103108", "战区"),
        ENG00103109("eng-00103109", "运营城区"),
        ENG00103110("eng-00103110", "生效定名"),
        ENG00103111("eng-00103111", "是否翻牌"),
        ENG00103112("eng-00103112", "开发经理"),
        ENG00103113("eng-00103113", "特许商对接人"),
        ENG00103114("eng-00103114", "对接人手机号"),
        ENG00103115("eng-00103115", "对接人邮箱"),
        ENG00103116("eng-00103116", "签约日期"),
        ENG00103117("eng-00103117", "签约房量"),
        ENG00103118("eng-00103118", "上会附件"),
        ENG00103140("eng-00103140", "上会次数"),
        ENG00103141("eng-00103141", "决策状态"),
        ENG00103142("eng-00103142", "决策概述"),
        ENG00103014("eng-00103014", "法务约定工程内容"),
        ENG00103027("eng-00103027", "红线描述"),
        ENG00103030("eng-00103030", "营建备忘录特殊事项描述"),
        ENG00103175("eng-00103175", "项目启动日期"),

        ENG00103102("eng-00103102", "特许商营建授权人"),
        ENG00103103("eng-00103103", "联系电话"),
        ENG00103104("eng-00103104", "联系邮箱"),

        DES00101037("des-00101037", "物业周边情况"),
        DES00101040("des-00101040", "外立面及窗户隔音"),
        DES00101043("des-00101043", "给水、热源"),
        DES00101046("des-00101046", "通风"),
        DES00101049("des-00101049", "电梯"),
        DES00101052("des-00101052", "强弱电"),
        DES00101055("des-00101055", "燃气"),
        DES00101058("des-00101058", "屋面"),
        DES00101062("des-00101062", "餐饮业服务场所（涉异味）"),
        DES00101065("des-00101065", "KTV（涉噪音）等娱乐场所"),
        DES00101068("des-00101068", "洗浴中心等休闲会所");

        private final String key;
        private final String spec;
    }

    /**
     * 设计勘测的设计单位信息带出到确认设计单位
     */
    @Getter
    @AllArgsConstructor
    public enum BroughtOutDesignUnitInformation {
        DES00101075("des-00101075", "des-00127019"),
        DES00101076("des-00101076", "des-00127020"),
        DES00101077("des-00101077", "des-00127021"),
        DES00101078("des-00101078", "des-00127022"),
        DES00101079("des-00101079", "des-00127023"),
        DES00101080("des-00101080", "des-00127024"),
        DES00101081("des-00101081", "des-00127025"),
        DES00101082("des-00101082", "des-00127026"),
        DES00101083("des-00101083", "des-00127027"),
        DES00101084("des-00101084", "des-00127028"),
        DES00101085("des-00101085", "des-00127029"),
        DES00101086("des-00101086", "des-00127030"),
        DES00101087("des-00101087", "des-00127031"),
        DES00101088("des-00101088", "des-00127032");

        private final String codeA;
        private final String codeB;
    }

    /**
     * 开发系统
     */
    @Getter
    @AllArgsConstructor
    public enum completionAcceptanceEnum {
        ENG00133012("eng-00133012", "验收日期"),
        ENG00133047("eng-00133047", "验收时间"),
        ENG00133009("eng-00133009", "总房量"),
        ENG00133022("eng-00133022", "酒店建筑"),
        ENG00133023("eng-00133023", "风险管理"),
        ENG00133024("eng-00133024", "设施管理"),
        ENG00133025("eng-00133025", "物业划分"),

        ENG00133030("eng-00133030", "联动测试"),
        ENG00133031("eng-00133031", "消防广播"),
        ENG00133032("eng-00133032", "安装标准"),
        ENG00133033("eng-00133033", "消防水系统"),
        ENG00133048("eng-00133048", "消防疏散"),
        ENG00133035("eng-00133035", "三方通话"),
        ENG00133036("eng-00133036", "梯控"),
        ENG00133037("eng-00133037", "合格证"),
        ENG00133039("eng-00133039", "门禁"),
        ENG00133041("eng-00133041", "验收单各总项汇总"),



        ENG00141008("eng-00141008", "验收日期"),
        ENG00129024("eng-00129024", "申请验收房间数"),
        ENG00129023("eng-00129023", "设计房间数"),
        ENG00129025("eng-00129025", "验收房号"),
        ENG00129058("eng-00129058", "验收人"),
        ENG00129112("eng-00129112", "项目决策意见(截图)"),
        ENG00129196("eng-00129196", "资料审核人"),

        ENG00129188("eng-00129188", "门店紧急维修联系表(按照固定模版)"),
        ENG00129189("eng-00129189", "提供日期"),
        ENG00129192("eng-00129192", "合伙人满意度评分表"),
        ENG00129193("eng-00129193", "提供日期"),

        ENG00135013("eng-00135013", "整改承诺书"),
        ENG00135011("eng-00135011", "复核状态"),
        ENG00135012("eng-00135012", "复核不合格原因"),
        ENG00135014("eng-00135014", "提交返回的POST");


        private final String key;
        private final String spec;
    }



    /**
     * 筹建启动会
     */
    @Getter
    @AllArgsConstructor
    public enum PreparationKickoffMeeting {
        ENG00103003("eng-00103003", "现场核对"),
        ENG00103004("eng-00103004", "不一致性说明"),
        ENG00103006("eng-00103006", "现场核对"),
        ENG00103007("eng-00103007", "不一致性说明"),
        ENG00103009("eng-00103009", "现场核对"),
        ENG00103010("eng-00103010", "不一致性说明"),
        ENG00103012("eng-00103012", "现场核对"),
        ENG00103013("eng-00103013", "不一致性说明"),
        ENG00103015("eng-00103015", "现场核对"),
        ENG00103016("eng-00103016", "不一致性说明"),
        ENG00103018("eng-00103018", "现场核对"),
        ENG00103019("eng-00103019", "不一致性说明"),
        ENG00103021("eng-00103021", "现场核对"),
        ENG00103022("eng-00103022", "不一致性说明"),
        ENG00103024("eng-00103024", "现场核对"),
        ENG00103025("eng-00103025", "不一致性说明"),
        ENG00103028("eng-00103028", "现场核对"),
        ENG00103029("eng-00103029", "不一致性说明"),
        ENG00103038("eng-00103038", "客房设计单位"),
        ENG00103039("eng-00103039", "客房装饰设计师"),
        ENG00103040("eng-00103040", "客房机电单位"),
        ENG00103041("eng-00103041", "客房机电设计师"),
        ENG00103042("eng-00103042", "公区设计单位"),
        ENG00103043("eng-00103043", "公区装饰设计师"),
        ENG00103044("eng-00103044", "公区机电单位"),
        ENG00103045("eng-00103045", "公区机电设计师");
        private final String key;
        private final String spec;
    }

    /**
     * 干系人update
     */
    @Getter
    @AllArgsConstructor
    public enum UpdateStakeholders {
        ENG00103056("eng-00103056", "变更人员为"),
        ENG00103055("eng-00103055", "业主制定对接人员是否变更"),
        ENG00103103("eng-00103103", "联系电话");
        private final String key;
        private final String spec;
    }


    /**
     * 设计师和项目经理的带出
     */
    @Getter
    @AllArgsConstructor
    public enum BreakOutSjsXmjl {
        SJS1("eng-00129027", "设计师"),
        SJS2("eng-00133017", "设计师"),
        SJS3("eng-00137009", "设计师"),
        SJS4("eng-00139016", "设计师"),
        XMJL8("eng-00129029", "项目经理"),
        XMJL9("eng-00133018", "项目经理"),
        XMJL10("eng-00137011", "项目经理"),
        XMJL11("eng-00139017", "项目经理");

        public static BreakOutSjsXmjl[] sjs() {
            final BreakOutSjsXmjl[] values = BreakOutSjsXmjl.values();
            final BreakOutSjsXmjl[] sjs = Arrays.stream(values).limit(4).toArray(BreakOutSjsXmjl[]::new);
            return sjs;
        }

        public static BreakOutSjsXmjl[] xmjl() {
            final BreakOutSjsXmjl[] values = BreakOutSjsXmjl.values();
            final BreakOutSjsXmjl[] xmjl = Arrays.stream(values).skip(4).toArray(BreakOutSjsXmjl[]::new);
            return xmjl;
        }

        private final String key;
        private final String spec;
    }

    /**
     * 工程问题整改模板的二级节点
     */
    @Getter
    @AllArgsConstructor
    public enum engineeringRectificationIssuesNodeTwo {
        ERI00103("eri-00103", "工程常规检查"),
        SSI00105("ssi-00105", "竣工常规项目自检"),
        RSI00107("rsi-00107", "竣工系统自检"),
        DSR00109("dsr-00109", "设计样板间验收");
        private final String key;
        private final String spec;
    }

    /**
     * 工程问题整改模板的三级节点
     */
    @Getter
    @AllArgsConstructor
    public enum EngineeringRectificationIssuesNodeThree {
        ERI00103002("eri-00103002", "问题类型", "other"),
        ERI00103003("eri-00103003", "任务节点", "other"),
        ERI00103004("eri-00103004", "功能区域", "functionalRegion"),
        ERI00103005("eri-00103005", "检查类别", "qualityName"),
        ERI00103006("eri-00103006", "检查内容", "qualityDescribe"),
        ERI00103007("eri-00103007", "问题重要性", "issueImportance"),
        ERI00103008("eri-00103008", "问题描述附件", "describeFile"),

        SSI00105002("ssi-00105002", "问题类型", null),
        SSI00105003("ssi-00105003", "任务节点", null),
        SSI00105004("ssi-00105004", "功能区域", null),
        SSI00105005("ssi-00105005", "检查类别", null),
        SSI00105006("ssi-00105006", "检查内容", null),
        SSI00105007("ssi-00105007", "问题重要性", null),
        SSI00105008("ssi-00105008", "问题描述附件", null),
        SSI00105009("ssi-00105009", "是否需要非标审批", null),

        RSI00107002("rsi-00107002", "问题类型", null),
        RSI00107003("rsi-00107003", "任务节点", null),
        RSI00107004("rsi-00107004", "项目", null),
        RSI00107005("rsi-00107005", "内容", null),
        RSI00107006("rsi-00107006", "结论", null),
        RSI00107007("rsi-00107007", "说明", null),

        DSR00109002("dsr-00109002", "检查分项", null),
        DSR00109003("dsr-00109003", "验收事项描述", null),
        DSR00109004("dsr-00109004", "验收情况", null),
        DSR00109005("dsr-00109005", "验收描述", null),
        DSR00109006("dsr-00109006", "建议整改方案", null),
        DSR00109007("dsr-00109007", "跟进人员", null),
        DSR00109008("dsr-00109008", "现场照片", null);


        public static EngineeringRectificationIssuesNodeThree[] codeERI() {
            final EngineeringRectificationIssuesNodeThree[] values = AtourSystemEnum.EngineeringRectificationIssuesNodeThree.values();
            final EngineeringRectificationIssuesNodeThree[] code = Arrays.stream(values).limit(7).toArray(EngineeringRectificationIssuesNodeThree[]::new);
            return code;
        }

        public static EngineeringRectificationIssuesNodeThree[] codeSSI() {
            final EngineeringRectificationIssuesNodeThree[] values = AtourSystemEnum.EngineeringRectificationIssuesNodeThree.values();
            final EngineeringRectificationIssuesNodeThree[] code = Arrays.stream(values).skip(7).limit(8).toArray(EngineeringRectificationIssuesNodeThree[]::new);
            return code;
        }

        public static EngineeringRectificationIssuesNodeThree[] codeRSI() {
            final EngineeringRectificationIssuesNodeThree[] values = AtourSystemEnum.EngineeringRectificationIssuesNodeThree.values();
            final EngineeringRectificationIssuesNodeThree[] code = Arrays.stream(values).skip(15).limit(6).toArray(EngineeringRectificationIssuesNodeThree[]::new);
            return code;
        }

        public static EngineeringRectificationIssuesNodeThree[] codeDSR() {
            final EngineeringRectificationIssuesNodeThree[] values = AtourSystemEnum.EngineeringRectificationIssuesNodeThree.values();
            final EngineeringRectificationIssuesNodeThree[] code = Arrays.stream(values).skip(21).limit(7).toArray(EngineeringRectificationIssuesNodeThree[]::new);
            return code;
        }

        private final String key;
        private final String spec;
        private final String fieldName;
    }

    /**
     * 施工日志的带出
     */
    @Getter
    @AllArgsConstructor
    public enum BroughtOutCol {
        COL00101002("col-00101002", "工程名称"),
        COL00101003("col-00101003", "项目地址"),
        COL00101007("col-00101007", "开工日期");

        private final String key;
        private final String spec;
    }


    /**
     *
     */
    @Getter
    @AllArgsConstructor
    public enum nodeCodeIdentifying {
        COL("col", "施工日志"),
        VSF("vsf", "签证报备");

        private final String key;
        private final String spec;
    }

    /**
     * 一级节点
     */
    @Getter
    @AllArgsConstructor
    public enum NodeOne {
        COL001("col-001","施工日志");
        private final String key;
        private final String spec;
    }


    /**
     * 工程的二级节点
     */
    @Getter
    @AllArgsConstructor
    public enum EngineeringNodeTow {
        ENG00101("eng-00101", "营建对接启动"),
        ENG00103("eng-00103", "筹建启动会"),
        ENG00105("eng-00105", "开工申请"),
        ENG00107("eng-00107", "正式开工"),
        ENG00109("eng-00109", "样板间隐蔽验收"),
        ENG00113("eng-00113", "样板间验收申请"),
        ENG00111("eng-00111", "样板间验收"),
        ENG00115("eng-00115", "隐蔽验收申请"),
        ENG00117("eng-00117", "隐蔽验收"),
        ENG00119("eng-00119", "封板前验收"),
        ENG00121("eng-00121", "派驻现长"),
        ENG00123("eng-00123", "家具进场"),
        ENG00125("eng-00125", "形象进度80%"),
        ENG00127("eng-00127", "竣工自检"),
        ENG00145("eng-00145", "竣工自检申请"),
        ENG00129("eng-00129", "竣工验收申请"),
        ENG00131("eng-00131", "业主方整改"),
        ENG00133("eng-00133", "竣工验收"),
        ENG00137("eng-00137", "弱电验收申请"),
        ENG00139("eng-00139", "弱电验收"),
        ENG00135("eng-00135", "竣工验收整改");
        private final String key;
        private final String spec;
    }

    /**
     * 设计的二级节点
     */
    @Getter
    @AllArgsConstructor
    public enum DesignNodeTow {
        DES00101("des-00101", "设计勘测"),
        DES00103("des-00103", "装饰勘测报告"),
        DES00105("des-00105", "设计启动"),
        DES00107("des-00107", "客房平面审核"),
        DES00111("des-00111", "客房样板间施工图审核"),
        DES00115("des-00115", "客房装饰施工图审核"),
        DES00117("des-00117", "客房装饰施工图整改"),
        PAD00127("pad-00127", "公区概念方案审核"),
        PAD00129("pad-00129", "公区概念方案整改"),
        PAD00135("pad-00135", "公区装饰施工图审核"),
        PAD00137("pad-00137", "公区装饰施工图整改"),
        DES00165("des-00165", "机电勘测报告"),
        DES00125("des-00125", "样板间验收"),
        SUP00101("sup-00101", "供应商信息"),
        DES00127("des-00127", "确认设计单位");
        private final String key;
        private final String spec;
    }

    /**
     * 决策的二级节点
     */
    @Getter
    @AllArgsConstructor
    public enum DecisionMakingTwo {
        DEC00101("dec-00101", "品牌"),
        DEC00201("dec-00201", "红线范围"),
        DEC00301("dec-00301", "营建勘测报告"),
        DEC00401("dec-00401", "改造清单"),
        DEC00501("dec-00501", "筹建重要事项告知书"),
        DEC00601("dec-00601", "设计版本"),
        DEC00701("dec-00701", "项目决策报告"),
        DEC00801("dec-00801", "投资估算"),
        DEC00901("dec-00901", "加盟合同");
        private final String key;
        private final String spec;
    }

    /**
     * 开工申请
     */
    @Getter
    @AllArgsConstructor
    public enum ApplicationForCommencementOfWork {
        ENG00105033("eng-00105033", "客房设计院认证人员"),
        ENG00105034("eng-00105034", "公区设计院认证人员"),
        ENG00105035("eng-00105035", "客房平面布置图"),
        ENG00105036("eng-00105036", "客房样板间施工图"),
        ENG00105037("eng-00105037", "客房隔墙尺寸图");
        private final String key;
        private final String spec;
    }

    /**
     * 【开工申请】的施工单位项目经理
     */
    @Getter
    @AllArgsConstructor
    public enum ZXDWXMJL {
        ENG00105020("eng-00105020", "施工单位项目经理"),
        COL00101005("col-00101005", "施工项目经理");

        private final String key;
        private final String spec;
    }

    /**
     * 正式开工
     */
    //@Getter
    //@AllArgsConstructor
    //public enum OfficialStartOfConstruction {
    //    ENG00105033("eng-00105033", "客房设计院认证人员"),
    //    ENG00105034("eng-00105034", "公区设计院认证人员"),
    //    ENG00105035("eng-00105035", "客房平面布置图"),
    //    ENG00105036("eng-00105036", "客房样板间施工图"),
    //    ENG00105037("eng-00105037", "客房隔墙尺寸图");
    //    private final String key;
    //    private final String spec;
    //}

    /**
     * 正式开工的【安全管理】带出到【样板间隐蔽验收】
     */
    @Getter
    @AllArgsConstructor
    public enum SafeAndCivilizedConstruction {
        ENG00107("eng-00107150-table_585", "eng-00109149-table_1174"),
        ENG00107MAJOR("eng-00107150-table_585", "eng-00111195-table_1182"),
        ENG00109("eng-00109149-table_1174", "eng-00111195-table_1182"),
        ENG00111("eng-00111195-table_1182", "eng-00117221-table_1190"),

        ENG00117("eng-00117221-table_1190", "eng-00119223-table_1198"),
        ENG00119("eng-00119223-table_1198", "eng-00123225-table_1206"),
        ENG00123("eng-00123225-table_1206", "eng-00125227-table_1214"),
        ENG00127("eng-00117221-table_1190", "eng-00127229-table_1222"),
        HEAD1(null, "eng-00119223-table-head"),
        HEAD2(null, "eng-00123225-table-head"),
        HEAD3(null, "eng-00125227-table-head"),
        HEAD4(null, "eng-00127229-table-head");

        public static SafeAndCivilizedConstruction[] head() {
            final SafeAndCivilizedConstruction[] values = AtourSystemEnum.SafeAndCivilizedConstruction.values();
            final SafeAndCivilizedConstruction[] head = Arrays.stream(values).skip(7).toArray(SafeAndCivilizedConstruction[]::new);
            return head;
        }

        private final String outKey;
        private final String intoKey;
    }

    /**
     * 样板间隐蔽验收
     */
    @Getter
    @AllArgsConstructor
    public enum SampleRoomConcealedAcceptance {
        ENG00109052("eng-00109052", "进展状态"),
        ENG00109056("eng-00109056", "进展状态"),
        ENG00109060("eng-00109060", "进展状态"),
        ENG00109064("eng-00109064", "进展状态"),
        ENG00109068("eng-00109068", "进展状态"),
        ENG00109053("eng-00109053", "单位名称"),
        ENG00109057("eng-00109057", "单位名称"),
        ENG00109061("eng-00109061", "单位名称"),
        ENG00109065("eng-00109065", "单位名称"),
        ENG00109069("eng-00109069", "单位名称"),
        ENG00109054("eng-00109054", "联系人"),
        ENG00109058("eng-00109058", "联系人"),
        ENG00109062("eng-00109062", "联系人"),
        ENG00109066("eng-00109066", "联系人"),
        ENG00109070("eng-00109070", "联系人");
        private final String key;
        private final String spec;
    }

    /**
     * 样板间验收
     */
    @Getter
    @AllArgsConstructor
    public enum SampleRoomAcceptance {
        ENG00111096("eng-00111096", "进展状态"),
        ENG00111100("eng-00111100", "进展状态"),
        ENG00111104("eng-00111104", "进展状态"),
        ENG00111108("eng-00111108", "进展状态"),
        ENG00111112("eng-00111112", "进展状态"),
        ENG00111097("eng-00111097", "单位名称"),
        ENG00111101("eng-00111101", "单位名称"),
        ENG00111105("eng-00111105", "单位名称"),
        ENG00111109("eng-00111109", "单位名称"),
        ENG00111113("eng-00111113", "单位名称"),
        ENG00111098("eng-00111098", "联系人"),
        ENG00111102("eng-00111102", "联系人"),
        ENG00111106("eng-00111106", "联系人"),
        ENG00111110("eng-00111110", "联系人"),
        ENG00111114("eng-00111114", "联系人");
        private final String key;
        private final String spec;
    }

    /**
     * 隐蔽验收
     */
    @Getter
    @AllArgsConstructor
    public enum ConcealedAcceptance {
        ENG00117096("eng-00117096", "进展状态"),
        ENG00117100("eng-00117100", "进展状态"),
        ENG00117104("eng-00117104", "进展状态"),
        ENG00117108("eng-00117108", "进展状态"),
        ENG00117112("eng-00117112", "进展状态"),
        ENG00117116("eng-00117116", "进展状态"),
        ENG00117120("eng-00117120", "进展状态"),
        ENG00117124("eng-00117124", "进展状态"),
        ENG00117128("eng-00117128", "进展状态"),
        ENG00117097("eng-00117097", "单位名称"),
        ENG00117101("eng-00117101", "单位名称"),
        ENG00117105("eng-00117105", "单位名称"),
        ENG00117109("eng-00117109", "单位名称"),
        ENG00117113("eng-00117113", "单位名称"),
        ENG00117117("eng-00117117", "单位名称"),
        ENG00117121("eng-00117121", "单位名称"),
        ENG00117125("eng-00117125", "单位名称"),
        ENG00117129("eng-00117129", "单位名称"),
        ENG00117098("eng-00117098", "联系人"),
        ENG00117102("eng-00117102", "联系人"),
        ENG00117106("eng-00117106", "联系人"),
        ENG00117110("eng-00117110", "联系人"),
        ENG00117114("eng-00117114", "联系人"),
        ENG00117118("eng-00117118", "联系人"),
        ENG00117122("eng-00117122", "联系人"),
        ENG00117126("eng-00117126", "联系人"),
        ENG00117130("eng-00117130", "联系人"),
        ENG00117318("eng-00117318", "信号源"),
        ENG00117319("eng-00117319", "联系电话");
        private final String key;
        private final String spec;
    }

    /**
     * 隐蔽验收
     */
    @Getter
    @AllArgsConstructor
    public enum ConcealedAcceptanceA {
        ENG00117096("eng-00117096", "进展状态"),
        ENG00117100("eng-00117100", "进展状态"),
        ENG00117104("eng-00117104", "进展状态"),
        ENG00117108("eng-00117108", "进展状态"),
        ENG00117112("eng-00117112", "进展状态"),
        ENG00117097("eng-00117097", "单位名称"),
        ENG00117101("eng-00117101", "单位名称"),
        ENG00117105("eng-00117105", "单位名称"),
        ENG00117109("eng-00117109", "单位名称"),
        ENG00117113("eng-00117113", "单位名称"),
        ENG00117098("eng-00117098", "联系人"),
        ENG00117102("eng-00117102", "联系人"),
        ENG00117106("eng-00117106", "联系人"),
        ENG00117110("eng-00117110", "联系人"),
        ENG00117114("eng-00117114", "联系人");
        private final String key;
        private final String spec;
    }

    /**
     * 封板前验收
     */
    @Getter
    @AllArgsConstructor
    public enum AcceptanceBeforeSealing {
        ENG00119096("eng-00119096", "进展状态"),
        ENG00119100("eng-00119100", "进展状态"),
        ENG00119104("eng-00119104", "进展状态"),
        ENG00119108("eng-00119108", "进展状态"),
        ENG00119112("eng-00119112", "进展状态"),
        ENG00119097("eng-00119097", "单位名称"),
        ENG00119101("eng-00119101", "单位名称"),
        ENG00119105("eng-00119105", "单位名称"),
        ENG00119109("eng-00119109", "单位名称"),
        ENG00119113("eng-00119113", "单位名称"),
        ENG00119098("eng-00119098", "联系人"),
        ENG00119102("eng-00119102", "联系人"),
        ENG00119106("eng-00119106", "联系人"),
        ENG00119110("eng-00119110", "联系人"),
        ENG00119114("eng-00119114", "联系人");
        private final String key;
        private final String spec;
    }

    /**
     * 封板前验收,【封板前验收】带出到【家具进场】的节点
     */
    @Getter
    @AllArgsConstructor
    public enum AcceptanceBeforeSealingTow {
        ENG00119096("eng-00119096", "进展状态"),
        ENG00119100("eng-00119100", "进展状态"),
        ENG00119104("eng-00119104", "进展状态"),
        ENG00119108("eng-00119108", "进展状态"),
        ENG00119112("eng-00119112", "进展状态"),
        ENG00119116("eng-00119116", "进展状态"),
        ENG00119120("eng-00119120", "进展状态"),
        ENG00119124("eng-00119124", "进展状态"),
        ENG00119128("eng-00119128", "进展状态"),
        ENG00119097("eng-00119097", "单位名称"),
        ENG00119101("eng-00119101", "单位名称"),
        ENG00119105("eng-00119105", "单位名称"),
        ENG00119109("eng-00119109", "单位名称"),
        ENG00119113("eng-00119113", "单位名称"),
        ENG00119117("eng-00119117", "单位名称"),
        ENG00119121("eng-00119121", "单位名称"),
        ENG00119125("eng-00119125", "单位名称"),
        ENG00119129("eng-00119129", "单位名称"),
        ENG00119098("eng-00119098", "联系人"),
        ENG00119102("eng-00119102", "联系人"),
        ENG00119106("eng-00119106", "联系人"),
        ENG00119110("eng-00119110", "联系人"),
        ENG00119114("eng-00119114", "联系人"),
        ENG00119118("eng-00119118", "联系人"),
        ENG00119122("eng-00119122", "联系人"),
        ENG00119126("eng-00119126", "联系人"),
        ENG00119130("eng-00119130", "联系人"),
        ENG00119325("eng-00119325", "信号源"),
        ENG00119326("eng-00119326", "联系电话");
        private final String key;
        private final String spec;
    }

    /**
     * 家具进场
     */
    @Getter
    @AllArgsConstructor
    public enum FurnitureEntry {
        ENG00123096("eng-00123096", "进展状态"),
        ENG00123100("eng-00123100", "进展状态"),
        ENG00123104("eng-00123104", "进展状态"),
        ENG00123108("eng-00123108", "进展状态"),
        ENG00123112("eng-00123112", "进展状态"),
        ENG00123116("eng-00123116", "进展状态"),
        ENG00123120("eng-00123120", "进展状态"),
        ENG00123124("eng-00123124", "进展状态"),
        ENG00123128("eng-00123128", "进展状态"),
        ENG00123097("eng-00123097", "单位名称"),
        ENG00123101("eng-00123101", "单位名称"),
        ENG00123105("eng-00123105", "单位名称"),
        ENG00123109("eng-00123109", "单位名称"),
        ENG00123113("eng-00123113", "单位名称"),
        ENG00123117("eng-00123117", "单位名称"),
        ENG00123121("eng-00123121", "单位名称"),
        ENG00123125("eng-00123125", "单位名称"),
        ENG00123129("eng-00123129", "单位名称"),
        ENG00123098("eng-00123098", "联系人"),
        ENG00123102("eng-00123102", "联系人"),
        ENG00123106("eng-00123106", "联系人"),
        ENG00123110("eng-00123110", "联系人"),
        ENG00123114("eng-00123114", "联系人"),
        ENG00123118("eng-00123118", "联系人"),
        ENG00123122("eng-00123122", "联系人"),
        ENG00123126("eng-00123126", "联系人"),
        ENG00123130("eng-00123130", "联系人"),
        ENG00123328("eng-00123328", "信号源"),
        ENG00123329("eng-00123329", "联系电话");
        private final String key;
        private final String spec;
    }

    /**
     * 形象进度80%
     */
    @Getter
    @AllArgsConstructor
    public enum ImageProgress {
        ENG00125096("eng-00125096", "进展状态"),
        ENG00125100("eng-00125100", "进展状态"),
        ENG00125104("eng-00125104", "进展状态"),
        ENG00125108("eng-00125108", "进展状态"),
        ENG00125112("eng-00125112", "进展状态"),
        ENG00125116("eng-00125116", "进展状态"),
        ENG00125120("eng-00125120", "进展状态"),
        ENG00125124("eng-00125124", "进展状态"),
        ENG00125128("eng-00125128", "进展状态"),
        ENG00125097("eng-00125097", "单位名称"),
        ENG00125101("eng-00125101", "单位名称"),
        ENG00125105("eng-00125105", "单位名称"),
        ENG00125109("eng-00125109", "单位名称"),
        ENG00125113("eng-00125113", "单位名称"),
        ENG00125117("eng-00125117", "单位名称"),
        ENG00125121("eng-00125121", "单位名称"),
        ENG00125125("eng-00125125", "单位名称"),
        ENG00125129("eng-00125129", "单位名称"),
        ENG00125098("eng-00125098", "联系人"),
        ENG00125102("eng-00125102", "联系人"),
        ENG00125106("eng-00125106", "联系人"),
        ENG00125110("eng-00125110", "联系人"),
        ENG00125114("eng-00125114", "联系人"),
        ENG00125118("eng-00125118", "联系人"),
        ENG00125122("eng-00125122", "联系人"),
        ENG00125126("eng-00125126", "联系人"),
        ENG00125130("eng-00125130", "联系人"),
        ENG00125333("eng-00125333", "信号源"),
        ENG00125334("eng-00125334", "联系电话");
        private final String key;
        private final String spec;
    }

    /**
     * 联系人的联系电话——【形象进度】带出到【竣工验收申请】
     */
    @Getter
    @AllArgsConstructor
    public enum ContactsPhone {
        ZB("eng-00125086", "eng-00125087", "eng-00129032"),
        RD("eng-00125101", "eng-00125102", "eng-00129035"),
        RS("eng-00125121", "eng-00125122", "eng-00129038"),
        KT("eng-00125097", "eng-00125098", "eng-00129041"),
        XF("eng-00125105", "eng-00125106", "eng-00129044");
        //单位名称编码
        private final String unit;
        //联系人编码
        private final String contact;
        //存放联系电话编码
        private final String phone;
    }

    /**
     * 竣工自检
     */
    @Getter
    @AllArgsConstructor
    public enum CompletionSelfInspection {
        ENG00127096("eng-00127096", "进展状态"),
        ENG00127100("eng-00127100", "进展状态"),
        ENG00127104("eng-00127104", "进展状态"),
        ENG00127108("eng-00127108", "进展状态"),
        ENG00127112("eng-00127112", "进展状态"),
        ENG00127116("eng-00127116", "进展状态"),
        ENG00127120("eng-00127120", "进展状态"),
        ENG00127124("eng-00127124", "进展状态"),
        ENG00127128("eng-00127128", "进展状态"),
        ENG00127097("eng-00127097", "单位名称"),
        ENG00127101("eng-00127101", "单位名称"),
        ENG00127105("eng-00127105", "单位名称"),
        ENG00127109("eng-00127109", "单位名称"),
        ENG00127113("eng-00127113", "单位名称"),
        ENG00127117("eng-00127117", "单位名称"),
        ENG00127121("eng-00127121", "单位名称"),
        ENG00127125("eng-00127125", "单位名称"),
        ENG00127129("eng-00127129", "单位名称"),
        ENG00127098("eng-00127098", "联系人"),
        ENG00127102("eng-00127102", "联系人"),
        ENG00127106("eng-00127106", "联系人"),
        ENG00127110("eng-00127110", "联系人"),
        ENG00127114("eng-00127114", "联系人"),
        ENG00127118("eng-00127118", "联系人"),
        ENG00127122("eng-00127122", "联系人"),
        ENG00127126("eng-00127126", "联系人"),
        ENG00127130("eng-00127130", "联系人");
        private final String key;
        private final String spec;
    }

    /**
     * 空调品牌和类型的带出
     */
    @Getter
    @AllArgsConstructor
    public enum AirConditioningBrandType {
        ENG00107177("eng-00107177", "eng-00109184"),
        ENG00107178("eng-00107178", "eng-00109185"),
        ENG00109184("eng-00109184", "eng-00111265"),
        ENG00109185("eng-00109185", "eng-00111266"),
        ENG00111265("eng-00111265", "eng-00117316"),
        ENG00111266("eng-00111266", "eng-00117317"),
        ENG00117316("eng-00117316", "eng-00119321"),
        ENG00117317("eng-00117317", "eng-00119322"),
        ENG00119321("eng-00119321", "eng-00123326"),
        ENG00119322("eng-00119322", "eng-00123327"),
        ENG00123326("eng-00123326", "eng-00125331"),
        ENG00123327("eng-00123327", "eng-00125332");
        private final String outKey;
        private final String intoKey;
    }

    /**
     * 常规项目自检
     */
    @Getter
    @AllArgsConstructor
    public enum CompletionSelfInspectionPro {
        ENG00127213("eng-00127213", "常规项目自检（表格）");
        private final String key;
        private final String spec;
    }

    /**
     * 系统自检
     */
    @Getter
    @AllArgsConstructor
    public enum CompletionSelfInspectionSys {
        ENG00127134("eng-00127134", "系统自检（表格）");
        private final String key;
        private final String spec;
    }

    /**
     * 【形象进度80%】提交后，【弱电单位】信息带出到【弱电验收申请】
     */
    @Getter
    @AllArgsConstructor
    public enum WeakCurrentApplication {
        ENG00137012("eng-00125101", "单位名称", "eng-00137012", "弱电施工单位名称"),
        ENG00137013("eng-00125102", "联系人", "eng-00137013", "弱电联系人"),
        ENG00137014(null, null, "eng-00137014", "联系电话");
        private final String key;
        private final String spec;
        private final String intoKey;
        private final String intoSpec;
    }

    /**
     * 【竣工自检】总分包管理带出到【竣工验收申请】
     */
    @Getter
    @AllArgsConstructor
    public enum CompAccApplication {
        ENG00127097("eng-00127097", "单位名称", "eng-00129039", "空调施工单位名称"),
        ENG00127098("eng-00127098", "联系人", "eng-00129040", "空调联系人"),
        ENG00127101("eng-00127101", "单位名称", "eng-00129033", "弱电施工单位名称"),
        ENG00127102("eng-00127102", "联系人", "eng-00129034", "弱电联系人"),
        ENG00127121("eng-00127121", "单位名称", "eng-00129036", "热水施工单位名称"),
        ENG00127122("eng-00127122", "联系人", "eng-00129037", "热水联系人"),
        ENG00127105("eng-00127105", "单位名称", "eng-00129042", "消防施工单位名称"),
        ENG00127106("eng-00127106", "联系人", "eng-00129043", "消防联系人");
        private final String key;
        private final String spec;
        private final String intoKey;
        private final String intoSpec;
    }

    /**
     * 【竣工验收申请】的联系人带出联系电话
     */
    @Getter
    @AllArgsConstructor
    public enum CompAccApplicationA {
        ENG00129034("eng-00129034", "弱电联系人", "eng-00129035", "联系电话"),
        ENG00129037("eng-00129037", "热水联系人", "eng-00129038", "联系电话"),
        ENG00129040("eng-00129040", "空调联系人", "eng-00129041", "联系电话"),
        ENG00129043("eng-00129043", "消防联系人", "eng-00129044", "联系电话");
        private final String key;
        private final String spec;
        private final String intoKey;
        private final String intoSpec;
    }

    /**
     * 成本管理:正式开工
     */
    @Getter
    @AllArgsConstructor
    public enum CostControlZSKG {
        ENG00107088("eng-00107088", "X1部分情况说明(物业非标)"),
        ENG00107089("eng-00107089", "情况描述"),
        ENG00107090("eng-00107090", "预估费用(元）"),
        ENG00107091("eng-00107091", "X2部分情况说明(业主个性化)"),
        ENG00107092("eng-00107092", "情况描述"),
        ENG00107093("eng-00107093", "预估费用(元）"),
        ENG00107094("eng-00107094", "标准部分情况说明"),
        ENG00107095("eng-00107095", "情况描述"),
        ENG00107096("eng-00107096", "预估费用(元）");
        private final String key;
        private final String spec;
    }

    /**
     * 成本管理:样板间隐蔽验收
     */
    @Getter
    @AllArgsConstructor
    public enum CostControlYBJYBYS {
        ENG00109087("eng-00109087", "X1部分情况说明(物业非标)"),
        ENG00109088("eng-00109088", "情况描述"),
        ENG00109089("eng-00109089", "预估费用(元）"),
        ENG00109090("eng-00109090", "X2部分情况说明(业主个性化)"),
        ENG00109091("eng-00109091", "情况描述"),
        ENG00109092("eng-00109092", "预估费用(元）"),
        ENG00109093("eng-00109093", "标准部分情况说明"),
        ENG00109094("eng-00109094", "情况描述"),
        ENG00109095("eng-00109095", "预估费用(元）");
        private final String key;
        private final String spec;
    }

    /**
     * 成本管理:样板间验收
     */
    @Getter
    @AllArgsConstructor
    public enum CostControlYBJYS {
        ENG00111131("eng-00111131", "X1部分情况说明(物业非标)"),
        ENG00111132("eng-00111132", "情况描述"),
        ENG00111133("eng-00111133", "预估费用(元）"),
        ENG00111134("eng-00111134", "X2部分情况说明(业主个性化)"),
        ENG00111135("eng-00111135", "情况描述"),
        ENG00111136("eng-00111136", "预估费用(元）"),
        ENG00111137("eng-00111137", "标准部分情况说明"),
        ENG00111138("eng-00111138", "情况描述"),
        ENG00111139("eng-00111139", "预估费用(元）");
        private final String key;
        private final String spec;
    }

    /**
     * 成本管理:隐蔽验收
     */
    @Getter
    @AllArgsConstructor
    public enum CostControlYBYS {
        ENG00117147("eng-00117147", "X1部分情况说明(物业非标)"),
        ENG00117148("eng-00117148", "情况描述"),
        ENG00117149("eng-00117149", "预估费用(元）"),
        ENG00117150("eng-00117150", "X2部分情况说明(业主个性化)"),
        ENG00117151("eng-00117151", "情况描述"),
        ENG00117152("eng-00117152", "预估费用(元）"),
        ENG00117153("eng-00117153", "标准部分情况说明"),
        ENG00117154("eng-00117154", "情况描述"),
        ENG00117155("eng-00117155", "预估费用(元）");
        private final String key;
        private final String spec;
    }

    /**
     * 成本管理:封板前验收
     */
    @Getter
    @AllArgsConstructor
    public enum CostControlFBQYS {
        ENG00119147("eng-00119147", "X1部分情况说明(物业非标)"),
        ENG00119148("eng-00119148", "情况描述"),
        ENG00119149("eng-00119149", "预估费用(元）"),
        ENG00119150("eng-00119150", "X2部分情况说明(业主个性化)"),
        ENG00119151("eng-00119151", "情况描述"),
        ENG00119152("eng-00119152", "预估费用(元）"),
        ENG00119153("eng-00119153", "标准部分情况说明"),
        ENG00119154("eng-00119154", "情况描述"),
        ENG00119155("eng-00119155", "预估费用(元）");
        private final String key;
        private final String spec;
    }

    /**
     * 现长预计到岗时间
     */
    @Getter
    @AllArgsConstructor
    public enum SealingConfirmation {
        ENG00119324("eng-00119324", "现长预计到岗时间");
        private final String key;
        private final String spec;
    }

    /**
     * 派驻现长
     */
    @Getter
    @AllArgsConstructor
    public enum ResidentChief {
        ENG00123147("eng-00121002", "现长姓名"),
        ENG00123148("eng-00121003", "现长联系电话"),
        ENG00123155("eng-00121004", "现长联系邮箱");
        private final String key;
        private final String spec;
    }

    /**
     * 成本管理:家具进场
     */
    @Getter
    @AllArgsConstructor
    public enum CostControlJJJC {
        ENG00123147("eng-00123147", "X1部分情况说明(物业非标)"),
        ENG00123148("eng-00123148", "情况描述"),
        ENG00123149("eng-00123149", "预估费用(元）"),
        ENG00123150("eng-00123150", "X2部分情况说明(业主个性化)"),
        ENG00123151("eng-00123151", "情况描述"),
        ENG00123152("eng-00123152", "预估费用(元）"),
        ENG00123153("eng-00123153", "标准部分情况说明"),
        ENG00123154("eng-00123154", "情况描述"),
        ENG00123155("eng-00123155", "预估费用(元）");
        private final String key;
        private final String spec;
    }

    /**
     * 成本管理:形象进度80%
     */
    @Getter
    @AllArgsConstructor
    public enum CostControlXXJD {
        ENG00125147("eng-00125147", "X1部分情况说明(物业非标)"),
        ENG00125148("eng-00125148", "情况描述"),
        ENG00125149("eng-00125149", "预估费用(元）"),
        ENG00125150("eng-00125150", "X2部分情况说明(业主个性化)"),
        ENG00125151("eng-00125151", "情况描述"),
        ENG00125152("eng-00125152", "预估费用(元）"),
        ENG00125153("eng-00125153", "标准部分情况说明"),
        ENG00125154("eng-00125154", "情况描述"),
        ENG00125155("eng-00125155", "预估费用(元）");
        private final String key;
        private final String spec;
    }

    /**
     * 成本管理:竣工自检
     */
    @Getter
    @AllArgsConstructor
    public enum CostControlJGZJ {
        ENG00127147("eng-00127147", "X1部分情况说明(物业非标)"),
        ENG00127148("eng-00127148", "情况描述"),
        ENG00127149("eng-00127149", "预估费用(元）"),
        ENG00127150("eng-00127150", "X2部分情况说明(业主个性化)"),
        ENG00127151("eng-00127151", "情况描述"),
        ENG00127152("eng-00127152", "预估费用(元）"),
        ENG00127153("eng-00127153", "标准部分情况说明"),
        ENG00127154("eng-00127154", "情况描述"),
        ENG00127155("eng-00127155", "预估费用(元）");
        private final String key;
        private final String spec;
    }

    /**
     * 弱电验收
     */
    @Getter
    @AllArgsConstructor
    public enum WeakCurrentAcceptance {
        ENG00139010("eng-00139010", "验收人"),
        ENG00141007("eng-00141007", "责任人"),
        ENG00139018("eng-00139018", "开工时间");
        private final String key;
        private final String spec;
    }

    /**
     * 弱电/竣工验收申请的选择验收标准版本
     */
    @Getter
    @AllArgsConstructor
    public enum WeakCurrentVersion {
        ENG00139025("eng-00139025", "验收单"),
        ENG00129105("eng-00129105", "选择验收标准版本"),
        ENG00137064("eng-00137064", "选择验收标准版本");
        private final String key;
        private final String spec;
    }

    /**
     * 竣工验收的开工日期
     */
    @Getter
    @AllArgsConstructor
    public enum CompletionAcceptanceTime {
        ENG00133019("eng-00133019", "开工日期"),
        ENG00141018("eng-00141018", "开工日期");
        private final String key;
        private final String spec;
    }

    /**
     * 设计勘测
     */
    @Getter
    @AllArgsConstructor
    public enum DesignSurvey {
        DES00103010("des-00103010", "现场核对"),
        DES00103011("des-00103011", "不一致性说明"),
        DES00103013("des-00103013", "现场核对"),
        DES00103014("des-00103014", "不一致性说明"),
        DES00103016("des-00103016", "现场核对"),
        DES00103017("des-00103017", "不一致性说明"),
        DES00103019("des-00103019", "现场核对"),
        DES00103020("des-00103020", "不一致性说明"),
        DES00103022("des-00103022", "现场核对"),
        DES00103023("des-00103023", "不一致性说明"),
        DES00103025("des-00103025", "现场核对"),
        DES00103026("des-00103026", "不一致性说明"),
        DES00103028("des-00103028", "现场核对"),
        DES00103029("des-00103029", "不一致性说明"),
        DES00103031("des-00103031", "现场核对"),
        DES00103032("des-00103032", "不一致性说明"),
        DES00103034("des-00103034", "现场核对"),
        DES00103035("des-00103035", "不一致性说明"),
        DES00101077("des-00101077", "客房设计单位"),
        DES00101078("des-00101078", "客房装饰设计师"),
        DES00101080("des-00101080", "客房机电单位"),
        DES00101081("des-00101081", "客房机电设计师"),
        DES00101083("des-00101083", "公区设计单位"),
        DES00101084("des-00101084", "公区装饰设计师"),
        DES00101086("des-00101086", "公区机电单位"),
        DES00101087("des-00101087", "公区机电设计师");
        private final String key;
        private final String spec;
    }

    /**
     * 勘测报告的带出
     */
    @Getter
    @AllArgsConstructor
    public enum SurveyReport {
        DES00103077("des-00103077", "勘测报告", "dec-00301002"),
        DES00165109("des-00165109", "机电勘测报告", "dec-00301003"),
        DEC00501002("dec-00501002", "装饰勘测报告", null),
        DEC00701002("dec-00701002", "机电勘测报告", null);
        private final String outKey;
        private final String spec;
        private final String intoKey;
    }


    /**
     * 设计勘测联系人带出到设计启动
     */
    @Getter
    @AllArgsConstructor
    public enum DesignStart {
        DES00105003("des-00105003", "联系人"),
        DES00105004("des-00105004", "联系电话"),
        DES00105005("des-00105005", "联系邮箱"),
        DES00105007("des-00105007", "联系人"),
        DES00105008("des-00105008", "联系电话"),
        DES00105009("des-00105009", "联系邮箱");
        private final String key;
        private final String spec;
    }

    /**
     * 客房/公区设计交底申请的设计单位联系方式带出
     */
    @Getter
    @AllArgsConstructor
    public enum DesignDisclosureApplication {

        KF1("des-00101078", "设计师姓名","des-00169031","设计师联系方式"),
        KF2("des-00101081", "设计师姓名","des-00169034","设计师联系方式"),
        GQ1("des-00101078", "设计师姓名","pad-00145031","设计师联系方式"),
        GQ2("des-00101081", "设计师姓名","pad-00145034","设计师联系方式");
        private final String outKey;
        private final String spec;
        private final String intoKey;
        private final String value;
    }

    /**
     * 客房平面及效果图
     */
    @Getter
    @AllArgsConstructor
    public enum DesignDrawing {
        DES00107057("des-00107057", "工程房号"),
        DES00107060("des-00107060", "工程房号");
        private final String key;
        private final String spec;
    }

    /**
     * 客房平面及效果图
     */
    @Getter
    @AllArgsConstructor
    public enum DesignDrawingTow {
        DES00111033("des-00111033", "工程房号"),
        DES00111036("des-00111036", "工程房号"),
        DES00111040("des-00111040", "工程房号");
        private final String key;
        private final String spec;
    }

    /**
     * 竣工和弱电验收申请的房号
     */
    @Getter
    @AllArgsConstructor
    public enum CompletionWeakRoom {

        ENG00129025("eng-00129025", "申请验收房号"),
        ENG00129023("eng-00129023", "设计房间数"),
        ENG00139038("eng-00139038", "设计房间数"),
        ENG00141013("eng-00141013", "设计房间数"),
        ENG00137008("eng-00137008", "验收房号"),
        ENG00139009("eng-00139009", "具备验收条件房号"),
        ENG00133051("eng-00133051", "具备验收条件房号"),
        ENG00141011("eng-00141011", "具备验收条件房号");
        private final String key;
        private final String spec;
    }

    /**
     * PDF生成的带出
     */
    @Getter
    @AllArgsConstructor
    public enum PdfBroughtOut {
//        ENG00137061("eng-00139","弱电验收","eng-00137061","弱电报验资料"),
        ENG00129059("eng-00117","隐蔽验收","eng-00129059","隐蔽验收申请自检资料、隐蔽验收报告"),
        ENG00129060("eng-00111","样板间验收","eng-00129060","样板间验收报告"),
        ENG00129063("eng-00127","竣工自检","eng-00129063","竣工自检节点表单");
//        ENG00137061("eng-00139","竣工自检","eng-00129061","竣工验收自检PPT（按照模版）"),


        private final String twoNodeCode;
        private final String nodeName;
        private final String key;
        private final String spec;
    }

    /**
     * 【客房平面及效果图】带出到【客房样板间施工图】
     */
    @Getter
    @AllArgsConstructor
    public enum DesignDrawingOut {
        DES00107057("des-00107057", "des-00111033"),
        DES00107058("des-00107058", "des-00111034"),
        DES00107059("des-00107059", "des-00111035"),
        DES00107060("des-00107060", "des-00111036"),
        DES00107061("des-00107061", "des-00111037"),
        DES00107062("des-00107062", "des-00111038");
        private final String outKey;
        private final String intoKey;
    }

    /**
     * 确认设计单位的变更干系人
     */
    @Getter
    @AllArgsConstructor
    public enum ConfirmDesignUnit {
        DES00127022("des-00127022", "客房装饰设计师", "sjdwsjs"),
        DES00127025("des-00127025", "客房机电设计师", "jdsjs"),
        DES00127028("des-00127028", "公区装饰设计师", "gqzssjs"),
        DES00127031("des-00127031", "公区机电设计师", "gqjdsjs");
        private final String key;
        private final String spec;
        private final String value;
    }

    /**
     * 客房平面审核
     */
    @Getter
    @AllArgsConstructor
    public enum RoomPlanReview {
        DES00107015("des-00107043-table_1451", "des-00107043-table_1452", "客房平面图（CAD/PDF）");
        private final String keyA;
        private final String keyB;
        private final String spec;
    }

    /**
     * 客房样板间施工图审核
     */
    @Getter
    @AllArgsConstructor
    public enum ReviewOfConstructionDrawingsForGuestRoomModelRooms {
        DES00111018("des-00111028-table_1457", "des-00111028-table_1459", "平面隔墙尺寸图（CAD/PDF）"),
        DES00111019("des-00111028-table_1458", "des-00111028-table_1460", "样板间施工图（CAD/PDF）");
        private final String keyA;
        private final String keyB;
        private final String spec;
    }

    /**
     * 客房装饰施工图审核-设计审核参考标准
     */
    @Getter
    @AllArgsConstructor
    public enum ReviewOfGuestRoomDecorationConstructionDrawings {
        DES00115010("des-00115010", "设计审核参考标准（表格）");

        private final String key;
        private final String spec;
    }

    /**
     * 客房装饰施工图整改-设计审核参考标准
     */
    @Getter
    @AllArgsConstructor
    public enum RectificationOfGuestRoomDecorationConstructionDrawings {
        DES00117010("des-00117010", "设计审核参考标准（表格）");
        private final String key;
        private final String spec;
    }

    /**
     * 公区概念方案审核-方案汇报文本、公区平面设计质量、公区方案质量
     */
    @Getter
    @AllArgsConstructor
    public enum ReviewOfPublicAreaConceptPlan {
        DES00127010("pad-00127010", "方案汇报文本（表格）"),
        DES00127012("pad-00127012", "公区平面设计质量（表格）"),
        DES00127014("pad-00127014", "公区方案质量（表格）");
        private final String key;
        private final String spec;
    }

    /**
     * 公区概念方案整改-方案汇报文本、公区平面设计质量、公区方案质量
     */
    @Getter
    @AllArgsConstructor
    public enum RectificationOfPublicAreaConceptPlan {
        DES00129010("pad-00129010", "方案汇报文本（表格）"),
        DES00129012("pad-00129012", "公区平面设计质量（表格）"),
        DES00129014("pad-00129014", "公区方案质量（表格）");
        private final String key;
        private final String spec;
    }

    /**
     * 公区装饰施工图审核-公区施工图检查表（表格）
     */
    @Getter
    @AllArgsConstructor
    public enum ReviewOfPublicAreaDecorationConstructionDrawings {
        DES00135010("pad-00135010", "公区施工图检查表（表格）");
        private final String key;
        private final String spec;
    }

    /**
     * 公区装饰施工图整改-公区施工图检查表（表格）
     */
    @Getter
    @AllArgsConstructor
    public enum RectificationOfPublicAreaDecorationConstructionDrawings {
        DES00137010("pad-00137010", "公区施工图检查表（表格）");
        private final String key;
        private final String spec;
    }

    /**
     * 业主指定对接人员是否变更
     */
    @Getter
    @AllArgsConstructor
    public enum hasDesignatedLiaisonPersonnelOwnerChanged {
        yes("yes", "是"),
        no("no", "否");
        private final String key;
        private final String spec;


    }

    @Getter
    @AllArgsConstructor
    public enum userGender {

        MALE("0", "男"),
        FEMALE("1", "女");
        private String key;
        private String spec;

        public static String getSpec(String key) {
            String phase = "男";
            AtourSystemEnum.userGender[] values = AtourSystemEnum.userGender.values();
            for (AtourSystemEnum.userGender task : values) {
                if (task.getKey().contains(key)) {
                    phase = task.getSpec();
                    return phase;
                }
            }
            return phase;
        }

        public static String getKey(String spec) {
            String phase = "0";
            AtourSystemEnum.userGender[] values = AtourSystemEnum.userGender.values();
            for (AtourSystemEnum.userGender task : values) {
                if (task.getSpec().contains(spec)) {
                    phase = task.getKey();
                    return phase;
                }
            }
            return phase;
        }

    }


    /**
     * 模版属性类型
     * 品牌	brand
     * 产品	product
     * 战区	war_zone
     * 项目类型	project_type
     */
    @Getter
    @AllArgsConstructor
    public enum conditionType {
        BRAND("brand", "品牌"),
        PRODUCT("product", "产品"),
        WAR_ZONE("war_zone", "战区"),
        PROJECT_TYPE("project_type", "项目类型");
        private final String key;
        private final String spec;

    }


    /**
     * 营建对接启动 负责角色
     */
    @Getter
    @AllArgsConstructor
    public enum constructionDockingStartedRole {
        CONSTRUCTION_DOCKING_STARTED_ROLE("zbzxfzr,jfzx-gcfzr,jfzx-jsfzr,jfzx-gxzc", "营建区域负责人"),
        ZBZXFZR("zbzxfzr", "营建区域负责人"),
        JFZX_GCFZR("jfzx-gcfzr", "交付中心-工程负责人"),
        JFZX_JXFZR("jfzx-jsfzr", "交付中心-技术负责人"),
        JFZX_GXZC("jfzx-gxzc", "交付中心-共享支持");
        private final String key;
        private final String spec;
    }


    /**
     * 整改确认
     */
    @Getter
    @AllArgsConstructor
    public enum rectificationConfirmationEnum {
        RECTIFICATION_COMPLETED("rectification_completed", "整改完成"),
        RECTIFICATION_NOT_COMPLETED("rectification_not_completed", "整改未完成");
        private final String key;
        private final String spec;
    }


    /**
     * 跳转地址
     */
    @Getter
    @AllArgsConstructor
    public enum jumpAddressEnum {
        BUILDINGKICK("/choujianqidonghui/detail", "营建启动会查看编辑页面"),
        SECOND("/project/backlogDetail", "二三级看板页面");
        private final String key;
        private final String spec;
    }



    /**
     * 深化方案三级
     */
    @Getter
    @AllArgsConstructor
    public enum DeepeningSchemeEnum {
        //客房区装饰画（喷绘方案）
        DES0011502601("des-0011502601", "客房装饰施工图");
        private final String key;
        private final String spec;
    }


    /**
     * 品牌
     */
    @Getter
    @AllArgsConstructor
    public enum BrandCodeEnum {
        BRAND1("1", "亚朵酒店"),
        BRAND3("3", "轻居酒店"),
        BRAND7("7", "亚朵S酒店"),
        BRAND9("9", "亚朵X酒店"),
        BRAND4("4", "萨和");
        private final String key;
        private final String spec;
    }

}
