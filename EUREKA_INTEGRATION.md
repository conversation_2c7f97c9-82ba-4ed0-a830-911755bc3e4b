# Eureka 服务注册中心集成文档

## 概述

本文档描述了如何将 atouradmin-system 服务接入 Eureka 服务注册中心。

## 集成内容

### 1. 依赖添加

在根 `pom.xml` 中添加了以下依赖：

```xml
<!-- Spring Cloud 版本管理 -->
<properties>
    <spring-cloud.version>Greenwich.SR6</spring-cloud.version>
</properties>

<!-- Spring Cloud Eureka Client -->
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
</dependency>

<!-- 依赖管理 -->
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-dependencies</artifactId>
            <version>${spring-cloud.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
    </dependencies>
</dependencyManagement>
```

### 2. 启动类修改

在 `AppRun.java` 中添加了 `@EnableEurekaClient` 注解：

```java
@EnableEurekaClient
@SpringBootApplication
public class AppRun extends SpringBootServletInitializer {
    // ...
}
```

### 3. 配置文件修改

#### 主配置文件 (application.yml)

```yaml
spring:
  application:
    name: ${application_prefix}${app.id}  # 服务名称

# Eureka Client 配置
eureka:
  client:
    register-with-eureka: true
    fetch-registry: true
  instance:
    instance-id: ${spring.application.name}:${server.port}
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90
```

#### 各环境配置

**开发环境 (application-dev.yml)**
```yaml
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
  instance:
    hostname: localhost
```

**QA环境 (application-qa.yml)**
```yaml
eureka:
  client:
    service-url:
      defaultZone: http://qa-eureka.at-our.com:8761/eureka/
  instance:
    hostname: qa-eureka.at-our.com
```

**测试环境 (application-test.yml)**
```yaml
eureka:
  client:
    service-url:
      defaultZone: http://test-eureka.at-our.com:8761/eureka/
  instance:
    hostname: test-eureka.at-our.com
```

**生产环境 (application-prod.yml)**
```yaml
eureka:
  client:
    service-url:
      defaultZone: http://prod-eureka.yaduo.com:8761/eureka/
  instance:
    hostname: prod-eureka.yaduo.com
```

**预发布环境 (application-pre.yml)**
```yaml
eureka:
  client:
    service-url:
      defaultZone: http://pre-eureka.yaduo.com:8761/eureka/
  instance:
    hostname: pre-eureka.yaduo.com
```

### 4. 健康检查端点

添加了 `HealthController` 提供健康检查和服务信息端点：

- `/health` - 健康检查端点
- `/info` - 服务信息端点

## 服务注册信息

- **服务名称**: `${application_prefix}${app.id}` (例如: `local-acms-backend`)
- **端口**: 8002
- **实例ID**: `服务名称:端口` (例如: `local-acms-backend:8002`)
- **健康检查**: `/health`

## 使用说明

### 1. 启动服务

确保 Eureka Server 已经启动，然后启动本服务：

```bash
# 开发环境
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# QA环境
mvn spring-boot:run -Dspring-boot.run.profiles=qa

# 生产环境
mvn spring-boot:run -Dspring-boot.run.profiles=prod
```

### 2. 验证注册

启动后可以通过以下方式验证服务是否成功注册：

1. 访问 Eureka Server 控制台 (通常是 `http://eureka-server:8761`)
2. 查看服务列表中是否包含 `atouradmin-system` 服务
3. 访问服务的健康检查端点: `http://服务地址:8002/health`

### 3. 服务发现

其他服务可以通过服务名称 `atouradmin-system` 来发现和调用本服务。

## 注意事项

1. **Eureka Server 地址**: 请根据实际环境修改各配置文件中的 Eureka Server 地址
2. **网络连通性**: 确保服务能够访问到 Eureka Server
3. **防火墙**: 确保 8002 端口和 Eureka Server 端口可以正常访问
4. **服务名称**: 服务名称会根据不同环境的 `application_prefix` 变化

## 故障排查

### 常见问题

1. **服务无法注册**
   - 检查 Eureka Server 是否启动
   - 检查网络连通性
   - 查看应用日志中的错误信息

2. **服务注册成功但显示为 DOWN**
   - 检查健康检查端点是否可访问
   - 检查防火墙设置
   - 确认服务实际运行状态

3. **无法发现其他服务**
   - 确认 `fetch-registry: true` 配置
   - 检查 Eureka Server 中是否有目标服务
   - 查看服务发现相关日志

### 日志查看

启动时会看到类似以下的日志：

```
DiscoveryClient_ATOURADMIN-SYSTEM/local-acms-backend:8002 - registration status: 204
```

表示服务注册成功。

## 版本兼容性

- Spring Boot: 2.1.0.RELEASE
- Spring Cloud: Greenwich.SR6
- Java: 1.8+
