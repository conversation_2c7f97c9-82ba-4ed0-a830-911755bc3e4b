INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992579, 'engineering', NULL, '决策批复', 'eng-00129110', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'show_label', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 10:30:49', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'master_template', NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178334, 'engineering(newYDXJD)', NULL, '决策批复', 'eng-00129110', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'show_label', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 10:30:49', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'master_template', NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992580, 'engineering', NULL, '项目红线图(签约PDF)', 'eng-00129111', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'input_show_block', NULL, 'eng-00103118', NULL, NULL, '1', NULL, b'1', b'0', '2024-10-08 10:30:49', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'master_template', NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178362, 'engineering(newYDXJD)', NULL, '项目红线图(签约PDF)', 'eng-00129111', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'input_show_block', NULL, 'eng-00103118', NULL, NULL, '1', NULL, b'1', b'0', '2024-10-08 10:30:49', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'master_template', NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992581, 'engineering', NULL, '项目决策意见(截图)', 'eng-00129112', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'input_show_block', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, '2', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178311, 'engineering(newYDXJD)', NULL, '项目决策意见(截图)', 'eng-00129112', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'input_show_block', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, '2', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992582, 'engineering', NULL, '分类', 'eng-00129113', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178312, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129113', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992583, 'engineering', NULL, '分类', 'eng-00129114', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178313, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129114', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992584, 'engineering', NULL, '资料审核', 'eng-00129115', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178314, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129115', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992585, 'engineering', NULL, '资料审核', 'eng-00129116', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178315, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129116', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992586, 'engineering', NULL, '过程管理', 'eng-00129117', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'show_label', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178316, 'engineering(newYDXJD)', NULL, '过程管理', 'eng-00129117', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'show_label', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992587, 'engineering', NULL, '过程管理节点单(9个节点签字单)', 'eng-00129118', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'input_show_block', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, '3', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178329, 'engineering(newYDXJD)', NULL, '过程管理节点单(9个节点签字单)', 'eng-00129118', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'input_show_block', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, '3', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992588, 'engineering', NULL, '核心检查项', 'eng-00129119', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'show_label', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178318, 'engineering(newYDXJD)', NULL, '核心检查项', 'eng-00129119', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'show_label', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992589, 'engineering', NULL, '提供联动视频（消防）', 'eng-00129120', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178319, 'engineering(newYDXJD)', NULL, '提供联动视频（消防）', 'eng-00129120', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992590, 'engineering', NULL, '分类', 'eng-00129121', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178376, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129121', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992591, 'engineering', NULL, '资料审核', 'eng-00129122', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178321, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129122', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992592, 'engineering', NULL, '提供水压照片（消防）', 'eng-00129123', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178322, 'engineering(newYDXJD)', NULL, '提供水压照片（消防）', 'eng-00129123', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992593, 'engineering', NULL, '分类', 'eng-00129124', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178323, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129124', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992594, 'engineering', NULL, '资料审核', 'eng-00129125', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178324, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129125', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992595, 'engineering', NULL, '消火栓放水视频（消防）', 'eng-00129126', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178325, 'engineering(newYDXJD)', NULL, '消火栓放水视频（消防）', 'eng-00129126', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992596, 'engineering', NULL, '分类', 'eng-00129127', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178326, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129127', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992597, 'engineering', NULL, '资料审核', 'eng-00129128', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178327, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129128', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992598, 'engineering', NULL, '提供运营100%烟感测试记录（消防）', 'eng-00129129', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178328, 'engineering(newYDXJD)', NULL, '提供运营100%烟感测试记录（消防）', 'eng-00129129', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992599, 'engineering', NULL, '分类', 'eng-00129130', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178317, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129130', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992600, 'engineering', NULL, '资料审核', 'eng-00129131', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178375, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129131', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992601, 'engineering', NULL, '提供客房电箱回路照片（强电）', 'eng-00129132', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178335, 'engineering(newYDXJD)', NULL, '提供客房电箱回路照片（强电）', 'eng-00129132', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992602, 'engineering', NULL, '分类', 'eng-00129133', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178310, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129133', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992603, 'engineering', NULL, '资料审核', 'eng-00129134', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178336, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129134', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992604, 'engineering', NULL, '提供各楼层电箱回路照片（强电）', 'eng-00129135', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178337, 'engineering(newYDXJD)', NULL, '提供各楼层电箱回路照片（强电）', 'eng-00129135', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992605, 'engineering', NULL, '分类', 'eng-00129136', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178338, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129136', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992606, 'engineering', NULL, '资料审核', 'eng-00129137', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178339, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129137', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992607, 'engineering', NULL, '提供现场设备及铭牌照片（暖通）', 'eng-00129138', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178306, 'engineering(newYDXJD)', NULL, '提供现场设备及铭牌照片（暖通）', 'eng-00129138', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992608, 'engineering', NULL, '分类', 'eng-00129139', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178341, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129139', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992609, 'engineering', NULL, '资料审核', 'eng-00129140', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178342, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129140', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992610, 'engineering', NULL, '提供现场设备及铭牌照片（给排水）', 'eng-00129141', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178343, 'engineering(newYDXJD)', NULL, '提供现场设备及铭牌照片（给排水）', 'eng-00129141', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992611, 'engineering', NULL, '分类', 'eng-00129142', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178344, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129142', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992612, 'engineering', NULL, '资料审核', 'eng-00129143', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178345, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129143', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992613, 'engineering', NULL, '提供各楼层水压检测照片（给排水）', 'eng-00129144', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178346, 'engineering(newYDXJD)', NULL, '提供各楼层水压检测照片（给排水）', 'eng-00129144', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992614, 'engineering', NULL, '分类', 'eng-00129145', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178347, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129145', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992615, 'engineering', NULL, '资料审核', 'eng-00129146', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178348, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129146', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992616, 'engineering', NULL, '提供梯控测试视频（电梯）', 'eng-00129147', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178349, 'engineering(newYDXJD)', NULL, '提供梯控测试视频（电梯）', 'eng-00129147', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992617, 'engineering', NULL, '分类', 'eng-00129148', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178350, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129148', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992618, 'engineering', NULL, '资料审核', 'eng-00129149', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178340, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129149', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992619, 'engineering', NULL, '提供合格证照片（电梯）', 'eng-00129150', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178307, 'engineering(newYDXJD)', NULL, '提供合格证照片（电梯）', 'eng-00129150', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992620, 'engineering', NULL, '分类', 'eng-00129151', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178308, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129151', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992621, 'engineering', NULL, '资料审核', 'eng-00129152', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178320, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129152', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992622, 'engineering', NULL, '提供三方通话测试视频（电梯）', 'eng-00129153', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178309, 'engineering(newYDXJD)', NULL, '提供三方通话测试视频（电梯）', 'eng-00129153', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992623, 'engineering', NULL, '分类', 'eng-00129154', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178372, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129154', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992624, 'engineering', NULL, '资料审核', 'eng-00129155', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178353, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129155', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992625, 'engineering', NULL, '提供100%客房隔音测试记录（隔音）', 'eng-00129156', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178354, 'engineering(newYDXJD)', NULL, '提供100%客房隔音测试记录（隔音）', 'eng-00129156', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992626, 'engineering', NULL, '分类', 'eng-00129157', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178355, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129157', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992627, 'engineering', NULL, '资料审核', 'eng-00129158', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178356, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129158', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992628, 'engineering', NULL, '分类', 'eng-00129159', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178357, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129159', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992629, 'engineering', NULL, '资料审核', 'eng-00129160', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178358, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129160', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992630, 'engineering', NULL, '分类', 'eng-00129161', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178359, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129161', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992631, 'engineering', NULL, '资料审核', 'eng-00129162', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178360, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129162', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992632, 'engineering', NULL, '竣工验收申请表', 'eng-00129163', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178361, 'engineering(newYDXJD)', NULL, '竣工验收申请表', 'eng-00129163', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992633, 'engineering', NULL, '分类', 'eng-00129164', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178374, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129164', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992634, 'engineering', NULL, '资料审核', 'eng-00129165', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178363, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129165', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992635, 'engineering', NULL, '飞行质检报告及整改回复', 'eng-00129166', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178364, 'engineering(newYDXJD)', NULL, '飞行质检报告及整改回复', 'eng-00129166', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992636, 'engineering', NULL, '分类', 'eng-00129167', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178365, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129167', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992637, 'engineering', NULL, '资料审核', 'eng-00129168', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178366, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129168', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992638, 'engineering', NULL, '分类', 'eng-00129169', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178367, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129169', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992639, 'engineering', NULL, '资料审核', 'eng-00129170', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178368, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129170', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992640, 'engineering', NULL, '分类', 'eng-00129171', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178369, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129171', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992641, 'engineering', NULL, '资料审核', 'eng-00129172', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178370, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129172', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992642, 'engineering', NULL, '施工单位、特许商签字确认竣工自检清单', 'eng-00129173', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178371, 'engineering(newYDXJD)', NULL, '施工单位、特许商签字确认竣工自检清单', 'eng-00129173', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992643, 'engineering', NULL, '分类', 'eng-00129174', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178373, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129174', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992644, 'engineering', NULL, '资料审核', 'eng-00129175', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178386, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129175', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992645, 'engineering', NULL, '分类', 'eng-00129176', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178377, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129176', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992646, 'engineering', NULL, '资料审核', 'eng-00129177', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178378, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129177', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992647, 'engineering', NULL, '隔音测试记录表(按照模版、运营确认/邮件)', 'eng-00129178', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178379, 'engineering(newYDXJD)', NULL, '隔音测试记录表(按照模版、运营确认/邮件)', 'eng-00129178', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992648, 'engineering', NULL, '分类', 'eng-00129179', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178380, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129179', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992649, 'engineering', NULL, '资料审核', 'eng-00129180', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178381, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129180', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840665969619992650, 'engineering', NULL, '分类', 'eng-00129181', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178382, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129181', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840672583164985347, 'engineering', NULL, '资料审核', 'eng-00129182', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178383, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129182', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840672583164985348, 'engineering', NULL, '分类', 'eng-00129183', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178384, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129183', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840672583164985349, 'engineering', NULL, '资料审核', 'eng-00129184', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178351, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129184', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840672583164985350, 'engineering', NULL, '门头雨棚结构证明文件', 'eng-00129185', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178385, 'engineering(newYDXJD)', NULL, '门头雨棚结构证明文件', 'eng-00129185', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, 1, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840672583164985351, 'engineering', NULL, '分类', 'eng-00129186', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178387, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129186', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840672583164985352, 'engineering', NULL, '资料审核', 'eng-00129187', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178388, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129187', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840672583164985353, 'engineering', NULL, '门店紧急维修联系表(按照固定模版)', 'eng-00129188', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178389, 'engineering(newYDXJD)', NULL, '门店紧急维修联系表(按照固定模版)', 'eng-00129188', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840672583164985354, 'engineering', NULL, '提供日期', 'eng-00129189', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_date', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178390, 'engineering(newYDXJD)', NULL, '提供日期', 'eng-00129189', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_date', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840672583164985355, 'engineering', NULL, '分类', 'eng-00129190', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'B', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178391, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129190', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'B', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840672583164985356, 'engineering', NULL, '资料审核', 'eng-00129191', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178392, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129191', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840672583164985357, 'engineering', NULL, '合伙人满意度评分表', 'eng-00129192', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178393, 'engineering(newYDXJD)', NULL, '合伙人满意度评分表', 'eng-00129192', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'file_upload', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840672583164985358, 'engineering', NULL, '提供日期', 'eng-00129193', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_date', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178394, 'engineering(newYDXJD)', NULL, '提供日期', 'eng-00129193', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_date', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840672583164985359, 'engineering', NULL, '分类', 'eng-00129194', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'B', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178395, 'engineering(newYDXJD)', NULL, '分类', 'eng-00129194', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_classification', NULL, NULL, b'1', NULL, NULL, '1', NULL, 'B', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840672583164985360, 'engineering', NULL, '资料审核', 'eng-00129195', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178396, 'engineering(newYDXJD)', NULL, '资料审核', 'eng-00129195', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, 'data_review', NULL, NULL, b'1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1840672583164985361, 'engineering', NULL, '资料审核人', 'eng-00129196', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, 'STA', 'zlsh,zgsh', '1', 'jgysry', b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '1', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178352, 'engineering(newYDXJD)', NULL, '资料审核人', 'eng-00129196', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'form_select', NULL, NULL, 'STA', 'zlsh,zgsh', '1', 'jgysry', b'1', b'0', '2024-09-30 16:53:00', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '1', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL);



INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779545, 1840665969619992579, 'engineering', 1700404548427969768, '决策批复', 'eng-00129110', 40, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779632, 1843591114286178334, 'engineering(newYDXJD)', 1785230497929105408, '决策批复', 'eng-00129110', 40, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779546, 1840665969619992580, 'engineering', 1700404548427969768, '项目红线图(签约PDF)', 'eng-00129111', 41, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779633, 1843591114286178362, 'engineering(newYDXJD)', 1785230497929105408, '项目红线图(签约PDF)', 'eng-00129111', 41, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779547, 1840665969619992581, 'engineering', 1700404548427969768, '项目决策意见(截图)', 'eng-00129112', 42, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779634, 1843591114286178311, 'engineering(newYDXJD)', 1785230497929105408, '项目决策意见(截图)', 'eng-00129112', 42, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779548, 1840665969619992582, 'engineering', 1700404548427969768, '分类-设计非标', 'eng-00129113', 44, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779635, 1843591114286178312, 'engineering(newYDXJD)', 1785230497929105408, '分类-设计非标', 'eng-00129113', 44, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779549, 1840665969619992583, 'engineering', 1700404548427969768, '分类-特殊项', 'eng-00129114', 47, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779636, 1843591114286178313, 'engineering(newYDXJD)', 1785230497929105408, '分类-特殊项', 'eng-00129114', 47, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779550, 1840665969619992584, 'engineering', 1700404548427969768, '资料审核-设计非标', 'eng-00129115', 45, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779637, 1843591114286178314, 'engineering(newYDXJD)', 1785230497929105408, '资料审核-设计非标', 'eng-00129115', 45, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779551, 1840665969619992585, 'engineering', 1700404548427969768, '资料审核-特殊项', 'eng-00129116', 48, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779638, 1843591114286178315, 'engineering(newYDXJD)', 1785230497929105408, '资料审核-特殊项', 'eng-00129116', 48, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779552, 1840665969619992586, 'engineering', 1700404548427969768, '过程管理', 'eng-00129117', 49, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779639, 1843591114286178316, 'engineering(newYDXJD)', 1785230497929105408, '过程管理', 'eng-00129117', 49, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779553, 1840665969619992587, 'engineering', 1700404548427969768, '过程管理节点单(9个节点签字单)', 'eng-00129118', 50, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779640, 1843591114286178329, 'engineering(newYDXJD)', 1785230497929105408, '过程管理节点单(9个节点签字单)', 'eng-00129118', 50, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779554, 1840665969619992588, 'engineering', 1700404548427969768, '核心检查项', 'eng-00129119', 51, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779641, 1843591114286178318, 'engineering(newYDXJD)', 1785230497929105408, '核心检查项', 'eng-00129119', 51, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779555, 1840665969619992589, 'engineering', 1700404548427969768, '提供联动视频（消防）', 'eng-00129120', 52, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779642, 1843591114286178319, 'engineering(newYDXJD)', 1785230497929105408, '提供联动视频（消防）', 'eng-00129120', 52, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779556, 1840665969619992590, 'engineering', 1700404548427969768, '分类', 'eng-00129121', 53, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779643, 1843591114286178376, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129121', 53, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779557, 1840665969619992591, 'engineering', 1700404548427969768, '资料审核', 'eng-00129122', 54, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779644, 1843591114286178321, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129122', 54, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779558, 1840665969619992592, 'engineering', 1700404548427969768, '提供水压照片（消防）', 'eng-00129123', 55, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779645, 1843591114286178322, 'engineering(newYDXJD)', 1785230497929105408, '提供水压照片（消防）', 'eng-00129123', 55, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779559, 1840665969619992593, 'engineering', 1700404548427969768, '分类', 'eng-00129124', 56, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779646, 1843591114286178323, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129124', 56, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779560, 1840665969619992594, 'engineering', 1700404548427969768, '资料审核', 'eng-00129125', 57, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779647, 1843591114286178324, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129125', 57, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779561, 1840665969619992595, 'engineering', 1700404548427969768, '消火栓放水视频（消防）', 'eng-00129126', 58, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779648, 1843591114286178325, 'engineering(newYDXJD)', 1785230497929105408, '消火栓放水视频（消防）', 'eng-00129126', 58, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779562, 1840665969619992596, 'engineering', 1700404548427969768, '分类', 'eng-00129127', 59, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779649, 1843591114286178326, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129127', 59, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779563, 1840665969619992597, 'engineering', 1700404548427969768, '资料审核', 'eng-00129128', 60, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779650, 1843591114286178327, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129128', 60, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779564, 1840665969619992598, 'engineering', 1700404548427969768, '提供运营100%烟感测试记录（消防）', 'eng-00129129', 61, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779651, 1843591114286178328, 'engineering(newYDXJD)', 1785230497929105408, '提供运营100%烟感测试记录（消防）', 'eng-00129129', 61, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779565, 1840665969619992599, 'engineering', 1700404548427969768, '分类', 'eng-00129130', 62, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779652, 1843591114286178317, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129130', 62, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779566, 1840665969619992600, 'engineering', 1700404548427969768, '资料审核', 'eng-00129131', 63, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779653, 1843591114286178375, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129131', 63, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779567, 1840665969619992601, 'engineering', 1700404548427969768, '提供客房电箱回路照片（强电）', 'eng-00129132', 64, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779654, 1843591114286178335, 'engineering(newYDXJD)', 1785230497929105408, '提供客房电箱回路照片（强电）', 'eng-00129132', 64, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779568, 1840665969619992602, 'engineering', 1700404548427969768, '分类', 'eng-00129133', 65, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779655, 1843591114286178310, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129133', 65, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779569, 1840665969619992603, 'engineering', 1700404548427969768, '资料审核', 'eng-00129134', 66, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779656, 1843591114286178336, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129134', 66, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779570, 1840665969619992604, 'engineering', 1700404548427969768, '提供各楼层电箱回路照片（强电）', 'eng-00129135', 67, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779657, 1843591114286178337, 'engineering(newYDXJD)', 1785230497929105408, '提供各楼层电箱回路照片（强电）', 'eng-00129135', 67, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779571, 1840665969619992605, 'engineering', 1700404548427969768, '分类', 'eng-00129136', 68, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779658, 1843591114286178338, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129136', 68, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779572, 1840665969619992606, 'engineering', 1700404548427969768, '资料审核', 'eng-00129137', 69, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779659, 1843591114286178339, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129137', 69, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779573, 1840665969619992607, 'engineering', 1700404548427969768, '提供现场设备及铭牌照片（暖通）', 'eng-00129138', 70, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779660, 1843591114286178306, 'engineering(newYDXJD)', 1785230497929105408, '提供现场设备及铭牌照片（暖通）', 'eng-00129138', 70, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779574, 1840665969619992608, 'engineering', 1700404548427969768, '分类', 'eng-00129139', 71, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779661, 1843591114286178341, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129139', 71, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779575, 1840665969619992609, 'engineering', 1700404548427969768, '资料审核', 'eng-00129140', 72, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779662, 1843591114286178342, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129140', 72, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779576, 1840665969619992610, 'engineering', 1700404548427969768, '提供现场设备及铭牌照片（给排水）', 'eng-00129141', 73, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779663, 1843591114286178343, 'engineering(newYDXJD)', 1785230497929105408, '提供现场设备及铭牌照片（给排水）', 'eng-00129141', 73, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779577, 1840665969619992611, 'engineering', 1700404548427969768, '分类', 'eng-00129142', 74, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779664, 1843591114286178344, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129142', 74, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779578, 1840665969619992612, 'engineering', 1700404548427969768, '资料审核', 'eng-00129143', 75, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779665, 1843591114286178345, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129143', 75, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779579, 1840665969619992613, 'engineering', 1700404548427969768, '提供各楼层水压检测照片（给排水）', 'eng-00129144', 76, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779666, 1843591114286178346, 'engineering(newYDXJD)', 1785230497929105408, '提供各楼层水压检测照片（给排水）', 'eng-00129144', 76, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779580, 1840665969619992614, 'engineering', 1700404548427969768, '分类', 'eng-00129145', 77, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779667, 1843591114286178347, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129145', 77, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779581, 1840665969619992615, 'engineering', 1700404548427969768, '资料审核', 'eng-00129146', 78, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779668, 1843591114286178348, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129146', 78, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779582, 1840665969619992616, 'engineering', 1700404548427969768, '提供梯控测试视频（电梯）', 'eng-00129147', 79, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779669, 1843591114286178349, 'engineering(newYDXJD)', 1785230497929105408, '提供梯控测试视频（电梯）', 'eng-00129147', 79, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779583, 1840665969619992617, 'engineering', 1700404548427969768, '分类', 'eng-00129148', 80, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779670, 1843591114286178350, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129148', 80, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779584, 1840665969619992618, 'engineering', 1700404548427969768, '资料审核', 'eng-00129149', 81, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779671, 1843591114286178340, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129149', 81, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779585, 1840665969619992619, 'engineering', 1700404548427969768, '提供合格证照片（电梯）', 'eng-00129150', 82, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779672, 1843591114286178307, 'engineering(newYDXJD)', 1785230497929105408, '提供合格证照片（电梯）', 'eng-00129150', 82, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779586, 1840665969619992620, 'engineering', 1700404548427969768, '分类', 'eng-00129151', 83, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779673, 1843591114286178308, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129151', 83, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779587, 1840665969619992621, 'engineering', 1700404548427969768, '资料审核', 'eng-00129152', 84, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779674, 1843591114286178320, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129152', 84, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779588, 1840665969619992622, 'engineering', 1700404548427969768, '提供三方通话测试视频（电梯）', 'eng-00129153', 85, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779675, 1843591114286178309, 'engineering(newYDXJD)', 1785230497929105408, '提供三方通话测试视频（电梯）', 'eng-00129153', 85, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779589, 1840665969619992623, 'engineering', 1700404548427969768, '分类', 'eng-00129154', 86, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779676, 1843591114286178372, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129154', 86, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779590, 1840665969619992624, 'engineering', 1700404548427969768, '资料审核', 'eng-00129155', 87, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779677, 1843591114286178353, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129155', 87, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779591, 1840665969619992625, 'engineering', 1700404548427969768, '提供100%客房隔音测试记录（隔音）', 'eng-00129156', 88, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779678, 1843591114286178354, 'engineering(newYDXJD)', 1785230497929105408, '提供100%客房隔音测试记录（隔音）', 'eng-00129156', 88, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779592, 1840665969619992626, 'engineering', 1700404548427969768, '分类', 'eng-00129157', 89, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779679, 1843591114286178355, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129157', 89, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779593, 1840665969619992627, 'engineering', 1700404548427969768, '资料审核', 'eng-00129158', 90, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779680, 1843591114286178356, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129158', 90, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779594, 1840665969619992628, 'engineering', 1700404548427969768, '分类-隐蔽验收报告', 'eng-00129159', 94, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779681, 1843591114286178357, 'engineering(newYDXJD)', 1785230497929105408, '分类-隐蔽验收报告', 'eng-00129159', 94, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779595, 1840665969619992629, 'engineering', 1700404548427969768, '资料审核-隐蔽验收报告', 'eng-00129160', 95, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779682, 1843591114286178358, 'engineering(newYDXJD)', 1785230497929105408, '资料审核-隐蔽验收报告', 'eng-00129160', 95, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779596, 1840665969619992630, 'engineering', 1700404548427969768, '分类-样板间验收报告', 'eng-00129161', 97, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779683, 1843591114286178359, 'engineering(newYDXJD)', 1785230497929105408, '分类-样板间验收报告', 'eng-00129161', 97, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779597, 1840665969619992631, 'engineering', 1700404548427969768, '资料审核-样板间验收报告', 'eng-00129162', 98, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779684, 1843591114286178360, 'engineering(newYDXJD)', 1785230497929105408, '资料审核-样板间验收报告', 'eng-00129162', 98, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779598, 1840665969619992632, 'engineering', 1700404548427969768, '竣工验收申请表', 'eng-00129163', 99, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779685, 1843591114286178361, 'engineering(newYDXJD)', 1785230497929105408, '竣工验收申请表', 'eng-00129163', 99, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779599, 1840665969619992633, 'engineering', 1700404548427969768, '分类', 'eng-00129164', 100, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779686, 1843591114286178374, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129164', 100, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779600, 1840665969619992634, 'engineering', 1700404548427969768, '资料审核', 'eng-00129165', 101, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779687, 1843591114286178363, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129165', 101, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779601, 1840665969619992635, 'engineering', 1700404548427969768, '飞行质检报告及整改回复', 'eng-00129166', 102, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779688, 1843591114286178364, 'engineering(newYDXJD)', 1785230497929105408, '飞行质检报告及整改回复', 'eng-00129166', 102, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779602, 1840665969619992636, 'engineering', 1700404548427969768, '分类', 'eng-00129167', 103, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779689, 1843591114286178365, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129167', 103, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779603, 1840665969619992637, 'engineering', 1700404548427969768, '资料审核', 'eng-00129168', 104, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779690, 1843591114286178366, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129168', 104, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779604, 1840665969619992638, 'engineering', 1700404548427969768, '分类-客房门切割', 'eng-00129169', 106, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779691, 1843591114286178367, 'engineering(newYDXJD)', 1785230497929105408, '分类-客房门切割', 'eng-00129169', 106, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779605, 1840665969619992639, 'engineering', 1700404548427969768, '资料审核-客房门切割', 'eng-00129170', 107, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779692, 1843591114286178368, 'engineering(newYDXJD)', 1785230497929105408, '资料审核-客房门切割', 'eng-00129170', 107, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779606, 1840665969619992640, 'engineering', 1700404548427969768, '分类-竣工验收申请函', 'eng-00129171', 109, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779693, 1843591114286178369, 'engineering(newYDXJD)', 1785230497929105408, '分类-竣工验收申请函', 'eng-00129171', 109, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779607, 1840665969619992641, 'engineering', 1700404548427969768, '资料审核-竣工验收申请函', 'eng-00129172', 110, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779694, 1843591114286178370, 'engineering(newYDXJD)', 1785230497929105408, '资料审核-竣工验收申请函', 'eng-00129172', 110, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779608, 1840665969619992642, 'engineering', 1700404548427969768, '施工单位、特许商签字确认竣工自检清单', 'eng-00129173', 111, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779695, 1843591114286178371, 'engineering(newYDXJD)', 1785230497929105408, '施工单位、特许商签字确认竣工自检清单', 'eng-00129173', 111, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779609, 1840665969619992643, 'engineering', 1700404548427969768, '分类', 'eng-00129174', 112, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779696, 1843591114286178373, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129174', 112, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779610, 1840665969619992644, 'engineering', 1700404548427969768, '资料审核', 'eng-00129175', 113, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779697, 1843591114286178386, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129175', 113, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779611, 1840665969619992645, 'engineering', 1700404548427969768, '分类-竣工自检ppt', 'eng-00129176', 115, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779698, 1843591114286178377, 'engineering(newYDXJD)', 1785230497929105408, '分类-竣工自检ppt', 'eng-00129176', 115, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779612, 1840665969619992646, 'engineering', 1700404548427969768, '资料审核-竣工自检ppt', 'eng-00129177', 116, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779699, 1843591114286178378, 'engineering(newYDXJD)', 1785230497929105408, '资料审核-竣工自检ppt', 'eng-00129177', 116, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779613, 1840665969619992647, 'engineering', 1700404548427969768, '隔音测试记录表(按照模版、运营确认/邮件)', 'eng-00129178', 117, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779700, 1843591114286178379, 'engineering(newYDXJD)', 1785230497929105408, '隔音测试记录表(按照模版、运营确认/邮件)', 'eng-00129178', 117, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779614, 1840665969619992648, 'engineering', 1700404548427969768, '分类', 'eng-00129179', 118, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779701, 1843591114286178380, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129179', 118, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779615, 1840665969619992649, 'engineering', 1700404548427969768, '资料审核', 'eng-00129180', 119, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779702, 1843591114286178381, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129180', 119, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779616, 1840665969619992650, 'engineering', 1700404548427969768, '分类-空气检测合格', 'eng-00129181', 121, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779703, 1843591114286178382, 'engineering(newYDXJD)', 1785230497929105408, '分类-空气检测合格', 'eng-00129181', 121, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, '系统:0', NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779617, 1840672583164985347, 'engineering', 1700404548427969768, '资料审核-空气检测合格', 'eng-00129182', 122, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779704, 1843591114286178383, 'engineering(newYDXJD)', 1785230497929105408, '资料审核-空气检测合格', 'eng-00129182', 122, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779618, 1840672583164985348, 'engineering', 1700404548427969768, '分类-装配板抽样检测', 'eng-00129183', 125, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779705, 1843591114286178384, 'engineering(newYDXJD)', 1785230497929105408, '分类-装配板抽样检测', 'eng-00129183', 125, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779619, 1840672583164985349, 'engineering', 1700404548427969768, '资料审核-装配板抽样检测', 'eng-00129184', 126, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779706, 1843591114286178351, 'engineering(newYDXJD)', 1785230497929105408, '资料审核-装配板抽样检测', 'eng-00129184', 126, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779620, 1840672583164985350, 'engineering', 1700404548427969768, '门头雨棚结构证明文件', 'eng-00129185', 127, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779707, 1843591114286178385, 'engineering(newYDXJD)', 1785230497929105408, '门头雨棚结构证明文件', 'eng-00129185', 127, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779621, 1840672583164985351, 'engineering', 1700404548427969768, '分类', 'eng-00129186', 128, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779708, 1843591114286178387, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129186', 128, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779622, 1840672583164985352, 'engineering', 1700404548427969768, '资料审核', 'eng-00129187', 129, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779709, 1843591114286178388, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129187', 129, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779623, 1840672583164985353, 'engineering', 1700404548427969768, '门店紧急维修联系表(按照固定模版)', 'eng-00129188', 130, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779710, 1843591114286178389, 'engineering(newYDXJD)', 1785230497929105408, '门店紧急维修联系表(按照固定模版)', 'eng-00129188', 130, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779624, 1840672583164985354, 'engineering', 1700404548427969768, '提供日期', 'eng-00129189', 131, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779711, 1843591114286178390, 'engineering(newYDXJD)', 1785230497929105408, '提供日期', 'eng-00129189', 131, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779625, 1840672583164985355, 'engineering', 1700404548427969768, '分类', 'eng-00129190', 132, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779712, 1843591114286178391, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129190', 132, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779626, 1840672583164985356, 'engineering', 1700404548427969768, '资料审核', 'eng-00129191', 133, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779713, 1843591114286178392, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129191', 133, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779627, 1840672583164985357, 'engineering', 1700404548427969768, '合伙人满意度评分表', 'eng-00129192', 134, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779714, 1843591114286178393, 'engineering(newYDXJD)', 1785230497929105408, '合伙人满意度评分表', 'eng-00129192', 134, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779628, 1840672583164985358, 'engineering', 1700404548427969768, '提供日期', 'eng-00129193', 135, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779715, 1843591114286178394, 'engineering(newYDXJD)', 1785230497929105408, '提供日期', 'eng-00129193', 135, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779629, 1840672583164985359, 'engineering', 1700404548427969768, '分类', 'eng-00129194', 136, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779716, 1843591114286178395, 'engineering(newYDXJD)', 1785230497929105408, '分类', 'eng-00129194', 136, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779630, 1840672583164985360, 'engineering', 1700404548427969768, '资料审核', 'eng-00129195', 137, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779717, 1843591114286178396, 'engineering(newYDXJD)', 1785230497929105408, '资料审核', 'eng-00129195', 137, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779631, 1840672583164985361, 'engineering', 1700404548427969768, '资料审核人', 'eng-00129196', 178, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (8866660999987779718, 1843591114286178352, 'engineering(newYDXJD)', 1785230497929105408, '资料审核人', 'eng-00129196', 178, 3, b'1', b'0', '2024-09-30 17:32:02', NULL, NULL, NULL, NULL, NULL, NULL);



-- 竣工验收增加三级字段
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`)
VALUES (1840665969619992578, 'engineering', NULL, '查看整改清单', 'eng-00133055', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'receipt_popup', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:12:20', NULL, '系统:0', NULL, NULL, NULL, NULL, NULL, NULL, '1', '1', b'1', NULL, NULL, NULL, NULL, NULL, NULL, 'master_template', NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`)
VALUES (1840672583164985346, 'engineering(newYDXJD)', NULL, '查看整改清单', 'eng-00133055', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'receipt_popup', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2024-09-30 16:38:37', NULL, '系统:0', NULL, NULL, NULL, NULL, NULL, NULL, '1', '1', b'1', NULL, NULL, NULL, NULL, NULL, NULL, 'master_template', NULL, NULL);

INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`)
VALUES (1840665969754210305, 1840665969619992578, 'engineering', 1700404548427971078, '查看整改清单', 'eng-00133055', 13, 3, b'1', b'0', '2024-09-30 16:12:20', '2024-09-30 16:12:20', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`)
VALUES (1840672583559249922, 1840672583164985346, 'engineering(newYDXJD)', 1785230548105564160, '查看整改清单', 'eng-00133055', 13, 3, b'1', b'0', '2024-09-30 16:38:37', '2024-09-30 16:38:37', NULL, NULL, NULL, NULL, NULL);

ALTER TABLE t_project_node_info_1 MODIFY COLUMN node_id bigint AUTO_INCREMENT;
ALTER TABLE t_project_node_info_2 MODIFY COLUMN node_id bigint AUTO_INCREMENT;
ALTER TABLE t_project_node_info_3 MODIFY COLUMN node_id bigint AUTO_INCREMENT;
ALTER TABLE t_project_node_info_4 MODIFY COLUMN node_id bigint AUTO_INCREMENT;
ALTER TABLE t_project_node_info_5 MODIFY COLUMN node_id bigint AUTO_INCREMENT;
ALTER TABLE t_project_node_info_6 MODIFY COLUMN node_id bigint AUTO_INCREMENT;
ALTER TABLE t_project_node_info_7 MODIFY COLUMN node_id bigint AUTO_INCREMENT;
ALTER TABLE t_project_node_info_8 MODIFY COLUMN node_id bigint AUTO_INCREMENT;
ALTER TABLE t_project_node_info_9 MODIFY COLUMN node_id bigint AUTO_INCREMENT;


INSERT INTO `t_project_node_info_1` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_name = '查看整改清单'
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_1';

INSERT INTO `t_project_node_info_2` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_name = '查看整改清单'
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_2';

INSERT INTO `t_project_node_info_3` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_name = '查看整改清单'
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_3';

INSERT INTO `t_project_node_info_4` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_name = '查看整改清单'
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_4';

INSERT INTO `t_project_node_info_5` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_name = '查看整改清单'
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_5';

INSERT INTO `t_project_node_info_6` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_name = '查看整改清单'
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_6';

INSERT INTO `t_project_node_info_7` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_name = '查看整改清单'
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_7';

INSERT INTO `t_project_node_info_8` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_name = '查看整改清单'
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_8';

INSERT INTO `t_project_node_info_9` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_name = '查看整改清单'
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收'
  AND b.node_table_name = 't_project_node_info_9';

-- 增加字段【重要项不合格扣分数】【一般项不合格扣分数】
ALTER TABLE t_template_group_completion_receipt ADD important_item_unqualified_score varchar(255);
ALTER TABLE t_template_group_completion_receipt ADD generic_item_unqualified_score varchar(255);


-- 拍摄照片能不能传视频,比如传关门视频、消防联动视频
ALTER TABLE t_project_completion_photo ADD standard_videos varchar(255);
ALTER TABLE t_project_completion_receipt ADD standard_videos varchar(255);


-- 竣工验收整改审批，显示每一个审批人的符合结果
update t_template_queue set node_name='复核状态（项目经理）' where node_code='eng-00135011';
update t_template_queue set node_index=30 where node_code='eng-00135014';


update t_project_template set node_name='复核结果' where node_code='eng-00135010';
update t_template_queue set node_name='复核结果' where node_code='eng-00135010';

delete from t_project_template  WHERE `node_name` LIKE '%复核意见%' OR `node_name` LIKE '%复核状态%' ;

-- 复核状态复核意见
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1700404548427971938, 'engineering', NULL, '复核状态（项目经理）', 'eng-00135011', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2023-11-27 10:22:46', '2024-10-08 17:27:18', '系统:0', 'admin:1', 'review_status', NULL, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'master_template', NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1700404548427971939, 'engineering', NULL, '复核意见', 'eng-00135012', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'textarea', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2023-11-27 10:22:46', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '2', b'1', NULL, NULL, NULL, NULL, NULL, NULL, 'master_template', NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1785230559673454603, 'engineering(newYDXJD)', NULL, '复核状态（项目经理）', 'eng-00135011', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2023-11-27 10:22:46', '2024-10-08 17:28:30', '系统:0', 'admin:1', 'review_status', NULL, NULL, b'1', NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, 1700404548427971103, 1700404548427971200, 'split_plate', NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1785230559673454604, 'engineering(newYDXJD)', NULL, '复核意见', 'eng-00135012', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'textarea', NULL, NULL, NULL, NULL, '1', NULL, b'1', b'0', '2023-11-27 10:22:46', NULL, '系统:0', NULL, NULL, NULL, NULL, b'1', NULL, '1', '2', b'1', NULL, NULL, NULL, NULL, 1700404548427971103, 1700404548427971200, 'split_plate', NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843589045261180930, 'engineering', NULL, '复核状态（整改审核人）', 'eng-00135015', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, NULL, NULL, b'1', b'0', '2024-10-08 17:47:35', NULL, 'admin:1', NULL, 'review_status', NULL, NULL, NULL, NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843589228753592322, 'engineering', NULL, '复核意见', 'eng-00135016', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'textarea', NULL, NULL, NULL, NULL, NULL, NULL, b'1', b'0', '2024-10-08 17:48:19', '2024-10-08 17:48:49', 'admin:1', 'admin:1', NULL, NULL, NULL, NULL, NULL, '1', '2', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843589607914479617, 'engineering', NULL, '复核状态（工程负责人）', 'eng-00135017', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, NULL, NULL, b'1', b'0', '2024-10-08 17:49:49', NULL, 'admin:1', NULL, 'review_status', NULL, NULL, NULL, NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843589686981304322, 'engineering', NULL, '复核意见', 'eng-00135018', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'textarea', NULL, NULL, NULL, NULL, NULL, NULL, b'1', b'0', '2024-10-08 17:50:08', NULL, 'admin:1', NULL, NULL, NULL, NULL, NULL, NULL, '1', '2', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843589807370412033, 'engineering', NULL, '复核状态（交付负责人）', 'eng-00135019', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, NULL, NULL, b'1', b'0', '2024-10-08 17:50:37', NULL, 'admin:1', NULL, 'review_status', NULL, NULL, NULL, NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843589875481714690, 'engineering', NULL, '复核意见', 'eng-00135020', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'textarea', NULL, NULL, NULL, NULL, NULL, NULL, b'1', b'0', '2024-10-08 17:50:53', NULL, 'admin:1', NULL, NULL, NULL, NULL, NULL, NULL, '1', '2', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843590231527792641, 'engineering(newYDXJD)', NULL, '复核状态（整改审核人）', 'eng-00135015', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, NULL, NULL, b'1', b'0', '2024-10-08 17:52:18', NULL, 'admin:1', NULL, 'review_status', NULL, NULL, NULL, NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843590326637830145, 'engineering(newYDXJD)', NULL, '复核意见', 'eng-00135016', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'textarea', NULL, NULL, NULL, NULL, NULL, NULL, b'1', b'0', '2024-10-08 17:52:41', NULL, 'admin:1', NULL, NULL, NULL, NULL, NULL, NULL, '1', '2', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843590889324683265, 'engineering(newYDXJD)', NULL, '复核状态（工程负责人）', 'eng-00135017', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, NULL, NULL, b'1', b'0', '2024-10-08 17:54:55', NULL, 'admin:1', NULL, 'review_status', NULL, NULL, NULL, NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843590957758947329, 'engineering(newYDXJD)', NULL, '复核意见', 'eng-00135018', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'textarea', NULL, NULL, NULL, NULL, NULL, NULL, b'1', b'0', '2024-10-08 17:55:11', NULL, 'admin:1', NULL, NULL, NULL, NULL, NULL, NULL, '1', '2', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591045768028161, 'engineering(newYDXJD)', NULL, '复核状态（交付负责人）', 'eng-00135019', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'radio_button', NULL, NULL, NULL, NULL, NULL, NULL, b'1', b'0', '2024-10-08 17:55:32', NULL, 'admin:1', NULL, 'review_status', NULL, NULL, NULL, NULL, '1', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_project_template` (`template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_wbs`, `front_wbs_config`, `front_finish_wbs`, `is_key`, `key_front_wbs`, `plan_day`, `node_level`, `node_type`, `notice_day`, `relation_code`, `relation_type`, `job_code`, `use_case`, `down_code`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `start_sign`, `end_sign`, `total_day`, `is_mobile`, `role_code`, `is_edit`, `seat`, `is_wrap`, `remark`, `icon`, `formula`, `formula_code`, `one_template_id`, `two_template_id`, `stencil_level`, `is_show`, `file_check_format`) VALUES (1843591114286178305, 'engineering(newYDXJD)', NULL, '复核意见', 'eng-00135020', 1, 2, NULL, NULL, NULL, NULL, NULL, 3, 'textarea', NULL, NULL, NULL, NULL, NULL, NULL, b'1', b'0', '2024-10-08 17:55:49', NULL, 'admin:1', NULL, NULL, NULL, NULL, NULL, NULL, '1', '2', b'1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (1843589045588336642, 1843589045261180930, 'engineering', 1700404548427971200, '复核状态（整改审核人）', 'eng-00135015', 21, 3, b'1', b'0', '2024-10-08 17:47:35', '2024-10-08 17:47:35', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (1843589229143662594, 1843589228753592322, 'engineering', 1700404548427971200, '复核意见', 'eng-00135016', 22, 3, b'1', b'0', '2024-10-08 17:48:19', '2024-10-08 17:48:49', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (1843589608115806209, 1843589607914479617, 'engineering', 1700404548427971200, '复核状态（工程负责人）', 'eng-00135017', 23, 3, b'1', b'0', '2024-10-08 17:49:49', '2024-10-08 17:49:49', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (1843589687174242305, 1843589686981304322, 'engineering', 1700404548427971200, '复核意见', 'eng-00135018', 24, 3, b'1', b'0', '2024-10-08 17:50:08', '2024-10-08 17:50:08', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (1843589807693373441, 1843589807370412033, 'engineering', 1700404548427971200, '复核状态（交付负责人）', 'eng-00135019', 25, 3, b'1', b'0', '2024-10-08 17:50:37', '2024-10-08 17:50:37', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (1843589875674652673, 1843589875481714690, 'engineering', 1700404548427971200, '复核意见', 'eng-00135020', 26, 3, b'1', b'0', '2024-10-08 17:50:53', '2024-10-08 17:50:53', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (1843590231917862913, 1843590231527792641, 'engineering(newYDXJD)', 1785230559673454592, '复核状态（整改审核人）', 'eng-00135015', 21, 3, b'1', b'0', '2024-10-08 17:52:18', '2024-10-08 17:52:18', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (1843590326897876994, 1843590326637830145, 'engineering(newYDXJD)', 1785230559673454592, '复核意见', 'eng-00135016', 22, 3, b'1', b'0', '2024-10-08 17:52:41', '2024-10-08 17:52:41', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (1843590889647644673, 1843590889324683265, 'engineering(newYDXJD)', 1785230559673454592, '复核状态（工程负责人）', 'eng-00135017', 23, 3, b'1', b'0', '2024-10-08 17:54:55', '2024-10-08 17:54:55', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (1843590958216126465, 1843590957758947329, 'engineering(newYDXJD)', 1785230559673454592, '复核意见', 'eng-00135018', 24, 3, b'1', b'0', '2024-10-08 17:55:11', '2024-10-08 17:55:11', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (1843591046288121857, 1843591045768028161, 'engineering(newYDXJD)', 1785230559673454592, '复核状态（交付负责人）', 'eng-00135019', 25, 3, b'1', b'0', '2024-10-08 17:55:32', '2024-10-08 17:55:32', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `t_template_queue` (`template_queue_id`, `template_id`, `template_code`, `parent_id`, `node_name`, `node_code`, `node_index`, `node_level`, `is_enabled`, `is_delete`, `create_time`, `update_time`, `create_by`, `update_by`, `one_template_id`, `two_template_id`, `stencil_level`) VALUES (1843591114680442882, 1843591114286178305, 'engineering(newYDXJD)', 1785230559673454592, '复核意见', 'eng-00135020', 26, 3, b'1', b'0', '2024-10-08 17:55:49', '2024-10-08 17:55:49', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `t_project_node_info_1` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135015','eng-00135016','eng-00135017','eng-00135018','eng-00135019','eng-00135020')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_1';

INSERT INTO `t_project_node_info_2` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135015','eng-00135016','eng-00135017','eng-00135018','eng-00135019','eng-00135020')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_2';

INSERT INTO `t_project_node_info_3` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135015','eng-00135016','eng-00135017','eng-00135018','eng-00135019','eng-00135020')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_3';

INSERT INTO `t_project_node_info_4` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135015','eng-00135016','eng-00135017','eng-00135018','eng-00135019','eng-00135020')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_4';

INSERT INTO `t_project_node_info_5` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135015','eng-00135016','eng-00135017','eng-00135018','eng-00135019','eng-00135020')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_5';

INSERT INTO `t_project_node_info_6` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135015','eng-00135016','eng-00135017','eng-00135018','eng-00135019','eng-00135020')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_6';

INSERT INTO `t_project_node_info_7` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135015','eng-00135016','eng-00135017','eng-00135018','eng-00135019','eng-00135020')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_7';

INSERT INTO `t_project_node_info_8` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135015','eng-00135016','eng-00135017','eng-00135018','eng-00135019','eng-00135020')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_8';

INSERT INTO `t_project_node_info_9` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap`,
    `remark`,
    `job_code`,
    `relation_type`,
    `down_code`,
    `start_sign`,
    `is_show`
) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      'zy' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap,
      d.`remark`,
      d.`job_code`,
      d.`relation_type`,
      d.`down_code`,
      d.`start_sign`,
      d.`is_show`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00135015','eng-00135016','eng-00135017','eng-00135018','eng-00135019','eng-00135020')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收整改'
  AND b.node_table_name = 't_project_node_info_9';


update t_project_node_info_1 set start_sign='review_status' where create_by='zy' and node_code in ('eng-00135015','eng-00135017','eng-00135019');
update t_project_node_info_2 set start_sign='review_status' where create_by='zy' and node_code in ('eng-00135015','eng-00135017','eng-00135019');
update t_project_node_info_3 set start_sign='review_status' where create_by='zy' and node_code in ('eng-00135015','eng-00135017','eng-00135019');
update t_project_node_info_4 set start_sign='review_status' where create_by='zy' and node_code in ('eng-00135015','eng-00135017','eng-00135019');
update t_project_node_info_5 set start_sign='review_status' where create_by='zy' and node_code in ('eng-00135015','eng-00135017','eng-00135019');
update t_project_node_info_6 set start_sign='review_status' where create_by='zy' and node_code in ('eng-00135015','eng-00135017','eng-00135019');
update t_project_node_info_7 set start_sign='review_status' where create_by='zy' and node_code in ('eng-00135015','eng-00135017','eng-00135019');
update t_project_node_info_8 set start_sign='review_status' where create_by='zy' and node_code in ('eng-00135015','eng-00135017','eng-00135019');
update t_project_node_info_9 set start_sign='review_status' where create_by='zy' and node_code in ('eng-00135015','eng-00135017','eng-00135019');

update t_project_node_info_1 set seat=2,is_wrap=1 where create_by='zy' and node_code in ('eng-00135016','eng-00135018','eng-00135020');
update t_project_node_info_2 set seat=2,is_wrap=1 where create_by='zy' and node_code in ('eng-00135016','eng-00135018','eng-00135020');
update t_project_node_info_3 set seat=2,is_wrap=1 where create_by='zy' and node_code in ('eng-00135016','eng-00135018','eng-00135020');
update t_project_node_info_4 set seat=2,is_wrap=1 where create_by='zy' and node_code in ('eng-00135016','eng-00135018','eng-00135020');
update t_project_node_info_5 set seat=2,is_wrap=1 where create_by='zy' and node_code in ('eng-00135016','eng-00135018','eng-00135020');
update t_project_node_info_6 set seat=2,is_wrap=1 where create_by='zy' and node_code in ('eng-00135016','eng-00135018','eng-00135020');
update t_project_node_info_7 set seat=2,is_wrap=1 where create_by='zy' and node_code in ('eng-00135016','eng-00135018','eng-00135020');
update t_project_node_info_8 set seat=2,is_wrap=1 where create_by='zy' and node_code in ('eng-00135016','eng-00135018','eng-00135020');
update t_project_node_info_9 set seat=2,is_wrap=1 where create_by='zy' and node_code in ('eng-00135016','eng-00135018','eng-00135020');

update t_project_node_info_1 set node_name='复核状态（项目经理）',end_sign=null where node_code='eng-00135011';
update t_project_node_info_2 set node_name='复核状态（项目经理）',end_sign=null where node_code='eng-00135011';
update t_project_node_info_3 set node_name='复核状态（项目经理）',end_sign=null where node_code='eng-00135011';
update t_project_node_info_4 set node_name='复核状态（项目经理）',end_sign=null where node_code='eng-00135011';
update t_project_node_info_5 set node_name='复核状态（项目经理）',end_sign=null where node_code='eng-00135011';
update t_project_node_info_6 set node_name='复核状态（项目经理）',end_sign=null where node_code='eng-00135011';
update t_project_node_info_7 set node_name='复核状态（项目经理）',end_sign=null where node_code='eng-00135011';
update t_project_node_info_8 set node_name='复核状态（项目经理）',end_sign=null where node_code='eng-00135011';
update t_project_node_info_9 set node_name='复核状态（项目经理）',end_sign=null where node_code='eng-00135011';

update t_project_node_info_1 set node_name='复核结果' where node_code='eng-00135010';
update t_project_node_info_2 set node_name='复核结果' where node_code='eng-00135010';
update t_project_node_info_3 set node_name='复核结果' where node_code='eng-00135010';
update t_project_node_info_4 set node_name='复核结果' where node_code='eng-00135010';
update t_project_node_info_5 set node_name='复核结果' where node_code='eng-00135010';
update t_project_node_info_6 set node_name='复核结果' where node_code='eng-00135010';
update t_project_node_info_7 set node_name='复核结果' where node_code='eng-00135010';
update t_project_node_info_8 set node_name='复核结果' where node_code='eng-00135010';
update t_project_node_info_9 set node_name='复核结果' where node_code='eng-00135010';


update t_approve_template_detail set approve_role=1407639269599150140,modifiable_code='eng-00135015,eng-00135016' where approve_template_detail_id=1716763633909895168;
delete from t_approve_template_detail where approve_template_detail_id=1716763760586264576;
update t_approve_template_detail set approve_num=1.3,approve_index=3,modifiable_code='eng-00135017,eng-00135018' where approve_template_detail_id=1716763820367679488;
update t_approve_template_detail set approve_num=1.4,approve_index=4,modifiable_code='eng-00135019,eng-00135020' where approve_template_detail_id=1836595799014903808;

update t_approve_template_detail set approve_role=1407639269599150140,modifiable_code='eng-00135015,eng-00135016' where approve_template_detail_id=1782255161247404218;
delete from t_approve_template_detail where approve_template_detail_id=1782255161247404219;
update t_approve_template_detail set approve_num=1.3,approve_index=3,modifiable_code='eng-00135017,eng-00135018' where approve_template_detail_id=1782255161247404220;
update t_approve_template_detail set approve_num=1.4,approve_index=4,modifiable_code='eng-00135019,eng-00135020' where approve_template_detail_id=1836597214009495552;




-- 竣工验收申请任务字段排序
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 93 WHERE `node_code` = 'eng-00129059';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 96 WHERE `node_code` = 'eng-00129060';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 114 WHERE `node_code` = 'eng-00129061';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 108 WHERE `node_code` = 'eng-00129062';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 92 WHERE `node_code` = 'eng-00129063';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 120 WHERE `node_code` = 'eng-00129064';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 124 WHERE `node_code` = 'eng-00129065';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 91 WHERE `node_code` = 'eng-00129005';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 160 WHERE `node_code` = 'eng-00129015';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 161 WHERE `node_code` = 'eng-00129016';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 176 WHERE `node_code` = 'eng-00129057';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 175 WHERE `node_code` = 'eng-00129058';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 43 WHERE `node_code` = 'eng-00129066';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 46 WHERE `node_code` = 'eng-00129067';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 138 WHERE `node_code` = 'eng-00129068';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 105 WHERE `node_code` = 'eng-00129069';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 139 WHERE `node_code` = 'eng-00129070';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 140 WHERE `node_code` = 'eng-00129071';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 141 WHERE `node_code` = 'eng-00129072';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 142 WHERE `node_code` = 'eng-00129073';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 143 WHERE `node_code` = 'eng-00129074';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 144 WHERE `node_code` = 'eng-00129075';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 145 WHERE `node_code` = 'eng-00129076';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 146 WHERE `node_code` = 'eng-00129077';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 147 WHERE `node_code` = 'eng-00129078';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 148 WHERE `node_code` = 'eng-00129079';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 149 WHERE `node_code` = 'eng-00129080';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 150 WHERE `node_code` = 'eng-00129081';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 151 WHERE `node_code` = 'eng-00129082';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 152 WHERE `node_code` = 'eng-00129083';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 153 WHERE `node_code` = 'eng-00129084';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 154 WHERE `node_code` = 'eng-00129085';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 155 WHERE `node_code` = 'eng-00129086';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 156 WHERE `node_code` = 'eng-00129087';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 157 WHERE `node_code` = 'eng-00129088';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 158 WHERE `node_code` = 'eng-00129089';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 162 WHERE `node_code` = 'eng-00129090';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 163 WHERE `node_code` = 'eng-00129091';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 164 WHERE `node_code` = 'eng-00129092';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 165 WHERE `node_code` = 'eng-00129093';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 166 WHERE `node_code` = 'eng-00129094';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 167 WHERE `node_code` = 'eng-00129095';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 168 WHERE `node_code` = 'eng-00129096';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 169 WHERE `node_code` = 'eng-00129097';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 170 WHERE `node_code` = 'eng-00129098';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 171 WHERE `node_code` = 'eng-00129099';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 172 WHERE `node_code` = 'eng-00129100';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 173 WHERE `node_code` = 'eng-00129101';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 174 WHERE `node_code` = 'eng-00129102';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 177 WHERE `node_code` = 'eng-00129105';
UPDATE `atour-con`.`t_template_queue` SET `node_index` = 159 WHERE `node_code` = 'eng-00129106';





-- 字典
INSERT INTO `s_sys_dict` (`dict_id`, `name`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (240, 'data_classification', '竣工验收申请-资料分类', 'admin', 'admin', '2024-09-30 18:03:24', '2024-09-21 21:56:28');
INSERT INTO `s_sys_dict` (`dict_id`, `name`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (241, 'data_review', '竣工验收申请-资料审核', NULL, NULL, '2024-09-30 18:03:24', NULL);
-- 字典详情
INSERT INTO `s_sys_dict_detail` (`detail_id`, `dict_id`, `label`, `value`, `dict_sort`, `create_by`, `update_by`, `create_time`, `update_time`, `parent_id`, `next_id`) VALUES (5616, 240, 'A', 'A', 1, NULL, NULL, '2024-09-30 17:58:45', NULL, NULL, NULL);
INSERT INTO `s_sys_dict_detail` (`detail_id`, `dict_id`, `label`, `value`, `dict_sort`, `create_by`, `update_by`, `create_time`, `update_time`, `parent_id`, `next_id`) VALUES (5617, 240, 'B', 'B', 2, NULL, NULL, '2024-09-30 17:59:15', NULL, NULL, NULL);
INSERT INTO `s_sys_dict_detail` (`detail_id`, `dict_id`, `label`, `value`, `dict_sort`, `create_by`, `update_by`, `create_time`, `update_time`, `parent_id`, `next_id`) VALUES (5618, 241, '通过', 'pass', 1, NULL, NULL, '2024-09-30 18:04:35', NULL, NULL, NULL);
INSERT INTO `s_sys_dict_detail` (`detail_id`, `dict_id`, `label`, `value`, `dict_sort`, `create_by`, `update_by`, `create_time`, `update_time`, `parent_id`, `next_id`) VALUES (5619, 241, '有条件通过', 'conditional_passage', 2, NULL, NULL, '2024-09-30 18:05:03', NULL, NULL, NULL);
INSERT INTO `s_sys_dict_detail` (`detail_id`, `dict_id`, `label`, `value`, `dict_sort`, `create_by`, `update_by`, `create_time`, `update_time`, `parent_id`, `next_id`) VALUES (5620, 241, '不通过', 'not_go', 3, NULL, NULL, '2024-09-30 18:05:33', NULL, NULL, NULL);

-- 竣工验收整改说明添加整改中
INSERT INTO `s_sys_dict_detail` (`detail_id`, `dict_id`, `label`, `value`, `dict_sort`, `create_by`, `update_by`, `create_time`, `update_time`, `parent_id`, `next_id`)
VALUES (5621, 231, '整改中', 'in_process', 3, 'admin', 'admin', '2024-10-08 11:45:45', '2024-10-08 11:45:45', NULL, NULL);


-- 修改审批字段
ALTER TABLE `atour-con`.`t_approve_template_detail`
    MODIFY COLUMN `is_modifiable` int NULL DEFAULT NULL COMMENT '该审批角色是否可以修改' AFTER `approve_index`;
ALTER TABLE `atour-con`.`t_project_approve_detail`
    MODIFY COLUMN `is_modifiable` int NULL DEFAULT NULL COMMENT '该审批角色是否可以修改' AFTER `approve_index`;
-- 修改
UPDATE `atour-con`.`t_approve_template_detail` SET `modifiable_code` = 'eng-00129058,eng-00129057,eng-00129105,eng-00129196' WHERE `approve_template_detail_id` = 1706139556413313024;
UPDATE `atour-con`.`t_approve_template_detail` SET `modifiable_code` = 'eng-00129058,eng-00129057,eng-00129105,eng-00129196' WHERE `approve_template_detail_id` = 1706139556413313024;
-- 修改竣工验收申请审批流程
INSERT INTO `t_approve_template_detail` (`approve_template_detail_id`, `approve_template_id`, `parent_id`, `approve_num`, `approve_role`, `has_child`, `approve_group`, `approve_level`, `approve_mode`, `approve_begin`, `approve_index`, `is_modifiable`, `modifiable_code`, `create_time`, `create_by`, `update_time`, `update_by`, `is_enabled`, `is_delete`) VALUES (1843493112199122944, 49, NULL, '1.3', '1407639269599150139', NULL, '1', '1', 'all_pass', 'in_order', 3, 2, 'eng-00129158,eng-00129195,eng-00129191,eng-00129187,eng-00129184,eng-00129182,eng-00129180,eng-00129177,eng-00129175,eng-00129172,eng-00129170,eng-00129168,eng-00129165,eng-00129162,eng-00129160,eng-00129115,eng-00129155,eng-00129152,eng-00129149,eng-00129146,eng-00129143,eng-00129140,eng-00129137,eng-00129134,eng-00129131,eng-00129128,eng-00129125,eng-00129122,eng-00129116', '2024-10-08 11:26:23', 'admin:1', NULL, NULL, NULL, b'0');
INSERT INTO `t_approve_template_detail` (`approve_template_detail_id`, `approve_template_id`, `parent_id`, `approve_num`, `approve_role`, `has_child`, `approve_group`, `approve_level`, `approve_mode`, `approve_begin`, `approve_index`, `is_modifiable`, `modifiable_code`, `create_time`, `create_by`, `update_time`, `update_by`, `is_enabled`, `is_delete`) VALUES (1843493210605883392, 173, NULL, '1.3', '1407639269599150139', NULL, '1', '1', 'all_pass', 'in_order', 3, 2, 'eng-00129158,eng-00129195,eng-00129191,eng-00129187,eng-00129184,eng-00129182,eng-00129180,eng-00129177,eng-00129175,eng-00129172,eng-00129170,eng-00129168,eng-00129165,eng-00129162,eng-00129160,eng-00129115,eng-00129155,eng-00129152,eng-00129149,eng-00129146,eng-00129143,eng-00129140,eng-00129137,eng-00129134,eng-00129131,eng-00129128,eng-00129125,eng-00129122,eng-00129116', '2024-10-08 11:26:47', 'admin:1', NULL, NULL, NULL, b'0');


-- 修改 竣工验收-项目红线图(签约PDF)  关联 上会附件
UPDATE `atour-con`.`t_project_template` SET `relation_code` = 'eng-00103118' WHERE `node_code` = 'eng-00129111';

UPDATE `atour-con`.`t_project_template` SET `node_type` = 'file_upload', `end_sign` = 1 WHERE `node_code` LIKE '%eng-00129%' AND `node_name` IN ('飞行质检报告及整改回复', '隔音测试记录表(按照模版、运营确认/邮件)', '竣工验收申请表', '竣工验收申请函（特许商签章）', '竣工验收自检PPT（按照模版）', '客房门切割照片（水印相机拍摄）', '空气检测合格报告（检测项满足标准）', '门头雨棚结构证明文件', '设计非标OA/邮件报备审批确认文件', '施工单位、特许商签字确认竣工自检清单', '特殊项、非标项OA/邮件报备审批确认文件', '提供100%客房隔音测试记录（隔音）', '提供各楼层电箱回路照片（强电）', '提供各楼层水压检测照片（给排水）', '提供合格证照片（电梯）', '提供客房电箱回路照片（强电）', '提供联动视频（消防）', '提供三方通话测试视频（电梯）', '提供水压照片（消防）', '提供梯控测试视频（电梯）', '提供现场设备及铭牌照片（给排水）', '提供现场设备及铭牌照片（暖通）', '提供运营100%烟感测试记录（消防）', '消火栓放水视频（消防）', '样板间验收报告', '隐蔽验收申请自检资料、隐蔽验收报告', '装配板抽样检测报告（检测项满足标准）');

-- 系统模板-竣工验收-验收单是 list 样式
UPDATE `atour-con`.`t_project_template` SET `node_type` = 'list' WHERE `node_code` = 'eng-00133027';


-- 同步修改项目竣工验收申请任务中与系统竣工验收申请任务不同的三级字段
UPDATE t_project_node_info_1 A
    INNER JOIN (
    SELECT  A.*,B.is_wrap,B.is_edit,B.seat, B.`end_sign`  from t_template_queue A left join t_project_template B on A.node_code = B.node_code and A.template_code = B.template_code
    where  A.node_level = 3 and A.template_code = B.template_code  and A.node_code like '%eng-00129%'
    ) B ON A.node_code = B.node_code AND A.template_id = B.template_id
    SET A.node_index = B.node_index ,A.is_wrap = B.is_wrap ,A.is_edit =B.is_edit,A.seat=B.seat,A.`end_sign`=B.`end_sign`
where A.project_id IN ( SELECT project_id FROM t_project_group WHERE node_code LIKE '%eng-00129%' AND node_status NOT IN ( 'task_submit', 'task_fin' ) GROUP BY project_id );

UPDATE t_project_node_info_2 A
    INNER JOIN (
    SELECT  A.*,B.is_wrap,B.is_edit,B.seat, B.`end_sign`  from t_template_queue A left join t_project_template B on A.node_code = B.node_code and A.template_code = B.template_code
    where  A.node_level = 3 and A.template_code='engineering' and A.node_code like '%eng-00129%'
    ) B ON A.node_code = B.node_code AND A.template_id = B.template_id
    SET A.node_index = B.node_index ,A.is_wrap = B.is_wrap ,A.is_edit =B.is_edit,A.seat=B.seat,A.`end_sign`=B.`end_sign`
where A.project_id IN ( SELECT project_id FROM t_project_group WHERE node_code LIKE '%eng-00129%' AND node_status NOT IN ( 'task_submit', 'task_fin' ) GROUP BY project_id );

UPDATE t_project_node_info_3 A
    INNER JOIN (
    SELECT  A.*,B.is_wrap,B.is_edit,B.seat, B.`end_sign`  from t_template_queue A left join t_project_template B on A.node_code = B.node_code and A.template_code = B.template_code
    where  A.node_level = 3 and A.template_code='engineering' and A.node_code like '%eng-00129%'
    ) B ON A.node_code = B.node_code AND A.template_id = B.template_id
    SET A.node_index = B.node_index ,A.is_wrap = B.is_wrap ,A.is_edit =B.is_edit,A.seat=B.seat,A.`end_sign`=B.`end_sign`
where A.project_id IN ( SELECT project_id FROM t_project_group WHERE node_code LIKE '%eng-00129%' AND node_status NOT IN ( 'task_submit', 'task_fin' ) GROUP BY project_id );

UPDATE t_project_node_info_4 A
    INNER JOIN (
    SELECT  A.*,B.is_wrap,B.is_edit,B.seat, B.`end_sign`  from t_template_queue A left join t_project_template B on A.node_code = B.node_code and A.template_code = B.template_code
    where  A.node_level = 3 and A.template_code='engineering' and A.node_code like '%eng-00129%'
    ) B ON A.node_code = B.node_code AND A.template_id = B.template_id
    SET A.node_index = B.node_index ,A.is_wrap = B.is_wrap ,A.is_edit =B.is_edit,A.seat=B.seat,A.`end_sign`=B.`end_sign`
where A.project_id IN ( SELECT project_id FROM t_project_group WHERE node_code LIKE '%eng-00129%' AND node_status NOT IN ( 'task_submit', 'task_fin' ) GROUP BY project_id );

UPDATE t_project_node_info_5 A
    INNER JOIN (
    SELECT  A.*,B.is_wrap,B.is_edit,B.seat, B.`end_sign`  from t_template_queue A left join t_project_template B on A.node_code = B.node_code and A.template_code = B.template_code
    where  A.node_level = 3 and A.template_code='engineering' and A.node_code like '%eng-00129%'
    ) B ON A.node_code = B.node_code AND A.template_id = B.template_id
    SET A.node_index = B.node_index ,A.is_wrap = B.is_wrap ,A.is_edit =B.is_edit,A.seat=B.seat,A.`end_sign`=B.`end_sign`
where A.project_id IN ( SELECT project_id FROM t_project_group WHERE node_code LIKE '%eng-00129%' AND node_status NOT IN ( 'task_submit', 'task_fin' ) GROUP BY project_id );

UPDATE t_project_node_info_6 A
    INNER JOIN (
    SELECT  A.*,B.is_wrap,B.is_edit,B.seat, B.`end_sign`  from t_template_queue A left join t_project_template B on A.node_code = B.node_code and A.template_code = B.template_code
    where  A.node_level = 3 and A.template_code='engineering' and A.node_code like '%eng-00129%'
    ) B ON A.node_code = B.node_code AND A.template_id = B.template_id
    SET A.node_index = B.node_index ,A.is_wrap = B.is_wrap ,A.is_edit =B.is_edit,A.seat=B.seat,A.`end_sign`=B.`end_sign`
where  A.project_id IN ( SELECT project_id FROM t_project_group WHERE node_code LIKE '%eng-00129%' AND node_status NOT IN ( 'task_submit', 'task_fin' ) GROUP BY project_id );

UPDATE t_project_node_info_7 A
    INNER JOIN (
    SELECT  A.*,B.is_wrap,B.is_edit,B.seat, B.`end_sign`  from t_template_queue A left join t_project_template B on A.node_code = B.node_code and A.template_code = B.template_code
    where  A.node_level = 3 and A.template_code='engineering' and A.node_code like '%eng-00129%'
    ) B ON A.node_code = B.node_code AND A.template_id = B.template_id
    SET A.node_index = B.node_index ,A.is_wrap = B.is_wrap ,A.is_edit =B.is_edit,A.seat=B.seat,A.`end_sign`=B.`end_sign`
where  A.project_id IN ( SELECT project_id FROM t_project_group WHERE node_code LIKE '%eng-00129%' AND node_status NOT IN ( 'task_submit', 'task_fin' ) GROUP BY project_id );

UPDATE t_project_node_info_8 A
    INNER JOIN (
    SELECT  A.*,B.is_wrap,B.is_edit,B.seat, B.`end_sign`  from t_template_queue A left join t_project_template B on A.node_code = B.node_code and A.template_code = B.template_code
    where  A.node_level = 3 and A.template_code='engineering' and A.node_code like '%eng-00129%'
    ) B ON A.node_code = B.node_code AND A.template_id = B.template_id
    SET A.node_index = B.node_index ,A.is_wrap = B.is_wrap ,A.is_edit =B.is_edit,A.seat=B.seat,A.`end_sign`=B.`end_sign`
where  A.project_id IN ( SELECT project_id FROM t_project_group WHERE node_code LIKE '%eng-00129%' AND node_status NOT IN ( 'task_submit', 'task_fin' ) GROUP BY project_id );

UPDATE t_project_node_info_9 A
    INNER JOIN (
    SELECT  A.*,B.is_wrap,B.is_edit,B.seat, B.`end_sign`  from t_template_queue A left join t_project_template B on A.node_code = B.node_code and A.template_code = B.template_code
    where  A.node_level = 3 and A.template_code='engineering' and A.node_code like '%eng-00129%'
    ) B ON A.node_code = B.node_code AND A.template_id = B.template_id
    SET A.node_index = B.node_index ,A.is_wrap = B.is_wrap ,A.is_edit =B.is_edit,A.seat=B.seat,A.`end_sign`=B.`end_sign`
where  A.project_id IN ( SELECT project_id FROM t_project_group WHERE node_code LIKE '%eng-00129%' AND node_status NOT IN ( 'task_submit', 'task_fin' ) GROUP BY project_id );


-- 同步新增-项目竣工验收申请任务 系统竣工验收申请任务的缺少的三级字段
INSERT INTO `t_project_node_info_1` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00129111','eng-00129165','eng-00129175','eng-00129174','eng-00129173','eng-00129172','eng-00129171','eng-00129170','eng-00129169','eng-00129168','eng-00129167','eng-00129166','eng-00129176','eng-00129164','eng-00129163','eng-00129162','eng-00129161','eng-00129160','eng-00129159','eng-00129158','eng-00129157','eng-00129156','eng-00129155','eng-00129186','eng-00129196','eng-00129195','eng-00129194','eng-00129193','eng-00129192','eng-00129191','eng-00129190','eng-00129189','eng-00129188','eng-00129187','eng-00129154','eng-00129185','eng-00129184','eng-00129183','eng-00129182','eng-00133055','eng-00129181','eng-00129180','eng-00129179','eng-00129178','eng-00129177','eng-00129121','eng-00129131','eng-00129130','eng-00129129','eng-00129128','eng-00129127','eng-00129126','eng-00129125','eng-00129124','eng-00129123','eng-00129122','eng-00129132','eng-00129120','eng-00129119','eng-00129118','eng-00129117','eng-00129116','eng-00129115','eng-00129114','eng-00129113','eng-00129112','eng-00129110','eng-00129143','eng-00129153','eng-00129152','eng-00129151','eng-00129150','eng-00129149','eng-00129148','eng-00129147','eng-00129146','eng-00129145','eng-00129144','eng-00133055','eng-00129142','eng-00129141','eng-00129140','eng-00129139','eng-00129138','eng-00129137','eng-00129136','eng-00129135','eng-00129134','eng-00129133')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收申请' AND a.node_status NOT IN ( 'task_submit', 'task_fin' )
  AND b.node_table_name = 't_project_node_info_1';


INSERT INTO `t_project_node_info_2` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00129111','eng-00129165','eng-00129175','eng-00129174','eng-00129173','eng-00129172','eng-00129171','eng-00129170','eng-00129169','eng-00129168','eng-00129167','eng-00129166','eng-00129176','eng-00129164','eng-00129163','eng-00129162','eng-00129161','eng-00129160','eng-00129159','eng-00129158','eng-00129157','eng-00129156','eng-00129155','eng-00129186','eng-00129196','eng-00129195','eng-00129194','eng-00129193','eng-00129192','eng-00129191','eng-00129190','eng-00129189','eng-00129188','eng-00129187','eng-00129154','eng-00129185','eng-00129184','eng-00129183','eng-00129182','eng-00133055','eng-00129181','eng-00129180','eng-00129179','eng-00129178','eng-00129177','eng-00129121','eng-00129131','eng-00129130','eng-00129129','eng-00129128','eng-00129127','eng-00129126','eng-00129125','eng-00129124','eng-00129123','eng-00129122','eng-00129132','eng-00129120','eng-00129119','eng-00129118','eng-00129117','eng-00129116','eng-00129115','eng-00129114','eng-00129113','eng-00129112','eng-00129110','eng-00129143','eng-00129153','eng-00129152','eng-00129151','eng-00129150','eng-00129149','eng-00129148','eng-00129147','eng-00129146','eng-00129145','eng-00129144','eng-00133055','eng-00129142','eng-00129141','eng-00129140','eng-00129139','eng-00129138','eng-00129137','eng-00129136','eng-00129135','eng-00129134','eng-00129133')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收申请' AND a.node_status NOT IN ( 'task_submit', 'task_fin' )
  AND b.node_table_name = 't_project_node_info_2';


INSERT INTO `t_project_node_info_3` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00129111','eng-00129165','eng-00129175','eng-00129174','eng-00129173','eng-00129172','eng-00129171','eng-00129170','eng-00129169','eng-00129168','eng-00129167','eng-00129166','eng-00129176','eng-00129164','eng-00129163','eng-00129162','eng-00129161','eng-00129160','eng-00129159','eng-00129158','eng-00129157','eng-00129156','eng-00129155','eng-00129186','eng-00129196','eng-00129195','eng-00129194','eng-00129193','eng-00129192','eng-00129191','eng-00129190','eng-00129189','eng-00129188','eng-00129187','eng-00129154','eng-00129185','eng-00129184','eng-00129183','eng-00129182','eng-00133055','eng-00129181','eng-00129180','eng-00129179','eng-00129178','eng-00129177','eng-00129121','eng-00129131','eng-00129130','eng-00129129','eng-00129128','eng-00129127','eng-00129126','eng-00129125','eng-00129124','eng-00129123','eng-00129122','eng-00129132','eng-00129120','eng-00129119','eng-00129118','eng-00129117','eng-00129116','eng-00129115','eng-00129114','eng-00129113','eng-00129112','eng-00129110','eng-00129143','eng-00129153','eng-00129152','eng-00129151','eng-00129150','eng-00129149','eng-00129148','eng-00129147','eng-00129146','eng-00129145','eng-00129144','eng-00133055','eng-00129142','eng-00129141','eng-00129140','eng-00129139','eng-00129138','eng-00129137','eng-00129136','eng-00129135','eng-00129134','eng-00129133')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收申请' AND a.node_status NOT IN ( 'task_submit', 'task_fin' )
  AND b.node_table_name = 't_project_node_info_3';

INSERT INTO `t_project_node_info_4` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00129111','eng-00129165','eng-00129175','eng-00129174','eng-00129173','eng-00129172','eng-00129171','eng-00129170','eng-00129169','eng-00129168','eng-00129167','eng-00129166','eng-00129176','eng-00129164','eng-00129163','eng-00129162','eng-00129161','eng-00129160','eng-00129159','eng-00129158','eng-00129157','eng-00129156','eng-00129155','eng-00129186','eng-00129196','eng-00129195','eng-00129194','eng-00129193','eng-00129192','eng-00129191','eng-00129190','eng-00129189','eng-00129188','eng-00129187','eng-00129154','eng-00129185','eng-00129184','eng-00129183','eng-00129182','eng-00133055','eng-00129181','eng-00129180','eng-00129179','eng-00129178','eng-00129177','eng-00129121','eng-00129131','eng-00129130','eng-00129129','eng-00129128','eng-00129127','eng-00129126','eng-00129125','eng-00129124','eng-00129123','eng-00129122','eng-00129132','eng-00129120','eng-00129119','eng-00129118','eng-00129117','eng-00129116','eng-00129115','eng-00129114','eng-00129113','eng-00129112','eng-00129110','eng-00129143','eng-00129153','eng-00129152','eng-00129151','eng-00129150','eng-00129149','eng-00129148','eng-00129147','eng-00129146','eng-00129145','eng-00129144','eng-00133055','eng-00129142','eng-00129141','eng-00129140','eng-00129139','eng-00129138','eng-00129137','eng-00129136','eng-00129135','eng-00129134','eng-00129133')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收申请' AND a.node_status NOT IN ( 'task_submit', 'task_fin' )
  AND b.node_table_name = 't_project_node_info_4';


INSERT INTO `t_project_node_info_5` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00129111','eng-00129165','eng-00129175','eng-00129174','eng-00129173','eng-00129172','eng-00129171','eng-00129170','eng-00129169','eng-00129168','eng-00129167','eng-00129166','eng-00129176','eng-00129164','eng-00129163','eng-00129162','eng-00129161','eng-00129160','eng-00129159','eng-00129158','eng-00129157','eng-00129156','eng-00129155','eng-00129186','eng-00129196','eng-00129195','eng-00129194','eng-00129193','eng-00129192','eng-00129191','eng-00129190','eng-00129189','eng-00129188','eng-00129187','eng-00129154','eng-00129185','eng-00129184','eng-00129183','eng-00129182','eng-00133055','eng-00129181','eng-00129180','eng-00129179','eng-00129178','eng-00129177','eng-00129121','eng-00129131','eng-00129130','eng-00129129','eng-00129128','eng-00129127','eng-00129126','eng-00129125','eng-00129124','eng-00129123','eng-00129122','eng-00129132','eng-00129120','eng-00129119','eng-00129118','eng-00129117','eng-00129116','eng-00129115','eng-00129114','eng-00129113','eng-00129112','eng-00129110','eng-00129143','eng-00129153','eng-00129152','eng-00129151','eng-00129150','eng-00129149','eng-00129148','eng-00129147','eng-00129146','eng-00129145','eng-00129144','eng-00133055','eng-00129142','eng-00129141','eng-00129140','eng-00129139','eng-00129138','eng-00129137','eng-00129136','eng-00129135','eng-00129134','eng-00129133')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收申请' AND a.node_status NOT IN ( 'task_submit', 'task_fin' )
  AND b.node_table_name = 't_project_node_info_5';


INSERT INTO `t_project_node_info_6` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00129111','eng-00129165','eng-00129175','eng-00129174','eng-00129173','eng-00129172','eng-00129171','eng-00129170','eng-00129169','eng-00129168','eng-00129167','eng-00129166','eng-00129176','eng-00129164','eng-00129163','eng-00129162','eng-00129161','eng-00129160','eng-00129159','eng-00129158','eng-00129157','eng-00129156','eng-00129155','eng-00129186','eng-00129196','eng-00129195','eng-00129194','eng-00129193','eng-00129192','eng-00129191','eng-00129190','eng-00129189','eng-00129188','eng-00129187','eng-00129154','eng-00129185','eng-00129184','eng-00129183','eng-00129182','eng-00133055','eng-00129181','eng-00129180','eng-00129179','eng-00129178','eng-00129177','eng-00129121','eng-00129131','eng-00129130','eng-00129129','eng-00129128','eng-00129127','eng-00129126','eng-00129125','eng-00129124','eng-00129123','eng-00129122','eng-00129132','eng-00129120','eng-00129119','eng-00129118','eng-00129117','eng-00129116','eng-00129115','eng-00129114','eng-00129113','eng-00129112','eng-00129110','eng-00129143','eng-00129153','eng-00129152','eng-00129151','eng-00129150','eng-00129149','eng-00129148','eng-00129147','eng-00129146','eng-00129145','eng-00129144','eng-00133055','eng-00129142','eng-00129141','eng-00129140','eng-00129139','eng-00129138','eng-00129137','eng-00129136','eng-00129135','eng-00129134','eng-00129133')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收申请' AND a.node_status NOT IN ( 'task_submit', 'task_fin' )
  AND b.node_table_name = 't_project_node_info_6';


INSERT INTO `t_project_node_info_7` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00129111','eng-00129165','eng-00129175','eng-00129174','eng-00129173','eng-00129172','eng-00129171','eng-00129170','eng-00129169','eng-00129168','eng-00129167','eng-00129166','eng-00129176','eng-00129164','eng-00129163','eng-00129162','eng-00129161','eng-00129160','eng-00129159','eng-00129158','eng-00129157','eng-00129156','eng-00129155','eng-00129186','eng-00129196','eng-00129195','eng-00129194','eng-00129193','eng-00129192','eng-00129191','eng-00129190','eng-00129189','eng-00129188','eng-00129187','eng-00129154','eng-00129185','eng-00129184','eng-00129183','eng-00129182','eng-00133055','eng-00129181','eng-00129180','eng-00129179','eng-00129178','eng-00129177','eng-00129121','eng-00129131','eng-00129130','eng-00129129','eng-00129128','eng-00129127','eng-00129126','eng-00129125','eng-00129124','eng-00129123','eng-00129122','eng-00129132','eng-00129120','eng-00129119','eng-00129118','eng-00129117','eng-00129116','eng-00129115','eng-00129114','eng-00129113','eng-00129112','eng-00129110','eng-00129143','eng-00129153','eng-00129152','eng-00129151','eng-00129150','eng-00129149','eng-00129148','eng-00129147','eng-00129146','eng-00129145','eng-00129144','eng-00133055','eng-00129142','eng-00129141','eng-00129140','eng-00129139','eng-00129138','eng-00129137','eng-00129136','eng-00129135','eng-00129134','eng-00129133')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收申请' AND a.node_status NOT IN ( 'task_submit', 'task_fin' )
  AND b.node_table_name = 't_project_node_info_7';


INSERT INTO `t_project_node_info_8` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00129111','eng-00129165','eng-00129175','eng-00129174','eng-00129173','eng-00129172','eng-00129171','eng-00129170','eng-00129169','eng-00129168','eng-00129167','eng-00129166','eng-00129176','eng-00129164','eng-00129163','eng-00129162','eng-00129161','eng-00129160','eng-00129159','eng-00129158','eng-00129157','eng-00129156','eng-00129155','eng-00129186','eng-00129196','eng-00129195','eng-00129194','eng-00129193','eng-00129192','eng-00129191','eng-00129190','eng-00129189','eng-00129188','eng-00129187','eng-00129154','eng-00129185','eng-00129184','eng-00129183','eng-00129182','eng-00133055','eng-00129181','eng-00129180','eng-00129179','eng-00129178','eng-00129177','eng-00129121','eng-00129131','eng-00129130','eng-00129129','eng-00129128','eng-00129127','eng-00129126','eng-00129125','eng-00129124','eng-00129123','eng-00129122','eng-00129132','eng-00129120','eng-00129119','eng-00129118','eng-00129117','eng-00129116','eng-00129115','eng-00129114','eng-00129113','eng-00129112','eng-00129110','eng-00129143','eng-00129153','eng-00129152','eng-00129151','eng-00129150','eng-00129149','eng-00129148','eng-00129147','eng-00129146','eng-00129145','eng-00129144','eng-00133055','eng-00129142','eng-00129141','eng-00129140','eng-00129139','eng-00129138','eng-00129137','eng-00129136','eng-00129135','eng-00129134','eng-00129133')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收申请' AND a.node_status NOT IN ( 'task_submit', 'task_fin' )
  AND b.node_table_name = 't_project_node_info_8';


INSERT INTO `t_project_node_info_9` (
    `project_id`,
    `template_queue_id`,
    `template_id`,
    `parent_id`,
    `node_code`,
    `node_name`,
    `node_wbs`,
    `node_index`,
    `node_level`,
    `node_type`,
    `node_status`,
    `use_case`,
    `is_delete`,
    `create_time`,
    `create_by`,
    `is_enabled`,
    `is_mobile`,
    `is_edit`,
    `seat`,
    `is_wrap` ,
    `remark` ,
    `job_code` ,
    `relation_type` ,
    `down_code` ,
    `start_sign` ,
    `is_show`,
    `end_sign`,
    `relation_code`

) SELECT
      a.project_id,
      c.template_queue_id,
      c.template_id,
      c.parent_id,
      d.node_code,
      d.node_name,
      d.node_wbs,
      c.node_index,
      d.node_level,
      d.node_type,
      'task_unfin' AS node_status,
      1 AS use_case,
      0 AS is_delete,
      NOW() AS create_time,
      '系统:0' AS create_by,
      1 AS is_enabled,
      1 AS is_mobile,
      d.is_edit,
      d.seat,
      d.is_wrap ,
      d.`remark` ,
      d.`job_code` ,
      d.`relation_type` ,
      d.`down_code` ,
      d.`start_sign` ,
      d.`is_show`,
      d.`end_sign`,
      d.`relation_code`
FROM
    t_project_group a
        INNER JOIN t_project_info b ON a.project_id = b.project_id
        INNER JOIN t_template_queue c ON a.template_id = c.parent_id
        AND c.node_level = 3
        AND c.node_code in ('eng-00129111','eng-00129165','eng-00129175','eng-00129174','eng-00129173','eng-00129172','eng-00129171','eng-00129170','eng-00129169','eng-00129168','eng-00129167','eng-00129166','eng-00129176','eng-00129164','eng-00129163','eng-00129162','eng-00129161','eng-00129160','eng-00129159','eng-00129158','eng-00129157','eng-00129156','eng-00129155','eng-00129186','eng-00129196','eng-00129195','eng-00129194','eng-00129193','eng-00129192','eng-00129191','eng-00129190','eng-00129189','eng-00129188','eng-00129187','eng-00129154','eng-00129185','eng-00129184','eng-00129183','eng-00129182','eng-00133055','eng-00129181','eng-00129180','eng-00129179','eng-00129178','eng-00129177','eng-00129121','eng-00129131','eng-00129130','eng-00129129','eng-00129128','eng-00129127','eng-00129126','eng-00129125','eng-00129124','eng-00129123','eng-00129122','eng-00129132','eng-00129120','eng-00129119','eng-00129118','eng-00129117','eng-00129116','eng-00129115','eng-00129114','eng-00129113','eng-00129112','eng-00129110','eng-00129143','eng-00129153','eng-00129152','eng-00129151','eng-00129150','eng-00129149','eng-00129148','eng-00129147','eng-00129146','eng-00129145','eng-00129144','eng-00133055','eng-00129142','eng-00129141','eng-00129140','eng-00129139','eng-00129138','eng-00129137','eng-00129136','eng-00129135','eng-00129134','eng-00129133')
        INNER JOIN t_project_template d ON c.template_id = d.template_id
WHERE
        a.node_level = 2
  AND a.node_name = '竣工验收申请' AND a.node_status NOT IN ( 'task_submit', 'task_fin' )
  AND b.node_table_name = 't_project_node_info_9';

-- 项目模板-竣工验收-验收单是 list 样式
UPDATE `atour-con`.`t_project_node_info_1` SET `node_type` = 'list' WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_2` SET `node_type` = 'list' WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_3` SET `node_type` = 'list' WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_4` SET `node_type` = 'list' WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_5` SET `node_type` = 'list' WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_6` SET `node_type` = 'list' WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_7` SET `node_type` = 'list' WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_8` SET `node_type` = 'list' WHERE `node_code` = 'eng-00133027';
UPDATE `atour-con`.`t_project_node_info_9` SET `node_type` = 'list' WHERE `node_code` = 'eng-00133027';


--  修改当前最大长度的限制 会话级
SET SESSION group_concat_max_len = 102400;

-- 修改项目模版-竣工验收申请-项目决策意见(截图)-remark
UPDATE t_project_node_info_1 A
    INNER JOIN (
    SELECT
    project_id,
    GROUP_CONCAT(
    ( CASE node_code WHEN 'eng-00103140' THEN CONCAT( node_name, '：', remark) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103141' THEN CONCAT('</br>', node_name, '：', (SELECT label FROM `atour-con`.`s_sys_dict_detail` WHERE `dict_id` = '197' and `value` = remark) ) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103142' THEN CONCAT('</br>', node_name, '：', remark ) ELSE '' END )
    ) AS nodeCodeRemark

    FROM
    t_project_node_info_1
    WHERE
    node_code IN (
    'eng-00103140',
    'eng-00103141',
    'eng-00103142')
    group by project_id
    ) B ON A.project_id = B.project_id
    SET A.remark = B.nodeCodeRemark
where A.node_code='eng-00129112';

UPDATE t_project_node_info_2 A
    INNER JOIN (
    SELECT
    project_id,
    GROUP_CONCAT(
    ( CASE node_code WHEN 'eng-00103140' THEN CONCAT( node_name, '：', remark) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103141' THEN CONCAT('</br>', node_name, '：', (SELECT label FROM `atour-con`.`s_sys_dict_detail` WHERE `dict_id` = '197' and `value` = remark) ) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103142' THEN CONCAT('</br>', node_name, '：', remark ) ELSE '' END )
    ) AS nodeCodeRemark

    FROM
    t_project_node_info_2
    WHERE
    node_code IN (
    'eng-00103140',
    'eng-00103141',
    'eng-00103142')
    group by project_id
    ) B ON A.project_id = B.project_id
    SET A.remark = B.nodeCodeRemark
where A.node_code='eng-00129112';

UPDATE t_project_node_info_3 A
    INNER JOIN (
    SELECT
    project_id,
    GROUP_CONCAT(
    ( CASE node_code WHEN 'eng-00103140' THEN CONCAT( node_name, '：', remark) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103141' THEN CONCAT('</br>', node_name, '：', (SELECT label FROM `atour-con`.`s_sys_dict_detail` WHERE `dict_id` = '197' and `value` = remark) ) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103142' THEN CONCAT('</br>', node_name, '：', remark ) ELSE '' END )
    ) AS nodeCodeRemark

    FROM
    t_project_node_info_3
    WHERE
    node_code IN (
    'eng-00103140',
    'eng-00103141',
    'eng-00103142')
    group by project_id
    ) B ON A.project_id = B.project_id
    SET A.remark = B.nodeCodeRemark
where A.node_code='eng-00129112';

UPDATE t_project_node_info_4 A
    INNER JOIN (
    SELECT
    project_id,
    GROUP_CONCAT(
    ( CASE node_code WHEN 'eng-00103140' THEN CONCAT( node_name, '：', remark) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103141' THEN CONCAT('</br>', node_name, '：', (SELECT label FROM `atour-con`.`s_sys_dict_detail` WHERE `dict_id` = '197' and `value` = remark) ) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103142' THEN CONCAT('</br>', node_name, '：', remark ) ELSE '' END )
    ) AS nodeCodeRemark

    FROM
    t_project_node_info_4
    WHERE
    node_code IN (
    'eng-00103140',
    'eng-00103141',
    'eng-00103142')
    group by project_id
    ) B ON A.project_id = B.project_id
    SET A.remark = B.nodeCodeRemark
where   A.node_code='eng-00129112';

UPDATE t_project_node_info_5 A
    INNER JOIN (
    SELECT
    project_id,
    GROUP_CONCAT(
    ( CASE node_code WHEN 'eng-00103140' THEN CONCAT( node_name, '：', remark) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103141' THEN CONCAT('</br>', node_name, '：', (SELECT label FROM `atour-con`.`s_sys_dict_detail` WHERE `dict_id` = '197' and `value` = remark) ) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103142' THEN CONCAT('</br>', node_name, '：', remark ) ELSE '' END )
    ) AS nodeCodeRemark

    FROM
    t_project_node_info_5
    WHERE
    node_code IN (
    'eng-00103140',
    'eng-00103141',
    'eng-00103142')
    group by project_id
    ) B ON A.project_id = B.project_id
    SET A.remark = B.nodeCodeRemark
where A.node_code='eng-00129112';

UPDATE t_project_node_info_6 A
    INNER JOIN (
    SELECT
    project_id,
    GROUP_CONCAT(
    ( CASE node_code WHEN 'eng-00103140' THEN CONCAT( node_name, '：', remark) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103141' THEN CONCAT('</br>', node_name, '：', (SELECT label FROM `atour-con`.`s_sys_dict_detail` WHERE `dict_id` = '197' and `value` = remark) ) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103142' THEN CONCAT('</br>', node_name, '：', remark ) ELSE '' END )
    ) AS nodeCodeRemark

    FROM
    t_project_node_info_6
    WHERE
    node_code IN (
    'eng-00103140',
    'eng-00103141',
    'eng-00103142')
    group by project_id
    ) B ON A.project_id = B.project_id
    SET A.remark = B.nodeCodeRemark
where A.node_code='eng-00129112';

UPDATE t_project_node_info_7 A
    INNER JOIN (
    SELECT
    project_id,
    GROUP_CONCAT(
    ( CASE node_code WHEN 'eng-00103140' THEN CONCAT( node_name, '：', remark) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103141' THEN CONCAT('</br>', node_name, '：', (SELECT label FROM `atour-con`.`s_sys_dict_detail` WHERE `dict_id` = '197' and `value` = remark) ) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103142' THEN CONCAT('</br>', node_name, '：', remark ) ELSE '' END )
    ) AS nodeCodeRemark

    FROM
    t_project_node_info_7
    WHERE
    node_code IN (
    'eng-00103140',
    'eng-00103141',
    'eng-00103142')
    group by project_id
    ) B ON A.project_id = B.project_id
    SET A.remark = B.nodeCodeRemark
where A.node_code='eng-00129112';

UPDATE t_project_node_info_8 A
    INNER JOIN (
    SELECT
    project_id,
    GROUP_CONCAT(
    ( CASE node_code WHEN 'eng-00103140' THEN CONCAT( node_name, '：', remark) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103141' THEN CONCAT('</br>', node_name, '：', (SELECT label FROM `atour-con`.`s_sys_dict_detail` WHERE `dict_id` = '197' and `value` = remark) ) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103142' THEN CONCAT('</br>', node_name, '：', remark ) ELSE '' END )
    ) AS nodeCodeRemark

    FROM
    t_project_node_info_8
    WHERE
    node_code IN (
    'eng-00103140',
    'eng-00103141',
    'eng-00103142')
    group by project_id
    ) B ON A.project_id = B.project_id
    SET A.remark = B.nodeCodeRemark
where A.node_code='eng-00129112';

UPDATE t_project_node_info_9 A
    INNER JOIN (
    SELECT
    project_id,
    GROUP_CONCAT(
    ( CASE node_code WHEN 'eng-00103140' THEN CONCAT( node_name, '：', remark) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103141' THEN CONCAT('</br>', node_name, '：', (SELECT label FROM `atour-con`.`s_sys_dict_detail` WHERE `dict_id` = '197' and `value` = remark) ) ELSE '' END ),
    ( CASE node_code WHEN 'eng-00103142' THEN CONCAT('</br>', node_name, '：', remark ) ELSE '' END )
    ) AS nodeCodeRemark

    FROM
    t_project_node_info_9
    WHERE
    node_code IN (
    'eng-00103140',
    'eng-00103141',
    'eng-00103142')
    group by project_id
    ) B ON A.project_id = B.project_id
    SET A.remark = B.nodeCodeRemark
where A.node_code='eng-00129112';